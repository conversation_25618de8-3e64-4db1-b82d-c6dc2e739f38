

/*
#include "7seg_display.h"
#include "main.h"
#include "stdint.h"

// Pattern definitions
const uint8_t dispSegLookUp[10] = {
    0b00111111, // 0
    0b00000110, // 1
    0b01011011, // 2
    0b01001111, // 3
    0b01100110, // 4
    0b01101101, // 5
    0b01111101, // 6
    0b00000111, // 7
    0b01111111, // 8
    0b01101111  // 9
};

#define PATTERN_C  0b00111001  // Letter C pattern
#define PATTERN_BLANK 10       // Blank display

// Global variables
uint8_t position = 0;
uint8_t displayBuffer[6] = {1, 2, 3, 4, 5, 6};


// Define missing variables and constants
#define BLINK_INTERVAL 500  // Blinking interval in milliseconds
#define noOfDig 6          // Number of digits

// GPIO Port definitions (replace with actual GPIO handling)
volatile uint8_t P0;       // Port 0
volatile uint8_t P2;       // Port 2

uint8_t displayPointer = 0;
uint8_t c_display_mode = 0;
uint32_t last_c_update = 0;
uint8_t blink_state = 0;
uint8_t c_pair = 0;
uint8_t settingState = 0;
uint8_t dotBlink = 0;

void update7segment(void) {
    P2 = 0xFF; // Turn off all digits

    // Load segment data for current digit
    P0 = (P0 & 0x80); // Clear segment data (keep other bits intact)
    P0 |= (displayBuffer[displayPointer] & 0x7F); // Apply segment data

    // Test mode handling
    if (c_test_mode != 0) {
        uint32_t current_time = HAL_GetTick();
        // Toggle blink state every BLINK_INTERVAL (250ms)
        if (current_time - last_test_blink >= BLINK_INTERVAL) {
            test_blink_state = !test_blink_state;
            last_test_blink = current_time;
        }

        // Determine which digit pair to blink
        uint8_t start_digit = (c_test_mode - 1) * 2;
        if (displayPointer == start_digit || displayPointer == start_digit + 1) {
            if (!test_blink_state) {
                // Blank the digit during off phase
                P2 = 0xFF;
                displayPointer = (displayPointer + 1) % noOfDig;
                return; // Skip enabling the digit
            }
        }
    }

    if (c_display_mode) {
        uint32_t current_time = HAL_GetTick();
        
        if (current_time - last_c_update >= BLINK_INTERVAL) {
            if (blink_state) {
                // Show only current C pair
                if ((c_pair == 0 && (displayPointer == 0 || displayPointer == 1)) ||
                    (c_pair == 1 && (displayPointer == 2 || displayPointer == 3)) ||
                    (c_pair == 2 && (displayPointer == 4 || displayPointer == 5))) {
                    P2 = ~(1 << displayPointer);
                } else {
                    P2 = 0xFF; // Other digits off
                }
            } else {
                P2 = 0xFF; // All digits off during blink
            }
            
            if (current_time - last_c_update >= BLINK_INTERVAL * 2) {
                blink_state = !blink_state;
                if (blink_state) {
                    c_pair = (c_pair + 1) % 3;  // Rotate through pairs
                }
                last_c_update = current_time;
            }
        }
    } 
    else {
        // Normal display mode
        if (settingState && !dotBlink) {
            if ((settingState == 3 && (displayPointer == 0 || displayPointer == 1)) ||
                (settingState == 2 && (displayPointer == 2 || displayPointer == 3)) ||
                (settingState == 1 && (displayPointer == 4 || displayPointer == 5))) {
                P2 = 0xFF; // turn off all digits
            } else {
                P2 = ~(1 << displayPointer);
            }
        } else {
            P2 = ~(1 << displayPointer);
        }
    }

    // Move to next digit
    displayPointer = (displayPointer + 1) % noOfDig;
}

void updateTimeDisplay(uint8_t hours, uint8_t minutes, uint8_t seconds)
{
    // Now bcd_to_decimal is properly declared
    uint8_t hrs = bcd_to_decimal(hours);
    uint8_t mins = bcd_to_decimal(minutes);
    uint8_t secs = bcd_to_decimal(seconds);

    // Update display buffer
    displayBuffer[0] = hrs / 10;
    displayBuffer[1] = hrs % 10;
    displayBuffer[2] = mins / 10;
    displayBuffer[3] = mins % 10;
    displayBuffer[4] = secs / 10;
    displayBuffer[5] = secs % 10;
}

// Function to force all 7-segment outputs off (blank display)
void shutdown_7seg(void)
{
    P2 = 0xFF;
    
    for(int i = 0; i < noOfDig; i++){
        displayBuffer[i] = PATTERN_BLANK;
    }
}

// Function to blink the display
void blink_7seg(void)
{
    static uint32_t last_toggle = 0;
    static uint8_t blink_on = 1;  // 1 = show content, 0 = blank
    
    uint32_t current_time = HAL_GetTick();
    
    if(current_time - last_toggle >= BLINK_INTERVAL) {
        blink_on = !blink_on;            // Toggle state
        last_toggle = current_time;
    }
    
    if(blink_on) {
        update7segment();
    } else {
        shutdown_7seg();
    }
}


// 🔹 Function to display a digit on a single 7-segment display
void DisplayDigit()
{
    __disable_irq();
    
    // Turn off all digits first
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15, GPIO_PIN_SET);
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8|GPIO_PIN_9, GPIO_PIN_SET);
    
    // Get segment data
    uint8_t data;
    if(displayBuffer[position] == PATTERN_BLANK) {
        data = 0;  // All segments off
    }
    else if(displayBuffer[position] == PATTERN_C) {
        data = PATTERN_C;  // Letter C pattern
    }
    else {
        data = dispSegLookUp[displayBuffer[position]];  // Normal digit
    }
    
    // Set segments
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_10, (data & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_11, (data & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_12, (data & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, (data & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_3,  (data & 0x10) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_4,  (data & 0x20) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6,  (data & 0x40) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    
    // Enable current digit
    switch(position) {
        case 0: HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET); break;
        case 1: HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET); break;
        case 2: HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_RESET); break;
        case 3: HAL_GPIO_WritePin(GPIOB, GPIO_PIN_15, GPIO_PIN_RESET); break;
        case 4: HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8,  GPIO_PIN_RESET); break;
        case 5: HAL_GPIO_WritePin(GPIOA, GPIO_PIN_9,  GPIO_PIN_RESET); break;
    }
    
    position = (position + 1) % 6;
    __enable_irq();
}


void test_c_digits(uint8_t pattern)
{
    // Clear all digits first
    for(int i = 0; i < 6; i++) {
        displayBuffer[i] = PATTERN_BLANK;
    }
    //for every 500ms flash/blink the pins

    // Show pattern based on state
    switch(pattern) {
        case 1:  // First press - C1,2
            displayBuffer[0] = PATTERN_C;
            displayBuffer[1] = 1;
            break;
            
        case 2:  // Second press - C3,4
            displayBuffer[2] = PATTERN_C;
            displayBuffer[3] = 3;
            break;
            
        case 3:  // Third press - C5,6
            displayBuffer[4] = PATTERN_C;
            displayBuffer[5] = 5;
            break;
            
        default:  // State 0 or invalid - all blank
            // Already blanked above
            break;
    }

}

*/

/*

version2
void test_c_digits(uint8_t pattern) {
    // Clear display buffer first
    for (int i = 0; i < 6; i++) {
        displayBuffer[i] = PATTERN_BLANK;
    }

    // Handle pattern selection and disable test mode when needed
    switch (pattern) {
        case 1: // C1 & 2
            displayBuffer[0] = PATTERN_C;
            displayBuffer[1] = 1;
            c_test_mode = 1;
            break;
            
        case 2: // C3 & 4
            displayBuffer[2] = PATTERN_C;
            displayBuffer[3] = 3;
            c_test_mode = 2;
            break;
            
        case 3: // C5 & 6
            displayBuffer[4] = PATTERN_C;
            displayBuffer[5] = 5;
            c_test_mode = 3;
            break;
            
        default: // Exit test mode
            c_test_mode = 0;
            // Clear display buffer for time display
            for (int i = 0; i < 6; i++) {
                displayBuffer[i] = 0;
            }
            break;
    }

    // Reset blink state
    test_blink_state = 0;
    last_test_blink = HAL_GetTick();
}

*/

#v3
void update7segment(void) {
    // Always start by turning off all digits
    P2 = 0xFF;
    
    // Handle test mode blinking
    if(c_test_mode != 0) {
        uint32_t current_time = HAL_GetTick();
        
        // Toggle blink state every BLINK_INTERVAL
        if(current_time - last_test_blink >= BLINK_INTERVAL) {
            c_test_blink_state = !c_test_blink_state;
            last_test_blink = current_time;
            
            // Update buffer state
            uint8_t dig1 = (c_test_mode-1)*2;
            uint8_t dig2 = dig1+1;
            
            if(c_test_blink_state) {
                displayBuffer[dig1] = PATTERN_BLANK;
                displayBuffer[dig2] = PATTERN_BLANK;
            } else {
                displayBuffer[dig1] = (dig1 == 0) ? PATTERN_C : (dig1/2);
                displayBuffer[dig2] = dig2;
            }
        }
    }

    // Normal display handling
    P0 = (P0 & 0x80) | (displayBuffer[displayPointer] & 0x7F);
    P2 = ~(1 << displayPointer);
    
    // Move to next digit
    displayPointer = (displayPointer + 1) % noOfDig;
}
