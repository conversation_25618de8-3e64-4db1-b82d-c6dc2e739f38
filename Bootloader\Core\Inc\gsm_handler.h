#ifndef GSM_HANDLER_H
#define GSM_HANDLER_H

#include "main.h"

#define SERVER_URL          "your-server.com/firmware"
#define GSM_BLOCK_SIZE     1024    // Larger blocks for 4G

typedef struct {
    uint32_t total_size;
    uint32_t version;
    uint32_t crc;
} firmware_info_t;

HAL_StatusTypeDef gsm_init(void);
HAL_StatusTypeDef gsm_check_update(void);
HAL_StatusTypeDef gsm_receive_firmware(void);

#endif /* GSM_HANDLER_H */
