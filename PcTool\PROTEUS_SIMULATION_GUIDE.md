# 🔬 Proteus Simulation Guide - STM32 Firmware Updater Testing

## 🎯 **What You'll See in Proteus**

This guide shows you exactly how to test your STM32 bootloader with Arduino simulation in Proteus, demonstrating the automated firmware update process.

## 📋 **Protocol Data for 20KB Firmware**

### **Size Calculation for 20,388 bytes:**
```
20,388 decimal = 0x4F94 hex
Low Byte  = 0x94 = 148 decimal  
High Byte = 0x4F = 79 decimal
```

### **Complete Protocol Sequence:**
```
1. STM32 → Arduino: '.' (0x2E) [Handshake - repeats every 20ms]
2. Arduino → STM32: 'o' (0x6F) [Start update]
3. STM32 → Arduino: 'y' (0x79) [Request size low byte]
4. Arduino → STM32: 0x94 (148) [Size low byte]
5. STM32 → Arduino: 'x' (0x78) [Request size high byte] 
6. Arduino → STM32: 0x4F (79)  [Size high byte]
7. Data transfer (20,388 bytes):
   - STM32 → Arduino: 'y' → Arduino → STM32: data_byte_low
   - STM32 → Arduino: 'x' → Arduino → STM32: data_byte_high
   - Repeat 10,194 times (20,388 ÷ 2)
```

## 🔧 **Proteus Setup Instructions**

### **1. Circuit Connections**
```
Arduino Uno          STM32F103C8T6
-----------          -------------
Pin 2 (RX)    ←→    PA9 (TX/USART3)
Pin 3 (TX)    ←→    PA10 (RX/USART3)  
GND           ←→    GND
```

### **2. Component Configuration**

#### **Arduino Uno Settings:**
- Upload the `Arduino_STM32_Updater_Proteus.ino` sketch
- Set Serial Monitor to 9600 baud
- Enable "Show transmitted data" in terminal

#### **STM32F103C8T6 Settings:**
- Load your bootloader hex file
- Configure USART3: 115200 baud, 8N1
- Set PA9 as USART3_TX, PA10 as USART3_RX

### **3. Virtual Terminal Setup**
- **Arduino Terminal (COM2)**: 9600 baud - Shows progress and debug info
- **STM32 Terminal (COM1)**: 115200 baud - Shows bootloader messages

## 🚀 **Running the Simulation**

### **Step 1: Start Simulation**
1. Click "Play" in Proteus
2. Open both virtual terminals
3. You should see:

**Arduino Terminal (COM2):**
```
=== Arduino STM32 Firmware Updater ===
Simulating 20KB firmware update
Firmware size: 20388 bytes
Size bytes: Low=0x94 (148), High=0x4F (79)

Waiting for STM32 bootloader...
```

**STM32 Terminal (COM1):**
```
Bootloader v1:0 Started!!!

 Press 'o' to start Firmware Update...
```

### **Step 2: Handshake Phase**
**You'll see:**

**Arduino Terminal:**
```
RX: '.' (0x2E)
→ Handshake received! Sending 'o' to start update...
TX: 'o' (start update)
```

**STM32 Terminal:**
```
Firmware Update Started
```

### **Step 3: Size Exchange**
**Arduino Terminal:**
```
RX: 'y' (0x79)
→ Size request (low byte). Sending size low byte...
TX: 0x94 (148) - Size Low Byte

RX: 'x' (0x78)  
→ Size request (high byte). Sending size high byte...
TX: 0x4F (79) - Size High Byte
Total size sent: 20388 bytes
→ Starting data transfer...
```

**STM32 Terminal:**
```
Application Size = 20388 bytes
Erasing the Flash memory...
```

### **Step 4: Data Transfer**
**Arduino Terminal:**
```
Progress: 2.5% (512/20388 bytes)
Progress: 5.0% (1024/20388 bytes)
Progress: 7.5% (1536/20388 bytes)
...
Progress: 97.5% (19968/20388 bytes)
Progress: 100.0% (20388/20388 bytes)
→ All data sent! Update complete.
```

**STM32 Terminal:**
```
Received Block[0]
Received Block[1]
Received Block[2]
...
Received Block[19]
Gonna Jump to Application...
```

## 📊 **What Each Terminal Shows**

### **Arduino Terminal (COM2) - Your Automation Tool**
- **Handshake detection**: When STM32 sends '.'
- **Protocol responses**: Sending 'o', size bytes, data
- **Progress tracking**: Percentage and byte counts
- **Debug information**: All TX/RX with hex values

### **STM32 Terminal (COM1) - Bootloader Status**
- **Startup message**: Bootloader version
- **Update prompts**: "Press 'o' to start..."
- **Size confirmation**: "Application Size = 20388 bytes"
- **Flash operations**: Erasing, writing blocks
- **Completion**: "Gonna Jump to Application..."

## 🔍 **Manual Testing (If You Want to Type)**

If you want to manually test without Arduino automation:

### **In STM32 Terminal (COM1), type exactly:**
```
o                    [Start update]
(wait for 'y')
148                  [Size low byte - decimal]
(wait for 'x') 
79                   [Size high byte - decimal]
(wait for 'y')
170                  [First data byte]
(wait for 'x')
85                   [Second data byte]
... continue for 20,388 bytes
```

**But this is exactly why we created the automation!** 😅

## ⚠️ **Common Issues & Solutions**

### **"No handshake received"**
- Check UART connections (TX↔RX crossed)
- Verify baud rates (Arduino: 115200, STM32: 115200)
- Ensure STM32 bootloader is running

### **"Size error" in STM32**
- Verify size bytes: Low=148 (0x94), High=79 (0x4F)
- Check endianness (little-endian: low byte first)

### **"Data transfer timeout"**
- Arduino should respond to every 'y' and 'x' request
- Check SoftwareSerial connections
- Verify timing (STM32 waits 5 seconds per byte)

## 🎯 **Expected Results**

### **Successful Update Indicators:**
1. ✅ Handshake completes ('.' → 'o')
2. ✅ Size accepted (20388 bytes confirmed)
3. ✅ Flash erasing starts
4. ✅ Data blocks received (0-19)
5. ✅ Jump to application

### **Performance Metrics:**
- **Total time**: ~3-5 minutes in simulation
- **Data rate**: ~115200 baud effective
- **Blocks**: 20 blocks of 1KB each
- **Success rate**: 100% with proper connections

## 🔧 **Troubleshooting Commands**

### **Check Arduino State:**
Add to Arduino loop():
```cpp
if (Serial.available()) {
  char cmd = Serial.read();
  if (cmd == 's') printState();  // Print current state
}
```

### **Monitor STM32 Debug:**
Watch for these messages:
- "Bootloader v1:0 Started!!!" - System ready
- "Firmware Update Started" - Handshake OK
- "Application Size = 20388 bytes" - Size OK
- "Received Block[X]" - Data transfer OK

## 🎉 **Success Confirmation**

When everything works correctly, you'll see:

**Final Arduino Message:**
```
Progress: 100.0% (20388/20388 bytes)
→ All data sent! Update complete.
Update completed successfully!
STM32 should now restart with new firmware.
```

**Final STM32 Message:**
```
Received Block[19]
Gonna Jump to Application...
```

This demonstrates your automated firmware updater working perfectly! The same protocol is used by your Python tool, but with real firmware data instead of dummy bytes.

---

**🎯 This simulation proves your automation works and shows exactly what happens during a real firmware update!**
