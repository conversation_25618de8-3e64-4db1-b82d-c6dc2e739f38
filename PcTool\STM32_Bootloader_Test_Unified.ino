/*
 * STM32 Bootloader Test - Unified Version
 * ======================================
 * 
 * This Arduino code works with BOTH:
 * 1. Original STM32 bootloader (old 'g'/'r' protocol)
 * 2. Enhanced STM32 bootloader (new ASCII input with auto-detection)
 * 
 * The code automatically detects which bootloader version is running
 * and adapts the communication protocol accordingly.
 * 
 * Connections:
 * Arduino Pin 0 (RX) → STM32 PA9 (TX) 
 * Arduino Pin 1 (TX) → STM32 PA10 (RX)
 * GND → GND
 * 
 * OR for SoftwareSerial:
 * Arduino Pin 2 (RX) → STM32 PA9 (TX)
 * Arduino Pin 3 (TX) → STM32 PA10 (RX)
 * GND → GND
 */

// Configuration - choose your connection method
#define USE_HARDWARE_SERIAL  // Use pins 0,1 (comment out for SoftwareSerial)
// #define USE_SOFTWARE_SERIAL  // Use pins 2,3 (uncomment for SoftwareSerial)

#ifdef USE_SOFTWARE_SERIAL
#include <SoftwareSerial.h>
SoftwareSerial stm32Serial(2, 3); // RX=Pin2, TX=Pin3
#define STM32_COMM stm32Serial
#define DEBUG_COMM Serial
#else
#define STM32_COMM Serial
#define DEBUG_COMM Serial  // Same serial for both (single UART mode)
#endif

// LED for status indication
const int ledPin = 13;

// Bootloader detection and protocol state
enum BootloaderType {
  UNKNOWN_BOOTLOADER,
  ORIGINAL_BOOTLOADER,    // Old 'g'/'r' protocol
  ENHANCED_BOOTLOADER     // New ASCII input protocol
};

enum ProtocolState {
  DETECTING_BOOTLOADER,
  
  // Original bootloader states
  WAITING_FOR_G,
  WAITING_FOR_SIZE_REQUEST,
  SENDING_FIRMWARE_OLD,
  
  // Enhanced bootloader states  
  WAITING_FOR_HANDSHAKE,
  WAITING_FOR_MENU,
  SENDING_FIRMWARE_NEW,
  
  TRANSFER_COMPLETED
};

BootloaderType detectedBootloader = UNKNOWN_BOOTLOADER;
ProtocolState currentState = DETECTING_BOOTLOADER;

// Firmware data - works for both protocols
// For testing: small firmware that fits both old and new bootloaders
byte testFirmware[] = {
  0x55, 0xAA, 0x55, 0xAA, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13,
  0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F
};

const int firmwareSize = sizeof(testFirmware);
int bytesSent = 0;
unsigned long lastActivity = 0;
unsigned long detectionStartTime = 0;

void setup() {
  // Initialize communications
  #ifdef USE_SOFTWARE_SERIAL
  DEBUG_COMM.begin(9600);      // Debug output
  STM32_COMM.begin(115200);    // STM32 communication
  #else
  STM32_COMM.begin(115200);    // Single UART mode
  #endif
  
  // Setup LED
  pinMode(ledPin, OUTPUT);
  digitalWrite(ledPin, LOW);
  
  // Wait for serial connection (if using USB)
  #ifdef USE_SOFTWARE_SERIAL
  while (!DEBUG_COMM) {
    digitalWrite(ledPin, HIGH);
    delay(100);
    digitalWrite(ledPin, LOW);
    delay(100);
  }
  #endif
  
  // Print startup message
  printMessage("\n=== STM32 Bootloader Test - Unified Version ===");
  printMessage("Supports both Original and Enhanced bootloaders");
  printMessage("");
  printMessage("Test firmware size: " + String(firmwareSize) + " bytes");
  printMessage("Low byte: " + String(firmwareSize & 0xFF));
  printMessage("High byte: " + String((firmwareSize >> 8) & 0xFF));
  printMessage("");
  printMessage("Detecting bootloader type...");
  
  detectionStartTime = millis();
  lastActivity = millis();
}

void loop() {
  // Check for timeout during detection (10 seconds)
  if (detectedBootloader == UNKNOWN_BOOTLOADER && 
      (millis() - detectionStartTime) > 10000) {
    printMessage("❌ Bootloader detection timeout!");
    printMessage("Please reset STM32 and try again.");
    while(1) {
      digitalWrite(ledPin, HIGH);
      delay(500);
      digitalWrite(ledPin, LOW);
      delay(500);
    }
  }
  
  // Handle incoming data from STM32
  if (STM32_COMM.available() > 0) {
    char receivedChar = STM32_COMM.read();
    lastActivity = millis();
    
    printMessage("RX: '" + String(receivedChar) + "' (0x" + String((uint8_t)receivedChar, HEX) + ")");
    
    handleSTM32Communication(receivedChar);
  }
  
  // Handle automatic data sending for enhanced bootloader
  if (detectedBootloader == ENHANCED_BOOTLOADER && 
      currentState == SENDING_FIRMWARE_NEW) {
    sendEnhancedFirmwareData();
  }
}

void handleSTM32Communication(char received) {
  switch (currentState) {
    
    case DETECTING_BOOTLOADER:
      if (received == 'g') {
        // Original bootloader detected
        detectedBootloader = ORIGINAL_BOOTLOADER;
        currentState = WAITING_FOR_G;
        printMessage("✅ Original bootloader detected!");
        printMessage("Using 'g'/'r' protocol...");
        handleOriginalProtocol(received);
      }
      else if (received == '.') {
        // Enhanced bootloader detected
        detectedBootloader = ENHANCED_BOOTLOADER;
        currentState = WAITING_FOR_HANDSHAKE;
        printMessage("✅ Enhanced bootloader detected!");
        printMessage("Using ASCII input protocol...");
        handleEnhancedProtocol(received);
      }
      break;
      
    case WAITING_FOR_G:
    case WAITING_FOR_SIZE_REQUEST:
    case SENDING_FIRMWARE_OLD:
      handleOriginalProtocol(received);
      break;
      
    case WAITING_FOR_HANDSHAKE:
    case WAITING_FOR_MENU:
    case SENDING_FIRMWARE_NEW:
      handleEnhancedProtocol(received);
      break;
      
    case TRANSFER_COMPLETED:
      printMessage("Transfer complete - received: 0x" + String((uint8_t)received, HEX));
      break;
  }
}

void handleOriginalProtocol(char received) {
  switch (currentState) {
    case WAITING_FOR_G:
      if (received == 'g') {
        printMessage("→ Sending 'r' to start update...");
        STM32_COMM.write('r');
        currentState = WAITING_FOR_SIZE_REQUEST;
        digitalWrite(ledPin, HIGH);
      }
      break;
      
    case WAITING_FOR_SIZE_REQUEST:
      if (received == 'y') {
        byte lowByte = firmwareSize & 0xFF;
        printMessage("→ Sending size low byte: " + String(lowByte));
        STM32_COMM.write(lowByte);
      }
      else if (received == 'x') {
        byte highByte = (firmwareSize >> 8) & 0xFF;
        printMessage("→ Sending size high byte: " + String(highByte));
        STM32_COMM.write(highByte);
        currentState = SENDING_FIRMWARE_OLD;
        bytesSent = 0;
        printMessage("→ Starting firmware transfer...");
      }
      break;
      
    case SENDING_FIRMWARE_OLD:
      if ((received == 'y' || received == 'x') && bytesSent < firmwareSize) {
        printMessage("→ Sending firmware byte " + String(bytesSent) + ": 0x" + String(testFirmware[bytesSent], HEX));
        STM32_COMM.write(testFirmware[bytesSent]);
        bytesSent++;
        
        if (bytesSent >= firmwareSize) {
          currentState = TRANSFER_COMPLETED;
          printMessage("✅ Original protocol transfer completed!");
          digitalWrite(ledPin, LOW);
        }
      }
      break;
  }
}

void handleEnhancedProtocol(char received) {
  static String menuBuffer = "";
  static bool menuChoiceSent = false;

  // Accumulate characters to detect the complete prompt
  menuBuffer += received;

  switch (currentState) {
    case WAITING_FOR_HANDSHAKE:
      if (received == '.') {
        printMessage("→ Sending 'o' to start update...");
        STM32_COMM.write('o');
        currentState = WAITING_FOR_MENU;
        menuBuffer = "";
        menuChoiceSent = false;
        delay(100);
      }
      break;

    case WAITING_FOR_MENU:
      // Look for the "Enter number:" prompt which comes after the menu
      if (menuBuffer.indexOf("Enter number:") >= 0 && !menuChoiceSent) {
        printMessage("→ STM32 asking for menu choice...");
        printMessage("→ Choosing option 3 (GPS clock default - 20388 bytes)...");

        // Send choice 3 for GPS clock default
        STM32_COMM.print("3\r\n");
        menuChoiceSent = true;
        menuBuffer = "";

        currentState = SENDING_FIRMWARE_NEW;
        bytesSent = 0;
        delay(1000); // Give STM32 time to process choice and prepare
      }
      // Keep accumulating until we see the prompt
      // Limit buffer size to prevent memory issues
      if (menuBuffer.length() > 500) {
        menuBuffer = menuBuffer.substring(250); // Keep last 250 chars
      }
      break;

    case SENDING_FIRMWARE_NEW:
      // Check if STM32 is ready to receive data
      if (menuBuffer.indexOf("Ready to receive") >= 0 ||
          menuBuffer.indexOf("Send data now") >= 0) {
        printMessage("→ STM32 ready for firmware data transfer...");
        menuBuffer = "";
        delay(500); // Give STM32 time to prepare
      }
      // Enhanced bootloader automatically receives data
      // Actual data sending handled in sendEnhancedFirmwareData()
      break;
  }
}

void sendEnhancedFirmwareData() {
  static unsigned long lastSendTime = 0;
  static int lastProgressReport = 0;
  
  // Send data at controlled rate
  if (millis() - lastSendTime > 2) {  // 2ms delay between bytes
    if (bytesSent < firmwareSize) {
      STM32_COMM.write(testFirmware[bytesSent]);
      bytesSent++;
      lastSendTime = millis();
      
      // Progress report every 8 bytes (for small test firmware)
      if (bytesSent - lastProgressReport >= 8) {
        int progressPercent = (bytesSent * 100) / firmwareSize;
        printMessage("Progress: " + String(progressPercent) + "% (" + String(bytesSent) + "/" + String(firmwareSize) + " bytes)");
        lastProgressReport = bytesSent;
      }
    } else {
      printMessage("✅ Enhanced protocol transfer completed!");
      printMessage("Total bytes sent: " + String(bytesSent));
      currentState = TRANSFER_COMPLETED;
      digitalWrite(ledPin, LOW);
    }
  }
}

void printMessage(String message) {
  #ifdef USE_SOFTWARE_SERIAL
  DEBUG_COMM.println(message);
  #else
  // In single UART mode, prefix debug messages to distinguish from protocol
  if (message.length() > 0 && message.charAt(0) != '\n') {
    // Don't print debug messages in single UART mode to avoid interference
    // STM32_COMM.println("[DEBUG] " + message);
  }
  #endif
}

/*
 * Expected Results:
 * ================
 * 
 * With Original Bootloader:
 * STM32: 'g' → Arduino: 'r' → Size exchange → Firmware transfer
 * 
 * With Enhanced Bootloader:
 * STM32: '.' → Arduino: 'o' → Menu choice → ASCII size → Auto transfer
 * 
 * This unified code automatically adapts to whichever bootloader
 * version you're running, making it perfect for testing both!
 */
