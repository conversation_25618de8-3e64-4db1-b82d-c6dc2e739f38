/**************************************************
 * STM32 Firmware Updater - GUI Version
 * 
 * Simple Windows GUI for non-technical users
 * Features:
 * - Drag & drop firmware files
 * - Automatic device detection
 * - Progress bar with status updates
 * - One-click firmware updates
 * 
 * Compile: gcc stm32_updater_gui.c RS232\rs232.c -IRS232 -lgdi32 -luser32 -lkernel32 -lcomctl32 -mwindows -o STM32_Updater.exe
 **************************************************/

#ifdef _WIN32
#include <windows.h>
#include <commctrl.h>
#include <commdlg.h>
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#include "rs232.h"

// Window controls IDs
#define ID_BUTTON_SELECT_FILE   1001
#define ID_BUTTON_UPDATE        1002
#define ID_BUTTON_DETECT        1003
#define ID_PROGRESS_BAR         1004
#define ID_STATUS_TEXT          1005
#define ID_FILE_PATH_TEXT       1006

// Constants
#define ETX_OTA_MAX_FW_SIZE     (48 * 1024)
#define WINDOW_WIDTH            500
#define WINDOW_HEIGHT           400

// Global variables
HWND hMainWindow;
HWND hProgressBar;
HWND hStatusText;
HWND hFilePathText;
HWND hUpdateButton;
HWND hDetectButton;

char selected_file[MAX_PATH] = {0};
uint8_t firmware_data[ETX_OTA_MAX_FW_SIZE];
uint32_t firmware_size = 0;
int detected_comport = -1;
bool update_in_progress = false;

// Function prototypes
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void CreateControls(HWND hwnd);
void SelectFirmwareFile(HWND hwnd);
void DetectDevice(void);
void UpdateFirmware(void);
void UpdateStatus(const char* message);
void UpdateProgress(int percentage);
int LoadFirmwareFile(const char* filename);
int PerformUpdate(int comport);
bool WaitForChar(int comport, unsigned char expected, int timeout_ms);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_PROGRESS_CLASS;
    InitCommonControlsEx(&icex);
    
    // Register window class
    WNDCLASS wc = {0};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = "STM32UpdaterWindow";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    
    if (!RegisterClass(&wc)) {
        MessageBox(NULL, "Failed to register window class", "Error", MB_OK | MB_ICONERROR);
        return -1;
    }
    
    // Create main window
    hMainWindow = CreateWindow(
        "STM32UpdaterWindow",
        "STM32 Firmware Updater v2.0",
        WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
        CW_USEDEFAULT, CW_USEDEFAULT,
        WINDOW_WIDTH, WINDOW_HEIGHT,
        NULL, NULL, hInstance, NULL
    );
    
    if (!hMainWindow) {
        MessageBox(NULL, "Failed to create window", "Error", MB_OK | MB_ICONERROR);
        return -1;
    }
    
    ShowWindow(hMainWindow, nCmdShow);
    UpdateWindow(hMainWindow);
    
    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return msg.wParam;
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_CREATE:
            CreateControls(hwnd);
            UpdateStatus("Ready. Please select a firmware file and detect your STM32 device.");
            break;
            
        case WM_COMMAND:
            switch (LOWORD(wParam)) {
                case ID_BUTTON_SELECT_FILE:
                    SelectFirmwareFile(hwnd);
                    break;
                    
                case ID_BUTTON_DETECT:
                    DetectDevice();
                    break;
                    
                case ID_BUTTON_UPDATE:
                    if (!update_in_progress) {
                        UpdateFirmware();
                    }
                    break;
            }
            break;
            
        case WM_DROPFILES: {
            HDROP hDrop = (HDROP)wParam;
            char filename[MAX_PATH];
            
            if (DragQueryFile(hDrop, 0, filename, sizeof(filename))) {
                strcpy(selected_file, filename);
                SetWindowText(hFilePathText, filename);
                
                if (LoadFirmwareFile(filename) == 0) {
                    UpdateStatus("Firmware file loaded successfully. Ready to update.");
                    EnableWindow(hUpdateButton, detected_comport >= 0);
                }
            }
            
            DragFinish(hDrop);
            break;
        }
        
        case WM_DESTROY:
            PostQuitMessage(0);
            break;
            
        default:
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
    return 0;
}

void CreateControls(HWND hwnd) {
    // Enable drag and drop
    DragAcceptFiles(hwnd, TRUE);
    
    // Title
    CreateWindow("STATIC", "STM32 Firmware Updater",
        WS_VISIBLE | WS_CHILD | SS_CENTER,
        10, 10, 460, 30, hwnd, NULL, NULL, NULL);
    
    // File selection group
    CreateWindow("STATIC", "Step 1: Select Firmware File",
        WS_VISIBLE | WS_CHILD,
        10, 50, 200, 20, hwnd, NULL, NULL, NULL);
    
    CreateWindow("BUTTON", "Browse...",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        10, 75, 100, 30, hwnd, (HMENU)ID_BUTTON_SELECT_FILE, NULL, NULL);
    
    hFilePathText = CreateWindow("STATIC", "No file selected (or drag & drop here)",
        WS_VISIBLE | WS_CHILD | SS_PATHELLIPSIS,
        120, 80, 350, 20, hwnd, (HMENU)ID_FILE_PATH_TEXT, NULL, NULL);
    
    // Device detection group
    CreateWindow("STATIC", "Step 2: Detect STM32 Device",
        WS_VISIBLE | WS_CHILD,
        10, 120, 200, 20, hwnd, NULL, NULL, NULL);
    
    hDetectButton = CreateWindow("BUTTON", "Auto-Detect Device",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        10, 145, 150, 30, hwnd, (HMENU)ID_BUTTON_DETECT, NULL, NULL);
    
    // Update group
    CreateWindow("STATIC", "Step 3: Update Firmware",
        WS_VISIBLE | WS_CHILD,
        10, 190, 200, 20, hwnd, NULL, NULL, NULL);
    
    hUpdateButton = CreateWindow("BUTTON", "Update Firmware",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        10, 215, 150, 35, hwnd, (HMENU)ID_BUTTON_UPDATE, NULL, NULL);
    
    EnableWindow(hUpdateButton, FALSE);
    
    // Progress bar
    hProgressBar = CreateWindow(PROGRESS_CLASS, NULL,
        WS_VISIBLE | WS_CHILD | PBS_SMOOTH,
        10, 260, 460, 25, hwnd, (HMENU)ID_PROGRESS_BAR, NULL, NULL);
    
    SendMessage(hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, 100));
    
    // Status text
    hStatusText = CreateWindow("STATIC", "",
        WS_VISIBLE | WS_CHILD | SS_LEFT,
        10, 295, 460, 60, hwnd, (HMENU)ID_STATUS_TEXT, NULL, NULL);
}

void SelectFirmwareFile(HWND hwnd) {
    OPENFILENAME ofn = {0};
    char filename[MAX_PATH] = {0};
    
    ofn.lStructSize = sizeof(OPENFILENAME);
    ofn.hwndOwner = hwnd;
    ofn.lpstrFile = filename;
    ofn.nMaxFile = sizeof(filename);
    ofn.lpstrFilter = "Binary Files (*.bin)\0*.bin\0All Files (*.*)\0*.*\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrTitle = "Select STM32 Firmware File";
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;
    
    if (GetOpenFileName(&ofn)) {
        strcpy(selected_file, filename);
        SetWindowText(hFilePathText, filename);
        
        if (LoadFirmwareFile(filename) == 0) {
            UpdateStatus("Firmware file loaded successfully. Ready to update.");
            EnableWindow(hUpdateButton, detected_comport >= 0);
        }
    }
}

void DetectDevice(void) {
    UpdateStatus("Scanning for STM32 bootloader...");
    UpdateProgress(0);
    
    // Disable detect button during scan
    EnableWindow(hDetectButton, FALSE);
    
    detected_comport = -1;
    
    // Try COM ports 1-20
    for (int port = 0; port < 20; port++) {
        UpdateProgress((port * 100) / 20);
        
        char status_msg[256];
        sprintf(status_msg, "Checking COM%d...", port + 1);
        UpdateStatus(status_msg);
        
        if (RS232_OpenComport(port, 115200, "8N1", 0) == 0) {
            // Test for bootloader presence
            unsigned char test_char;
            bool found = false;
            
            DWORD start_time = GetTickCount();
            while ((GetTickCount() - start_time) < 2000) {  // 2 second timeout
                if (RS232_PollComport(port, &test_char, 1) > 0) {
                    if (test_char == '.') {
                        found = true;
                        break;
                    }
                }
                Sleep(10);
            }
            
            if (found) {
                detected_comport = port;
                sprintf(status_msg, "STM32 bootloader found on COM%d! Ready to update.", port + 1);
                UpdateStatus(status_msg);
                UpdateProgress(100);
                EnableWindow(hUpdateButton, firmware_size > 0);
                EnableWindow(hDetectButton, TRUE);
                return;
            }
            
            RS232_CloseComport(port);
        }
    }
    
    UpdateStatus("No STM32 bootloader detected. Please check connections and ensure device is in bootloader mode.");
    UpdateProgress(0);
    EnableWindow(hDetectButton, TRUE);
}

void UpdateFirmware(void) {
    if (detected_comport < 0 || firmware_size == 0) {
        MessageBox(hMainWindow, "Please select a firmware file and detect your device first.", 
                   "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    update_in_progress = true;
    EnableWindow(hUpdateButton, FALSE);
    EnableWindow(hDetectButton, FALSE);
    
    UpdateStatus("Starting firmware update...");
    UpdateProgress(0);
    
    if (PerformUpdate(detected_comport) == 0) {
        UpdateStatus("Firmware update completed successfully! Device should restart with new firmware.");
        UpdateProgress(100);
        MessageBox(hMainWindow, "Firmware update completed successfully!\n\nYour STM32 device should now be running the new firmware.", 
                   "Success", MB_OK | MB_ICONINFORMATION);
    } else {
        UpdateStatus("Firmware update failed. Please check connections and try again.");
        UpdateProgress(0);
        MessageBox(hMainWindow, "Firmware update failed!\n\nPlease check your connections and ensure the device is in bootloader mode.", 
                   "Error", MB_OK | MB_ICONERROR);
    }
    
    update_in_progress = false;
    EnableWindow(hUpdateButton, TRUE);
    EnableWindow(hDetectButton, TRUE);
}

void UpdateStatus(const char* message) {
    SetWindowText(hStatusText, message);
    UpdateWindow(hStatusText);
}

void UpdateProgress(int percentage) {
    SendMessage(hProgressBar, PBM_SETPOS, percentage, 0);
    UpdateWindow(hProgressBar);
}

int LoadFirmwareFile(const char* filename) {
    FILE* file = fopen(filename, "rb");
    if (!file) {
        UpdateStatus("Error: Cannot open firmware file.");
        return -1;
    }
    
    fseek(file, 0L, SEEK_END);
    firmware_size = ftell(file);
    fseek(file, 0L, SEEK_SET);
    
    if (firmware_size > ETX_OTA_MAX_FW_SIZE) {
        UpdateStatus("Error: Firmware file too large (max 48KB).");
        fclose(file);
        return -1;
    }
    
    if (fread(firmware_data, 1, firmware_size, file) != firmware_size) {
        UpdateStatus("Error: Failed to read firmware file.");
        fclose(file);
        return -1;
    }
    
    fclose(file);
    
    char status_msg[256];
    sprintf(status_msg, "Firmware loaded: %s (%u bytes)", filename, firmware_size);
    UpdateStatus(status_msg);
    
    return 0;
}

bool WaitForChar(int comport, unsigned char expected, int timeout_ms) {
    unsigned char received;
    DWORD start_time = GetTickCount();
    
    while ((GetTickCount() - start_time) < timeout_ms) {
        if (RS232_PollComport(comport, &received, 1) > 0) {
            if (received == expected) {
                return true;
            }
        }
        Sleep(1);
    }
    return false;
}

int PerformUpdate(int comport) {
    // Handshake
    UpdateStatus("Establishing communication with bootloader...");
    if (!WaitForChar(comport, '.', 6000)) {
        return -1;
    }
    
    if (RS232_SendByte(comport, 'o') != 0) {
        return -1;
    }
    
    // Send size
    UpdateStatus("Sending firmware size...");
    if (!WaitForChar(comport, 'y', 5000) || 
        RS232_SendByte(comport, (uint8_t)(firmware_size & 0xFF)) != 0 ||
        !WaitForChar(comport, 'x', 5000) ||
        RS232_SendByte(comport, (uint8_t)((firmware_size >> 8) & 0xFF)) != 0) {
        return -1;
    }
    
    // Send data
    UpdateStatus("Transferring firmware data...");
    for (uint32_t i = 0; i < firmware_size; i += 2) {
        if (!WaitForChar(comport, 'y', 5000) ||
            RS232_SendByte(comport, firmware_data[i]) != 0) {
            return -1;
        }
        
        uint8_t high_byte = (i + 1 < firmware_size) ? firmware_data[i + 1] : 0x00;
        if (!WaitForChar(comport, 'x', 5000) ||
            RS232_SendByte(comport, high_byte) != 0) {
            return -1;
        }
        
        if ((i % 128) == 0) {
            UpdateProgress((i * 100) / firmware_size);
        }
    }
    
    return 0;
}

#else
int main() {
    printf("This GUI version is only available on Windows.\n");
    printf("Please use the command-line version on other platforms.\n");
    return -1;
}
#endif
