/*
 * Arduino STM32 Firmware Updater for Proteus Simulation
 * =====================================================
 * 
 * This Arduino sketch simulates the PC tool for testing STM32 bootloader
 * in Proteus. It sends a 20KB dummy firmware to demonstrate the protocol.
 * 
 * Connections in Proteus:
 * - Arduino Pin 2 (RX) → STM32 PA9 (TX)
 * - Arduino Pin 3 (TX) → STM32 PA10 (RX) 
 * - Common GND
 * 
 * Protocol for 20KB (20,388 bytes):
 * 1. Wait for STM32 to send '.'
 * 2. Send 'o' to start update
 * 3. Send size: 0x94 (low), 0x4F (high)
 * 4. Send 20,388 bytes of dummy data
 */

#include <SoftwareSerial.h>

// Create SoftwareSerial for STM32 communication
SoftwareSerial stm32(2, 3); // RX=2, TX=3

// Firmware parameters for 20KB
const uint16_t FIRMWARE_SIZE = 20388;  // Your GPS clock firmware size
const uint8_t SIZE_LOW = FIRMWARE_SIZE & 0xFF;        // 0x94 (148)
const uint8_t SIZE_HIGH = (FIRMWARE_SIZE >> 8) & 0xFF; // 0x4F (79)

// State machine
enum UpdateState {
  WAITING_FOR_HANDSHAKE,
  SENDING_SIZE_LOW,
  SENDING_SIZE_HIGH, 
  SENDING_DATA,
  COMPLETE
};

UpdateState currentState = WAITING_FOR_HANDSHAKE;
uint16_t bytesSent = 0;
bool waitingForRequest = true;
unsigned long lastActivity = 0;

void setup() {
  // Initialize serial communications
  Serial.begin(9600);    // For Arduino IDE Serial Monitor
  stm32.begin(115200);   // For STM32 communication
  
  Serial.println("=== Arduino STM32 Firmware Updater ===");
  Serial.println("Simulating 20KB firmware update");
  Serial.print("Firmware size: ");
  Serial.print(FIRMWARE_SIZE);
  Serial.println(" bytes");
  Serial.print("Size bytes: Low=0x");
  Serial.print(SIZE_LOW, HEX);
  Serial.print(" (");
  Serial.print(SIZE_LOW);
  Serial.print("), High=0x");
  Serial.print(SIZE_HIGH, HEX);
  Serial.print(" (");
  Serial.print(SIZE_HIGH);
  Serial.println(")");
  Serial.println();
  Serial.println("Waiting for STM32 bootloader...");
  
  lastActivity = millis();
}

void loop() {
  // Check for timeout (30 seconds)
  if (millis() - lastActivity > 30000) {
    Serial.println("TIMEOUT: No activity for 30 seconds");
    while(1); // Stop execution
  }
  
  // Check for incoming data from STM32
  if (stm32.available()) {
    char received = stm32.read();
    lastActivity = millis();
    
    Serial.print("RX: '");
    Serial.print(received);
    Serial.print("' (0x");
    Serial.print((uint8_t)received, HEX);
    Serial.println(")");
    
    handleProtocol(received);
  }
}

void handleProtocol(char received) {
  switch (currentState) {
    
    case WAITING_FOR_HANDSHAKE:
      if (received == '.') {
        Serial.println("→ Handshake received! Sending 'o' to start update...");
        stm32.write('o');
        Serial.println("TX: 'o' (start update)");
        currentState = SENDING_SIZE_LOW;
        waitingForRequest = true;
      }
      break;
      
    case SENDING_SIZE_LOW:
      if (received == 'y' && waitingForRequest) {
        Serial.println("→ Size request (low byte). Sending size low byte...");
        stm32.write(SIZE_LOW);
        Serial.print("TX: 0x");
        Serial.print(SIZE_LOW, HEX);
        Serial.print(" (");
        Serial.print(SIZE_LOW);
        Serial.println(") - Size Low Byte");
        currentState = SENDING_SIZE_HIGH;
        waitingForRequest = true;
      }
      break;
      
    case SENDING_SIZE_HIGH:
      if (received == 'x' && waitingForRequest) {
        Serial.println("→ Size request (high byte). Sending size high byte...");
        stm32.write(SIZE_HIGH);
        Serial.print("TX: 0x");
        Serial.print(SIZE_HIGH, HEX);
        Serial.print(" (");
        Serial.print(SIZE_HIGH);
        Serial.println(") - Size High Byte");
        Serial.print("Total size sent: ");
        Serial.print(FIRMWARE_SIZE);
        Serial.println(" bytes");
        Serial.println("→ Starting data transfer...");
        currentState = SENDING_DATA;
        waitingForRequest = true;
        bytesSent = 0;
      }
      break;
      
    case SENDING_DATA:
      if (bytesSent >= FIRMWARE_SIZE) {
        Serial.println("→ All data sent! Update complete.");
        currentState = COMPLETE;
        return;
      }
      
      if (received == 'y' && waitingForRequest) {
        // Send low byte of current data pair
        uint8_t lowByte = generateDummyByte(bytesSent);
        stm32.write(lowByte);
        
        // Progress reporting every 512 bytes
        if (bytesSent % 512 == 0) {
          float progress = (float)bytesSent / FIRMWARE_SIZE * 100;
          Serial.print("Progress: ");
          Serial.print(progress, 1);
          Serial.print("% (");
          Serial.print(bytesSent);
          Serial.print("/");
          Serial.print(FIRMWARE_SIZE);
          Serial.println(" bytes)");
        }
        
        bytesSent++;
        waitingForRequest = true;
        
      } else if (received == 'x' && waitingForRequest) {
        // Send high byte of current data pair
        uint8_t highByte = generateDummyByte(bytesSent);
        stm32.write(highByte);
        bytesSent++;
        waitingForRequest = true;
      }
      break;
      
    case COMPLETE:
      Serial.println("Update completed successfully!");
      Serial.println("STM32 should now restart with new firmware.");
      while(1); // Stop execution
      break;
  }
}

// Generate dummy firmware data (simple pattern for testing)
uint8_t generateDummyByte(uint16_t index) {
  // Create a simple pattern that's easy to verify
  // This simulates real firmware data
  return (uint8_t)(index & 0xFF) ^ 0xAA;
}

// Helper function to print current state
void printState() {
  Serial.print("State: ");
  switch (currentState) {
    case WAITING_FOR_HANDSHAKE: Serial.println("WAITING_FOR_HANDSHAKE"); break;
    case SENDING_SIZE_LOW: Serial.println("SENDING_SIZE_LOW"); break;
    case SENDING_SIZE_HIGH: Serial.println("SENDING_SIZE_HIGH"); break;
    case SENDING_DATA: Serial.println("SENDING_DATA"); break;
    case COMPLETE: Serial.println("COMPLETE"); break;
  }
}
