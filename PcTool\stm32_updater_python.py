#!/usr/bin/env python3
"""
STM32 Firmware Updater - Python GUI Version
============================================

Simple cross-platform GUI for STM32F103 firmware updates.
Requires: pip install pyserial tkinter

Features:
- Cross-platform (Windows, Linux, macOS)
- Automatic COM port detection
- Drag & drop support
- Progress tracking
- User-friendly interface

Usage: python stm32_updater_python.py
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import serial
import serial.tools.list_ports
import threading
import time
import os
import sys

class STM32UpdaterGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("STM32 Firmware Updater v2.0")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # Variables
        self.firmware_file = tk.StringVar()
        self.status_text = tk.StringVar(value="Ready. Please select a firmware file.")
        self.progress_var = tk.DoubleVar()
        self.detected_port = None
        self.firmware_data = None
        self.update_in_progress = False
        
        self.create_widgets()
        self.center_window()
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")
        
    def create_widgets(self):
        """Create and layout GUI widgets"""
        # Title
        title_label = tk.Label(self.root, text="STM32 Firmware Updater", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(self.root, text="Automated FUOTA Tool for STM32F103", 
                                 font=("Arial", 10))
        subtitle_label.pack(pady=(0, 20))
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Step 1: File Selection
        step1_frame = ttk.LabelFrame(main_frame, text="Step 1: Select Firmware File", padding="10")
        step1_frame.pack(fill=tk.X, pady=(0, 15))
        
        file_frame = ttk.Frame(step1_frame)
        file_frame.pack(fill=tk.X)
        
        ttk.Button(file_frame, text="Browse...", command=self.select_file).pack(side=tk.LEFT)
        
        file_label = ttk.Label(file_frame, textvariable=self.firmware_file, 
                              foreground="blue", cursor="hand2")
        file_label.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
        file_label.bind("<Button-1>", lambda e: self.select_file())
        
        # Drag and drop hint
        drop_label = ttk.Label(step1_frame, text="💡 Tip: You can also drag & drop firmware files here", 
                              font=("Arial", 9), foreground="gray")
        drop_label.pack(pady=(5, 0))
        
        # Step 2: Device Detection
        step2_frame = ttk.LabelFrame(main_frame, text="Step 2: Detect STM32 Device", padding="10")
        step2_frame.pack(fill=tk.X, pady=(0, 15))
        
        detect_frame = ttk.Frame(step2_frame)
        detect_frame.pack(fill=tk.X)
        
        self.detect_button = ttk.Button(detect_frame, text="Auto-Detect Device", 
                                       command=self.detect_device)
        self.detect_button.pack(side=tk.LEFT)
        
        self.port_label = ttk.Label(detect_frame, text="No device detected", 
                                   foreground="red")
        self.port_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Manual port selection
        manual_frame = ttk.Frame(step2_frame)
        manual_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(manual_frame, text="Manual selection:").pack(side=tk.LEFT)
        
        self.port_combo = ttk.Combobox(manual_frame, width=15, state="readonly")
        self.port_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.port_combo.bind("<<ComboboxSelected>>", self.manual_port_selected)
        
        ttk.Button(manual_frame, text="Refresh", command=self.refresh_ports).pack(side=tk.LEFT, padx=(5, 0))
        
        # Step 3: Update Firmware
        step3_frame = ttk.LabelFrame(main_frame, text="Step 3: Update Firmware", padding="10")
        step3_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.update_button = ttk.Button(step3_frame, text="Update Firmware", 
                                       command=self.update_firmware, state=tk.DISABLED)
        self.update_button.pack()
        
        # Progress bar
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100, length=500)
        self.progress_bar.pack(fill=tk.X)
        
        # Status
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True)
        
        self.status_label = ttk.Label(status_frame, textvariable=self.status_text, 
                                     wraplength=550, justify=tk.LEFT)
        self.status_label.pack(anchor=tk.W)
        
        # Initialize port list
        self.refresh_ports()
        
    def select_file(self):
        """Open file dialog to select firmware file"""
        filename = filedialog.askopenfilename(
            title="Select STM32 Firmware File",
            filetypes=[("Binary files", "*.bin"), ("All files", "*.*")]
        )
        
        if filename:
            self.load_firmware_file(filename)
            
    def load_firmware_file(self, filename):
        """Load and validate firmware file"""
        try:
            with open(filename, 'rb') as f:
                self.firmware_data = f.read()
                
            file_size = len(self.firmware_data)
            max_size = 48 * 1024  # 48KB
            
            if file_size > max_size:
                messagebox.showerror("Error", f"Firmware file too large!\nMaximum size: {max_size//1024}KB\nFile size: {file_size//1024}KB")
                return
                
            if file_size == 0:
                messagebox.showerror("Error", "Firmware file is empty!")
                return
                
            # Update UI
            self.firmware_file.set(os.path.basename(filename))
            self.status_text.set(f"Firmware loaded: {os.path.basename(filename)} ({file_size} bytes)")
            self.check_ready_to_update()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load firmware file:\n{str(e)}")
            
    def refresh_ports(self):
        """Refresh the list of available COM ports"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports:
            self.port_combo.set(ports[0])
            
    def manual_port_selected(self, event=None):
        """Handle manual port selection"""
        selected_port = self.port_combo.get()
        if selected_port:
            self.detected_port = selected_port
            self.port_label.config(text=f"Selected: {selected_port}", foreground="blue")
            self.check_ready_to_update()
            
    def detect_device(self):
        """Auto-detect STM32 bootloader on available ports"""
        def detection_thread():
            self.detect_button.config(state=tk.DISABLED)
            self.status_text.set("Scanning for STM32 bootloader...")
            self.progress_var.set(0)
            
            ports = [port.device for port in serial.tools.list_ports.comports()]
            
            for i, port in enumerate(ports):
                self.progress_var.set((i * 100) / len(ports))
                self.status_text.set(f"Checking {port}...")
                self.root.update()
                
                try:
                    with serial.Serial(port, 115200, timeout=2) as ser:
                        # Look for bootloader handshake signal
                        start_time = time.time()
                        while (time.time() - start_time) < 2:
                            if ser.in_waiting > 0:
                                data = ser.read(1)
                                if data == b'.':
                                    self.detected_port = port
                                    self.port_label.config(text=f"Found on {port}", foreground="green")
                                    self.status_text.set(f"STM32 bootloader detected on {port}")
                                    self.progress_var.set(100)
                                    self.check_ready_to_update()
                                    self.detect_button.config(state=tk.NORMAL)
                                    return
                            time.sleep(0.01)
                            
                except (serial.SerialException, OSError):
                    continue
                    
            # No device found
            self.status_text.set("No STM32 bootloader detected. Please check connections and ensure device is in bootloader mode.")
            self.progress_var.set(0)
            self.detect_button.config(state=tk.NORMAL)
            
        threading.Thread(target=detection_thread, daemon=True).start()
        
    def check_ready_to_update(self):
        """Check if ready to perform update"""
        ready = (self.firmware_data is not None and 
                self.detected_port is not None and 
                not self.update_in_progress)
        self.update_button.config(state=tk.NORMAL if ready else tk.DISABLED)
        
    def update_firmware(self):
        """Perform firmware update"""
        if not self.firmware_data or not self.detected_port:
            messagebox.showerror("Error", "Please select firmware file and detect device first.")
            return
            
        def update_thread():
            self.update_in_progress = True
            self.update_button.config(state=tk.DISABLED)
            self.detect_button.config(state=tk.DISABLED)
            
            try:
                result = self.perform_update()
                if result:
                    self.status_text.set("✅ Firmware update completed successfully! Device should restart with new firmware.")
                    self.progress_var.set(100)
                    messagebox.showinfo("Success", "Firmware update completed successfully!\n\nYour STM32 device should now be running the new firmware.")
                else:
                    self.status_text.set("❌ Firmware update failed. Please check connections and try again.")
                    self.progress_var.set(0)
                    messagebox.showerror("Error", "Firmware update failed!\n\nPlease check your connections and ensure the device is in bootloader mode.")
                    
            except Exception as e:
                self.status_text.set(f"❌ Update failed: {str(e)}")
                self.progress_var.set(0)
                messagebox.showerror("Error", f"Update failed:\n{str(e)}")
                
            finally:
                self.update_in_progress = False
                self.update_button.config(state=tk.NORMAL)
                self.detect_button.config(state=tk.NORMAL)
                
        threading.Thread(target=update_thread, daemon=True).start()
        
    def perform_update(self):
        """Perform the actual firmware update"""
        try:
            with serial.Serial(self.detected_port, 115200, timeout=5) as ser:
                # Step 1: Handshake
                self.status_text.set("📡 Establishing communication...")
                self.progress_var.set(5)
                
                # Wait for bootloader ready signal
                if not self.wait_for_char(ser, b'.', 6000):
                    raise Exception("Timeout waiting for bootloader handshake")
                    
                # Send start signal
                ser.write(b'o')
                
                # Step 2: Send firmware size
                self.status_text.set("📏 Sending firmware size...")
                self.progress_var.set(10)
                
                fw_size = len(self.firmware_data)
                
                if not self.wait_for_char(ser, b'y', 5000):
                    raise Exception("Timeout waiting for size request (low byte)")
                ser.write(bytes([fw_size & 0xFF]))
                
                if not self.wait_for_char(ser, b'x', 5000):
                    raise Exception("Timeout waiting for size request (high byte)")
                ser.write(bytes([(fw_size >> 8) & 0xFF]))
                
                # Step 3: Send firmware data
                self.status_text.set("📤 Transferring firmware data...")
                
                for i in range(0, fw_size, 2):
                    # Send low byte
                    if not self.wait_for_char(ser, b'y', 5000):
                        raise Exception(f"Timeout waiting for data request (byte {i})")
                    ser.write(bytes([self.firmware_data[i]]))
                    
                    # Send high byte (or padding)
                    high_byte = self.firmware_data[i + 1] if (i + 1) < fw_size else 0
                    if not self.wait_for_char(ser, b'x', 5000):
                        raise Exception(f"Timeout waiting for data request (byte {i + 1})")
                    ser.write(bytes([high_byte]))
                    
                    # Update progress
                    progress = 10 + ((i * 90) // fw_size)
                    self.progress_var.set(progress)
                    
                    if i % 128 == 0:  # Update status every 128 bytes
                        self.status_text.set(f"📤 Transferring firmware data... {i}/{fw_size} bytes")
                        self.root.update()
                        
                return True
                
        except Exception as e:
            raise Exception(f"Communication error: {str(e)}")
            
    def wait_for_char(self, ser, expected, timeout_ms):
        """Wait for expected character with timeout"""
        start_time = time.time()
        while (time.time() - start_time) * 1000 < timeout_ms:
            if ser.in_waiting > 0:
                received = ser.read(1)
                if received == expected:
                    return True
            time.sleep(0.001)
        return False

def main():
    """Main entry point"""
    try:
        import serial
    except ImportError:
        print("Error: pyserial not installed")
        print("Please install it with: pip install pyserial")
        sys.exit(1)
        
    root = tk.Tk()
    app = STM32UpdaterGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass

if __name__ == "__main__":
    main()
