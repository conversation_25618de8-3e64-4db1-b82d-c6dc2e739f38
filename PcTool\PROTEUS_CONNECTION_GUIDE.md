# 🔌 Proteus Connection Guide - STM32 Bootloader Testing

## 📍 **Component Placement in Proteus**

### **Required Components:**
1. **Arduino Uno** (from Arduino library)
2. **STM32F103C8T6** (Blue Pill board)
3. **Virtual Terminal** x2 (for monitoring communication)
4. **Ground connections**

## 🔗 **Step-by-Step Connection Instructions**

### **Step 1: Place Components**
1. **Drag Arduino Uno** from component library to workspace
2. **Drag STM32F103C8T6** (or Blue Pill) to workspace
3. **Add 2 Virtual Terminals** for monitoring

### **Step 2: Identify Connection Points**

#### **Arduino Uno Pins:**
```
Arduino Side:
┌─────────────────┐
│     ARDUINO     │
│                 │
│  Pin 2 (RX) ●   │ ← SoftwareSerial RX
│  Pin 3 (TX) ●   │ ← SoftwareSerial TX
│                 │
│  GND        ●   │ ← Ground
└─────────────────┘
```

#### **STM32F103C8T6 Pins:**
```
STM32 Side:
┌─────────────────┐
│     STM32       │
│                 │
│  PA9 (TX)   ●   │ ← USART3 TX
│  PA10 (RX)  ●   │ ← USART3 RX
│                 │
│  GND        ●   │ ← Ground
└─────────────────┘
```

### **Step 3: Make Connections (CROSS-CONNECTED)**

#### **Connection 1: Arduino Pin 2 → STM32 PA9**
- **Click** on Arduino **Pin 2** (Digital Pin 2)
- **Drag wire** to STM32 **PA9** pin
- **Label**: "Arduino RX ← STM32 TX"

#### **Connection 2: Arduino Pin 3 → STM32 PA10**
- **Click** on Arduino **Pin 3** (Digital Pin 3)  
- **Drag wire** to STM32 **PA10** pin
- **Label**: "Arduino TX → STM32 RX"

#### **Connection 3: Ground**
- **Connect** Arduino **GND** to STM32 **GND**

### **Visual Wiring Diagram:**
```
Arduino Uno                    STM32F103C8T6
┌─────────────┐               ┌─────────────┐
│             │               │             │
│  Pin 2 (RX) ●───────────────● PA9 (TX)    │
│             │               │             │
│  Pin 3 (TX) ●───────────────● PA10 (RX)   │
│             │               │             │
│  GND        ●───────────────● GND         │
│             │               │             │
└─────────────┘               └─────────────┘
```

## 🖥️ **Virtual Terminal Setup**

### **Terminal 1: Arduino Monitor**
- **Connect to**: Arduino USB/Serial (usually COM2)
- **Baud Rate**: 9600
- **Purpose**: User input and protocol debugging
- **Shows**: User prompts, protocol messages, progress

### **Terminal 2: STM32 Monitor**  
- **Connect to**: STM32 USART1 (usually COM1)
- **Baud Rate**: 115200
- **Purpose**: STM32 bootloader output
- **Shows**: Bootloader messages, size confirmation, block reception

## 📝 **Configuration Settings**

### **Arduino Configuration:**
```cpp
// In Arduino sketch
SoftwareSerial stm32(2, 3); // RX=Pin2, TX=Pin3
Serial.begin(9600);          // For user interface
stm32.begin(115200);         // For STM32 communication
```

### **STM32 Configuration:**
- **USART3**: 115200 baud, 8N1
- **PA9**: USART3_TX (output to Arduino Pin 2)
- **PA10**: USART3_RX (input from Arduino Pin 3)
- **USART1**: 115200 baud (for debug terminal)

## 🎯 **Testing the Connections**

### **Step 1: Upload Arduino Code**
1. **Load** `Arduino_ASCII_Friendly.ino` into Arduino
2. **Compile and upload** in Proteus
3. **Open Serial Monitor** (9600 baud)

### **Step 2: Load STM32 Bootloader**
1. **Load** your STM32 bootloader hex file
2. **Configure** USART3 pins (PA9/PA10)
3. **Open STM32 terminal** (115200 baud)

### **Step 3: Start Simulation**
1. **Click Play** in Proteus
2. **Check both terminals** are working
3. **Look for handshake** (dots from STM32)

## ✅ **Expected Behavior**

### **Arduino Terminal (COM2 - 9600 baud):**
```
=== ASCII-Friendly STM32 Bootloader Test ===
Easy decimal input for non-technical users

Proteus Connections:
Arduino Pin 2 (RX) → STM32 PA9 (TX)
Arduino Pin 3 (TX) → STM32 PA10 (RX)
GND → GND

Waiting for STM32 handshake...
(STM32 should send dots '.' when ready)

STM32 sent: '.' (0x2E)
✅ Handshake received!
Sending 'o' to start update...

📏 FIRMWARE SIZE - LOW BYTE
For 20KB (20,388 bytes): enter 148
For 10 bytes: enter 10
For 1KB: enter 0
Enter size low byte (0-255): 
```

### **STM32 Terminal (COM1 - 115200 baud):**
```
Bootloader v1:0 Started!!!

 Press 'o' to start Firmware Update...
Firmware Update Started
Application Size = [will show based on your input]
Erasing the Flash memory...
Received Block[0]
...
```

## 🔧 **Troubleshooting Connections**

### **No Communication:**
- ✅ **Check cross-connection**: Arduino TX → STM32 RX, Arduino RX → STM32 TX
- ✅ **Verify baud rates**: Arduino 115200 to STM32, Serial Monitor 9600
- ✅ **Check ground connection**: Must be connected
- ✅ **Confirm pin assignments**: Pin 2/3 on Arduino, PA9/PA10 on STM32

### **Wrong Data Received:**
- ✅ **Verify pin mapping**: PA9=TX, PA10=RX on STM32
- ✅ **Check USART configuration**: USART3 enabled, correct pins assigned
- ✅ **Confirm wire connections**: No crossed or loose connections

### **STM32 Not Responding:**
- ✅ **Check bootloader is running**: Should show startup message
- ✅ **Verify USART3 configuration**: Enabled in STM32 code
- ✅ **Check crystal/clock**: STM32 clock configuration correct

## 🎯 **Quick Test Procedure**

### **Easy 10-Byte Test:**
1. **Start simulation**
2. **Wait for dots** from STM32
3. **In Arduino terminal, type:**
   - `10` (size low byte)
   - `0` (size high byte)  
   - `1`, `2`, `3`, `4`, `5`, `6`, `7`, `8`, `9`, `10` (data bytes)

### **Expected Result:**
**STM32 Terminal shows:**
```
Application Size = 10 bytes ✅
Received Block[0]
Gonna Jump to Application...
```

## 📊 **Pin Reference Table**

| Function | Arduino Pin | STM32 Pin | Direction | Purpose |
|----------|-------------|-----------|-----------|---------|
| Data RX | Pin 2 | PA9 (TX) | Arduino ← STM32 | Receive protocol responses |
| Data TX | Pin 3 | PA10 (RX) | Arduino → STM32 | Send firmware data |
| Ground | GND | GND | Common | Reference voltage |
| Debug | USB Serial | PA9 (USART1) | PC ← STM32 | Monitor bootloader |

---

## 🎉 **Success Indicators**

✅ **Arduino terminal shows prompts for input**  
✅ **STM32 terminal shows bootloader startup**  
✅ **Handshake completes** (dots → 'o' → size request)  
✅ **Correct size displayed** on STM32 terminal  
✅ **Data transfer progresses** with block confirmations  

**Once this works in Proteus, your real hardware setup will work the same way!** 🚀
