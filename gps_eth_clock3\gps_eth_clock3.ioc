#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
I2C1.ClockSpeed=100000
I2C1.IPParameters=ClockSpeed
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=I2C1
Mcu.IP1=NVIC
Mcu.IP2=RCC
Mcu.IP3=SYS
Mcu.IP4=TIM2
Mcu.IPNb=5
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin10=PA8
Mcu.Pin11=PA9
Mcu.Pin12=PA10
Mcu.Pin13=PA11
Mcu.Pin14=PA12
Mcu.Pin15=PA15
Mcu.Pin16=PB3
Mcu.Pin17=PB4
Mcu.Pin18=PB5
Mcu.Pin19=PB6
Mcu.Pin2=PD0-OSC_IN
Mcu.Pin20=PB7
Mcu.Pin21=PB8
Mcu.Pin22=PB9
Mcu.Pin23=VP_SYS_VS_ND
Mcu.Pin24=VP_SYS_VS_Systick
Mcu.Pin25=VP_TIM2_VS_ClockSourceINT
Mcu.Pin3=PD1-OSC_OUT
Mcu.Pin4=PB10
Mcu.Pin5=PB11
Mcu.Pin6=PB12
Mcu.Pin7=PB13
Mcu.Pin8=PB14
Mcu.Pin9=PB15
Mcu.PinsNb=26
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.13.0
MxDb.Version=DB.6.0.130
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.I2C1_ER_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C1_EV_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Locked=true
PA10.Signal=GPIO_Output
PA11.Locked=true
PA11.Signal=GPIO_Output
PA12.Locked=true
PA12.Signal=GPIO_Output
PA15.Locked=true
PA15.Signal=GPIO_Output
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Locked=true
PA9.Signal=GPIO_Output
PB10.Locked=true
PB10.Signal=GPIO_Input
PB11.Locked=true
PB11.Signal=GPIO_Input
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.Locked=true
PB15.Signal=GPIO_Output
PB3.Locked=true
PB3.Signal=GPIO_Output
PB4.Locked=true
PB4.Signal=GPIO_Output
PB5.Locked=true
PB5.Signal=GPIO_Input
PB6.Locked=true
PB6.Signal=GPIO_Output
PB7.Locked=true
PB7.Signal=GPIO_Output
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PD0-OSC_IN.Locked=true
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Locked=true
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=gps_eth_clock3.ioc
ProjectManager.ProjectName=gps_eth_clock3
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_TIM2_Init-TIM2-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true
RCC.ADCFreqValue=8000000
RCC.AHBFreq_Value=16000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=8000000
RCC.APB1TimFreq_Value=16000000
RCC.APB2Freq_Value=16000000
RCC.APB2TimFreq_Value=16000000
RCC.FCLKCortexFreq_Value=16000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=16000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=16000000
RCC.PLLCLKFreq_Value=16000000
RCC.PLLMCOFreq_Value=8000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=16000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=16000000
RCC.USBFreq_Value=16000000
RCC.VCOOutput2Freq_Value=8000000
TIM2.IPParameters=Prescaler,Period
TIM2.Period=10
TIM2.Prescaler=1600-1
VP_SYS_VS_ND.Mode=No_Debug
VP_SYS_VS_ND.Signal=SYS_VS_ND
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
board=custom
isbadioc=false
