# 🔧 Binary Values Reference - STM32 Bootloader Protocol

## ❌ **Why You Got 12554 Bytes Instead of 20388**

### **What Happened:**
When you typed `148` in the terminal:
- Terminal sent: `'1'`, `'4'`, `'8'` (ASCII characters)
- ASCII values: `0x31`, `0x34`, `0x38`
- STM32 read only first byte: `0x31` = **49 decimal**
- Then it skipped to next phase without reading high byte properly
- Result: Wrong size calculation

### **What Should Happen:**
- Send raw binary byte: `0x94` (148 decimal)
- STM32 reads: `0x94` = **148 decimal**
- Combined with high byte: `148 + (79 << 8) = 20388` ✅

## 📊 **Correct Binary Values for Common Sizes**

### **20KB (20,388 bytes) - Your GPS Clock:**
```
20388 = 0x4F94
Low Byte:  0x94 = 148 decimal
High Byte: 0x4F = 79 decimal
```

### **10 Bytes (Easy Test):**
```
10 = 0x000A
Low Byte:  0x0A = 10 decimal
High Byte: 0x00 = 0 decimal
```

### **1KB (1,024 bytes):**
```
1024 = 0x0400
Low Byte:  0x00 = 0 decimal
High Byte: 0x04 = 4 decimal
```

### **5KB (5,120 bytes):**
```
5120 = 0x1400
Low Byte:  0x00 = 0 decimal
High Byte: 0x14 = 20 decimal
```

## 🚫 **Why Manual Terminal Input Doesn't Work**

### **Terminal Limitation:**
Most terminals (including Arduino Serial Monitor) send **ASCII text**, not **raw binary**. 

When you type `148`:
- Sends 3 bytes: `0x31` ('1'), `0x34` ('4'), `0x38` ('8')
- STM32 expects 1 byte: `0x94`

### **ASCII vs Binary Comparison:**
| What You Type | ASCII Sent | Binary Needed | STM32 Reads |
|---------------|------------|---------------|-------------|
| `148` | `0x31, 0x34, 0x38` | `0x94` | `0x31` (49) ❌ |
| `79` | `0x37, 0x39` | `0x4F` | `0x37` (55) ❌ |
| `10` | `0x31, 0x30` | `0x0A` | `0x31` (49) ❌ |

## ✅ **Working Solutions**

### **1. Arduino Automation (Recommended)**
```cpp
// Arduino sends raw binary correctly
stm32.write(0x94);  // Sends single byte 148
stm32.write(0x4F);  // Sends single byte 79
```

### **2. Python Tool (Production)**
```python
# Python sends raw binary correctly  
ser.write(bytes([148]))  # Sends 0x94
ser.write(bytes([79]))   # Sends 0x4F
```

### **3. Hex Terminal (Advanced)**
Some advanced terminals can send hex values:
- Send: `\x94` (not `148`)
- Send: `\x4F` (not `79`)

## 🎯 **Easy Testing Strategy**

### **Step 1: Test with 10 Bytes**
Use `Arduino_Simple_Test.ino` with:
```cpp
const uint16_t FIRMWARE_SIZE = 10;
```

**Expected STM32 Output:**
```
Application Size = 10 bytes  ✅
Received Block[0]
Gonna Jump to Application...
```

### **Step 2: Test with 20KB**
Change to:
```cpp
const uint16_t FIRMWARE_SIZE = 20388;
```

**Expected STM32 Output:**
```
Application Size = 20388 bytes  ✅
Received Block[0]
Received Block[1]
...
Received Block[19]
Gonna Jump to Application...
```

## 🔍 **Debugging Wrong Sizes**

### **If STM32 Shows Wrong Size:**
1. **Check if you're sending ASCII instead of binary**
2. **Verify byte order (little-endian: low byte first)**
3. **Ensure single byte per request (not multi-character strings)**

### **Common Wrong Sizes and Their Causes:**
| Wrong Size | Likely Cause | ASCII Sent | Should Send |
|------------|--------------|------------|-------------|
| 49 | Typed "148" | 0x31 | 0x94 |
| 55 | Typed "79" | 0x37 | 0x4F |
| 12554 | Mixed ASCII/binary | 0x31, 0x0A | 0x94, 0x4F |

## 🚀 **Recommended Workflow**

### **For Proteus Testing:**
1. **Use Arduino automation** - handles all binary conversion
2. **Start with 10-byte test** - easy to verify
3. **Scale up to 20KB** - matches your real firmware
4. **Monitor both terminals** - Arduino shows protocol, STM32 shows results

### **For Real Hardware:**
1. **Use Python tool** - professional interface
2. **Load actual .bin files** - real firmware data
3. **Automatic detection** - no manual setup needed

## 💡 **Key Takeaway**

**The STM32 bootloader protocol requires raw binary data, not ASCII text.** This is why automation tools are essential - they handle the binary conversion correctly while providing a user-friendly interface.

**Manual typing in terminals is impractical for this protocol!** 🎯

---

**Use the Arduino automation for Proteus testing, and the Python tool for real firmware updates.** Both handle the binary protocol correctly! 🚀
