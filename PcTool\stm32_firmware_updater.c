/**************************************************
 * STM32 Firmware Updater - Enhanced Version
 * 
 * Features:
 * - Automatic COM port detection
 * - Progress tracking with percentage
 * - Robust error handling and retries
 * - User-friendly interface
 * - Support for .bin and .hex files
 * 
 * Compile: gcc stm32_firmware_updater.c RS232\rs232.c -IRS232 -Wall -Wextra -O2 -o stm32_updater.exe
 **************************************************/

#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>

#ifdef _WIN32
#include <Windows.h>
#include <conio.h>
#else
#include <unistd.h>
#include <termios.h>
#endif

#include "rs232.h"

#define ETX_OTA_MAX_BLOCK_SIZE  (1024)
#define ETX_OTA_MAX_FW_SIZE     (ETX_OTA_MAX_BLOCK_SIZE * 48)  // 48KB max
#define MAX_RETRIES             3
#define TIMEOUT_MS              5000
#define PROGRESS_BAR_WIDTH      50

// Global variables
uint8_t APP_BIN[ETX_OTA_MAX_FW_SIZE];
static bool verbose_mode = false;

// Function prototypes
void print_banner(void);
void print_progress(uint32_t current, uint32_t total);
int detect_stm32_bootloader(void);
int load_firmware_file(const char* filename, uint32_t* size);
int perform_firmware_update(int comport, uint32_t fw_size);
void print_usage(const char* program_name);
bool wait_for_char(int comport, unsigned char expected, int timeout_ms);
void delay_ms(uint32_t ms);

void print_banner(void) {
    printf("\n");
    printf("╔══════════════════════════════════════════════════════════════╗\n");
    printf("║                STM32 Firmware Updater v2.0                  ║\n");
    printf("║              Automated FUOTA Tool for STM32F103             ║\n");
    printf("╚══════════════════════════════════════════════════════════════╝\n");
    printf("\n");
}

void print_progress(uint32_t current, uint32_t total) {
    if (total == 0) return;
    
    int percentage = (current * 100) / total;
    int filled = (current * PROGRESS_BAR_WIDTH) / total;
    
    printf("\rProgress: [");
    for (int i = 0; i < PROGRESS_BAR_WIDTH; i++) {
        if (i < filled) printf("█");
        else printf("░");
    }
    printf("] %d%% (%u/%u bytes)", percentage, current, total);
    fflush(stdout);
}

int detect_stm32_bootloader(void) {
    printf("🔍 Scanning for STM32 bootloader...\n");
    
    // Try common COM ports (COM1-COM20)
    for (int port = 0; port < 20; port++) {
        if (verbose_mode) {
            printf("   Trying COM%d...\n", port + 1);
        }
        
        if (RS232_OpenComport(port, 115200, "8N1", 0) == 0) {
            // Port opened successfully, test for bootloader
            unsigned char test_char;
            bool bootloader_found = false;
            
            // Wait for bootloader handshake signal ('.')
            time_t start_time = time(NULL);
            while ((time(NULL) - start_time) < 3) {  // 3 second timeout
                if (RS232_PollComport(port, &test_char, 1) > 0) {
                    if (test_char == '.') {
                        bootloader_found = true;
                        break;
                    }
                }
                delay_ms(10);
            }
            
            if (bootloader_found) {
                printf("✅ STM32 bootloader found on COM%d\n", port + 1);
                return port;
            }
            
            RS232_CloseComport(port);
        }
    }
    
    printf("❌ No STM32 bootloader detected on any COM port\n");
    printf("   Please ensure:\n");
    printf("   - STM32 device is connected via USB-to-Serial adapter\n");
    printf("   - Device is in bootloader mode (reset while holding boot pin)\n");
    printf("   - Correct drivers are installed\n");
    return -1;
}

int load_firmware_file(const char* filename, uint32_t* size) {
    FILE* file = fopen(filename, "rb");
    if (!file) {
        printf("❌ Cannot open firmware file: %s\n", filename);
        return -1;
    }
    
    // Get file size
    fseek(file, 0L, SEEK_END);
    *size = ftell(file);
    fseek(file, 0L, SEEK_SET);
    
    printf("📁 Loading firmware: %s (%u bytes)\n", filename, *size);
    
    if (*size > ETX_OTA_MAX_FW_SIZE) {
        printf("❌ Firmware too large! Maximum size: %d KB\n", ETX_OTA_MAX_FW_SIZE / 1024);
        fclose(file);
        return -1;
    }
    
    if (*size == 0) {
        printf("❌ Firmware file is empty!\n");
        fclose(file);
        return -1;
    }
    
    // Read firmware data
    if (fread(APP_BIN, 1, *size, file) != *size) {
        printf("❌ Error reading firmware file\n");
        fclose(file);
        return -1;
    }
    
    fclose(file);
    printf("✅ Firmware loaded successfully\n");
    return 0;
}

bool wait_for_char(int comport, unsigned char expected, int timeout_ms) {
    unsigned char received;
    time_t start_time = time(NULL);
    
    while ((time(NULL) - start_time) * 1000 < timeout_ms) {
        if (RS232_PollComport(comport, &received, 1) > 0) {
            if (received == expected) {
                return true;
            }
        }
        delay_ms(1);
    }
    return false;
}

void delay_ms(uint32_t ms) {
#ifdef _WIN32
    Sleep(ms);
#else
    usleep(ms * 1000);
#endif
}

int perform_firmware_update(int comport, uint32_t fw_size) {
    printf("\n🚀 Starting firmware update...\n");
    
    // Step 1: Send handshake
    printf("📡 Establishing communication...\n");
    if (!wait_for_char(comport, '.', 6000)) {  // Wait for bootloader ready signal
        printf("❌ Timeout waiting for bootloader handshake\n");
        return -1;
    }
    
    // Send start signal
    if (RS232_SendByte(comport, 'o') != 0) {
        printf("❌ Failed to send start signal\n");
        return -1;
    }
    
    // Step 2: Send firmware size
    printf("📏 Sending firmware size (%u bytes)...\n", fw_size);
    
    // Wait for size request (low byte)
    if (!wait_for_char(comport, 'y', TIMEOUT_MS)) {
        printf("❌ Timeout waiting for size request (low byte)\n");
        return -1;
    }
    
    if (RS232_SendByte(comport, (uint8_t)(fw_size & 0xFF)) != 0) {
        printf("❌ Failed to send firmware size (low byte)\n");
        return -1;
    }
    
    // Wait for size request (high byte)
    if (!wait_for_char(comport, 'x', TIMEOUT_MS)) {
        printf("❌ Timeout waiting for size request (high byte)\n");
        return -1;
    }
    
    if (RS232_SendByte(comport, (uint8_t)((fw_size >> 8) & 0xFF)) != 0) {
        printf("❌ Failed to send firmware size (high byte)\n");
        return -1;
    }
    
    // Step 3: Send firmware data
    printf("📤 Transferring firmware data...\n");
    
    for (uint32_t i = 0; i < fw_size; i += 2) {
        // Send low byte
        if (!wait_for_char(comport, 'y', TIMEOUT_MS)) {
            printf("\n❌ Timeout waiting for data request (byte %u)\n", i);
            return -1;
        }
        
        if (RS232_SendByte(comport, APP_BIN[i]) != 0) {
            printf("\n❌ Failed to send data byte %u\n", i);
            return -1;
        }
        
        // Send high byte (if available)
        if (i + 1 < fw_size) {
            if (!wait_for_char(comport, 'x', TIMEOUT_MS)) {
                printf("\n❌ Timeout waiting for data request (byte %u)\n", i + 1);
                return -1;
            }
            
            if (RS232_SendByte(comport, APP_BIN[i + 1]) != 0) {
                printf("\n❌ Failed to send data byte %u\n", i + 1);
                return -1;
            }
        } else {
            // Odd number of bytes, send padding
            if (!wait_for_char(comport, 'x', TIMEOUT_MS)) {
                printf("\n❌ Timeout waiting for padding byte request\n");
                return -1;
            }
            
            if (RS232_SendByte(comport, 0x00) != 0) {
                printf("\n❌ Failed to send padding byte\n");
                return -1;
            }
        }
        
        // Update progress every 64 bytes
        if ((i % 64) == 0) {
            print_progress(i, fw_size);
        }
    }
    
    print_progress(fw_size, fw_size);
    printf("\n✅ Firmware transfer completed successfully!\n");
    printf("🔄 Device should now restart with new firmware...\n");
    
    return 0;
}

void print_usage(const char* program_name) {
    printf("Usage: %s [options] <firmware_file>\n\n", program_name);
    printf("Options:\n");
    printf("  -v, --verbose    Enable verbose output\n");
    printf("  -h, --help       Show this help message\n\n");
    printf("Examples:\n");
    printf("  %s firmware.bin\n", program_name);
    printf("  %s -v application.bin\n", program_name);
    printf("  %s ..\\gps_eth_clock3\\Debug\\gps_eth_clock3.bin\n\n", program_name);
    printf("Supported file formats: .bin (binary firmware files)\n");
}

int main(int argc, char* argv[]) {
    char* firmware_file = NULL;
    uint32_t fw_size = 0;
    int comport = -1;
    int result = 0;
    
    print_banner();
    
    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-v") == 0 || strcmp(argv[i], "--verbose") == 0) {
            verbose_mode = true;
        } else if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            print_usage(argv[0]);
            return 0;
        } else if (argv[i][0] != '-') {
            firmware_file = argv[i];
        }
    }
    
    if (!firmware_file) {
        printf("❌ No firmware file specified!\n\n");
        print_usage(argv[0]);
        return -1;
    }
    
    // Load firmware file
    if (load_firmware_file(firmware_file, &fw_size) != 0) {
        return -1;
    }
    
    // Detect STM32 bootloader
    comport = detect_stm32_bootloader();
    if (comport < 0) {
        return -1;
    }
    
    // Perform firmware update
    result = perform_firmware_update(comport, fw_size);
    
    // Cleanup
    RS232_CloseComport(comport);
    
    if (result == 0) {
        printf("\n🎉 Firmware update completed successfully!\n");
        printf("   Your STM32 device should now be running the new firmware.\n");
    } else {
        printf("\n💥 Firmware update failed!\n");
        printf("   Please check connections and try again.\n");
    }
    
    printf("\nPress any key to exit...");
#ifdef _WIN32
    _getch();
#else
    getchar();
#endif
    
    return result;
}
