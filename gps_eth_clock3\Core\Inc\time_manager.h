#ifndef TIME_MANAGER_H
#define TIME_MANAGER_H

#include "main.h"
#include "stdint.h"

// Time variables
extern volatile uint8_t manual_seconds;
extern volatile uint8_t manual_minutes;
extern volatile uint8_t manual_hours;
extern volatile uint8_t is_pm;

// Flag variables
extern volatile uint8_t display_update_needed;
extern volatile uint8_t rtc_read_needed;
extern volatile uint8_t rtc_write_needed;
extern volatile uint8_t button_operation_in_progress;
extern volatile uint8_t time_setting_mode;
extern volatile uint8_t selected_field;

// Function prototypes
void init_time_manager(void);
void handle_time_management(uint32_t current_time);
void read_time_from_rtc(void);
void write_time_to_rtc(void);
void update_display(void);
void system_init(void);

#endif /* TIME_MANAGER_H */
