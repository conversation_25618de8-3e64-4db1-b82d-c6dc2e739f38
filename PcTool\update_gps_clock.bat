@echo off
echo ========================================
echo GPS Clock Firmware Update
echo ========================================
echo.

REM Check if firmware file exists
set FIRMWARE_FILE=..\gps_eth_clock3\Debug\gps_eth_clock3.bin
if not exist "%FIRMWARE_FILE%" (
    echo ERROR: GPS clock firmware not found!
    echo Expected location: %FIRMWARE_FILE%
    echo.
    echo Please build the gps_eth_clock3 project first:
    echo 1. Open STM32CubeIDE
    echo 2. Build the gps_eth_clock3 project
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

REM Get file size
for %%A in ("%FIRMWARE_FILE%") do set FIRMWARE_SIZE=%%~zA
echo Found firmware file: %FIRMWARE_FILE%
echo File size: %FIRMWARE_SIZE% bytes
echo.

REM Check if updater exists
if exist "STM32_Updater.exe" (
    echo Starting GUI updater...
    echo.
    echo Instructions:
    echo 1. Put your STM32 device in bootloader mode
    echo 2. The GUI will open automatically
    echo 3. Click "Auto-Detect Device"
    echo 4. Click "Update Firmware"
    echo.
    STM32_Updater.exe
) else if exist "stm32_updater.exe" (
    echo GUI updater not found, using command-line version...
    echo.
    echo Please ensure your STM32 device is connected and in bootloader mode
    echo Press any key when ready...
    pause >nul
    echo.
    stm32_updater.exe "%FIRMWARE_FILE%"
) else (
    echo ERROR: No updater found!
    echo Please build the updater tools first by running: build.bat
    echo.
    pause
    exit /b 1
)

echo.
echo Update process completed.
pause
