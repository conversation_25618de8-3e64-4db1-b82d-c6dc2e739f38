@echo off
echo ========================================
echo STM32 Firmware Updater Build Script
echo ========================================
echo.

REM Check if GCC is available
gcc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: GCC compiler not found!
    echo Please install MinGW-w64 or TDM-GCC
    echo Download from: https://www.mingw-w64.org/downloads/
    pause
    exit /b 1
)

echo Building command-line version...
gcc stm32_firmware_updater.c RS232\rs232.c -IRS232 -Wall -Wextra -O2 -o stm32_updater.exe
if %errorlevel% neq 0 (
    echo ERROR: Failed to build command-line version
    pause
    exit /b 1
)
echo ✓ Command-line version built: stm32_updater.exe

echo.
echo Building GUI version...
gcc stm32_updater_gui.c RS232\rs232.c -IRS232 -lgdi32 -luser32 -lkernel32 -lcomctl32 -mwindows -O2 -o STM32_Updater.exe
if %errorlevel% neq 0 (
    echo ERROR: Failed to build GUI version
    pause
    exit /b 1
)
echo ✓ GUI version built: STM32_Updater.exe

echo.
echo Creating distribution package...
if not exist "dist" mkdir dist
copy STM32_Updater.exe dist\
copy stm32_updater.exe dist\
copy README_UPDATER.txt dist\ 2>nul

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Files created:
echo   - stm32_updater.exe      (Command-line version)
echo   - STM32_Updater.exe      (GUI version)
echo   - dist\                  (Distribution folder)
echo.
echo For end users, distribute the STM32_Updater.exe file
echo along with the README_UPDATER.txt instructions.
echo.
pause
