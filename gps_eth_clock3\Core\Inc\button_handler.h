#ifndef BUTTON_HANDLER_H
#define BUTTON_HANDLER_H

#include "main.h"
#include "stdint.h"

// Function prototypes
void handle_buttons(uint32_t current_time);
void init_button_handler(void);
void incrementSelected<PERSON><PERSON>(void);
void resetSelectedField(void);
void toggleAmPm(void);
void showDate(void);

// External variables
extern volatile uint8_t time_setting_mode;
extern volatile uint8_t selected_field;
extern volatile uint8_t is_pm;
extern volatile uint8_t manual_seconds;
extern volatile uint8_t manual_minutes;
extern volatile uint8_t manual_hours;
extern volatile uint8_t button_operation_in_progress;
extern volatile uint8_t display_update_needed;
extern volatile uint8_t rtc_read_needed;
extern volatile uint8_t rtc_write_needed;

// Button handling constants
#define DEBOUNCE_TIME 300
#define LONG_PRESS_TIME 1500
#define BLINK_INTERVAL 500

#endif /* BUTTON_HANDLER_H */
