===============================================================================
                        STM32 Firmware Updater v2.0
                     Automated FUOTA Tool for STM32F103
===============================================================================

OVERVIEW
--------
This tool allows you to easily update firmware on STM32F103 devices without 
requiring any technical knowledge. Simply connect your device, select the 
firmware file, and click update!

WHAT'S INCLUDED
---------------
- STM32_Updater.exe     : Easy-to-use GUI version (recommended)
- stm32_updater.exe     : Command-line version for advanced users

QUICK START GUIDE
-----------------
1. Connect your STM32 device to your computer via USB-to-Serial adapter
2. Put the device in bootloader mode (see instructions below)
3. Run STM32_Updater.exe
4. Follow the 3 simple steps in the GUI:
   - Step 1: Select your firmware file (.bin format)
   - Step 2: Click "Auto-Detect Device" 
   - Step 3: Click "Update Firmware"

PUTTING DEVICE IN BOOTLOADER MODE
----------------------------------
Method 1 (Recommended):
1. Disconnect power from your STM32 device
2. Connect BOOT0 pin to 3.3V (or press and hold BOOT button if available)
3. Connect power to the device
4. Release BOOT0 pin (or BOOT button)
5. The device is now in bootloader mode

Method 2 (Reset method):
1. Hold BOOT0 pin high (3.3V)
2. Press and release RESET button
3. Release BOOT0 pin
4. Device should now be in bootloader mode

WIRING CONNECTIONS
------------------
STM32F103 Pin    USB-to-Serial Adapter
-------------    -------------------
PA9 (TX)      -> RX
PA10 (RX)     -> TX  
GND           -> GND
3.3V          -> 3.3V (if powering from adapter)

Note: TX and RX are crossed between devices!

SUPPORTED FIRMWARE FILES
-------------------------
- .bin files (binary firmware format)
- Maximum size: 48KB
- Must be compiled for STM32F103C8T6 with correct memory layout

EXAMPLE FIRMWARE FILES
-----------------------
Your project includes these example firmware files:
- gps_eth_clock3\Debug\gps_eth_clock3.bin (20KB GPS clock application)
- Application\Debug\Application.bin (if built)

TROUBLESHOOTING
---------------

Problem: "No STM32 bootloader detected"
Solution: 
- Check USB-to-Serial adapter connections
- Ensure device is in bootloader mode
- Try different COM ports manually
- Check if drivers are installed for your USB adapter

Problem: "Firmware file too large"
Solution:
- Ensure firmware is compiled for STM32F103C8T6
- Check linker script memory layout
- Maximum application size is 47KB

Problem: "Update failed during transfer"
Solution:
- Check power supply stability
- Ensure good connections (no loose wires)
- Try a different USB cable
- Restart the update process

Problem: Device doesn't respond after update
Solution:
- Power cycle the device
- Check if firmware is compatible with your hardware
- Verify firmware file integrity

COMMAND-LINE USAGE (Advanced)
------------------------------
For automated scripts or advanced users:

stm32_updater.exe [options] <firmware_file>

Options:
  -v, --verbose    Enable detailed output
  -h, --help       Show help message

Examples:
  stm32_updater.exe firmware.bin
  stm32_updater.exe -v gps_eth_clock3.bin

TECHNICAL SPECIFICATIONS
-------------------------
- Supported MCU: STM32F103C8T6 (Blue Pill)
- Communication: UART at 115200 baud
- Protocol: Custom bootloader protocol
- Transfer speed: ~8KB/s
- Maximum firmware size: 48KB
- Supported OS: Windows 7/8/10/11

SAFETY NOTES
------------
- Always backup your current firmware before updating
- Ensure stable power supply during update process
- Do not disconnect device during firmware transfer
- Use only firmware files designed for your specific hardware

SUPPORT
-------
If you encounter issues:
1. Check the troubleshooting section above
2. Verify your hardware connections
3. Ensure you're using the correct firmware file
4. Try the verbose mode for detailed error messages

For technical support, contact your firmware developer with:
- Error messages (if any)
- Hardware configuration details
- Firmware file information

VERSION HISTORY
---------------
v2.0 - December 2024
- Added GUI interface for non-technical users
- Automatic COM port detection
- Progress tracking and status updates
- Improved error handling and recovery
- Drag & drop firmware file support

v1.0 - Initial release
- Basic command-line interface
- Manual COM port specification required

===============================================================================
                    Copyright 2024 - STM32 Development Team
===============================================================================
