#ifndef LORA_HANDLER_H
#define LORA_HANDLER_H

#include "main.h"

#define LORA_FREQUENCY     868E6
#define LORA_BLOCK_SIZE    256     // Smaller blocks for LoRa transmission

typedef struct {
    uint32_t total_size;
    uint32_t block_number;
    uint32_t crc;
    uint8_t data[LORA_BLOCK_SIZE];
} lora_packet_t;

HAL_StatusTypeDef lora_init(void);
HAL_StatusTypeDef lora_check_update(void);
HAL_StatusTypeDef lora_receive_firmware(void);

#endif /* LORA_HANDLER_H */
