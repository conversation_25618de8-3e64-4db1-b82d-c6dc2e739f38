#!/usr/bin/env python3
"""
STM32F103 Firmware Updater - Simple Production Tool
==================================================

This tool provides a simple, automated firmware update process for your
STM32F103 bootloader system. It uses Option 3 (predefined sizes) for
maximum reliability.

Usage:
    python simple_firmware_updater.py COM3 Application.bin

Features:
- Automatic bootloader detection
- Option 3 (GPS clock size) selection
- Progress tracking
- Error handling and timeout detection
- Works with your enhanced bootloader

Author: AI Assistant
Date: 2025
"""

import serial
import time
import sys
import os
from pathlib import Path

class STM32FirmwareUpdater:
    def __init__(self, port, firmware_file, verbose=True):
        self.port = port
        self.firmware_file = firmware_file
        self.verbose = verbose
        self.serial = None
        self.firmware_data = None
        
    def log(self, message):
        """Print message if verbose mode is enabled"""
        if self.verbose:
            print(f"[INFO] {message}")
    
    def error(self, message):
        """Print error message"""
        print(f"[ERROR] {message}")
    
    def load_firmware(self):
        """Load firmware file and validate"""
        try:
            if not os.path.exists(self.firmware_file):
                self.error(f"Firmware file not found: {self.firmware_file}")
                return False
            
            with open(self.firmware_file, 'rb') as f:
                self.firmware_data = f.read()
            
            file_size = len(self.firmware_data)
            self.log(f"Loaded firmware: {self.firmware_file}")
            self.log(f"Firmware size: {file_size} bytes ({file_size/1024:.1f} KB)")
            
            # Validate size (must fit in 47KB application space)
            if file_size > 47 * 1024:
                self.error(f"Firmware too large: {file_size} bytes (max: 48128 bytes)")
                return False
            
            if file_size == 0:
                self.error("Firmware file is empty")
                return False
                
            return True
            
        except Exception as e:
            self.error(f"Failed to load firmware: {e}")
            return False
    
    def connect(self):
        """Connect to STM32 via serial port"""
        try:
            self.log(f"Connecting to {self.port} at 115200 baud...")
            self.serial = serial.Serial(
                port=self.port,
                baudrate=115200,
                bytesize=8,
                parity='N',
                stopbits=1,
                timeout=1,
                xonxoff=False,
                rtscts=False,
                dsrdtr=False
            )
            
            # Clear any existing data
            self.serial.flushInput()
            self.serial.flushOutput()
            
            self.log("✅ Serial connection established")
            return True
            
        except Exception as e:
            self.error(f"Failed to connect to {self.port}: {e}")
            return False
    
    def wait_for_bootloader(self, timeout=10):
        """Wait for bootloader handshake ('.' character)"""
        self.log("Waiting for bootloader handshake...")
        self.log("(Reset STM32 if needed)")
        
        start_time = time.time()
        received_data = ""
        
        while time.time() - start_time < timeout:
            if self.serial.in_waiting > 0:
                char = self.serial.read(1).decode('ascii', errors='ignore')
                received_data += char
                
                if char == '.':
                    self.log("✅ Bootloader handshake received!")
                    return True
                
                # Show some bootloader output for debugging
                if len(received_data) > 50:
                    received_data = received_data[-50:]  # Keep last 50 chars
        
        self.error(f"Timeout waiting for bootloader handshake")
        self.error(f"Received: {repr(received_data[-100:])}")
        return False
    
    def start_update(self):
        """Send 'o' to start firmware update"""
        self.log("Starting firmware update...")
        self.serial.write(b'o')
        time.sleep(0.5)  # Give bootloader time to process
        return True
    
    def select_option_3(self, timeout=5):
        """Wait for menu and select Option 3 (GPS clock size)"""
        self.log("Waiting for bootloader menu...")
        
        start_time = time.time()
        menu_buffer = ""
        
        while time.time() - start_time < timeout:
            if self.serial.in_waiting > 0:
                char = self.serial.read(1).decode('ascii', errors='ignore')
                menu_buffer += char
                
                # Look for the "Enter number:" prompt
                if "Enter number:" in menu_buffer:
                    self.log("✅ Menu prompt detected")
                    self.log("Selecting Option 3 (GPS clock default size)...")
                    
                    # Send choice 3
                    self.serial.write(b'3\r\n')
                    time.sleep(1)  # Give bootloader time to process
                    return True
        
        self.error("Timeout waiting for menu prompt")
        self.error(f"Received: {repr(menu_buffer[-200:])}")
        return False
    
    def send_firmware_data(self):
        """Send firmware data to bootloader"""
        self.log(f"Sending {len(self.firmware_data)} bytes of firmware data...")
        
        bytes_sent = 0
        last_progress = 0
        start_time = time.time()
        
        for byte_value in self.firmware_data:
            # Send byte
            self.serial.write(bytes([byte_value]))
            bytes_sent += 1
            
            # Progress reporting every 1KB
            if bytes_sent - last_progress >= 1024:
                progress_percent = (bytes_sent * 100) // len(self.firmware_data)
                elapsed = time.time() - start_time
                speed = bytes_sent / elapsed if elapsed > 0 else 0
                
                self.log(f"Progress: {progress_percent}% ({bytes_sent}/{len(self.firmware_data)} bytes) "
                        f"Speed: {speed:.0f} bytes/sec")
                last_progress = bytes_sent
            
            # Small delay to prevent overwhelming the bootloader
            time.sleep(0.001)  # 1ms delay between bytes
        
        elapsed = time.time() - start_time
        speed = len(self.firmware_data) / elapsed if elapsed > 0 else 0
        
        self.log(f"✅ Firmware data sent successfully!")
        self.log(f"Total time: {elapsed:.1f} seconds")
        self.log(f"Average speed: {speed:.0f} bytes/sec")
        
        return True
    
    def wait_for_completion(self, timeout=10):
        """Wait for bootloader completion message"""
        self.log("Waiting for update completion...")
        
        start_time = time.time()
        response_buffer = ""
        
        while time.time() - start_time < timeout:
            if self.serial.in_waiting > 0:
                char = self.serial.read(1).decode('ascii', errors='ignore')
                response_buffer += char
                
                # Look for success indicators
                if "Firmware Update Completed Successfully" in response_buffer:
                    self.log("✅ Bootloader confirmed successful update!")
                    return True
                elif "Gonna Jump to Application" in response_buffer:
                    self.log("✅ Bootloader jumping to application!")
                    return True
                elif "Application" in response_buffer and "Started" in response_buffer:
                    self.log("✅ Application started successfully!")
                    return True
        
        # Even if we don't see completion message, the update likely succeeded
        self.log("⚠️  Timeout waiting for completion message (update likely succeeded)")
        return True
    
    def disconnect(self):
        """Close serial connection"""
        if self.serial:
            self.serial.close()
            self.log("Serial connection closed")
    
    def update_firmware(self):
        """Complete firmware update process"""
        try:
            # Step 1: Load and validate firmware
            if not self.load_firmware():
                return False
            
            # Step 2: Connect to STM32
            if not self.connect():
                return False
            
            # Step 3: Wait for bootloader
            if not self.wait_for_bootloader():
                return False
            
            # Step 4: Start update
            if not self.start_update():
                return False
            
            # Step 5: Select Option 3
            if not self.select_option_3():
                return False
            
            # Step 6: Send firmware data
            if not self.send_firmware_data():
                return False
            
            # Step 7: Wait for completion
            if not self.wait_for_completion():
                return False
            
            self.log("🎉 Firmware update completed successfully!")
            return True
            
        except KeyboardInterrupt:
            self.error("Update cancelled by user")
            return False
        except Exception as e:
            self.error(f"Unexpected error: {e}")
            return False
        finally:
            self.disconnect()

def main():
    """Main function"""
    print("=" * 60)
    print("STM32F103 Firmware Updater v1.0")
    print("=" * 60)
    
    if len(sys.argv) != 3:
        print("Usage: python simple_firmware_updater.py <COM_PORT> <FIRMWARE_FILE>")
        print("")
        print("Examples:")
        print("  python simple_firmware_updater.py COM3 Application.bin")
        print("  python simple_firmware_updater.py /dev/ttyUSB0 gps_clock.bin")
        print("")
        print("Supported firmware files: .bin, .hex")
        return 1
    
    port = sys.argv[1]
    firmware_file = sys.argv[2]
    
    # Create updater and run
    updater = STM32FirmwareUpdater(port, firmware_file, verbose=True)
    success = updater.update_firmware()
    
    if success:
        print("\n✅ SUCCESS: Firmware update completed!")
        print("Your STM32 should now be running the new application.")
        return 0
    else:
        print("\n❌ FAILED: Firmware update failed!")
        print("Check connections and try again.")
        return 1

if __name__ == "__main__":
    exit(main())
