/*
 * STM32 Bootloader Test - Works with Common Proteus Microcontrollers
 * ==================================================================
 *
 * Compatible with: ATMEGA328P, PIC16F877A, ATMEGA16, etc.
 *
 * Proteus Connections:
 * MCU UART TX → STM32 PA10 (RX)
 * MCU UART RX → STM32 PA9 (TX)
 * GND → GND
 *
 * For ATMEGA328P: Use pins PD0(RX), PD1(TX) or SoftwareSerial
 * For PIC16F877A: Use pins RC6(TX), RC7(RX)
 * For ATMEGA16: Use pins PD0(RX), PD1(TX)
 *
 * Usage:
 * 1. Open Virtual Terminal (9600 baud)
 * 2. Type decimal numbers when prompted
 * 3. For 20KB: 148, 79, then data bytes
 * 4. For easy test: 10, 0, then 10 data bytes
 */

// Configuration - uncomment your microcontroller type
#define USE_ATMEGA328P_SOFTSERIAL  // Arduino-style with SoftwareSerial
// #define USE_HARDWARE_UART       // For PIC, ATMEGA16, etc.

#ifdef USE_ATMEGA328P_SOFTSERIAL
#include <SoftwareSerial.h>
SoftwareSerial stm32(2, 3); // RX=Pin2, TX=Pin3
#define STM32_UART stm32
#define USER_UART Serial
#else
// For microcontrollers with single hardware UART
#define STM32_UART Serial
#define USER_UART Serial
#endif

enum State {
  WAITING_HANDSHAKE,
  WAITING_SIZE_LOW_INPUT,
  WAITING_SIZE_HIGH_INPUT,
  SENDING_SIZE_LOW,
  SENDING_SIZE_HIGH,
  WAITING_DATA_INPUT,
  SENDING_DATA,
  COMPLETE
};

State currentState = WAITING_HANDSHAKE;
uint16_t firmwareSize = 0;
uint16_t bytesSent = 0;
uint8_t sizeLow = 0;
uint8_t sizeHigh = 0;
uint8_t currentDataByte = 0;
bool waitingForSTM32 = false;

void setup() {
  Serial.begin(9600);
  stm32.begin(115200);
  
  Serial.println("=== ASCII-Friendly STM32 Bootloader Test ===");
  Serial.println("Easy decimal input for non-technical users");
  Serial.println();
  Serial.println("Proteus Connections:");
  Serial.println("Arduino Pin 2 (RX) → STM32 PA9 (TX)");
  Serial.println("Arduino Pin 3 (TX) → STM32 PA10 (RX)");
  Serial.println("GND → GND");
  Serial.println();
  Serial.println("Waiting for STM32 handshake...");
  Serial.println("(STM32 should send dots '.' when ready)");
}

void loop() {
  // Handle STM32 communication
  if (stm32.available()) {
    char received = stm32.read();
    handleSTM32Protocol(received);
  }
  
  // Handle user input from Serial Monitor
  if (Serial.available()) {
    handleUserInput();
  }
}

void handleSTM32Protocol(char received) {
  Serial.print("STM32 sent: '");
  Serial.print(received);
  Serial.print("' (0x");
  Serial.print((uint8_t)received, HEX);
  Serial.println(")");
  
  switch (currentState) {
    
    case WAITING_HANDSHAKE:
      if (received == '.') {
        Serial.println("✅ Handshake received!");
        Serial.println("Sending 'o' to start update...");
        stm32.write('o');
        currentState = WAITING_SIZE_LOW_INPUT;
        promptForSizeLow();
      }
      break;
      
    case SENDING_SIZE_LOW:
      if (received == 'y') {
        Serial.print("Sending size low byte: ");
        Serial.print(sizeLow);
        Serial.print(" (0x");
        Serial.print(sizeLow, HEX);
        Serial.println(")");
        stm32.write(sizeLow);
        currentState = SENDING_SIZE_HIGH;
        waitingForSTM32 = true;
      }
      break;
      
    case SENDING_SIZE_HIGH:
      if (received == 'x') {
        Serial.print("Sending size high byte: ");
        Serial.print(sizeHigh);
        Serial.print(" (0x");
        Serial.print(sizeHigh, HEX);
        Serial.println(")");
        stm32.write(sizeHigh);
        
        firmwareSize = sizeLow + (sizeHigh << 8);
        Serial.print("Total firmware size: ");
        Serial.print(firmwareSize);
        Serial.println(" bytes");
        Serial.println("Starting data transfer...");
        
        currentState = WAITING_DATA_INPUT;
        bytesSent = 0;
        promptForDataByte();
      }
      break;
      
    case SENDING_DATA:
      if (bytesSent >= firmwareSize) {
        Serial.println("✅ All data sent! Update complete!");
        currentState = COMPLETE;
        return;
      }
      
      if (received == 'y' || received == 'x') {
        Serial.print("Sending data byte ");
        Serial.print(bytesSent + 1);
        Serial.print("/");
        Serial.print(firmwareSize);
        Serial.print(": ");
        Serial.print(currentDataByte);
        Serial.print(" (0x");
        Serial.print(currentDataByte, HEX);
        Serial.println(")");
        
        stm32.write(currentDataByte);
        bytesSent++;
        
        if (bytesSent < firmwareSize) {
          currentState = WAITING_DATA_INPUT;
          promptForDataByte();
        }
      }
      break;
      
    case COMPLETE:
      Serial.println("🎉 Firmware update completed successfully!");
      break;
  }
}

void handleUserInput() {
  String input = Serial.readStringUntil('\n');
  input.trim();
  
  int value = input.toInt();
  
  switch (currentState) {
    
    case WAITING_SIZE_LOW_INPUT:
      if (value >= 0 && value <= 255) {
        sizeLow = (uint8_t)value;
        Serial.print("✅ Size low byte set to: ");
        Serial.println(sizeLow);
        currentState = WAITING_SIZE_HIGH_INPUT;
        promptForSizeHigh();
      } else {
        Serial.println("❌ Invalid input! Please enter a number between 0-255");
        promptForSizeLow();
      }
      break;
      
    case WAITING_SIZE_HIGH_INPUT:
      if (value >= 0 && value <= 255) {
        sizeHigh = (uint8_t)value;
        Serial.print("✅ Size high byte set to: ");
        Serial.println(sizeHigh);
        Serial.println("Waiting for STM32 to request size...");
        currentState = SENDING_SIZE_LOW;
      } else {
        Serial.println("❌ Invalid input! Please enter a number between 0-255");
        promptForSizeHigh();
      }
      break;
      
    case WAITING_DATA_INPUT:
      if (value >= 0 && value <= 255) {
        currentDataByte = (uint8_t)value;
        Serial.print("✅ Data byte set to: ");
        Serial.println(currentDataByte);
        Serial.println("Waiting for STM32 to request data...");
        currentState = SENDING_DATA;
      } else {
        Serial.println("❌ Invalid input! Please enter a number between 0-255");
        promptForDataByte();
      }
      break;
      
    default:
      Serial.println("Input not expected at this time.");
      break;
  }
}

void promptForSizeLow() {
  Serial.println();
  Serial.println("📏 FIRMWARE SIZE - LOW BYTE");
  Serial.println("For 20KB (20,388 bytes): enter 148");
  Serial.println("For 10 bytes: enter 10");
  Serial.println("For 1KB: enter 0");
  Serial.print("Enter size low byte (0-255): ");
}

void promptForSizeHigh() {
  Serial.println();
  Serial.println("📏 FIRMWARE SIZE - HIGH BYTE");
  Serial.println("For 20KB (20,388 bytes): enter 79");
  Serial.println("For 10 bytes: enter 0");
  Serial.println("For 1KB: enter 4");
  Serial.print("Enter size high byte (0-255): ");
}

void promptForDataByte() {
  Serial.println();
  Serial.print("📤 DATA BYTE ");
  Serial.print(bytesSent + 1);
  Serial.print("/");
  Serial.print(firmwareSize);
  Serial.println();
  Serial.println("Enter any number 0-255 (or use pattern like byte number % 256):");
  Serial.print("Suggested value: ");
  Serial.print((bytesSent + 1) % 256);
  Serial.print(" - Enter data byte: ");
}

/*
 * QUICK REFERENCE FOR 20KB FIRMWARE:
 * 
 * 1. Wait for dots from STM32
 * 2. Enter: 148 (size low byte)
 * 3. Enter: 79 (size high byte)
 * 4. Enter data bytes 1-20388 (any values 0-255)
 * 
 * EASY TEST WITH 10 BYTES:
 * 
 * 1. Wait for dots from STM32
 * 2. Enter: 10 (size low byte)
 * 3. Enter: 0 (size high byte)
 * 4. Enter 10 data bytes (e.g., 1,2,3,4,5,6,7,8,9,10)
 * 
 * STM32 EXPECTED OUTPUT:
 * "Application Size = 10 bytes" or "Application Size = 20388 bytes"
 */
