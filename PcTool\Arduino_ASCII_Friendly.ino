/*
 * Enhanced STM32 Bootloader Test - ASCII Input Support
 * ===================================================
 *
 * Works with the new enhanced STM32 bootloader that supports:
 * - ASCII decimal input for firmware size
 * - Auto-detection of firmware size
 * - Automatic data transfer without handshaking
 * - Rollback on errors
 *
 * Proteus Connections:
 * MCU UART TX → STM32 PA10 (RX)
 * MCU UART RX → STM32 PA9 (TX)
 * GND → GND
 *
 * Usage:
 * 1. STM32 will ask for size input method (1, 2, or 3)
 * 2. Choose option 3 for GPS clock (20388 bytes) or option 1 for manual
 * 3. STM32 will automatically receive firmware data
 * 4. No more 'y'/'x' handshaking needed!
 */

// Configuration
#define USE_SOFTSERIAL  // Use SoftwareSerial for Arduino/ATMEGA328P
// #define USE_HARDWARE_UART  // Use hardware UART for other MCUs

#ifdef USE_SOFTSERIAL
#include <SoftwareSerial.h>
SoftwareSerial stm32(2, 3); // RX=Pin2, TX=Pin3
#define STM32_UART stm32
#define DEBUG_UART Serial
#else
#define STM32_UART Serial
#define DEBUG_UART Serial
#endif

// Test firmware data (20KB GPS clock simulation)
const uint32_t FIRMWARE_SIZE = 20388;  // Your GPS clock size
uint8_t firmware_data[FIRMWARE_SIZE];

enum State {
  WAITING_HANDSHAKE,
  WAITING_SIZE_CHOICE,
  SENDING_FIRMWARE_DATA,
  COMPLETE
};

State currentState = WAITING_HANDSHAKE;
uint32_t bytesSent = 0;
bool dataTransferStarted = false;

void setup() {
  DEBUG_UART.begin(9600);
  STM32_UART.begin(115200);

  // Generate test firmware data (simple pattern)
  for(uint32_t i = 0; i < FIRMWARE_SIZE; i++) {
    firmware_data[i] = (uint8_t)(i & 0xFF) ^ 0xAA;  // Simple test pattern
  }

  DEBUG_UART.println("=== Enhanced STM32 Bootloader Test ===");
  DEBUG_UART.println("Testing with new ASCII input and auto-transfer features");
  DEBUG_UART.println();
  DEBUG_UART.print("Firmware size: ");
  DEBUG_UART.print(FIRMWARE_SIZE);
  DEBUG_UART.println(" bytes");
  DEBUG_UART.println();
  DEBUG_UART.println("Waiting for STM32 handshake...");
  DEBUG_UART.println("(STM32 should send dots '.' when ready)");
}

void loop() {
  // Handle STM32 communication
  if (STM32_UART.available()) {
    char received = STM32_UART.read();
    handleSTM32Protocol(received);
  }

  // Handle automatic data transfer
  if (dataTransferStarted && currentState == SENDING_FIRMWARE_DATA) {
    sendFirmwareData();
  }
}

void handleSTM32Protocol(char received) {
  DEBUG_UART.print("STM32: '");
  DEBUG_UART.print(received);
  DEBUG_UART.print("' (0x");
  DEBUG_UART.print((uint8_t)received, HEX);
  DEBUG_UART.println(")");

  switch (currentState) {

    case WAITING_HANDSHAKE:
      if (received == '.') {
        DEBUG_UART.println("✅ Handshake received!");
        DEBUG_UART.println("Sending 'o' to start update...");
        STM32_UART.write('o');
        currentState = WAITING_SIZE_CHOICE;
        delay(100); // Give STM32 time to process
      }
      break;

    case WAITING_SIZE_CHOICE:
      // STM32 will ask for size input method choice
      // We'll automatically choose option 3 (GPS clock default)
      if (received == ':' || received == ' ') {  // End of prompt
        DEBUG_UART.println("STM32 asking for size input method...");
        DEBUG_UART.println("Sending '3' for GPS clock default size");
        STM32_UART.print("3\r\n");  // Choose option 3 with carriage return
        currentState = SENDING_FIRMWARE_DATA;
        dataTransferStarted = true;
        bytesSent = 0;
        delay(500); // Give STM32 time to process choice
      }
      break;

    case SENDING_FIRMWARE_DATA:
      // STM32 will automatically receive data, no handshaking needed
      // This is handled in sendFirmwareData() function
      break;

    case COMPLETE:
      DEBUG_UART.println("🎉 Firmware update completed successfully!");
      break;
  }
}

void sendFirmwareData() {
  static unsigned long lastSendTime = 0;
  static uint32_t lastProgressReport = 0;

  // Send data at controlled rate (not too fast for STM32 to handle)
  if (millis() - lastSendTime > 1) {  // 1ms delay between bytes
    if (bytesSent < FIRMWARE_SIZE) {
      STM32_UART.write(firmware_data[bytesSent]);
      bytesSent++;
      lastSendTime = millis();

      // Progress report every 1KB
      if (bytesSent - lastProgressReport >= 1024) {
        float progress = (float)bytesSent * 100.0f / FIRMWARE_SIZE;
        DEBUG_UART.print("Progress: ");
        DEBUG_UART.print(progress, 1);
        DEBUG_UART.print("% (");
        DEBUG_UART.print(bytesSent);
        DEBUG_UART.print("/");
        DEBUG_UART.print(FIRMWARE_SIZE);
        DEBUG_UART.println(" bytes)");
        lastProgressReport = bytesSent;
      }
    } else {
      // All data sent
      DEBUG_UART.println("✅ All firmware data sent!");
      DEBUG_UART.print("Total bytes sent: ");
      DEBUG_UART.println(bytesSent);
      currentState = COMPLETE;
      dataTransferStarted = false;
    }
  }
}

/*
 * Enhanced STM32 Bootloader Test Results
 * =====================================
 *
 * This Arduino code demonstrates the new enhanced bootloader features:
 *
 * 1. ✅ ASCII Input Support
 *    - STM32 accepts decimal numbers like "20388" instead of binary bytes
 *    - User-friendly prompts and input validation
 *    - Backspace support and echo
 *
 * 2. ✅ Auto-Detection Options
 *    - Option 1: Manual ASCII input
 *    - Option 2: Auto-detect from data stream
 *    - Option 3: Default GPS clock size (20388 bytes)
 *
 * 3. ✅ Automatic Data Transfer
 *    - No more 'y'/'x' handshaking protocol
 *    - STM32 automatically receives firmware data
 *    - Real-time progress tracking
 *
 * 4. ✅ Error Handling and Rollback
 *    - Automatic rollback on flash write errors
 *    - Timeout detection and recovery
 *    - LED blinking to indicate error states
 *
 * Expected STM32 Output:
 * ======================
 *
 * Bootloader v1:0 Started!!!
 *
 *  Press 'o' to start Firmware Update...
 * === Enhanced Firmware Update Started ===
 * Choose firmware size input method:
 * 1. Enter size manually (ASCII decimal)
 * 2. Auto-detect from data stream
 * 3. Use default size for GPS clock (20388 bytes)
 * Enter number: 3
 * Using default GPS clock size: 20388 bytes
 * Confirmed firmware size: 20388 bytes
 * Starting automatic data transfer...
 * Ready to receive 20388 bytes. Send data now...
 * Progress: 1024/20388 bytes (5.0%)
 * Progress: 2048/20388 bytes (10.0%)
 * ...
 * Writing block 0 to flash...
 * Block 0 written successfully
 * ...
 * === Firmware Update Completed Successfully ===
 * Total bytes written: 20388
 * Total blocks written: 20
 * Gonna Jump to Application...
 *
 * This demonstrates a complete transformation from manual binary input
 * to a fully automated, user-friendly firmware update system!
 */
