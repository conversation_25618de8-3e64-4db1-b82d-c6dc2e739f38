# 📝 Manual Testing Guide - Type Values Yourself

## 🎯 **If You Want to Type Values Manually in Arduino Terminal**

This guide shows you exactly what to type in the Arduino Serial Monitor (COM2) to simulate the firmware update protocol.

## 📋 **For 20KB (20,388 bytes) Firmware**

### **Step-by-Step Manual Input**

#### **Step 1: Wait for STM32 Handshake**
**STM32 will send:** `'.'` (you'll see dots appearing)
**You type in Arduino terminal:** `o`

#### **Step 2: Send Firmware Size**
**STM32 sends:** `'y'` (requesting low byte)
**You type:** `148` (this is 20388 & 0xFF)

**STM32 sends:** `'x'` (requesting high byte)  
**You type:** `79` (this is (20388 >> 8) & 0xFF)

#### **Step 3: Send Data Bytes (20,388 times!)**
For each data byte pair:
**STM32 sends:** `'y'` → **You type:** any number 0-255
**STM32 sends:** `'x'` → **You type:** any number 0-255

**Repeat this 10,194 times!** (That's why we made automation! 😅)

## 🔢 **Size Calculation Examples**

### **For Different Firmware Sizes:**

#### **20KB (20,388 bytes):**
```
20388 = 0x4F94
Low byte: 148 (0x94)
High byte: 79 (0x4F)
```

#### **10KB (10,240 bytes):**
```
10240 = 0x2800  
Low byte: 0 (0x00)
High byte: 40 (0x28)
```

#### **5KB (5,120 bytes):**
```
5120 = 0x1400
Low byte: 0 (0x00)
High byte: 20 (0x14)
```

## 🖥️ **What You'll Type in Arduino Terminal**

### **Complete Manual Sequence:**
```
o                    [When you see dots from STM32]
148                  [When STM32 sends 'y' for size low]
79                   [When STM32 sends 'x' for size high]
170                  [When STM32 sends 'y' for data byte 1]
85                   [When STM32 sends 'x' for data byte 2]
123                  [When STM32 sends 'y' for data byte 3]
45                   [When STM32 sends 'x' for data byte 4]
... continue for 20,384 more bytes!
```

## ⚡ **Quick Test with Smaller Size**

### **Test with 10 bytes instead:**
```
Size: 10 bytes = 0x000A
Low byte: 10 (0x0A)
High byte: 0 (0x00)

Manual input:
o        [Start]
10       [Size low]
0        [Size high]  
1        [Data byte 1]
2        [Data byte 2]
3        [Data byte 3]
4        [Data byte 4]
5        [Data byte 5]
6        [Data byte 6]
7        [Data byte 7]
8        [Data byte 8]
9        [Data byte 9]
10       [Data byte 10]
```

## 🔍 **What STM32 Will Show**

### **For 10-byte test:**
```
Bootloader v1:0 Started!!!

 Press 'o' to start Firmware Update...
Firmware Update Started
Application Size = 10 bytes
Erasing the Flash memory...
Received Block[0]
Gonna Jump to Application...
```

### **For 20KB test:**
```
Bootloader v1:0 Started!!!

 Press 'o' to start Firmware Update...
Firmware Update Started  
Application Size = 20388 bytes
Erasing the Flash memory...
Received Block[0]
Received Block[1]
Received Block[2]
...
Received Block[19]
Gonna Jump to Application...
```

## ⚠️ **Why Manual Testing is Impractical**

### **For 20KB firmware, you need to type:**
- 1 start command: `o`
- 2 size bytes: `148`, `79`  
- 20,388 data bytes: `1`, `2`, `3`, ..., `255`, `1`, `2`, ...

**Total: 20,391 manual inputs!** 😱

**Time estimate:** ~6-8 hours of continuous typing!

## 🚀 **That's Why We Created Automation!**

### **Arduino Automation (Proteus):**
- ✅ Handles all 20,391 inputs automatically
- ✅ Perfect timing and no typos
- ✅ Complete in 3-5 minutes
- ✅ Shows progress and debug info

### **Python Tool (Real Hardware):**
- ✅ Loads actual firmware files
- ✅ Automatic COM port detection
- ✅ Real-time progress tracking
- ✅ Professional user interface

## 🎯 **Recommended Testing Approach**

### **1. Quick Manual Test (10 bytes):**
```bash
# Test basic protocol with tiny firmware
o
10
0
1,2,3,4,5,6,7,8,9,10
```

### **2. Arduino Automation (Proteus):**
```bash
# Test full 20KB with automation
Upload Arduino_STM32_Updater_Proteus.ino
Run simulation
Watch automatic transfer
```

### **3. Python Tool (Real Hardware):**
```bash
# Production firmware updates
python stm32_updater_python.py firmware.bin
```

## 📊 **Comparison Table**

| Method | Time | Effort | Accuracy | Real Use |
|--------|------|--------|----------|----------|
| Manual 10B | 2 min | High | Error-prone | Testing only |
| Manual 20KB | 6+ hours | Extreme | Impossible | Never! |
| Arduino Auto | 5 min | None | Perfect | Proteus demo |
| Python Tool | 30 sec | None | Perfect | Production |

---

## 🎉 **Conclusion**

Manual testing is useful for understanding the protocol, but automation is essential for real firmware updates. Your Python tool eliminates the need for any manual input while providing a professional, foolproof update experience!

**Use manual testing only for protocol verification, then switch to automation for actual firmware updates.** 🚀
