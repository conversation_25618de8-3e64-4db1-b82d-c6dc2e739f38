/*
 * STM32 Enhanced Bootloader Test
 * =============================
 * 
 * Tests the new enhanced STM32 bootloader with:
 * - ASCII input support
 * - Auto-detection of firmware size
 * - Automatic data transfer
 * - Error handling and rollback
 * 
 * Proteus Connections:
 * Arduino Pin 2 (RX) → STM32 PA9 (TX)
 * Arduino Pin 3 (TX) → STM32 PA10 (RX)
 * GND → GND
 * 
 * This file should be opened alone in Arduino IDE
 * (not with other .ino files in the same folder)
 */

#include <SoftwareSerial.h>

// Configuration
SoftwareSerial stm32Serial(2, 3); // RX=Pin2, TX=Pin3

// Test firmware data (20KB GPS clock simulation)
const uint32_t TEST_FIRMWARE_SIZE = 20388;  // Your GPS clock size
uint8_t testFirmwareData[TEST_FIRMWARE_SIZE];

// State machine for enhanced protocol
enum EnhancedState {
  WAIT_HANDSHAKE,
  WAIT_SIZE_MENU,
  SEND_FIRMWARE,
  TRANSFER_COMPLETE
};

EnhancedState protocolState = WAIT_HANDSHAKE;
uint32_t dataBytesSent = 0;
bool transferActive = false;

void setup() {
  Serial.begin(9600);
  stm32Serial.begin(115200);
  
  // Generate test firmware data (simple pattern)
  for(uint32_t i = 0; i < TEST_FIRMWARE_SIZE; i++) {
    testFirmwareData[i] = (uint8_t)(i & 0xFF) ^ 0xAA;  // Test pattern
  }
  
  Serial.println("=== STM32 Enhanced Bootloader Test ===");
  Serial.println("Testing new ASCII input and auto-transfer features");
  Serial.println();
  Serial.print("Test firmware size: ");
  Serial.print(TEST_FIRMWARE_SIZE);
  Serial.println(" bytes");
  Serial.println();
  Serial.println("Waiting for STM32 handshake...");
  Serial.println("(STM32 should send dots '.' when ready)");
}

void loop() {
  // Handle STM32 communication
  if (stm32Serial.available()) {
    char receivedChar = stm32Serial.read();
    handleSTM32Communication(receivedChar);
  }
  
  // Handle automatic data transfer
  if (transferActive && protocolState == SEND_FIRMWARE) {
    sendFirmwareDataStream();
  }
}

void handleSTM32Communication(char received) {
  Serial.print("STM32: '");
  Serial.print(received);
  Serial.print("' (0x");
  Serial.print((uint8_t)received, HEX);
  Serial.println(")");
  
  switch (protocolState) {
    
    case WAIT_HANDSHAKE:
      if (received == '.') {
        Serial.println("✅ Handshake received!");
        Serial.println("Sending 'o' to start update...");
        stm32Serial.write('o');
        protocolState = WAIT_SIZE_MENU;
        delay(100); // Give STM32 time to process
      }
      break;
      
    case WAIT_SIZE_MENU:
      // STM32 will display menu and ask for choice
      // We'll automatically choose option 3 (GPS clock default)
      if (received == ':' || received == ' ') {  // End of prompt
        Serial.println("STM32 showing size input menu...");
        Serial.println("Automatically choosing option 3 (GPS clock default)");
        stm32Serial.print("3\r\n");  // Send choice with carriage return
        protocolState = SEND_FIRMWARE;
        transferActive = true;
        dataBytesSent = 0;
        delay(500); // Give STM32 time to process choice
      }
      break;
      
    case SEND_FIRMWARE:
      // STM32 will automatically receive data
      // Data transfer is handled in sendFirmwareDataStream()
      break;
      
    case TRANSFER_COMPLETE:
      Serial.println("🎉 Transfer completed successfully!");
      break;
  }
}

void sendFirmwareDataStream() {
  static unsigned long lastSendTime = 0;
  static uint32_t lastProgressUpdate = 0;
  
  // Send data at controlled rate (1ms between bytes)
  if (millis() - lastSendTime > 1) {
    if (dataBytesSent < TEST_FIRMWARE_SIZE) {
      stm32Serial.write(testFirmwareData[dataBytesSent]);
      dataBytesSent++;
      lastSendTime = millis();
      
      // Progress report every 1KB
      if (dataBytesSent - lastProgressUpdate >= 1024) {
        // Calculate percentage using integer arithmetic (no float needed)
        uint32_t progressPercent = (dataBytesSent * 100) / TEST_FIRMWARE_SIZE;
        Serial.print("Progress: ");
        Serial.print(progressPercent);
        Serial.print("% (");
        Serial.print(dataBytesSent);
        Serial.print("/");
        Serial.print(TEST_FIRMWARE_SIZE);
        Serial.println(" bytes)");
        lastProgressUpdate = dataBytesSent;
      }
    } else {
      // All data sent
      Serial.println("✅ All firmware data sent!");
      Serial.print("Total bytes transmitted: ");
      Serial.println(dataBytesSent);
      protocolState = TRANSFER_COMPLETE;
      transferActive = false;
    }
  }
}

/*
 * Expected STM32 Output with Enhanced Bootloader:
 * ===============================================
 * 
 * Bootloader v1:0 Started!!!
 * 
 *  Press 'o' to start Firmware Update...
 * === Enhanced Firmware Update Started ===
 * Choose firmware size input method:
 * 1. Enter size manually (ASCII decimal)
 * 2. Auto-detect from data stream
 * 3. Use default size for GPS clock (20388 bytes)
 * Enter number: 3
 * Using default GPS clock size: 20388 bytes
 * Confirmed firmware size: 20388 bytes
 * Starting automatic data transfer...
 * Ready to receive 20388 bytes. Send data now...
 * Progress: 1024/20388 bytes (5.0%)
 * Progress: 2048/20388 bytes (10.0%)
 * Progress: 3072/20388 bytes (15.1%)
 * ...
 * Writing block 0 to flash...
 * Block 0 written successfully
 * Writing block 1 to flash...
 * Block 1 written successfully
 * ...
 * Writing block 19 to flash...
 * Block 19 written successfully
 * === Firmware Update Completed Successfully ===
 * Total bytes written: 20388
 * Total blocks written: 20
 * Gonna Jump to Application...
 * 
 * This demonstrates the complete transformation from manual
 * binary input to automated ASCII-based firmware updates!
 */
