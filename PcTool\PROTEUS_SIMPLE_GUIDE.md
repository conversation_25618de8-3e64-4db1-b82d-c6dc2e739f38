# 🔬 Proteus STM32 Bootloader Testing - Simple Guide

## 🎯 **Goal**
Test your STM32 bootloader with any available microcontroller in Proteus.

## 📦 **Find These Components in Proteus**

### **Microcontroller Options (pick any one):**
- **ATMEGA328P** or **ATMEGA328P-PU** (Arduino chip)
- **PIC16F877A** (very common in Proteus)
- **ATMEGA16** or **ATMEGA32**
- **8051** variants
- **Any microcontroller with UART**

### **If No Microcontroller Available:**
- **VIRTUAL TERMINAL** (direct connection to STM32)
- **COMPIM** (virtual serial port)

## 🔌 **Simple Connection (Any Microcontroller)**

```
Microcontroller              STM32F103C8T6
┌─────────────┐               ┌─────────────┐
│             │               │             │
│  UART TX    ●───────────────● PA10 (RX)   │
│  UART RX    ●───────────────● PA9 (TX)    │
│             │               │             │
│  GND        ●───────────────● GND         │
└─────────────┘               └─────────────┘
```

### **Specific Pin Examples:**
| Microcontroller | TX Pin | RX Pin |
|-----------------|--------|--------|
| ATMEGA328P | PD1 (Pin 3) | PD0 (Pin 2) |
| PIC16F877A | RC6 (Pin 25) | RC7 (Pin 26) |
| ATMEGA16 | PD1 (Pin 15) | PD0 (Pin 14) |

## 📝 **Easy Test Values**

### **For 10-byte firmware (easy test):**
```
Size calculation: 10 = 0x000A
Low byte: 10 (0x0A)
High byte: 0 (0x00)
```

### **For 20KB firmware (your GPS clock):**
```
Size calculation: 20388 = 0x4F94
Low byte: 148 (0x94)  
High byte: 79 (0x4F)
```

## 🎯 **Testing Steps**

### **Step 1: Basic Setup**
1. **Place STM32F103C8T6** in Proteus
2. **Place any microcontroller** with UART
3. **Connect TX/RX crossed** (TX→RX, RX→TX)
4. **Connect GND**

### **Step 2: Load Code**
1. **STM32**: Your bootloader hex file
2. **Microcontroller**: Simple UART code (see below)

### **Step 3: Test Protocol**
1. **Start simulation**
2. **STM32 sends dots** ('.')
3. **Microcontroller sends 'o'**
4. **Follow size exchange protocol**

## 💻 **Simple C Code for Any Microcontroller**

### **Minimal UART Test Code:**
```c
// Universal UART test for STM32 bootloader
// Adapt UART functions for your specific microcontroller

void main() {
    uart_init(115200);  // Initialize UART
    
    // Wait for STM32 handshake
    while(1) {
        if(uart_available()) {
            char c = uart_read();
            if(c == '.') {
                uart_write('o');  // Start update
                break;
            }
        }
    }
    
    // Send size for 10-byte test
    while(uart_read() != 'y');  // Wait for size request
    uart_write(10);             // Size low byte
    
    while(uart_read() != 'x');  // Wait for size request  
    uart_write(0);              // Size high byte
    
    // Send 10 data bytes
    for(int i = 1; i <= 10; i++) {
        while(uart_read() != 'y');  // Wait for data request
        uart_write(i);              // Send data byte
        
        while(uart_read() != 'x');  // Wait for data request
        uart_write(i+10);           // Send data byte
    }
}
```

## 🔧 **Alternative: Virtual Terminal Only**

### **If you can't get microcontroller working:**

1. **Connect Virtual Terminal** directly to STM32 USART3
2. **Set terminal to HEX mode** (if available)
3. **Send hex values:**
   - `6F` (for 'o' start command)
   - `0A` (for 10 decimal - size low)
   - `00` (for 0 decimal - size high)
   - `01, 02, 03...` (for data bytes)

### **Terminal Settings:**
- **Baud Rate**: 115200
- **Data Format**: 8N1
- **Mode**: HEX input (if available)

## ✅ **Expected Results**

### **STM32 Terminal Output:**
```
Bootloader v1:0 Started!!!

 Press 'o' to start Firmware Update...
Firmware Update Started
Application Size = 10 bytes        ← Should show correct size
Erasing the Flash memory...
Received Block[0]
Gonna Jump to Application...
```

### **Success Indicators:**
- ✅ **Handshake works** (dots → 'o' → "Firmware Update Started")
- ✅ **Correct size shown** (10 bytes or 20388 bytes)
- ✅ **Data transfer completes** ("Received Block[0]")
- ✅ **No timeouts or errors**

## 🚫 **Common Issues**

### **"No handshake":**
- Check TX/RX connections (must be crossed)
- Verify baud rates match (115200)
- Ensure GND connected

### **"Wrong size displayed":**
- Microcontroller sending ASCII instead of binary
- Need to send raw byte values, not text

### **"Timeout errors":**
- STM32 expects responses within 5 seconds
- Check microcontroller UART timing

## 🎯 **Quick Success Test**

### **Easiest approach:**
1. **Use PIC16F877A** (most common in Proteus)
2. **Test with 10 bytes** (easier than 20KB)
3. **Use hardware UART** (more reliable than software)
4. **Start with handshake only** (just send 'o' when you see dots)

### **Minimal working test:**
```
STM32 sends: '.' (dots)
Your MCU sends: 'o' 
STM32 shows: "Firmware Update Started" ✅
```

**Once handshake works, the rest of the protocol follows the same pattern!**

---

## 🎉 **Success = Your Automation Works!**

When this basic test works in Proteus, it proves:
- ✅ Your STM32 bootloader protocol is correct
- ✅ UART communication is working  
- ✅ Your Python automation tool will work on real hardware
- ✅ The firmware update process is solid

**The same protocol your Python tool uses, just demonstrated step-by-step!** 🚀
