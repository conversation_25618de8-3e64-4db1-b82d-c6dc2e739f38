# STM32 Firmware Updater - Complete Setup Guide

## 🎯 Overview

This guide will help you set up and use the automated STM32 firmware updater tools. These tools eliminate the need for manual byte-by-byte input and provide a foolproof update process for non-technical users.

## 📦 What's Included

### For End Users (Non-Technical)
- **STM32_Updater.exe** - Simple GUI application with 3-step process
- **README_UPDATER.txt** - User-friendly instructions

### For Developers/Advanced Users
- **stm32_updater.exe** - Command-line version with verbose output
- **stm32_updater_python.py** - Cross-platform Python GUI version
- **Source code** - All C and Python source files for customization

### Build Tools
- **build.bat** - Automated build script for Windows
- **update_gps_clock.bat** - Quick update script for your GPS clock project

## 🚀 Quick Start for End Users

### Step 1: Get the Tools
1. Download or copy `STM32_Updater.exe` to your computer
2. No installation required - it's a standalone executable

### Step 2: Prepare Your Device
1. Connect STM32 device via USB-to-Serial adapter
2. Put device in bootloader mode:
   - Hold BOOT0 pin high (3.3V)
   - Press RESET button
   - Release BOOT0 pin

### Step 3: Update Firmware
1. Double-click `STM32_Updater.exe`
2. Follow the 3 steps in the GUI:
   - **Step 1**: Browse and select your `.bin` firmware file
   - **Step 2**: Click "Auto-Detect Device" 
   - **Step 3**: Click "Update Firmware"
3. Wait for completion (progress bar shows status)

## 🛠️ Developer Setup

### Prerequisites
- **Windows**: MinGW-w64 or TDM-GCC compiler
- **Python version**: Python 3.6+ with tkinter and pyserial
- **Cross-platform**: GCC compiler for command-line version

### Building the Tools

#### Windows (Recommended)
```batch
# Install MinGW-w64 from: https://www.mingw-w64.org/downloads/
# Or TDM-GCC from: https://jmeubank.github.io/tdm-gcc/

# Build all versions
build.bat

# This creates:
# - stm32_updater.exe (command-line)
# - STM32_Updater.exe (GUI)
# - dist/ folder with distribution files
```

#### Manual Compilation
```bash
# Command-line version
gcc stm32_firmware_updater.c RS232/rs232.c -IRS232 -Wall -Wextra -O2 -o stm32_updater.exe

# GUI version (Windows only)
gcc stm32_updater_gui.c RS232/rs232.c -IRS232 -lgdi32 -luser32 -lkernel32 -lcomctl32 -mwindows -O2 -o STM32_Updater.exe
```

#### Python Version Setup
```bash
# Install dependencies
pip install pyserial

# Run directly
python stm32_updater_python.py

# Or create executable with PyInstaller
pip install pyinstaller
pyinstaller --onefile --windowed stm32_updater_python.py
```

## 📋 Usage Examples

### GUI Version (Recommended for End Users)
```
1. Run STM32_Updater.exe
2. Drag & drop firmware file OR click Browse
3. Click "Auto-Detect Device"
4. Click "Update Firmware"
```

### Command-Line Version
```bash
# Basic usage
stm32_updater.exe firmware.bin

# With verbose output
stm32_updater.exe -v firmware.bin

# Update GPS clock specifically
stm32_updater.exe ../gps_eth_clock3/Debug/gps_eth_clock3.bin
```

### Python Version
```bash
# GUI version
python stm32_updater_python.py

# The Python version provides the same GUI as the Windows executable
# but works on Linux and macOS as well
```

### Quick GPS Clock Update
```batch
# Use the provided batch file
update_gps_clock.bat

# This automatically:
# 1. Finds your GPS clock firmware
# 2. Launches the appropriate updater
# 3. Provides step-by-step guidance
```

## 🔧 Troubleshooting

### Common Issues

#### "No STM32 bootloader detected"
**Causes:**
- Device not in bootloader mode
- Wrong COM port or driver issues
- Loose connections

**Solutions:**
1. Verify bootloader mode (see device preparation steps)
2. Check Device Manager for COM ports
3. Try manual port selection in GUI
4. Test connections with loopback

#### "Firmware file too large"
**Causes:**
- Firmware exceeds 48KB limit
- Wrong memory layout in linker script

**Solutions:**
1. Check your linker script memory configuration
2. Optimize firmware size (remove debug symbols, unused code)
3. Verify you're building for STM32F103C8T6

#### "Update failed during transfer"
**Causes:**
- Power supply instability
- Communication interference
- Hardware issues

**Solutions:**
1. Use stable power supply (not just USB power)
2. Check all connections are secure
3. Try different USB cable/adapter
4. Reduce baud rate if needed (modify source)

### Debug Mode
Enable verbose output for detailed troubleshooting:

```bash
# Command-line version
stm32_updater.exe -v firmware.bin

# This shows:
# - Detailed COM port scanning
# - Protocol handshake messages
# - Transfer progress with byte counts
# - Error details
```

## 📁 File Structure

```
PcTool/
├── STM32_Updater.exe              # Main GUI tool for end users
├── stm32_updater.exe              # Command-line version
├── stm32_updater_python.py        # Cross-platform Python GUI
├── README_UPDATER.txt             # User instructions
├── SETUP_GUIDE.md                 # This file
├── build.bat                      # Build script
├── update_gps_clock.bat           # Quick update for GPS project
├── stm32_firmware_updater.c       # Enhanced command-line source
├── stm32_updater_gui.c            # Windows GUI source
└── RS232/                         # Serial communication library
    ├── rs232.h
    └── rs232.c
```

## 🎯 Features Comparison

| Feature | GUI Version | Command-Line | Python GUI |
|---------|-------------|--------------|------------|
| User-friendly | ✅ Excellent | ❌ Technical | ✅ Excellent |
| Auto-detection | ✅ Yes | ✅ Yes | ✅ Yes |
| Progress tracking | ✅ Visual bar | ✅ Text | ✅ Visual bar |
| Drag & drop | ✅ Yes | ❌ No | ✅ Yes |
| Cross-platform | ❌ Windows only | ✅ Yes | ✅ Yes |
| Dependencies | ❌ None | ❌ None | ✅ Python + pyserial |
| File size | ~200KB | ~150KB | ~50KB + Python |

## 📊 Performance Specifications

- **Transfer Speed**: ~8KB/s (practical)
- **Maximum Firmware Size**: 48KB
- **Supported Baud Rate**: 115200 (configurable in source)
- **Timeout Values**: 5 seconds per operation
- **Retry Mechanism**: Built-in error recovery
- **Memory Usage**: <2MB RAM during operation

## 🔒 Security Considerations

### Current Implementation
- ✅ CRC checking at protocol level
- ✅ Size validation
- ✅ Timeout protection
- ❌ No encryption
- ❌ No digital signatures

### Recommended Enhancements
For production use, consider adding:
1. **Firmware signing** - Verify authenticity
2. **Encryption** - Protect firmware during transfer
3. **Version checking** - Prevent downgrades
4. **Secure bootloader** - Hardware-based security

## 📞 Support

### For End Users
1. Check the troubleshooting section
2. Verify hardware connections
3. Try the verbose mode for error details
4. Contact your firmware developer

### For Developers
1. Review source code comments
2. Check protocol implementation in bootloader
3. Verify memory layout compatibility
4. Test with different hardware configurations

## 📝 License

This tool is provided as-is for educational and development purposes. 
Modify and distribute according to your project requirements.

---

**Last Updated**: December 2024  
**Version**: 2.0  
**Compatibility**: STM32F103C8T6 with custom bootloader
