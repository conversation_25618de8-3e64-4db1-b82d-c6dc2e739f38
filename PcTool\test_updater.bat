@echo off
echo ========================================
echo STM32 Firmware Updater - Test Script
echo ========================================
echo.

echo Available options:
echo 1. Test CLI mode with GPS clock firmware
echo 2. Launch GUI mode
echo 3. Show available COM ports
echo 4. Exit
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto cli_test
if "%choice%"=="2" goto gui_test
if "%choice%"=="3" goto show_ports
if "%choice%"=="4" goto exit
goto invalid

:cli_test
echo.
echo Testing CLI mode with GPS clock firmware...
echo.
echo IMPORTANT: Before proceeding, ensure your STM32 device is:
echo 1. Connected via USB-to-Serial adapter
echo 2. In bootloader mode (BOOT0 pin high, then reset)
echo.
pause
echo.
python stm32_updater_python.py ..\gps_eth_clock3\Debug\gps_eth_clock3.bin
goto end

:gui_test
echo.
echo Launching GUI mode...
echo.
python stm32_updater_python.py
goto end

:show_ports
echo.
echo Available COM ports:
python -c "import serial.tools.list_ports; [print(f'  {p.device} - {p.description}') for p in serial.tools.list_ports.comports()]"
echo.
pause
goto end

:invalid
echo Invalid choice. Please enter 1, 2, 3, or 4.
pause
goto end

:exit
echo Exiting...
goto end

:end
echo.
echo Press any key to exit...
pause >nul
