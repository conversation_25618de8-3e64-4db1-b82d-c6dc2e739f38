#include "time_manager.h"
#include "disp_i2c.h"
#include "7seg_display.h"
#include "button_handler.h"
#include "main.h"
#include "stdint.h"

// Function declarations
void init_button_handler(void);

// Time management variables
static uint32_t last_rtc_read = 0;

// Global variables that need to be accessible from other files
volatile uint8_t time_setting_mode = 0;    // 0 = normal mode, 1 = setting mode
volatile uint8_t selected_field = 0;       // 0 = seconds, 1 = minutes, 2 = hours
volatile uint8_t is_pm = 0;                // AM/PM flag (0 = AM, 1 = PM)
volatile uint8_t manual_seconds = 0;
volatile uint8_t manual_minutes = 0;
volatile uint8_t manual_hours = 0;
volatile uint8_t button_operation_in_progress = 0;
volatile uint8_t display_update_needed = 0;
volatile uint8_t rtc_read_needed = 0;
volatile uint8_t rtc_write_needed = 0;

// External variables
extern uint8_t seconds;
extern uint8_t minutes;
extern uint8_t hours;
extern volatile uint8_t i2c_busy;
extern uint8_t test_mode;

/**
 * @brief Initialize time manager
 */
void init_time_manager(void)
{
    last_rtc_read = 0;
    rtc_read_needed = 1;
    display_update_needed = 1;
}

/**
 * @brief Read time from RTC
 */
void read_time_from_rtc(void)
{
    // Try to read time from RTC
    if (ds1307_read_time_safe()) {
        // Successfully read time from RTC
        rtc_read_needed = 0;
        
        // Force display update
        display_update_needed = 1;
    }
}

/**
 * @brief Write time to RTC
 */
void write_time_to_rtc(void)
{
    // Try to write time to RTC
    if (ds1307_write_time_safe(manual_hours, manual_minutes, manual_seconds)) {
        // Successfully wrote time to RTC
        rtc_write_needed = 0;
        
        // Force display update
        display_update_needed = 1;
    }
}

/**
 * @brief Update display with current time
 */
void update_display(void)
{
    // Update display with current time
    updateTimeDisplay(manual_hours, manual_minutes, manual_seconds);
    
    // Clear flag
    display_update_needed = 0;
}

/**
 * @brief Handle time management tasks
 * @param current_time Current system time in milliseconds
 */
void handle_time_management(uint32_t current_time)
{
    static uint32_t last_second_tick = 0;
    static uint8_t first_run = 1;
    
    // Skip time management if button operation is in progress
    if (button_operation_in_progress) {
        return;
    }
    
    // On first run, force RTC read
    if (first_run) {
        if (ds1307_read_time_safe()) {
            first_run = 0;
            last_second_tick = current_time;
        }
        return;
    }
    
    // Handle RTC write request
    if (rtc_write_needed) {
        // Try to write time to RTC
        if (ds1307_write_time_safe(manual_hours, manual_minutes, manual_seconds)) {
            rtc_write_needed = 0;
            display_update_needed = 1;
        }
    }
    
    // Handle second tick (only in normal mode, not in setting mode)
    if (!time_setting_mode && (current_time - last_second_tick >= 1000)) {
        // Update last second tick
        last_second_tick = current_time;
        
        // Read from RTC every second
        ds1307_read_time_safe();
        
        // Force display update
        display_update_needed = 1;
    }
    
    // Handle display update
    if (display_update_needed) {
        // Update display with current time
        updateTimeDisplay(manual_hours, manual_minutes, manual_seconds);
        display_update_needed = 0;
    }
}

/**
 * @brief Initialize the system at startup
 */
void system_init(void)
{
    // Initialize RTC
    ds1307_init();
    
    // Add delay to ensure RTC is stable
    HAL_Delay(50);
    
    // Read current time from RTC
    if (ds1307_read_time_safe()) {
        // Time was read successfully, manual time variables are already updated
        // in the ds1307_read_time_safe function
        
        // Update AM/PM flag
        is_pm = (manual_hours >= 12) ? 1 : 0;
        
        // Update display
        updateTimeDisplay(manual_hours, manual_minutes, manual_seconds);
    } else {
        // If RTC read fails, set default time to current time (14:45)
        manual_hours = 14;
        manual_minutes = 45;
        manual_seconds = 0;
        is_pm = 1;           // PM
        
        // Write default time to RTC
        ds1307_write_time_safe(manual_hours, manual_minutes, manual_seconds);
        
        // Update display
        updateTimeDisplay(manual_hours, manual_minutes, manual_seconds);
    }
    
    // Initialize button handler
    init_button_handler();
    
    // Initialize time manager
    init_time_manager();
    
    // Force display update
    display_update_needed = 1;
    
    // Force first RTC read
    rtc_read_needed = 1;
}
