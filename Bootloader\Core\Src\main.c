/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2022 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <stdlib.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
#define MAX_BLOCK_SIZE          ( 1024 )                  //1KB
#define ETX_APP_START_ADDRESS   0x08004400
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
#define MAJOR 1
#define MINOR 0
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
UART_HandleTypeDef huart1;
UART_HandleTypeDef huart3;

/* USER CODE BEGIN PV */
uint8_t BL_Version[2] = { MAJOR, MINOR };
uint32_t application_size = 0;
uint32_t application_write_idx = 0;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_USART1_UART_Init(void);
static void MX_USART3_UART_Init(void);
/* USER CODE BEGIN PFP */
static void goto_application( void );
static void Firmware_Update( void );
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART1_UART_Init();
  MX_USART3_UART_Init();
  /* USER CODE BEGIN 2 */
  printf("Bootloader v%d:%d Started!!!\n", BL_Version[0], BL_Version[1]);
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  Firmware_Update();

  // Jump to application
  goto_application();
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
  * @brief USART1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_USART1_UART_Init(void)
{

  /* USER CODE BEGIN USART1_Init 0 */

  /* USER CODE END USART1_Init 0 */

  /* USER CODE BEGIN USART1_Init 1 */

  /* USER CODE END USART1_Init 1 */
  huart1.Instance = USART1;
  huart1.Init.BaudRate = 115200;
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
  huart1.Init.StopBits = UART_STOPBITS_1;
  huart1.Init.Parity = UART_PARITY_NONE;
  huart1.Init.Mode = UART_MODE_TX_RX;
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART1_Init 2 */

  /* USER CODE END USART1_Init 2 */

}

/**
  * @brief USART3 Initialization Function
  * @param None
  * @retval None
  */
static void MX_USART3_UART_Init(void)
{

  /* USER CODE BEGIN USART3_Init 0 */

  /* USER CODE END USART3_Init 0 */

  /* USER CODE BEGIN USART3_Init 1 */

  /* USER CODE END USART3_Init 1 */
  huart3.Instance = USART3;
  huart3.Init.BaudRate = 115200;
  huart3.Init.WordLength = UART_WORDLENGTH_8B;
  huart3.Init.StopBits = UART_STOPBITS_1;
  huart3.Init.Parity = UART_PARITY_NONE;
  huart3.Init.Mode = UART_MODE_TX_RX;
  huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart3.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart3) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART3_Init 2 */

  /* USER CODE END USART3_Init 2 */

}

/**
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};
/* USER CODE BEGIN MX_GPIO_Init_1 */
/* USER CODE END MX_GPIO_Init_1 */

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET);

  /*Configure GPIO pin : PB13 */
  GPIO_InitStruct.Pin = GPIO_PIN_13;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

/* USER CODE BEGIN MX_GPIO_Init_2 */
/* USER CODE END MX_GPIO_Init_2 */
}

/* USER CODE BEGIN 4 */

#ifdef __GNUC__
  /* With GCC, small printf (option LD Linker->Libraries->Small printf
     set to 'Yes') calls __io_putchar() */
int __io_putchar(int ch)
#else
int fputc(int ch, FILE *f)
#endif /* __GNUC__ */
{
  /* Place your implementation of fputc here */
  /* e.g. write a character to the UART3 and Loop until the end of transmission */
  HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);

  return ch;
}


static int UART_Write_Loop( void )
{
  printf("\n Press 'o' to start Firmware Update...\r\n");
  char tx = '.';
  char rx = '0';
  HAL_StatusTypeDef ex;
  int ret = 0;
  int count = 0;

  while(1)
  {
    //Toggle GPIO
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_13);

    HAL_UART_Transmit(&huart3, (uint8_t *)&tx, 1, HAL_MAX_DELAY);

    ex = HAL_UART_Receive(&huart3, (uint8_t *)&rx, 1, 20);  // Changed from 10ms to 20ms to give Arduino more time to respond

    if( ( ex == HAL_OK ) && ( rx == 'o' ) )
    {
      //received data
      printf("Firmware Update Started\r\n");
      ret = 1;
      break;
    }

    if( count == 250 )  // Changed from 100 to 250 to make it 5 seconds (250 * 20ms = 5000ms = 5 seconds)
    {
      //received nothing
      printf("No Data Received for Firmware Update\r\n");
      break;
    }
    count++;
    HAL_Delay(20);              //20ms delay
  }

  return ret;
}

/**
  * @brief Write data to the Application's actual flash location.
  * @param data data to be written
  * @param data_len data length
  * @is_first_block true - if this is first block, false - not first block
  * @retval HAL_StatusTypeDef
  */
static HAL_StatusTypeDef write_data_to_flash_app( uint8_t *data,
                                        uint16_t data_len, bool is_first_block )
{
  HAL_StatusTypeDef ret;

  do
  {
    ret = HAL_FLASH_Unlock();
    if( ret != HAL_OK )
    {
      break;
    }

    //No need to erase every time. Erase only the first time.
    if( is_first_block )
    {
      printf("Erasing the Flash memory...\r\n");
      //Erase the Flash
      FLASH_EraseInitTypeDef EraseInitStruct;
      uint32_t SectorError;

      EraseInitStruct.TypeErase     = FLASH_TYPEERASE_PAGES;
      EraseInitStruct.PageAddress   = ETX_APP_START_ADDRESS;
      EraseInitStruct.NbPages       = 47;                     //47 Pages

      ret = HAL_FLASHEx_Erase( &EraseInitStruct, &SectorError );
      if( ret != HAL_OK )
      {
        break;
      }
      application_write_idx = 0;
    }

    for(int i = 0; i < data_len/2; i++)
    {
      uint16_t halfword_data = data[i * 2] | (data[i * 2 + 1] << 8);
      ret = HAL_FLASH_Program( FLASH_TYPEPROGRAM_HALFWORD,
                               (ETX_APP_START_ADDRESS + application_write_idx ),
                               halfword_data
                             );
      if( ret == HAL_OK )
      {
        //update the data count
        application_write_idx += 2;
      }
      else
      {
        printf("Flash Write Error...HALT!!!\r\n");
        break;
      }
    }

    if( ret != HAL_OK )
    {
      break;
    }

    ret = HAL_FLASH_Lock();
    if( ret != HAL_OK )
    {
      break;
    }
  }while( false );

  return ret;
}


// Helper function to receive ASCII decimal number
static uint32_t receive_ascii_number(void)
{
  char buffer[16] = {0};
  uint8_t index = 0;
  uint8_t received_char;
  HAL_StatusTypeDef status;

  printf("Enter number: ");

  while(index < 15)
  {
    status = HAL_UART_Receive(&huart3, &received_char, 1, 10000); // 10 second timeout
    if(status != HAL_OK)
    {
      printf("Timeout waiting for input\r\n");
      return 0;
    }

    // Echo the character
    HAL_UART_Transmit(&huart3, &received_char, 1, HAL_MAX_DELAY);

    if(received_char == '\r' || received_char == '\n')
    {
      printf("\r\n");
      break;
    }
    else if(received_char >= '0' && received_char <= '9')
    {
      buffer[index++] = received_char;
    }
    else if(received_char == '\b' && index > 0) // Backspace
    {
      index--;
      printf(" \b"); // Erase character on terminal
    }
  }

  buffer[index] = '\0';
  return atoi(buffer);
}

// Auto-detect firmware size from data stream
static uint32_t auto_detect_firmware_size(void)
{
  printf("Auto-detecting firmware size...\r\n");
  printf("Send firmware data continuously (end with 'END' or timeout after 30 seconds)\r\n");

  uint32_t byte_count = 0;
  uint8_t received_byte;
  HAL_StatusTypeDef status;
  uint32_t start_time = HAL_GetTick();
  uint8_t end_sequence[3] = {0};
  uint8_t end_index = 0;

  while(1)
  {
    status = HAL_UART_Receive(&huart3, &received_byte, 1, 100); // 100ms timeout per byte

    if(status == HAL_OK)
    {
      byte_count++;

      // Check for "END" sequence
      if(received_byte == 'E' && end_index == 0) end_index = 1;
      else if(received_byte == 'N' && end_index == 1) end_index = 2;
      else if(received_byte == 'D' && end_index == 2)
      {
        byte_count -= 3; // Don't count the "END" sequence
        printf("End sequence detected. Firmware size: %lu bytes\r\n", byte_count);
        return byte_count;
      }
      else end_index = 0;

      // Progress indicator every 1KB
      if(byte_count % 1024 == 0)
      {
        printf("Received %lu bytes...\r\n", byte_count);
      }
    }
    else
    {
      // Check for overall timeout (30 seconds)
      if(HAL_GetTick() - start_time > 30000)
      {
        printf("Auto-detection timeout. Detected size: %lu bytes\r\n", byte_count);
        return byte_count;
      }
    }
  }
}

/**
  * @brief Enhanced Firmware Update with ASCII input and auto-detection
  * @retval None
  */
static void Firmware_Update(void)
{
  HAL_StatusTypeDef ex = HAL_OK;
  uint32_t current_app_size = 0;
  uint32_t i = 0;
  uint8_t block[MAX_BLOCK_SIZE] = { 0 };
  uint8_t backup_block[MAX_BLOCK_SIZE] = { 0 }; // For rollback
  uint32_t blocks_written = 0;
  bool update_successful = false;

  do
  {
    if( UART_Write_Loop() != 0 )
    {
      printf("=== Enhanced Firmware Update Started ===\r\n");

      // Option 1: Manual size input (ASCII)
      printf("Choose firmware size input method:\r\n");
      printf("1. Enter size manually (ASCII decimal)\r\n");
      printf("2. Auto-detect from data stream\r\n");
      printf("3. Use default size for GPS clock (20388 bytes)\r\n");

      uint32_t choice = receive_ascii_number();

      switch(choice)
      {
        case 1:
          printf("Enter firmware size in bytes: ");
          application_size = receive_ascii_number();
          break;

        case 2:
          application_size = auto_detect_firmware_size();
          break;

        case 3:
          application_size = 20388; // Your GPS clock size
          printf("Using default GPS clock size: %lu bytes\r\n", application_size);
          break;

        default:
          printf("Invalid choice. Using auto-detection...\r\n");
          application_size = auto_detect_firmware_size();
          break;
      }

      if(application_size == 0 || application_size > (47 * 1024))
      {
        printf("Invalid firmware size: %lu bytes (max 48KB)\r\n", application_size);
        ex = HAL_ERROR;
        break;
      }

      printf("Confirmed firmware size: %lu bytes\r\n", application_size);
      printf("Starting automatic data transfer...\r\n");

      // Automatic data reception loop
      printf("Ready to receive %lu bytes. Send data now...\r\n", application_size);

      while(current_app_size < application_size)
      {
        // Receive data byte automatically (no handshake needed)
        uint8_t data_byte;
        HAL_StatusTypeDef rx_status = HAL_UART_Receive(&huart3, &data_byte, 1, 5000);

        if(rx_status != HAL_OK)
        {
          printf("Data reception timeout at byte %lu. Initiating rollback...\r\n", current_app_size);
          ex = HAL_ERROR;
          break;
        }

        // Store data in block
        block[i++] = data_byte;
        current_app_size++;

        // Progress indicator every 1KB
        if(current_app_size % 1024 == 0)
        {
          // Calculate percentage using integer arithmetic (avoid float)
          uint32_t progress_percent = (current_app_size * 100) / application_size;
          printf("Progress: %lu/%lu bytes (%lu%%)\r\n",
                 current_app_size, application_size, progress_percent);
        }

        // Write block when full or at end of firmware
        if(i == MAX_BLOCK_SIZE || current_app_size >= application_size)
        {
          printf("Writing block %lu to flash...\r\n", blocks_written);

          // Backup current block for rollback
          memcpy(backup_block, block, MAX_BLOCK_SIZE);

          // Write to flash
          ex = write_data_to_flash_app(block, i, (blocks_written == 0));

          if(ex != HAL_OK)
          {
            printf("Flash write error at block %lu. Initiating rollback...\r\n", blocks_written);
            break;
          }

          printf("Block %lu written successfully\r\n", blocks_written);
          blocks_written++;

          // Clear block for next data
          memset(block, 0, MAX_BLOCK_SIZE);
          i = 0;
        }
      }

      // Check if update was successful
      if(ex == HAL_OK && current_app_size >= application_size)
      {
        update_successful = true;
        printf("=== Firmware Update Completed Successfully ===\r\n");
        printf("Total bytes written: %lu\r\n", current_app_size);
        printf("Total blocks written: %lu\r\n", blocks_written);
      }
    }
  }
  while(false);

  // Handle errors and rollback
  if(!update_successful)
  {
    printf("=== Firmware Update Failed ===\r\n");
    printf("Initiating rollback procedure...\r\n");

    // Erase the corrupted application area
    HAL_StatusTypeDef erase_status = HAL_FLASH_Unlock();
    if(erase_status == HAL_OK)
    {
      FLASH_EraseInitTypeDef EraseInitStruct;
      uint32_t SectorError;

      EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
      EraseInitStruct.PageAddress = ETX_APP_START_ADDRESS;
      EraseInitStruct.NbPages = 47;

      erase_status = HAL_FLASHEx_Erase(&EraseInitStruct, &SectorError);
      HAL_FLASH_Lock();

      if(erase_status == HAL_OK)
      {
        printf("Application area erased. System ready for new firmware.\r\n");
      }
      else
      {
        printf("Rollback erase failed. Manual intervention required.\r\n");
      }
    }

    printf("=== Rollback Complete ===\r\n");
    printf("Please retry firmware update or check your firmware file.\r\n");

    // Don't jump to application - stay in bootloader
    while(1)
    {
      HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_13); // Blink LED to indicate error
      HAL_Delay(500);
    }
  }
}

static void goto_application( void )
{
	printf("Gonna Jump to Application...\n");
	void (*app_reset_handler)(void) = (void*)(*((volatile uint32_t*)(ETX_APP_START_ADDRESS + 4U)));

	if( app_reset_handler == (void*)0xFFFFFFFF )
	{
	  printf("Invalid Application... HALT!!!\r\n");
	  while(1);
	}

	__set_MSP(*(volatile uint32_t*) ETX_APP_START_ADDRESS);

	// Turn OFF the Led to tell the user that Bootloader is not running
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET );

	app_reset_handler();    //call the app reset handler
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
