/*
 * Simple Arduino STM32 Bootloader Test
 * ===================================
 * 
 * This sends a small firmware (10 bytes) to test the protocol easily.
 * You can modify FIRMWARE_SIZE to test different sizes.
 * 
 * Connections:
 * Arduino Pin 2 (RX) → STM32 PA9 (TX)
 * Arduino Pin 3 (TX) → STM32 PA10 (RX)
 * 
 * For 20KB test, change FIRMWARE_SIZE to 20388
 */

#include <SoftwareSerial.h>

SoftwareSerial stm32(2, 3); // RX=2, TX=3

// *** CHANGE THIS VALUE TO TEST DIFFERENT SIZES ***
const uint16_t FIRMWARE_SIZE = 10;  // Start with 10 bytes for easy testing
// const uint16_t FIRMWARE_SIZE = 20388;  // Uncomment for 20KB test

const uint8_t SIZE_LOW = FIRMWARE_SIZE & 0xFF;
const uint8_t SIZE_HIGH = (FIRMWARE_SIZE >> 8) & 0xFF;

enum State {
  WAITING_HANDSHAKE,
  SENDING_SIZE_LOW,
  SENDING_SIZE_HIGH,
  SENDING_DATA,
  COMPLETE
};

State currentState = WAITING_HANDSHAKE;
uint16_t bytesSent = 0;

void setup() {
  Serial.begin(9600);
  stm32.begin(115200);
  
  Serial.println("=== Simple STM32 Bootloader Test ===");
  Serial.print("Testing with firmware size: ");
  Serial.print(FIRMWARE_SIZE);
  Serial.println(" bytes");
  Serial.print("Size Low Byte: ");
  Serial.print(SIZE_LOW);
  Serial.print(" (0x");
  Serial.print(SIZE_LOW, HEX);
  Serial.println(")");
  Serial.print("Size High Byte: ");
  Serial.print(SIZE_HIGH);
  Serial.print(" (0x");
  Serial.print(SIZE_HIGH, HEX);
  Serial.println(")");
  Serial.println();
  Serial.println("Waiting for STM32 handshake...");
}

void loop() {
  if (stm32.available()) {
    char received = stm32.read();
    
    Serial.print("Received: '");
    Serial.print(received);
    Serial.print("' (0x");
    Serial.print((uint8_t)received, HEX);
    Serial.println(")");
    
    handleProtocol(received);
  }
}

void handleProtocol(char received) {
  switch (currentState) {
    
    case WAITING_HANDSHAKE:
      if (received == '.') {
        Serial.println("→ Handshake! Sending 'o'...");
        stm32.write('o');
        currentState = SENDING_SIZE_LOW;
      }
      break;
      
    case SENDING_SIZE_LOW:
      if (received == 'y') {
        Serial.print("→ Sending size low byte: ");
        Serial.print(SIZE_LOW);
        Serial.print(" (0x");
        Serial.print(SIZE_LOW, HEX);
        Serial.println(")");
        stm32.write(SIZE_LOW);  // Send RAW binary byte
        currentState = SENDING_SIZE_HIGH;
      }
      break;
      
    case SENDING_SIZE_HIGH:
      if (received == 'x') {
        Serial.print("→ Sending size high byte: ");
        Serial.print(SIZE_HIGH);
        Serial.print(" (0x");
        Serial.print(SIZE_HIGH, HEX);
        Serial.println(")");
        stm32.write(SIZE_HIGH);  // Send RAW binary byte
        Serial.print("Total size: ");
        Serial.print(FIRMWARE_SIZE);
        Serial.println(" bytes");
        Serial.println("→ Starting data transfer...");
        currentState = SENDING_DATA;
        bytesSent = 0;
      }
      break;
      
    case SENDING_DATA:
      if (bytesSent >= FIRMWARE_SIZE) {
        Serial.println("→ All data sent! Test complete!");
        currentState = COMPLETE;
        return;
      }
      
      if (received == 'y') {
        uint8_t dataByte = (bytesSent % 256);  // Simple test pattern
        stm32.write(dataByte);
        Serial.print("Sent data byte ");
        Serial.print(bytesSent + 1);
        Serial.print("/");
        Serial.print(FIRMWARE_SIZE);
        Serial.print(": ");
        Serial.print(dataByte);
        Serial.print(" (0x");
        Serial.print(dataByte, HEX);
        Serial.println(")");
        bytesSent++;
        
      } else if (received == 'x') {
        uint8_t dataByte = ((bytesSent % 256) ^ 0xAA);  // Different pattern
        stm32.write(dataByte);
        Serial.print("Sent data byte ");
        Serial.print(bytesSent + 1);
        Serial.print("/");
        Serial.print(FIRMWARE_SIZE);
        Serial.print(": ");
        Serial.print(dataByte);
        Serial.print(" (0x");
        Serial.print(dataByte, HEX);
        Serial.println(")");
        bytesSent++;
      }
      break;
      
    case COMPLETE:
      Serial.println("Test completed successfully!");
      Serial.println("STM32 should show correct firmware size and complete transfer.");
      break;
  }
}

/*
 * TESTING DIFFERENT SIZES:
 * 
 * 10 bytes:    FIRMWARE_SIZE = 10     → Low=10, High=0
 * 256 bytes:   FIRMWARE_SIZE = 256    → Low=0,  High=1  
 * 1024 bytes:  FIRMWARE_SIZE = 1024   → Low=0,  High=4
 * 20388 bytes: FIRMWARE_SIZE = 20388  → Low=148, High=79
 * 
 * Expected STM32 output for 10 bytes:
 * "Application Size = 10 bytes"
 * "Received Block[0]"
 * "Gonna Jump to Application..."
 */
