#ifndef DISP_I2C_H
#define DISP_I2C_H
#include <stm32f1xx_hal.h>
#include "main.h"
#include "stdint.h"

// I2C Addresses
#define DS1307_ADDRESS_WRITE 0xD0
#define DS1307_ADDRESS_READ 0xD1

// DS1307 Register Addresses
#define DS1307_SECONDS_REG 0x00
#define DS1307_MINUTES_REG 0x01
#define DS1307_HOURS_REG 0x02
#define DS1307_DAY_REG 0x03
#define DS1307_DATE_REG 0x04
#define DS1307_MONTH_REG 0x05
#define DS1307_YEAR_REG 0x06
#define DS1307_CONTROL_REG 0x07

// GPIO Pin Definitions
#define SDA_GPIO_Port GPIOB
#define SDA_Pin GPIO_PIN_9
#define SCL_GPIO_Port GPIOB
#define SCL_Pin GPIO_PIN_8

// Function Prototypes
void DISP_I2C_Init(void);
void DISP_I2C_SendCommand(uint8_t cmd);
void DISP_I2C_SendData(uint8_t data);
void DISP_I2C_WriteBuffer(uint8_t *buf, uint16_t len);
void DISP_I2C_Clear(void);

void i2c_init(void);
void i2c_start(void);
void i2c_stop(void);
void i2c_write(unsigned char dat);
unsigned char i2c_read(void);
void i2c_send_ack(void);
void i2c_send_nack(void);
void i2c_delay(void);

void ds1307_init(void);
void ds1307_read_time(void);
void ds1307_write_time(uint8_t h, uint8_t m, uint8_t s);

// Safe versions of RTC functions
uint8_t ds1307_read_time_safe(void);
uint8_t ds1307_write_time_safe(uint8_t h, uint8_t m, uint8_t s);
uint8_t ds1307_read_date_safe(uint8_t *day, uint8_t *date, uint8_t *month, uint8_t *year);

unsigned char bcd_to_decimal(unsigned char bcd);
unsigned char decimal_to_packedBCD(unsigned char decimal);

void save_to_sram(unsigned char address, unsigned char data);
unsigned char read_from_sram(unsigned char address);

// External variables
extern volatile uint8_t i2c_busy;
extern uint8_t seconds;
extern uint8_t minutes;
extern uint8_t hours;
extern volatile uint8_t manual_seconds;
extern volatile uint8_t manual_minutes;
extern volatile uint8_t manual_hours;
extern volatile uint8_t day;
extern volatile uint8_t date;
extern volatile uint8_t month;
extern volatile uint8_t year;
extern volatile uint8_t show_date_mode;

#endif /* DISP_I2C_H */
