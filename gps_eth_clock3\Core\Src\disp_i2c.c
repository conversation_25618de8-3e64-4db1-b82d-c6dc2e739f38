#include "disp_i2c.h"
#include "main.h"
#include "stdint.h"

extern I2C_HandleTypeDef hi2c1; // Assuming I2C1 is used, modify as needed

// Time variables
uint8_t seconds = 0;
uint8_t minutes = 0;
uint8_t hours = 0;

// External time variables
extern volatile uint8_t manual_seconds;
extern volatile uint8_t manual_minutes;
extern volatile uint8_t manual_hours;

// Date variables
volatile uint8_t day = 0;
volatile uint8_t date = 0;
volatile uint8_t month = 0;
volatile uint8_t year = 0;
volatile uint8_t show_date_mode = 0;

// Flags to track I2C busy state
volatile uint8_t i2c_busy = 0;

#define DISP_I2C_ADDRESS 0x3C // Adjust based on your display's I2C address

/**
 * @brief  Initializes the I2C display.
 * @retval None
 */
void DISP_I2C_Init(void)
{
    // Initialize the display if needed (e.g., send initialization commands)
}

/**
 * @brief  Sends a command to the I2C display.
 * @param  cmd: Command byte to send
 * @retval None
 */
void DISP_I2C_SendCommand(uint8_t cmd)
{
    uint8_t data[2] = {0x00, cmd}; // 0x00 indicates a command
    HAL_I2C_Master_Transmit(&hi2c1, DISP_I2C_ADDRESS << 1, data, 2, HAL_MAX_DELAY);
}

/**
 * @brief  Sends data to the I2C display.
 * @param  data: Data byte to send
 * @retval None
 */
void DISP_I2C_SendData(uint8_t data)
{
    uint8_t buf[2] = {0x40, data}; // 0x40 indicates data
    HAL_I2C_Master_Transmit(&hi2c1, DISP_I2C_ADDRESS << 1, buf, 2, HAL_MAX_DELAY);
}

/**
 * @brief  Writes a buffer of data to the display.
 * @param  buf: Pointer to data buffer
 * @param  len: Length of data
 * @retval None
 */
void DISP_I2C_WriteBuffer(uint8_t *buf, uint16_t len)
{
    HAL_I2C_Master_Transmit(&hi2c1, DISP_I2C_ADDRESS << 1, buf, len, HAL_MAX_DELAY);
}

/**
 * @brief  Clears the display.
 * @retval None
 */
void DISP_I2C_Clear(void)
{
    // Assuming a clear screen command exists; if not, send blank data to the display
    for (uint16_t i = 0; i < 128 * 64 / 8; i++)
    {
        DISP_I2C_SendData(0x00);
    }
}

void i2c_init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    __HAL_RCC_GPIOB_CLK_ENABLE();

    // Configure I2C pins with proper timing characteristics
    GPIO_InitStruct.Pin = GPIO_PIN_8 | GPIO_PIN_9;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;  // Changed from HIGH to LOW for better stability
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // Set initial pin states high
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_8 | GPIO_PIN_9, GPIO_PIN_SET);
    
    // Initial delay - increased for better startup stability
    HAL_Delay(100);
    
    // Reset I2C bus by toggling SCL several times
    for(uint8_t i = 0; i < 10; i++) {
        HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_RESET);
        i2c_delay();
        HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_SET);
        i2c_delay();
    }
    
    // Send stop condition to reset any device that might be in the middle of a transaction
    i2c_stop();
}

unsigned char bcd_to_decimal(unsigned char bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

unsigned char decimal_to_packedBCD(unsigned char decimal)
{
    return (((decimal / 10) << 4) | (decimal % 10));
}

void i2c_start(void)
{
    // Set busy flag
    i2c_busy = 1;
    
    // Ensure SDA and SCL are high before starting
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_SET);
    i2c_delay();
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_SET);
    i2c_delay();
    
    // START condition: SDA goes low while SCL is high
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_RESET);
    i2c_delay();
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_RESET);
    i2c_delay();
}

void i2c_stop(void)
{
    // STOP condition: SDA goes high while SCL is high
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_RESET);
    i2c_delay();
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_SET);
    i2c_delay();
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_SET);
    i2c_delay();
    
    // Clear busy flag
    i2c_busy = 0;
    
    // Add extra delay after stop for stability
    i2c_delay();
    i2c_delay();
}

void i2c_write(unsigned char dat)
{
    for (uint8_t i = 0; i < 8; i++)
    {
        // Set SDA before SCL goes high
        HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, (dat & 0x80) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        dat <<= 1;
        i2c_delay();
        
        // Clock pulse
        HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_SET);
        i2c_delay();
        HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_RESET);
        i2c_delay();
    }
    
    // Release SDA for ACK
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_SET);
    i2c_delay();
    
    // Clock pulse for ACK
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_SET);
    i2c_delay();
    
    // Check for ACK (SDA should be pulled low by slave)
    uint8_t ack = HAL_GPIO_ReadPin(SDA_GPIO_Port, SDA_Pin);
    
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_RESET);
    i2c_delay();
    
    // If no ACK, send stop condition to reset the bus
    if (ack != GPIO_PIN_RESET) {
        i2c_stop();
        i2c_start();
    }
}

unsigned char i2c_read(void)
{
    uint8_t dat = 0;
    
    // Release SDA for slave to control
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_SET);
    
    for (uint8_t i = 0; i < 8; i++)
    {
        dat <<= 1;
        i2c_delay();
        
        // Clock pulse
        HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_SET);
        i2c_delay();
        
        // Read bit while SCL is high
        if (HAL_GPIO_ReadPin(SDA_GPIO_Port, SDA_Pin) == GPIO_PIN_SET)
        {
            dat |= 1;
        }
        
        HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_RESET);
        i2c_delay();
    }
    
    return dat;
}

void i2c_send_ack(void)
{
    // Pull SDA low for ACK
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_RESET);
    i2c_delay();
    
    // Clock pulse
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_SET);
    i2c_delay();
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_RESET);
    i2c_delay();
    
    // Release SDA
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_SET);
    i2c_delay();
}

void i2c_send_nack(void)
{
    // Keep SDA high for NACK
    HAL_GPIO_WritePin(SDA_GPIO_Port, SDA_Pin, GPIO_PIN_SET);
    i2c_delay();
    
    // Clock pulse
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_SET);
    i2c_delay();
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin, GPIO_PIN_RESET);
    i2c_delay();
}

void i2c_delay(void)
{
    // Adjusted delay for proper I2C timing - increased for better stability
    for(volatile uint16_t i = 0; i < 100; i++)
    {
        __NOP();  // No operation instruction
    }
}

/**
 * @brief Initialize the DS1307 RTC
 */
void ds1307_init(void)
{
    // Reset I2C bus first to ensure clean communication
    i2c_stop();
    HAL_Delay(20);
    
    // Set busy flag
    i2c_busy = 1;
    
    // Ensure control register is properly set
    i2c_start();
    i2c_write(DS1307_ADDRESS_WRITE);
    i2c_write(DS1307_CONTROL_REG);
    i2c_write(0x00); // Disable square wave output, enable oscillator
    i2c_stop();
    HAL_Delay(10);
    
    // Read current seconds register to check CH bit
    i2c_start();
    i2c_write(DS1307_ADDRESS_WRITE);
    i2c_write(DS1307_SECONDS_REG);
    i2c_start();
    i2c_write(DS1307_ADDRESS_READ);
    uint8_t current_seconds = i2c_read();
    i2c_send_nack();
    i2c_stop();
    HAL_Delay(10);
    
    // If CH bit is set (bit 7 = 1), the clock is halted
    // We need to clear this bit while preserving the seconds value
    if (current_seconds & 0x80) {
        // Clear CH bit while preserving seconds value
        current_seconds &= 0x7F;
        
        // Write back to seconds register to start the clock
        i2c_start();
        i2c_write(DS1307_ADDRESS_WRITE);
        i2c_write(DS1307_SECONDS_REG);
        i2c_write(current_seconds);
        i2c_stop();
        HAL_Delay(10);
    }
    
    // Set current time if RTC is not running or has invalid data
    // Read hours register to check if it's valid
    i2c_start();
    i2c_write(DS1307_ADDRESS_WRITE);
    i2c_write(DS1307_HOURS_REG);
    i2c_start();
    i2c_write(DS1307_ADDRESS_READ);
    uint8_t current_hours = i2c_read();
    i2c_send_nack();
    i2c_stop();
    HAL_Delay(10);
    
    // If hours are invalid (>23 in 24h mode) or RTC was just started, set default time
    if ((current_hours & 0x3F) > 0x23 || (current_seconds & 0x80)) {
        // Set default time to current time (15:09:00)
        i2c_start();
        i2c_write(DS1307_ADDRESS_WRITE);
        i2c_write(DS1307_SECONDS_REG);
        i2c_write(0x00); // 00 seconds, CH bit cleared
        i2c_write(0x09); // 09 minutes
        i2c_write(0x15); // 15 hours (24h mode)
        i2c_write(0x03); // Tuesday (day of week, 1-7)
        i2c_write(0x29); // 29th day
        i2c_write(0x04); // April
        i2c_write(0x25); // 2025
        i2c_stop();
        HAL_Delay(10);
    }
    
    // Clear busy flag
    i2c_busy = 0;
    
    // Add delay after initialization
    HAL_Delay(50);
}

/**
 * @brief Read time from RTC with safety checks to prevent I2C conflicts
 * @return 1 if successful, 0 if failed due to I2C busy
 */
uint8_t ds1307_read_time_safe(void)
{
    // If I2C is busy, return failure
    if (i2c_busy) {
        return 0;
    }
    
    // Set busy flag
    i2c_busy = 1;
    
    // Reset I2C bus first to ensure clean communication
    i2c_stop();
    HAL_Delay(5);
    
    unsigned char tempS, tempM, tempH;
    uint8_t success = 1;
    
    // Read time from RTC
    i2c_start();
    i2c_write(DS1307_ADDRESS_WRITE);
    i2c_write(DS1307_SECONDS_REG);
    i2c_start();
    i2c_write(DS1307_ADDRESS_READ);
    tempS = i2c_read();
    i2c_send_ack();
    tempM = i2c_read();
    i2c_send_ack();
    tempH = i2c_read();
    i2c_send_nack();
    i2c_stop();
    
    // Check if CH bit is set (clock halted)
    if (tempS & 0x80) {
        // Clock is halted, clear CH bit and restart
        tempS &= 0x7F;
        
        // Write back to seconds register to start the clock
        i2c_start();
        i2c_write(DS1307_ADDRESS_WRITE);
        i2c_write(DS1307_SECONDS_REG);
        i2c_write(tempS);
        i2c_stop();
    }
    
    // Convert BCD to decimal
    uint8_t sec = bcd_to_decimal(tempS & 0x7F); // Mask out CH bit
    uint8_t min = bcd_to_decimal(tempM & 0x7F); // Mask out unused bits
    uint8_t hr = bcd_to_decimal(tempH & 0x3F);  // Mask out 12/24 hour bit
    
    // Verify values are within valid ranges
    if (sec >= 60 || min >= 60 || hr >= 24) {
        success = 0; // Invalid values detected
    } else {
        // Update global time variables
        seconds = sec;
        minutes = min;
        hours = hr;
        
        // Update manual time variables directly
        extern volatile uint8_t manual_seconds;
        extern volatile uint8_t manual_minutes;
        extern volatile uint8_t manual_hours;
        extern volatile uint8_t is_pm;
        extern volatile uint8_t display_update_needed;
        
        manual_seconds = sec;
        manual_minutes = min;
        manual_hours = hr;
        
        // Update AM/PM flag
        is_pm = (manual_hours >= 12) ? 1 : 0;
        
        // Force display update
        display_update_needed = 1;
    }
    
    // Clear busy flag
    i2c_busy = 0;
    return success;
}

/**
 * @brief Write time to RTC with safety checks to prevent I2C conflicts
 * @param h Hours value to write
 * @param m Minutes value to write
 * @param s Seconds value to write
 * @return 1 if successful, 0 if failed due to I2C busy
 */
uint8_t ds1307_write_time_safe(uint8_t h, uint8_t m, uint8_t s)
{
    if (i2c_busy) {
        return 0; // I2C is busy, can't write time now
    }
    
    // Validate input values
    if (s >= 60 || m >= 60 || h >= 24) {
        return 0; // Invalid values
    }
    
    i2c_busy = 1;
    
    // Reset I2C bus first to ensure clean communication
    i2c_stop();
    HAL_Delay(5);
    
    // Convert to BCD
    uint8_t hours_bcd = decimal_to_packedBCD(h);
    uint8_t minutes_bcd = decimal_to_packedBCD(m);
    uint8_t seconds_bcd = decimal_to_packedBCD(s);
    
    // Write to RTC
    i2c_start();
    i2c_write(DS1307_ADDRESS_WRITE);
    i2c_write(DS1307_SECONDS_REG);
    i2c_write(seconds_bcd & 0x7F); // Ensure CH bit is clear to keep clock running
    i2c_write(minutes_bcd);
    i2c_write(hours_bcd);
    i2c_stop();
    
    // Update the global time variables
    seconds = s;
    minutes = m;
    hours = h;
    
    // Update manual time variables
    extern volatile uint8_t manual_seconds;
    extern volatile uint8_t manual_minutes;
    extern volatile uint8_t manual_hours;
    extern volatile uint8_t is_pm;
    
    manual_seconds = s;
    manual_minutes = m;
    manual_hours = h;
    
    // Update AM/PM flag
    is_pm = (manual_hours >= 12) ? 1 : 0;
    
    i2c_busy = 0;
    return 1;
}

void ds1307_read_time(void)
{
    ds1307_read_time_safe();
}

void ds1307_write_time(uint8_t h, uint8_t m, uint8_t s)
{
    ds1307_write_time_safe(h, m, s);
}

void save_to_sram(unsigned char address, unsigned char data)
{
    // If I2C is busy, return
    if (i2c_busy) {
        return;
    }
    
    // Set busy flag
    i2c_busy = 1;
    
    if (address > 63)
        return;
    i2c_start();
    i2c_write(DS1307_ADDRESS_WRITE);
    i2c_write(address);
    i2c_write(data);
    i2c_stop();
    
    // Clear busy flag
    i2c_busy = 0;
}

unsigned char read_from_sram(unsigned char address)
{
    // If I2C is busy, return error
    if (i2c_busy) {
        return 0xFF;
    }
    
    // Set busy flag
    i2c_busy = 1;
    
    unsigned char data;
    if (address > 63) {
        i2c_busy = 0;
        return 0xFF;
    }
    
    i2c_start();
    i2c_write(DS1307_ADDRESS_WRITE);
    i2c_write(address);
    i2c_start();
    i2c_write(DS1307_ADDRESS_READ);
    data = i2c_read();
    i2c_send_nack();
    i2c_stop();
    
    // Clear busy flag
    i2c_busy = 0;
    
    return data;
}

/**
 * @brief Read date from RTC with safety checks to prevent I2C conflicts
 * @param day Pointer to store day of week (1-7)
 * @param date Pointer to store date (1-31)
 * @param month Pointer to store month (1-12)
 * @param year Pointer to store year (0-99)
 * @return 1 if successful, 0 if failed due to I2C busy
 */
uint8_t ds1307_read_date_safe(uint8_t *day, uint8_t *date, uint8_t *month, uint8_t *year)
{
    // If I2C is busy, return failure
    if (i2c_busy) {
        return 0;
    }
    
    // Set busy flag
    i2c_busy = 1;
    
    // Reset I2C bus first to ensure clean communication
    i2c_stop();
    HAL_Delay(5);
    
    unsigned char tempDay, tempDate, tempMonth, tempYear;
    uint8_t success = 1;
    
    // Read date from RTC
    i2c_start();
    i2c_write(DS1307_ADDRESS_WRITE);
    i2c_write(DS1307_DAY_REG);
    i2c_start();
    i2c_write(DS1307_ADDRESS_READ);
    tempDay = i2c_read();
    i2c_send_ack();
    tempDate = i2c_read();
    i2c_send_ack();
    tempMonth = i2c_read();
    i2c_send_ack();
    tempYear = i2c_read();
    i2c_send_nack();
    i2c_stop();
    
    // Convert BCD to decimal
    *day = bcd_to_decimal(tempDay & 0x07);    // Day is 1-7
    *date = bcd_to_decimal(tempDate & 0x3F);  // Date is 1-31
    *month = bcd_to_decimal(tempMonth & 0x1F); // Month is 1-12
    *year = bcd_to_decimal(tempYear);         // Year is 0-99
    
    // Clear busy flag
    i2c_busy = 0;
    return success;
}
