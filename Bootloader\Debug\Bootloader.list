
Bootloader.elf:     file format elf32-littlearm

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .isr_vector   0000010c  08000000  08000000  00001000  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  1 .text         00003248  0800010c  0800010c  0000110c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  2 .rodata       000007ec  08003354  08003354  00004354  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  3 .ARM.extab    00000000  08003b40  08003b40  0000506c  2**0
                  CONTENTS
  4 .ARM          00000000  08003b40  08003b40  0000506c  2**0
                  CONTENTS
  5 .preinit_array 00000000  08003b40  08003b40  0000506c  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  6 .init_array   00000004  08003b40  08003b40  00004b40  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  7 .fini_array   00000004  08003b44  08003b44  00004b44  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .data         0000006c  20000000  08003b48  00005000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  9 .bss          0000022c  20000070  08003bb4  00005070  2**3
                  ALLOC
 10 ._user_heap_stack 00000604  2000029c  08003bb4  0000529c  2**0
                  ALLOC
 11 .ARM.attributes 00000029  00000000  00000000  0000506c  2**0
                  CONTENTS, READONLY
 12 .debug_info   0000a168  00000000  00000000  00005095  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 13 .debug_abbrev 00001c25  00000000  00000000  0000f1fd  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 14 .debug_loclists 0000445f  00000000  00000000  00010e22  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 15 .debug_aranges 000007d8  00000000  00000000  00015288  2**3
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 16 .debug_rnglists 00000631  00000000  00000000  00015a60  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 17 .debug_macro  0000238c  00000000  00000000  00016091  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 18 .debug_line   0000c86d  00000000  00000000  0001841d  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 19 .debug_str    000843f9  00000000  00000000  00024c8a  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 20 .comment      00000043  00000000  00000000  000a9083  2**0
                  CONTENTS, READONLY
 21 .debug_frame  00001d20  00000000  00000000  000a90c8  2**2
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 22 .debug_line_str 00000096  00000000  00000000  000aade8  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS

Disassembly of section .text:

0800010c <__do_global_dtors_aux>:
 800010c:	b510      	push	{r4, lr}
 800010e:	4c05      	ldr	r4, [pc, #20]	@ (8000124 <__do_global_dtors_aux+0x18>)
 8000110:	7823      	ldrb	r3, [r4, #0]
 8000112:	b933      	cbnz	r3, 8000122 <__do_global_dtors_aux+0x16>
 8000114:	4b04      	ldr	r3, [pc, #16]	@ (8000128 <__do_global_dtors_aux+0x1c>)
 8000116:	b113      	cbz	r3, 800011e <__do_global_dtors_aux+0x12>
 8000118:	4804      	ldr	r0, [pc, #16]	@ (800012c <__do_global_dtors_aux+0x20>)
 800011a:	f3af 8000 	nop.w
 800011e:	2301      	movs	r3, #1
 8000120:	7023      	strb	r3, [r4, #0]
 8000122:	bd10      	pop	{r4, pc}
 8000124:	20000070 	.word	0x20000070
 8000128:	00000000 	.word	0x00000000
 800012c:	0800333c 	.word	0x0800333c

08000130 <frame_dummy>:
 8000130:	b508      	push	{r3, lr}
 8000132:	4b03      	ldr	r3, [pc, #12]	@ (8000140 <frame_dummy+0x10>)
 8000134:	b11b      	cbz	r3, 800013e <frame_dummy+0xe>
 8000136:	4903      	ldr	r1, [pc, #12]	@ (8000144 <frame_dummy+0x14>)
 8000138:	4803      	ldr	r0, [pc, #12]	@ (8000148 <frame_dummy+0x18>)
 800013a:	f3af 8000 	nop.w
 800013e:	bd08      	pop	{r3, pc}
 8000140:	00000000 	.word	0x00000000
 8000144:	20000074 	.word	0x20000074
 8000148:	0800333c 	.word	0x0800333c

0800014c <__aeabi_drsub>:
 800014c:	f081 4100 	eor.w	r1, r1, #2147483648	@ 0x80000000
 8000150:	e002      	b.n	8000158 <__adddf3>
 8000152:	bf00      	nop

08000154 <__aeabi_dsub>:
 8000154:	f083 4300 	eor.w	r3, r3, #2147483648	@ 0x80000000

08000158 <__adddf3>:
 8000158:	b530      	push	{r4, r5, lr}
 800015a:	ea4f 0441 	mov.w	r4, r1, lsl #1
 800015e:	ea4f 0543 	mov.w	r5, r3, lsl #1
 8000162:	ea94 0f05 	teq	r4, r5
 8000166:	bf08      	it	eq
 8000168:	ea90 0f02 	teqeq	r0, r2
 800016c:	bf1f      	itttt	ne
 800016e:	ea54 0c00 	orrsne.w	ip, r4, r0
 8000172:	ea55 0c02 	orrsne.w	ip, r5, r2
 8000176:	ea7f 5c64 	mvnsne.w	ip, r4, asr #21
 800017a:	ea7f 5c65 	mvnsne.w	ip, r5, asr #21
 800017e:	f000 80e2 	beq.w	8000346 <__adddf3+0x1ee>
 8000182:	ea4f 5454 	mov.w	r4, r4, lsr #21
 8000186:	ebd4 5555 	rsbs	r5, r4, r5, lsr #21
 800018a:	bfb8      	it	lt
 800018c:	426d      	neglt	r5, r5
 800018e:	dd0c      	ble.n	80001aa <__adddf3+0x52>
 8000190:	442c      	add	r4, r5
 8000192:	ea80 0202 	eor.w	r2, r0, r2
 8000196:	ea81 0303 	eor.w	r3, r1, r3
 800019a:	ea82 0000 	eor.w	r0, r2, r0
 800019e:	ea83 0101 	eor.w	r1, r3, r1
 80001a2:	ea80 0202 	eor.w	r2, r0, r2
 80001a6:	ea81 0303 	eor.w	r3, r1, r3
 80001aa:	2d36      	cmp	r5, #54	@ 0x36
 80001ac:	bf88      	it	hi
 80001ae:	bd30      	pophi	{r4, r5, pc}
 80001b0:	f011 4f00 	tst.w	r1, #2147483648	@ 0x80000000
 80001b4:	ea4f 3101 	mov.w	r1, r1, lsl #12
 80001b8:	f44f 1c80 	mov.w	ip, #1048576	@ 0x100000
 80001bc:	ea4c 3111 	orr.w	r1, ip, r1, lsr #12
 80001c0:	d002      	beq.n	80001c8 <__adddf3+0x70>
 80001c2:	4240      	negs	r0, r0
 80001c4:	eb61 0141 	sbc.w	r1, r1, r1, lsl #1
 80001c8:	f013 4f00 	tst.w	r3, #2147483648	@ 0x80000000
 80001cc:	ea4f 3303 	mov.w	r3, r3, lsl #12
 80001d0:	ea4c 3313 	orr.w	r3, ip, r3, lsr #12
 80001d4:	d002      	beq.n	80001dc <__adddf3+0x84>
 80001d6:	4252      	negs	r2, r2
 80001d8:	eb63 0343 	sbc.w	r3, r3, r3, lsl #1
 80001dc:	ea94 0f05 	teq	r4, r5
 80001e0:	f000 80a7 	beq.w	8000332 <__adddf3+0x1da>
 80001e4:	f1a4 0401 	sub.w	r4, r4, #1
 80001e8:	f1d5 0e20 	rsbs	lr, r5, #32
 80001ec:	db0d      	blt.n	800020a <__adddf3+0xb2>
 80001ee:	fa02 fc0e 	lsl.w	ip, r2, lr
 80001f2:	fa22 f205 	lsr.w	r2, r2, r5
 80001f6:	1880      	adds	r0, r0, r2
 80001f8:	f141 0100 	adc.w	r1, r1, #0
 80001fc:	fa03 f20e 	lsl.w	r2, r3, lr
 8000200:	1880      	adds	r0, r0, r2
 8000202:	fa43 f305 	asr.w	r3, r3, r5
 8000206:	4159      	adcs	r1, r3
 8000208:	e00e      	b.n	8000228 <__adddf3+0xd0>
 800020a:	f1a5 0520 	sub.w	r5, r5, #32
 800020e:	f10e 0e20 	add.w	lr, lr, #32
 8000212:	2a01      	cmp	r2, #1
 8000214:	fa03 fc0e 	lsl.w	ip, r3, lr
 8000218:	bf28      	it	cs
 800021a:	f04c 0c02 	orrcs.w	ip, ip, #2
 800021e:	fa43 f305 	asr.w	r3, r3, r5
 8000222:	18c0      	adds	r0, r0, r3
 8000224:	eb51 71e3 	adcs.w	r1, r1, r3, asr #31
 8000228:	f001 4500 	and.w	r5, r1, #2147483648	@ 0x80000000
 800022c:	d507      	bpl.n	800023e <__adddf3+0xe6>
 800022e:	f04f 0e00 	mov.w	lr, #0
 8000232:	f1dc 0c00 	rsbs	ip, ip, #0
 8000236:	eb7e 0000 	sbcs.w	r0, lr, r0
 800023a:	eb6e 0101 	sbc.w	r1, lr, r1
 800023e:	f5b1 1f80 	cmp.w	r1, #1048576	@ 0x100000
 8000242:	d31b      	bcc.n	800027c <__adddf3+0x124>
 8000244:	f5b1 1f00 	cmp.w	r1, #2097152	@ 0x200000
 8000248:	d30c      	bcc.n	8000264 <__adddf3+0x10c>
 800024a:	0849      	lsrs	r1, r1, #1
 800024c:	ea5f 0030 	movs.w	r0, r0, rrx
 8000250:	ea4f 0c3c 	mov.w	ip, ip, rrx
 8000254:	f104 0401 	add.w	r4, r4, #1
 8000258:	ea4f 5244 	mov.w	r2, r4, lsl #21
 800025c:	f512 0f80 	cmn.w	r2, #4194304	@ 0x400000
 8000260:	f080 809a 	bcs.w	8000398 <__adddf3+0x240>
 8000264:	f1bc 4f00 	cmp.w	ip, #2147483648	@ 0x80000000
 8000268:	bf08      	it	eq
 800026a:	ea5f 0c50 	movseq.w	ip, r0, lsr #1
 800026e:	f150 0000 	adcs.w	r0, r0, #0
 8000272:	eb41 5104 	adc.w	r1, r1, r4, lsl #20
 8000276:	ea41 0105 	orr.w	r1, r1, r5
 800027a:	bd30      	pop	{r4, r5, pc}
 800027c:	ea5f 0c4c 	movs.w	ip, ip, lsl #1
 8000280:	4140      	adcs	r0, r0
 8000282:	eb41 0101 	adc.w	r1, r1, r1
 8000286:	3c01      	subs	r4, #1
 8000288:	bf28      	it	cs
 800028a:	f5b1 1f80 	cmpcs.w	r1, #1048576	@ 0x100000
 800028e:	d2e9      	bcs.n	8000264 <__adddf3+0x10c>
 8000290:	f091 0f00 	teq	r1, #0
 8000294:	bf04      	itt	eq
 8000296:	4601      	moveq	r1, r0
 8000298:	2000      	moveq	r0, #0
 800029a:	fab1 f381 	clz	r3, r1
 800029e:	bf08      	it	eq
 80002a0:	3320      	addeq	r3, #32
 80002a2:	f1a3 030b 	sub.w	r3, r3, #11
 80002a6:	f1b3 0220 	subs.w	r2, r3, #32
 80002aa:	da0c      	bge.n	80002c6 <__adddf3+0x16e>
 80002ac:	320c      	adds	r2, #12
 80002ae:	dd08      	ble.n	80002c2 <__adddf3+0x16a>
 80002b0:	f102 0c14 	add.w	ip, r2, #20
 80002b4:	f1c2 020c 	rsb	r2, r2, #12
 80002b8:	fa01 f00c 	lsl.w	r0, r1, ip
 80002bc:	fa21 f102 	lsr.w	r1, r1, r2
 80002c0:	e00c      	b.n	80002dc <__adddf3+0x184>
 80002c2:	f102 0214 	add.w	r2, r2, #20
 80002c6:	bfd8      	it	le
 80002c8:	f1c2 0c20 	rsble	ip, r2, #32
 80002cc:	fa01 f102 	lsl.w	r1, r1, r2
 80002d0:	fa20 fc0c 	lsr.w	ip, r0, ip
 80002d4:	bfdc      	itt	le
 80002d6:	ea41 010c 	orrle.w	r1, r1, ip
 80002da:	4090      	lslle	r0, r2
 80002dc:	1ae4      	subs	r4, r4, r3
 80002de:	bfa2      	ittt	ge
 80002e0:	eb01 5104 	addge.w	r1, r1, r4, lsl #20
 80002e4:	4329      	orrge	r1, r5
 80002e6:	bd30      	popge	{r4, r5, pc}
 80002e8:	ea6f 0404 	mvn.w	r4, r4
 80002ec:	3c1f      	subs	r4, #31
 80002ee:	da1c      	bge.n	800032a <__adddf3+0x1d2>
 80002f0:	340c      	adds	r4, #12
 80002f2:	dc0e      	bgt.n	8000312 <__adddf3+0x1ba>
 80002f4:	f104 0414 	add.w	r4, r4, #20
 80002f8:	f1c4 0220 	rsb	r2, r4, #32
 80002fc:	fa20 f004 	lsr.w	r0, r0, r4
 8000300:	fa01 f302 	lsl.w	r3, r1, r2
 8000304:	ea40 0003 	orr.w	r0, r0, r3
 8000308:	fa21 f304 	lsr.w	r3, r1, r4
 800030c:	ea45 0103 	orr.w	r1, r5, r3
 8000310:	bd30      	pop	{r4, r5, pc}
 8000312:	f1c4 040c 	rsb	r4, r4, #12
 8000316:	f1c4 0220 	rsb	r2, r4, #32
 800031a:	fa20 f002 	lsr.w	r0, r0, r2
 800031e:	fa01 f304 	lsl.w	r3, r1, r4
 8000322:	ea40 0003 	orr.w	r0, r0, r3
 8000326:	4629      	mov	r1, r5
 8000328:	bd30      	pop	{r4, r5, pc}
 800032a:	fa21 f004 	lsr.w	r0, r1, r4
 800032e:	4629      	mov	r1, r5
 8000330:	bd30      	pop	{r4, r5, pc}
 8000332:	f094 0f00 	teq	r4, #0
 8000336:	f483 1380 	eor.w	r3, r3, #1048576	@ 0x100000
 800033a:	bf06      	itte	eq
 800033c:	f481 1180 	eoreq.w	r1, r1, #1048576	@ 0x100000
 8000340:	3401      	addeq	r4, #1
 8000342:	3d01      	subne	r5, #1
 8000344:	e74e      	b.n	80001e4 <__adddf3+0x8c>
 8000346:	ea7f 5c64 	mvns.w	ip, r4, asr #21
 800034a:	bf18      	it	ne
 800034c:	ea7f 5c65 	mvnsne.w	ip, r5, asr #21
 8000350:	d029      	beq.n	80003a6 <__adddf3+0x24e>
 8000352:	ea94 0f05 	teq	r4, r5
 8000356:	bf08      	it	eq
 8000358:	ea90 0f02 	teqeq	r0, r2
 800035c:	d005      	beq.n	800036a <__adddf3+0x212>
 800035e:	ea54 0c00 	orrs.w	ip, r4, r0
 8000362:	bf04      	itt	eq
 8000364:	4619      	moveq	r1, r3
 8000366:	4610      	moveq	r0, r2
 8000368:	bd30      	pop	{r4, r5, pc}
 800036a:	ea91 0f03 	teq	r1, r3
 800036e:	bf1e      	ittt	ne
 8000370:	2100      	movne	r1, #0
 8000372:	2000      	movne	r0, #0
 8000374:	bd30      	popne	{r4, r5, pc}
 8000376:	ea5f 5c54 	movs.w	ip, r4, lsr #21
 800037a:	d105      	bne.n	8000388 <__adddf3+0x230>
 800037c:	0040      	lsls	r0, r0, #1
 800037e:	4149      	adcs	r1, r1
 8000380:	bf28      	it	cs
 8000382:	f041 4100 	orrcs.w	r1, r1, #2147483648	@ 0x80000000
 8000386:	bd30      	pop	{r4, r5, pc}
 8000388:	f514 0480 	adds.w	r4, r4, #4194304	@ 0x400000
 800038c:	bf3c      	itt	cc
 800038e:	f501 1180 	addcc.w	r1, r1, #1048576	@ 0x100000
 8000392:	bd30      	popcc	{r4, r5, pc}
 8000394:	f001 4500 	and.w	r5, r1, #2147483648	@ 0x80000000
 8000398:	f045 41fe 	orr.w	r1, r5, #2130706432	@ 0x7f000000
 800039c:	f441 0170 	orr.w	r1, r1, #15728640	@ 0xf00000
 80003a0:	f04f 0000 	mov.w	r0, #0
 80003a4:	bd30      	pop	{r4, r5, pc}
 80003a6:	ea7f 5c64 	mvns.w	ip, r4, asr #21
 80003aa:	bf1a      	itte	ne
 80003ac:	4619      	movne	r1, r3
 80003ae:	4610      	movne	r0, r2
 80003b0:	ea7f 5c65 	mvnseq.w	ip, r5, asr #21
 80003b4:	bf1c      	itt	ne
 80003b6:	460b      	movne	r3, r1
 80003b8:	4602      	movne	r2, r0
 80003ba:	ea50 3401 	orrs.w	r4, r0, r1, lsl #12
 80003be:	bf06      	itte	eq
 80003c0:	ea52 3503 	orrseq.w	r5, r2, r3, lsl #12
 80003c4:	ea91 0f03 	teqeq	r1, r3
 80003c8:	f441 2100 	orrne.w	r1, r1, #524288	@ 0x80000
 80003cc:	bd30      	pop	{r4, r5, pc}
 80003ce:	bf00      	nop

080003d0 <__aeabi_ui2d>:
 80003d0:	f090 0f00 	teq	r0, #0
 80003d4:	bf04      	itt	eq
 80003d6:	2100      	moveq	r1, #0
 80003d8:	4770      	bxeq	lr
 80003da:	b530      	push	{r4, r5, lr}
 80003dc:	f44f 6480 	mov.w	r4, #1024	@ 0x400
 80003e0:	f104 0432 	add.w	r4, r4, #50	@ 0x32
 80003e4:	f04f 0500 	mov.w	r5, #0
 80003e8:	f04f 0100 	mov.w	r1, #0
 80003ec:	e750      	b.n	8000290 <__adddf3+0x138>
 80003ee:	bf00      	nop

080003f0 <__aeabi_i2d>:
 80003f0:	f090 0f00 	teq	r0, #0
 80003f4:	bf04      	itt	eq
 80003f6:	2100      	moveq	r1, #0
 80003f8:	4770      	bxeq	lr
 80003fa:	b530      	push	{r4, r5, lr}
 80003fc:	f44f 6480 	mov.w	r4, #1024	@ 0x400
 8000400:	f104 0432 	add.w	r4, r4, #50	@ 0x32
 8000404:	f010 4500 	ands.w	r5, r0, #2147483648	@ 0x80000000
 8000408:	bf48      	it	mi
 800040a:	4240      	negmi	r0, r0
 800040c:	f04f 0100 	mov.w	r1, #0
 8000410:	e73e      	b.n	8000290 <__adddf3+0x138>
 8000412:	bf00      	nop

08000414 <__aeabi_f2d>:
 8000414:	0042      	lsls	r2, r0, #1
 8000416:	ea4f 01e2 	mov.w	r1, r2, asr #3
 800041a:	ea4f 0131 	mov.w	r1, r1, rrx
 800041e:	ea4f 7002 	mov.w	r0, r2, lsl #28
 8000422:	bf1f      	itttt	ne
 8000424:	f012 437f 	andsne.w	r3, r2, #4278190080	@ 0xff000000
 8000428:	f093 4f7f 	teqne	r3, #4278190080	@ 0xff000000
 800042c:	f081 5160 	eorne.w	r1, r1, #939524096	@ 0x38000000
 8000430:	4770      	bxne	lr
 8000432:	f032 427f 	bics.w	r2, r2, #4278190080	@ 0xff000000
 8000436:	bf08      	it	eq
 8000438:	4770      	bxeq	lr
 800043a:	f093 4f7f 	teq	r3, #4278190080	@ 0xff000000
 800043e:	bf04      	itt	eq
 8000440:	f441 2100 	orreq.w	r1, r1, #524288	@ 0x80000
 8000444:	4770      	bxeq	lr
 8000446:	b530      	push	{r4, r5, lr}
 8000448:	f44f 7460 	mov.w	r4, #896	@ 0x380
 800044c:	f001 4500 	and.w	r5, r1, #2147483648	@ 0x80000000
 8000450:	f021 4100 	bic.w	r1, r1, #2147483648	@ 0x80000000
 8000454:	e71c      	b.n	8000290 <__adddf3+0x138>
 8000456:	bf00      	nop

08000458 <__aeabi_ul2d>:
 8000458:	ea50 0201 	orrs.w	r2, r0, r1
 800045c:	bf08      	it	eq
 800045e:	4770      	bxeq	lr
 8000460:	b530      	push	{r4, r5, lr}
 8000462:	f04f 0500 	mov.w	r5, #0
 8000466:	e00a      	b.n	800047e <__aeabi_l2d+0x16>

08000468 <__aeabi_l2d>:
 8000468:	ea50 0201 	orrs.w	r2, r0, r1
 800046c:	bf08      	it	eq
 800046e:	4770      	bxeq	lr
 8000470:	b530      	push	{r4, r5, lr}
 8000472:	f011 4500 	ands.w	r5, r1, #2147483648	@ 0x80000000
 8000476:	d502      	bpl.n	800047e <__aeabi_l2d+0x16>
 8000478:	4240      	negs	r0, r0
 800047a:	eb61 0141 	sbc.w	r1, r1, r1, lsl #1
 800047e:	f44f 6480 	mov.w	r4, #1024	@ 0x400
 8000482:	f104 0432 	add.w	r4, r4, #50	@ 0x32
 8000486:	ea5f 5c91 	movs.w	ip, r1, lsr #22
 800048a:	f43f aed8 	beq.w	800023e <__adddf3+0xe6>
 800048e:	f04f 0203 	mov.w	r2, #3
 8000492:	ea5f 0cdc 	movs.w	ip, ip, lsr #3
 8000496:	bf18      	it	ne
 8000498:	3203      	addne	r2, #3
 800049a:	ea5f 0cdc 	movs.w	ip, ip, lsr #3
 800049e:	bf18      	it	ne
 80004a0:	3203      	addne	r2, #3
 80004a2:	eb02 02dc 	add.w	r2, r2, ip, lsr #3
 80004a6:	f1c2 0320 	rsb	r3, r2, #32
 80004aa:	fa00 fc03 	lsl.w	ip, r0, r3
 80004ae:	fa20 f002 	lsr.w	r0, r0, r2
 80004b2:	fa01 fe03 	lsl.w	lr, r1, r3
 80004b6:	ea40 000e 	orr.w	r0, r0, lr
 80004ba:	fa21 f102 	lsr.w	r1, r1, r2
 80004be:	4414      	add	r4, r2
 80004c0:	e6bd      	b.n	800023e <__adddf3+0xe6>
 80004c2:	bf00      	nop

080004c4 <__aeabi_frsub>:
 80004c4:	f080 4000 	eor.w	r0, r0, #2147483648	@ 0x80000000
 80004c8:	e002      	b.n	80004d0 <__addsf3>
 80004ca:	bf00      	nop

080004cc <__aeabi_fsub>:
 80004cc:	f081 4100 	eor.w	r1, r1, #2147483648	@ 0x80000000

080004d0 <__addsf3>:
 80004d0:	0042      	lsls	r2, r0, #1
 80004d2:	bf1f      	itttt	ne
 80004d4:	ea5f 0341 	movsne.w	r3, r1, lsl #1
 80004d8:	ea92 0f03 	teqne	r2, r3
 80004dc:	ea7f 6c22 	mvnsne.w	ip, r2, asr #24
 80004e0:	ea7f 6c23 	mvnsne.w	ip, r3, asr #24
 80004e4:	d06a      	beq.n	80005bc <__addsf3+0xec>
 80004e6:	ea4f 6212 	mov.w	r2, r2, lsr #24
 80004ea:	ebd2 6313 	rsbs	r3, r2, r3, lsr #24
 80004ee:	bfc1      	itttt	gt
 80004f0:	18d2      	addgt	r2, r2, r3
 80004f2:	4041      	eorgt	r1, r0
 80004f4:	4048      	eorgt	r0, r1
 80004f6:	4041      	eorgt	r1, r0
 80004f8:	bfb8      	it	lt
 80004fa:	425b      	neglt	r3, r3
 80004fc:	2b19      	cmp	r3, #25
 80004fe:	bf88      	it	hi
 8000500:	4770      	bxhi	lr
 8000502:	f010 4f00 	tst.w	r0, #2147483648	@ 0x80000000
 8000506:	f440 0000 	orr.w	r0, r0, #8388608	@ 0x800000
 800050a:	f020 407f 	bic.w	r0, r0, #4278190080	@ 0xff000000
 800050e:	bf18      	it	ne
 8000510:	4240      	negne	r0, r0
 8000512:	f011 4f00 	tst.w	r1, #2147483648	@ 0x80000000
 8000516:	f441 0100 	orr.w	r1, r1, #8388608	@ 0x800000
 800051a:	f021 417f 	bic.w	r1, r1, #4278190080	@ 0xff000000
 800051e:	bf18      	it	ne
 8000520:	4249      	negne	r1, r1
 8000522:	ea92 0f03 	teq	r2, r3
 8000526:	d03f      	beq.n	80005a8 <__addsf3+0xd8>
 8000528:	f1a2 0201 	sub.w	r2, r2, #1
 800052c:	fa41 fc03 	asr.w	ip, r1, r3
 8000530:	eb10 000c 	adds.w	r0, r0, ip
 8000534:	f1c3 0320 	rsb	r3, r3, #32
 8000538:	fa01 f103 	lsl.w	r1, r1, r3
 800053c:	f000 4300 	and.w	r3, r0, #2147483648	@ 0x80000000
 8000540:	d502      	bpl.n	8000548 <__addsf3+0x78>
 8000542:	4249      	negs	r1, r1
 8000544:	eb60 0040 	sbc.w	r0, r0, r0, lsl #1
 8000548:	f5b0 0f00 	cmp.w	r0, #8388608	@ 0x800000
 800054c:	d313      	bcc.n	8000576 <__addsf3+0xa6>
 800054e:	f1b0 7f80 	cmp.w	r0, #16777216	@ 0x1000000
 8000552:	d306      	bcc.n	8000562 <__addsf3+0x92>
 8000554:	0840      	lsrs	r0, r0, #1
 8000556:	ea4f 0131 	mov.w	r1, r1, rrx
 800055a:	f102 0201 	add.w	r2, r2, #1
 800055e:	2afe      	cmp	r2, #254	@ 0xfe
 8000560:	d251      	bcs.n	8000606 <__addsf3+0x136>
 8000562:	f1b1 4f00 	cmp.w	r1, #2147483648	@ 0x80000000
 8000566:	eb40 50c2 	adc.w	r0, r0, r2, lsl #23
 800056a:	bf08      	it	eq
 800056c:	f020 0001 	biceq.w	r0, r0, #1
 8000570:	ea40 0003 	orr.w	r0, r0, r3
 8000574:	4770      	bx	lr
 8000576:	0049      	lsls	r1, r1, #1
 8000578:	eb40 0000 	adc.w	r0, r0, r0
 800057c:	3a01      	subs	r2, #1
 800057e:	bf28      	it	cs
 8000580:	f5b0 0f00 	cmpcs.w	r0, #8388608	@ 0x800000
 8000584:	d2ed      	bcs.n	8000562 <__addsf3+0x92>
 8000586:	fab0 fc80 	clz	ip, r0
 800058a:	f1ac 0c08 	sub.w	ip, ip, #8
 800058e:	ebb2 020c 	subs.w	r2, r2, ip
 8000592:	fa00 f00c 	lsl.w	r0, r0, ip
 8000596:	bfaa      	itet	ge
 8000598:	eb00 50c2 	addge.w	r0, r0, r2, lsl #23
 800059c:	4252      	neglt	r2, r2
 800059e:	4318      	orrge	r0, r3
 80005a0:	bfbc      	itt	lt
 80005a2:	40d0      	lsrlt	r0, r2
 80005a4:	4318      	orrlt	r0, r3
 80005a6:	4770      	bx	lr
 80005a8:	f092 0f00 	teq	r2, #0
 80005ac:	f481 0100 	eor.w	r1, r1, #8388608	@ 0x800000
 80005b0:	bf06      	itte	eq
 80005b2:	f480 0000 	eoreq.w	r0, r0, #8388608	@ 0x800000
 80005b6:	3201      	addeq	r2, #1
 80005b8:	3b01      	subne	r3, #1
 80005ba:	e7b5      	b.n	8000528 <__addsf3+0x58>
 80005bc:	ea4f 0341 	mov.w	r3, r1, lsl #1
 80005c0:	ea7f 6c22 	mvns.w	ip, r2, asr #24
 80005c4:	bf18      	it	ne
 80005c6:	ea7f 6c23 	mvnsne.w	ip, r3, asr #24
 80005ca:	d021      	beq.n	8000610 <__addsf3+0x140>
 80005cc:	ea92 0f03 	teq	r2, r3
 80005d0:	d004      	beq.n	80005dc <__addsf3+0x10c>
 80005d2:	f092 0f00 	teq	r2, #0
 80005d6:	bf08      	it	eq
 80005d8:	4608      	moveq	r0, r1
 80005da:	4770      	bx	lr
 80005dc:	ea90 0f01 	teq	r0, r1
 80005e0:	bf1c      	itt	ne
 80005e2:	2000      	movne	r0, #0
 80005e4:	4770      	bxne	lr
 80005e6:	f012 4f7f 	tst.w	r2, #4278190080	@ 0xff000000
 80005ea:	d104      	bne.n	80005f6 <__addsf3+0x126>
 80005ec:	0040      	lsls	r0, r0, #1
 80005ee:	bf28      	it	cs
 80005f0:	f040 4000 	orrcs.w	r0, r0, #2147483648	@ 0x80000000
 80005f4:	4770      	bx	lr
 80005f6:	f112 7200 	adds.w	r2, r2, #33554432	@ 0x2000000
 80005fa:	bf3c      	itt	cc
 80005fc:	f500 0000 	addcc.w	r0, r0, #8388608	@ 0x800000
 8000600:	4770      	bxcc	lr
 8000602:	f000 4300 	and.w	r3, r0, #2147483648	@ 0x80000000
 8000606:	f043 40fe 	orr.w	r0, r3, #2130706432	@ 0x7f000000
 800060a:	f440 0000 	orr.w	r0, r0, #8388608	@ 0x800000
 800060e:	4770      	bx	lr
 8000610:	ea7f 6222 	mvns.w	r2, r2, asr #24
 8000614:	bf16      	itet	ne
 8000616:	4608      	movne	r0, r1
 8000618:	ea7f 6323 	mvnseq.w	r3, r3, asr #24
 800061c:	4601      	movne	r1, r0
 800061e:	0242      	lsls	r2, r0, #9
 8000620:	bf06      	itte	eq
 8000622:	ea5f 2341 	movseq.w	r3, r1, lsl #9
 8000626:	ea90 0f01 	teqeq	r0, r1
 800062a:	f440 0080 	orrne.w	r0, r0, #4194304	@ 0x400000
 800062e:	4770      	bx	lr

08000630 <__aeabi_ui2f>:
 8000630:	f04f 0300 	mov.w	r3, #0
 8000634:	e004      	b.n	8000640 <__aeabi_i2f+0x8>
 8000636:	bf00      	nop

08000638 <__aeabi_i2f>:
 8000638:	f010 4300 	ands.w	r3, r0, #2147483648	@ 0x80000000
 800063c:	bf48      	it	mi
 800063e:	4240      	negmi	r0, r0
 8000640:	ea5f 0c00 	movs.w	ip, r0
 8000644:	bf08      	it	eq
 8000646:	4770      	bxeq	lr
 8000648:	f043 4396 	orr.w	r3, r3, #1258291200	@ 0x4b000000
 800064c:	4601      	mov	r1, r0
 800064e:	f04f 0000 	mov.w	r0, #0
 8000652:	e01c      	b.n	800068e <__aeabi_l2f+0x2a>

08000654 <__aeabi_ul2f>:
 8000654:	ea50 0201 	orrs.w	r2, r0, r1
 8000658:	bf08      	it	eq
 800065a:	4770      	bxeq	lr
 800065c:	f04f 0300 	mov.w	r3, #0
 8000660:	e00a      	b.n	8000678 <__aeabi_l2f+0x14>
 8000662:	bf00      	nop

08000664 <__aeabi_l2f>:
 8000664:	ea50 0201 	orrs.w	r2, r0, r1
 8000668:	bf08      	it	eq
 800066a:	4770      	bxeq	lr
 800066c:	f011 4300 	ands.w	r3, r1, #2147483648	@ 0x80000000
 8000670:	d502      	bpl.n	8000678 <__aeabi_l2f+0x14>
 8000672:	4240      	negs	r0, r0
 8000674:	eb61 0141 	sbc.w	r1, r1, r1, lsl #1
 8000678:	ea5f 0c01 	movs.w	ip, r1
 800067c:	bf02      	ittt	eq
 800067e:	4684      	moveq	ip, r0
 8000680:	4601      	moveq	r1, r0
 8000682:	2000      	moveq	r0, #0
 8000684:	f043 43b6 	orr.w	r3, r3, #1526726656	@ 0x5b000000
 8000688:	bf08      	it	eq
 800068a:	f1a3 5380 	subeq.w	r3, r3, #268435456	@ 0x10000000
 800068e:	f5a3 0300 	sub.w	r3, r3, #8388608	@ 0x800000
 8000692:	fabc f28c 	clz	r2, ip
 8000696:	3a08      	subs	r2, #8
 8000698:	eba3 53c2 	sub.w	r3, r3, r2, lsl #23
 800069c:	db10      	blt.n	80006c0 <__aeabi_l2f+0x5c>
 800069e:	fa01 fc02 	lsl.w	ip, r1, r2
 80006a2:	4463      	add	r3, ip
 80006a4:	fa00 fc02 	lsl.w	ip, r0, r2
 80006a8:	f1c2 0220 	rsb	r2, r2, #32
 80006ac:	f1bc 4f00 	cmp.w	ip, #2147483648	@ 0x80000000
 80006b0:	fa20 f202 	lsr.w	r2, r0, r2
 80006b4:	eb43 0002 	adc.w	r0, r3, r2
 80006b8:	bf08      	it	eq
 80006ba:	f020 0001 	biceq.w	r0, r0, #1
 80006be:	4770      	bx	lr
 80006c0:	f102 0220 	add.w	r2, r2, #32
 80006c4:	fa01 fc02 	lsl.w	ip, r1, r2
 80006c8:	f1c2 0220 	rsb	r2, r2, #32
 80006cc:	ea50 004c 	orrs.w	r0, r0, ip, lsl #1
 80006d0:	fa21 f202 	lsr.w	r2, r1, r2
 80006d4:	eb43 0002 	adc.w	r0, r3, r2
 80006d8:	bf08      	it	eq
 80006da:	ea20 70dc 	biceq.w	r0, r0, ip, lsr #31
 80006de:	4770      	bx	lr

080006e0 <__aeabi_fmul>:
 80006e0:	f04f 0cff 	mov.w	ip, #255	@ 0xff
 80006e4:	ea1c 52d0 	ands.w	r2, ip, r0, lsr #23
 80006e8:	bf1e      	ittt	ne
 80006ea:	ea1c 53d1 	andsne.w	r3, ip, r1, lsr #23
 80006ee:	ea92 0f0c 	teqne	r2, ip
 80006f2:	ea93 0f0c 	teqne	r3, ip
 80006f6:	d06f      	beq.n	80007d8 <__aeabi_fmul+0xf8>
 80006f8:	441a      	add	r2, r3
 80006fa:	ea80 0c01 	eor.w	ip, r0, r1
 80006fe:	0240      	lsls	r0, r0, #9
 8000700:	bf18      	it	ne
 8000702:	ea5f 2141 	movsne.w	r1, r1, lsl #9
 8000706:	d01e      	beq.n	8000746 <__aeabi_fmul+0x66>
 8000708:	f04f 6300 	mov.w	r3, #134217728	@ 0x8000000
 800070c:	ea43 1050 	orr.w	r0, r3, r0, lsr #5
 8000710:	ea43 1151 	orr.w	r1, r3, r1, lsr #5
 8000714:	fba0 3101 	umull	r3, r1, r0, r1
 8000718:	f00c 4000 	and.w	r0, ip, #2147483648	@ 0x80000000
 800071c:	f5b1 0f00 	cmp.w	r1, #8388608	@ 0x800000
 8000720:	bf3e      	ittt	cc
 8000722:	0049      	lslcc	r1, r1, #1
 8000724:	ea41 71d3 	orrcc.w	r1, r1, r3, lsr #31
 8000728:	005b      	lslcc	r3, r3, #1
 800072a:	ea40 0001 	orr.w	r0, r0, r1
 800072e:	f162 027f 	sbc.w	r2, r2, #127	@ 0x7f
 8000732:	2afd      	cmp	r2, #253	@ 0xfd
 8000734:	d81d      	bhi.n	8000772 <__aeabi_fmul+0x92>
 8000736:	f1b3 4f00 	cmp.w	r3, #2147483648	@ 0x80000000
 800073a:	eb40 50c2 	adc.w	r0, r0, r2, lsl #23
 800073e:	bf08      	it	eq
 8000740:	f020 0001 	biceq.w	r0, r0, #1
 8000744:	4770      	bx	lr
 8000746:	f090 0f00 	teq	r0, #0
 800074a:	f00c 4c00 	and.w	ip, ip, #2147483648	@ 0x80000000
 800074e:	bf08      	it	eq
 8000750:	0249      	lsleq	r1, r1, #9
 8000752:	ea4c 2050 	orr.w	r0, ip, r0, lsr #9
 8000756:	ea40 2051 	orr.w	r0, r0, r1, lsr #9
 800075a:	3a7f      	subs	r2, #127	@ 0x7f
 800075c:	bfc2      	ittt	gt
 800075e:	f1d2 03ff 	rsbsgt	r3, r2, #255	@ 0xff
 8000762:	ea40 50c2 	orrgt.w	r0, r0, r2, lsl #23
 8000766:	4770      	bxgt	lr
 8000768:	f440 0000 	orr.w	r0, r0, #8388608	@ 0x800000
 800076c:	f04f 0300 	mov.w	r3, #0
 8000770:	3a01      	subs	r2, #1
 8000772:	dc5d      	bgt.n	8000830 <__aeabi_fmul+0x150>
 8000774:	f112 0f19 	cmn.w	r2, #25
 8000778:	bfdc      	itt	le
 800077a:	f000 4000 	andle.w	r0, r0, #2147483648	@ 0x80000000
 800077e:	4770      	bxle	lr
 8000780:	f1c2 0200 	rsb	r2, r2, #0
 8000784:	0041      	lsls	r1, r0, #1
 8000786:	fa21 f102 	lsr.w	r1, r1, r2
 800078a:	f1c2 0220 	rsb	r2, r2, #32
 800078e:	fa00 fc02 	lsl.w	ip, r0, r2
 8000792:	ea5f 0031 	movs.w	r0, r1, rrx
 8000796:	f140 0000 	adc.w	r0, r0, #0
 800079a:	ea53 034c 	orrs.w	r3, r3, ip, lsl #1
 800079e:	bf08      	it	eq
 80007a0:	ea20 70dc 	biceq.w	r0, r0, ip, lsr #31
 80007a4:	4770      	bx	lr
 80007a6:	f092 0f00 	teq	r2, #0
 80007aa:	f000 4c00 	and.w	ip, r0, #2147483648	@ 0x80000000
 80007ae:	bf02      	ittt	eq
 80007b0:	0040      	lsleq	r0, r0, #1
 80007b2:	f410 0f00 	tsteq.w	r0, #8388608	@ 0x800000
 80007b6:	3a01      	subeq	r2, #1
 80007b8:	d0f9      	beq.n	80007ae <__aeabi_fmul+0xce>
 80007ba:	ea40 000c 	orr.w	r0, r0, ip
 80007be:	f093 0f00 	teq	r3, #0
 80007c2:	f001 4c00 	and.w	ip, r1, #2147483648	@ 0x80000000
 80007c6:	bf02      	ittt	eq
 80007c8:	0049      	lsleq	r1, r1, #1
 80007ca:	f411 0f00 	tsteq.w	r1, #8388608	@ 0x800000
 80007ce:	3b01      	subeq	r3, #1
 80007d0:	d0f9      	beq.n	80007c6 <__aeabi_fmul+0xe6>
 80007d2:	ea41 010c 	orr.w	r1, r1, ip
 80007d6:	e78f      	b.n	80006f8 <__aeabi_fmul+0x18>
 80007d8:	ea0c 53d1 	and.w	r3, ip, r1, lsr #23
 80007dc:	ea92 0f0c 	teq	r2, ip
 80007e0:	bf18      	it	ne
 80007e2:	ea93 0f0c 	teqne	r3, ip
 80007e6:	d00a      	beq.n	80007fe <__aeabi_fmul+0x11e>
 80007e8:	f030 4c00 	bics.w	ip, r0, #2147483648	@ 0x80000000
 80007ec:	bf18      	it	ne
 80007ee:	f031 4c00 	bicsne.w	ip, r1, #2147483648	@ 0x80000000
 80007f2:	d1d8      	bne.n	80007a6 <__aeabi_fmul+0xc6>
 80007f4:	ea80 0001 	eor.w	r0, r0, r1
 80007f8:	f000 4000 	and.w	r0, r0, #2147483648	@ 0x80000000
 80007fc:	4770      	bx	lr
 80007fe:	f090 0f00 	teq	r0, #0
 8000802:	bf17      	itett	ne
 8000804:	f090 4f00 	teqne	r0, #2147483648	@ 0x80000000
 8000808:	4608      	moveq	r0, r1
 800080a:	f091 0f00 	teqne	r1, #0
 800080e:	f091 4f00 	teqne	r1, #2147483648	@ 0x80000000
 8000812:	d014      	beq.n	800083e <__aeabi_fmul+0x15e>
 8000814:	ea92 0f0c 	teq	r2, ip
 8000818:	d101      	bne.n	800081e <__aeabi_fmul+0x13e>
 800081a:	0242      	lsls	r2, r0, #9
 800081c:	d10f      	bne.n	800083e <__aeabi_fmul+0x15e>
 800081e:	ea93 0f0c 	teq	r3, ip
 8000822:	d103      	bne.n	800082c <__aeabi_fmul+0x14c>
 8000824:	024b      	lsls	r3, r1, #9
 8000826:	bf18      	it	ne
 8000828:	4608      	movne	r0, r1
 800082a:	d108      	bne.n	800083e <__aeabi_fmul+0x15e>
 800082c:	ea80 0001 	eor.w	r0, r0, r1
 8000830:	f000 4000 	and.w	r0, r0, #2147483648	@ 0x80000000
 8000834:	f040 40fe 	orr.w	r0, r0, #2130706432	@ 0x7f000000
 8000838:	f440 0000 	orr.w	r0, r0, #8388608	@ 0x800000
 800083c:	4770      	bx	lr
 800083e:	f040 40fe 	orr.w	r0, r0, #2130706432	@ 0x7f000000
 8000842:	f440 0040 	orr.w	r0, r0, #12582912	@ 0xc00000
 8000846:	4770      	bx	lr

08000848 <__aeabi_fdiv>:
 8000848:	f04f 0cff 	mov.w	ip, #255	@ 0xff
 800084c:	ea1c 52d0 	ands.w	r2, ip, r0, lsr #23
 8000850:	bf1e      	ittt	ne
 8000852:	ea1c 53d1 	andsne.w	r3, ip, r1, lsr #23
 8000856:	ea92 0f0c 	teqne	r2, ip
 800085a:	ea93 0f0c 	teqne	r3, ip
 800085e:	d069      	beq.n	8000934 <__aeabi_fdiv+0xec>
 8000860:	eba2 0203 	sub.w	r2, r2, r3
 8000864:	ea80 0c01 	eor.w	ip, r0, r1
 8000868:	0249      	lsls	r1, r1, #9
 800086a:	ea4f 2040 	mov.w	r0, r0, lsl #9
 800086e:	d037      	beq.n	80008e0 <__aeabi_fdiv+0x98>
 8000870:	f04f 5380 	mov.w	r3, #268435456	@ 0x10000000
 8000874:	ea43 1111 	orr.w	r1, r3, r1, lsr #4
 8000878:	ea43 1310 	orr.w	r3, r3, r0, lsr #4
 800087c:	f00c 4000 	and.w	r0, ip, #2147483648	@ 0x80000000
 8000880:	428b      	cmp	r3, r1
 8000882:	bf38      	it	cc
 8000884:	005b      	lslcc	r3, r3, #1
 8000886:	f142 027d 	adc.w	r2, r2, #125	@ 0x7d
 800088a:	f44f 0c00 	mov.w	ip, #8388608	@ 0x800000
 800088e:	428b      	cmp	r3, r1
 8000890:	bf24      	itt	cs
 8000892:	1a5b      	subcs	r3, r3, r1
 8000894:	ea40 000c 	orrcs.w	r0, r0, ip
 8000898:	ebb3 0f51 	cmp.w	r3, r1, lsr #1
 800089c:	bf24      	itt	cs
 800089e:	eba3 0351 	subcs.w	r3, r3, r1, lsr #1
 80008a2:	ea40 005c 	orrcs.w	r0, r0, ip, lsr #1
 80008a6:	ebb3 0f91 	cmp.w	r3, r1, lsr #2
 80008aa:	bf24      	itt	cs
 80008ac:	eba3 0391 	subcs.w	r3, r3, r1, lsr #2
 80008b0:	ea40 009c 	orrcs.w	r0, r0, ip, lsr #2
 80008b4:	ebb3 0fd1 	cmp.w	r3, r1, lsr #3
 80008b8:	bf24      	itt	cs
 80008ba:	eba3 03d1 	subcs.w	r3, r3, r1, lsr #3
 80008be:	ea40 00dc 	orrcs.w	r0, r0, ip, lsr #3
 80008c2:	011b      	lsls	r3, r3, #4
 80008c4:	bf18      	it	ne
 80008c6:	ea5f 1c1c 	movsne.w	ip, ip, lsr #4
 80008ca:	d1e0      	bne.n	800088e <__aeabi_fdiv+0x46>
 80008cc:	2afd      	cmp	r2, #253	@ 0xfd
 80008ce:	f63f af50 	bhi.w	8000772 <__aeabi_fmul+0x92>
 80008d2:	428b      	cmp	r3, r1
 80008d4:	eb40 50c2 	adc.w	r0, r0, r2, lsl #23
 80008d8:	bf08      	it	eq
 80008da:	f020 0001 	biceq.w	r0, r0, #1
 80008de:	4770      	bx	lr
 80008e0:	f00c 4c00 	and.w	ip, ip, #2147483648	@ 0x80000000
 80008e4:	ea4c 2050 	orr.w	r0, ip, r0, lsr #9
 80008e8:	327f      	adds	r2, #127	@ 0x7f
 80008ea:	bfc2      	ittt	gt
 80008ec:	f1d2 03ff 	rsbsgt	r3, r2, #255	@ 0xff
 80008f0:	ea40 50c2 	orrgt.w	r0, r0, r2, lsl #23
 80008f4:	4770      	bxgt	lr
 80008f6:	f440 0000 	orr.w	r0, r0, #8388608	@ 0x800000
 80008fa:	f04f 0300 	mov.w	r3, #0
 80008fe:	3a01      	subs	r2, #1
 8000900:	e737      	b.n	8000772 <__aeabi_fmul+0x92>
 8000902:	f092 0f00 	teq	r2, #0
 8000906:	f000 4c00 	and.w	ip, r0, #2147483648	@ 0x80000000
 800090a:	bf02      	ittt	eq
 800090c:	0040      	lsleq	r0, r0, #1
 800090e:	f410 0f00 	tsteq.w	r0, #8388608	@ 0x800000
 8000912:	3a01      	subeq	r2, #1
 8000914:	d0f9      	beq.n	800090a <__aeabi_fdiv+0xc2>
 8000916:	ea40 000c 	orr.w	r0, r0, ip
 800091a:	f093 0f00 	teq	r3, #0
 800091e:	f001 4c00 	and.w	ip, r1, #2147483648	@ 0x80000000
 8000922:	bf02      	ittt	eq
 8000924:	0049      	lsleq	r1, r1, #1
 8000926:	f411 0f00 	tsteq.w	r1, #8388608	@ 0x800000
 800092a:	3b01      	subeq	r3, #1
 800092c:	d0f9      	beq.n	8000922 <__aeabi_fdiv+0xda>
 800092e:	ea41 010c 	orr.w	r1, r1, ip
 8000932:	e795      	b.n	8000860 <__aeabi_fdiv+0x18>
 8000934:	ea0c 53d1 	and.w	r3, ip, r1, lsr #23
 8000938:	ea92 0f0c 	teq	r2, ip
 800093c:	d108      	bne.n	8000950 <__aeabi_fdiv+0x108>
 800093e:	0242      	lsls	r2, r0, #9
 8000940:	f47f af7d 	bne.w	800083e <__aeabi_fmul+0x15e>
 8000944:	ea93 0f0c 	teq	r3, ip
 8000948:	f47f af70 	bne.w	800082c <__aeabi_fmul+0x14c>
 800094c:	4608      	mov	r0, r1
 800094e:	e776      	b.n	800083e <__aeabi_fmul+0x15e>
 8000950:	ea93 0f0c 	teq	r3, ip
 8000954:	d104      	bne.n	8000960 <__aeabi_fdiv+0x118>
 8000956:	024b      	lsls	r3, r1, #9
 8000958:	f43f af4c 	beq.w	80007f4 <__aeabi_fmul+0x114>
 800095c:	4608      	mov	r0, r1
 800095e:	e76e      	b.n	800083e <__aeabi_fmul+0x15e>
 8000960:	f030 4c00 	bics.w	ip, r0, #2147483648	@ 0x80000000
 8000964:	bf18      	it	ne
 8000966:	f031 4c00 	bicsne.w	ip, r1, #2147483648	@ 0x80000000
 800096a:	d1ca      	bne.n	8000902 <__aeabi_fdiv+0xba>
 800096c:	f030 4200 	bics.w	r2, r0, #2147483648	@ 0x80000000
 8000970:	f47f af5c 	bne.w	800082c <__aeabi_fmul+0x14c>
 8000974:	f031 4300 	bics.w	r3, r1, #2147483648	@ 0x80000000
 8000978:	f47f af3c 	bne.w	80007f4 <__aeabi_fmul+0x114>
 800097c:	e75f      	b.n	800083e <__aeabi_fmul+0x15e>
 800097e:	bf00      	nop

08000980 <MX_GPIO_Init>:
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
 8000980:	b530      	push	{r4, r5, lr}
 8000982:	b087      	sub	sp, #28
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 8000984:	2400      	movs	r4, #0
 8000986:	9402      	str	r4, [sp, #8]
 8000988:	9403      	str	r4, [sp, #12]
 800098a:	9404      	str	r4, [sp, #16]
 800098c:	9405      	str	r4, [sp, #20]
/* USER CODE BEGIN MX_GPIO_Init_1 */
/* USER CODE END MX_GPIO_Init_1 */

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOB_CLK_ENABLE();
 800098e:	4b14      	ldr	r3, [pc, #80]	@ (80009e0 <MX_GPIO_Init+0x60>)
 8000990:	699a      	ldr	r2, [r3, #24]
 8000992:	f042 0208 	orr.w	r2, r2, #8
 8000996:	619a      	str	r2, [r3, #24]
 8000998:	699a      	ldr	r2, [r3, #24]
 800099a:	f002 0208 	and.w	r2, r2, #8
 800099e:	9200      	str	r2, [sp, #0]
 80009a0:	9a00      	ldr	r2, [sp, #0]
  __HAL_RCC_GPIOA_CLK_ENABLE();
 80009a2:	699a      	ldr	r2, [r3, #24]
 80009a4:	f042 0204 	orr.w	r2, r2, #4
 80009a8:	619a      	str	r2, [r3, #24]
 80009aa:	699b      	ldr	r3, [r3, #24]
 80009ac:	f003 0304 	and.w	r3, r3, #4
 80009b0:	9301      	str	r3, [sp, #4]
 80009b2:	9b01      	ldr	r3, [sp, #4]

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET);
 80009b4:	4d0b      	ldr	r5, [pc, #44]	@ (80009e4 <MX_GPIO_Init+0x64>)
 80009b6:	4622      	mov	r2, r4
 80009b8:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 80009bc:	4628      	mov	r0, r5
 80009be:	f000 ffa1 	bl	8001904 <HAL_GPIO_WritePin>

  /*Configure GPIO pin : PB13 */
  GPIO_InitStruct.Pin = GPIO_PIN_13;
 80009c2:	f44f 5300 	mov.w	r3, #8192	@ 0x2000
 80009c6:	9302      	str	r3, [sp, #8]
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 80009c8:	2301      	movs	r3, #1
 80009ca:	9303      	str	r3, [sp, #12]
  GPIO_InitStruct.Pull = GPIO_NOPULL;
 80009cc:	9404      	str	r4, [sp, #16]
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 80009ce:	2302      	movs	r3, #2
 80009d0:	9305      	str	r3, [sp, #20]
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 80009d2:	a902      	add	r1, sp, #8
 80009d4:	4628      	mov	r0, r5
 80009d6:	f000 fe87 	bl	80016e8 <HAL_GPIO_Init>

/* USER CODE BEGIN MX_GPIO_Init_2 */
/* USER CODE END MX_GPIO_Init_2 */
}
 80009da:	b007      	add	sp, #28
 80009dc:	bd30      	pop	{r4, r5, pc}
 80009de:	bf00      	nop
 80009e0:	40021000 	.word	0x40021000
 80009e4:	40010c00 	.word	0x40010c00

080009e8 <goto_application>:
    }
  }
}

static void goto_application( void )
{
 80009e8:	b510      	push	{r4, lr}
	printf("Gonna Jump to Application...\n");
 80009ea:	480d      	ldr	r0, [pc, #52]	@ (8000a20 <goto_application+0x38>)
 80009ec:	f001 fe2a 	bl	8002644 <puts>
	void (*app_reset_handler)(void) = (void*)(*((volatile uint32_t*)(ETX_APP_START_ADDRESS + 4U)));
 80009f0:	4b0c      	ldr	r3, [pc, #48]	@ (8000a24 <goto_application+0x3c>)
 80009f2:	f8d3 4404 	ldr.w	r4, [r3, #1028]	@ 0x404

	if( app_reset_handler == (void*)0xFFFFFFFF )
 80009f6:	f1b4 3fff 	cmp.w	r4, #4294967295
 80009fa:	d00c      	beq.n	8000a16 <goto_application+0x2e>
	{
	  printf("Invalid Application... HALT!!!\r\n");
	  while(1);
	}

	__set_MSP(*(volatile uint32_t*) ETX_APP_START_ADDRESS);
 80009fc:	4b09      	ldr	r3, [pc, #36]	@ (8000a24 <goto_application+0x3c>)
 80009fe:	f8d3 3400 	ldr.w	r3, [r3, #1024]	@ 0x400
  \details Assigns the given value to the Main Stack Pointer (MSP).
  \param [in]    topOfMainStack  Main Stack Pointer value to set
 */
__STATIC_FORCEINLINE void __set_MSP(uint32_t topOfMainStack)
{
  __ASM volatile ("MSR msp, %0" : : "r" (topOfMainStack) : );
 8000a02:	f383 8808 	msr	MSP, r3

	// Turn OFF the Led to tell the user that Bootloader is not running
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET );
 8000a06:	2200      	movs	r2, #0
 8000a08:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000a0c:	4806      	ldr	r0, [pc, #24]	@ (8000a28 <goto_application+0x40>)
 8000a0e:	f000 ff79 	bl	8001904 <HAL_GPIO_WritePin>

	app_reset_handler();    //call the app reset handler
 8000a12:	47a0      	blx	r4
}
 8000a14:	bd10      	pop	{r4, pc}
	  printf("Invalid Application... HALT!!!\r\n");
 8000a16:	4805      	ldr	r0, [pc, #20]	@ (8000a2c <goto_application+0x44>)
 8000a18:	f001 fe14 	bl	8002644 <puts>
	  while(1);
 8000a1c:	e7fe      	b.n	8000a1c <goto_application+0x34>
 8000a1e:	bf00      	nop
 8000a20:	08003358 	.word	0x08003358
 8000a24:	08004000 	.word	0x08004000
 8000a28:	40010c00 	.word	0x40010c00
 8000a2c:	08003378 	.word	0x08003378

08000a30 <UART_Write_Loop>:
{
 8000a30:	b530      	push	{r4, r5, lr}
 8000a32:	b083      	sub	sp, #12
  printf("\n Press 'o' to start Firmware Update...\r\n");
 8000a34:	481b      	ldr	r0, [pc, #108]	@ (8000aa4 <UART_Write_Loop+0x74>)
 8000a36:	f001 fe05 	bl	8002644 <puts>
  char tx = '.';
 8000a3a:	232e      	movs	r3, #46	@ 0x2e
 8000a3c:	f88d 3007 	strb.w	r3, [sp, #7]
  char rx = '0';
 8000a40:	2330      	movs	r3, #48	@ 0x30
 8000a42:	f88d 3006 	strb.w	r3, [sp, #6]
  int count = 0;
 8000a46:	2500      	movs	r5, #0
 8000a48:	e005      	b.n	8000a56 <UART_Write_Loop+0x26>
    if( count == 250 )  // Changed from 100 to 250 to make it 5 seconds (250 * 20ms = 5000ms = 5 seconds)
 8000a4a:	2dfa      	cmp	r5, #250	@ 0xfa
 8000a4c:	d024      	beq.n	8000a98 <UART_Write_Loop+0x68>
    count++;
 8000a4e:	3501      	adds	r5, #1
    HAL_Delay(20);              //20ms delay
 8000a50:	2014      	movs	r0, #20
 8000a52:	f000 fc67 	bl	8001324 <HAL_Delay>
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_13);
 8000a56:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000a5a:	4813      	ldr	r0, [pc, #76]	@ (8000aa8 <UART_Write_Loop+0x78>)
 8000a5c:	f000 ff58 	bl	8001910 <HAL_GPIO_TogglePin>
    HAL_UART_Transmit(&huart3, (uint8_t *)&tx, 1, HAL_MAX_DELAY);
 8000a60:	4c12      	ldr	r4, [pc, #72]	@ (8000aac <UART_Write_Loop+0x7c>)
 8000a62:	f04f 33ff 	mov.w	r3, #4294967295
 8000a66:	2201      	movs	r2, #1
 8000a68:	f10d 0107 	add.w	r1, sp, #7
 8000a6c:	4620      	mov	r0, r4
 8000a6e:	f001 fb68 	bl	8002142 <HAL_UART_Transmit>
    ex = HAL_UART_Receive(&huart3, (uint8_t *)&rx, 1, 20);  // Changed from 10ms to 20ms to give Arduino more time to respond
 8000a72:	2314      	movs	r3, #20
 8000a74:	2201      	movs	r2, #1
 8000a76:	f10d 0106 	add.w	r1, sp, #6
 8000a7a:	4620      	mov	r0, r4
 8000a7c:	f001 fbc7 	bl	800220e <HAL_UART_Receive>
    if( ( ex == HAL_OK ) && ( rx == 'o' ) )
 8000a80:	2800      	cmp	r0, #0
 8000a82:	d1e2      	bne.n	8000a4a <UART_Write_Loop+0x1a>
 8000a84:	f89d 3006 	ldrb.w	r3, [sp, #6]
 8000a88:	2b6f      	cmp	r3, #111	@ 0x6f
 8000a8a:	d1de      	bne.n	8000a4a <UART_Write_Loop+0x1a>
      printf("Firmware Update Started\r\n");
 8000a8c:	4808      	ldr	r0, [pc, #32]	@ (8000ab0 <UART_Write_Loop+0x80>)
 8000a8e:	f001 fdd9 	bl	8002644 <puts>
      ret = 1;
 8000a92:	2001      	movs	r0, #1
}
 8000a94:	b003      	add	sp, #12
 8000a96:	bd30      	pop	{r4, r5, pc}
      printf("No Data Received for Firmware Update\r\n");
 8000a98:	4806      	ldr	r0, [pc, #24]	@ (8000ab4 <UART_Write_Loop+0x84>)
 8000a9a:	f001 fdd3 	bl	8002644 <puts>
  int ret = 0;
 8000a9e:	2000      	movs	r0, #0
      break;
 8000aa0:	e7f8      	b.n	8000a94 <UART_Write_Loop+0x64>
 8000aa2:	bf00      	nop
 8000aa4:	08003398 	.word	0x08003398
 8000aa8:	40010c00 	.word	0x40010c00
 8000aac:	20000094 	.word	0x20000094
 8000ab0:	080033c4 	.word	0x080033c4
 8000ab4:	080033e0 	.word	0x080033e0

08000ab8 <receive_ascii_number>:
{
 8000ab8:	b510      	push	{r4, lr}
 8000aba:	b086      	sub	sp, #24
  char buffer[16] = {0};
 8000abc:	2400      	movs	r4, #0
 8000abe:	9402      	str	r4, [sp, #8]
 8000ac0:	9403      	str	r4, [sp, #12]
 8000ac2:	9404      	str	r4, [sp, #16]
 8000ac4:	9405      	str	r4, [sp, #20]
  printf("Enter number: ");
 8000ac6:	4825      	ldr	r0, [pc, #148]	@ (8000b5c <receive_ascii_number+0xa4>)
 8000ac8:	f001 fd54 	bl	8002574 <iprintf>
  while(index < 15)
 8000acc:	e015      	b.n	8000afa <receive_ascii_number+0x42>
      printf("Timeout waiting for input\r\n");
 8000ace:	4824      	ldr	r0, [pc, #144]	@ (8000b60 <receive_ascii_number+0xa8>)
 8000ad0:	f001 fdb8 	bl	8002644 <puts>
      return 0;
 8000ad4:	2000      	movs	r0, #0
 8000ad6:	e00c      	b.n	8000af2 <receive_ascii_number+0x3a>
      printf("\r\n");
 8000ad8:	4822      	ldr	r0, [pc, #136]	@ (8000b64 <receive_ascii_number+0xac>)
 8000ada:	f001 fdb3 	bl	8002644 <puts>
  buffer[index] = '\0';
 8000ade:	f104 0318 	add.w	r3, r4, #24
 8000ae2:	eb0d 0403 	add.w	r4, sp, r3
 8000ae6:	2300      	movs	r3, #0
 8000ae8:	f804 3c10 	strb.w	r3, [r4, #-16]
  return atoi(buffer);
 8000aec:	a802      	add	r0, sp, #8
 8000aee:	f001 fbf7 	bl	80022e0 <atoi>
}
 8000af2:	b006      	add	sp, #24
 8000af4:	bd10      	pop	{r4, pc}
    else if(received_char == '\b' && index > 0) // Backspace
 8000af6:	2b08      	cmp	r3, #8
 8000af8:	d027      	beq.n	8000b4a <receive_ascii_number+0x92>
  while(index < 15)
 8000afa:	2c0e      	cmp	r4, #14
 8000afc:	d8ef      	bhi.n	8000ade <receive_ascii_number+0x26>
    status = HAL_UART_Receive(&huart3, &received_char, 1, 10000); // 10 second timeout
 8000afe:	f242 7310 	movw	r3, #10000	@ 0x2710
 8000b02:	2201      	movs	r2, #1
 8000b04:	f10d 0107 	add.w	r1, sp, #7
 8000b08:	4817      	ldr	r0, [pc, #92]	@ (8000b68 <receive_ascii_number+0xb0>)
 8000b0a:	f001 fb80 	bl	800220e <HAL_UART_Receive>
    if(status != HAL_OK)
 8000b0e:	2800      	cmp	r0, #0
 8000b10:	d1dd      	bne.n	8000ace <receive_ascii_number+0x16>
    HAL_UART_Transmit(&huart3, &received_char, 1, HAL_MAX_DELAY);
 8000b12:	f04f 33ff 	mov.w	r3, #4294967295
 8000b16:	2201      	movs	r2, #1
 8000b18:	f10d 0107 	add.w	r1, sp, #7
 8000b1c:	4812      	ldr	r0, [pc, #72]	@ (8000b68 <receive_ascii_number+0xb0>)
 8000b1e:	f001 fb10 	bl	8002142 <HAL_UART_Transmit>
    if(received_char == '\r' || received_char == '\n')
 8000b22:	f89d 3007 	ldrb.w	r3, [sp, #7]
 8000b26:	2b0d      	cmp	r3, #13
 8000b28:	d0d6      	beq.n	8000ad8 <receive_ascii_number+0x20>
 8000b2a:	2b0a      	cmp	r3, #10
 8000b2c:	d0d4      	beq.n	8000ad8 <receive_ascii_number+0x20>
    else if(received_char >= '0' && received_char <= '9')
 8000b2e:	f1a3 0230 	sub.w	r2, r3, #48	@ 0x30
 8000b32:	b2d2      	uxtb	r2, r2
 8000b34:	2a09      	cmp	r2, #9
 8000b36:	d8de      	bhi.n	8000af6 <receive_ascii_number+0x3e>
      buffer[index++] = received_char;
 8000b38:	1c62      	adds	r2, r4, #1
 8000b3a:	f104 0118 	add.w	r1, r4, #24
 8000b3e:	eb0d 0401 	add.w	r4, sp, r1
 8000b42:	f804 3c10 	strb.w	r3, [r4, #-16]
 8000b46:	b2d4      	uxtb	r4, r2
 8000b48:	e7d7      	b.n	8000afa <receive_ascii_number+0x42>
    else if(received_char == '\b' && index > 0) // Backspace
 8000b4a:	2c00      	cmp	r4, #0
 8000b4c:	d0d5      	beq.n	8000afa <receive_ascii_number+0x42>
      index--;
 8000b4e:	3c01      	subs	r4, #1
 8000b50:	b2e4      	uxtb	r4, r4
      printf(" \b"); // Erase character on terminal
 8000b52:	4806      	ldr	r0, [pc, #24]	@ (8000b6c <receive_ascii_number+0xb4>)
 8000b54:	f001 fd0e 	bl	8002574 <iprintf>
 8000b58:	e7cf      	b.n	8000afa <receive_ascii_number+0x42>
 8000b5a:	bf00      	nop
 8000b5c:	08003408 	.word	0x08003408
 8000b60:	08003418 	.word	0x08003418
 8000b64:	080034a8 	.word	0x080034a8
 8000b68:	20000094 	.word	0x20000094
 8000b6c:	08003434 	.word	0x08003434

08000b70 <auto_detect_firmware_size>:
{
 8000b70:	b5f0      	push	{r4, r5, r6, r7, lr}
 8000b72:	b083      	sub	sp, #12
  printf("Auto-detecting firmware size...\r\n");
 8000b74:	4826      	ldr	r0, [pc, #152]	@ (8000c10 <auto_detect_firmware_size+0xa0>)
 8000b76:	f001 fd65 	bl	8002644 <puts>
  printf("Send firmware data continuously (end with 'END' or timeout after 30 seconds)\r\n");
 8000b7a:	4826      	ldr	r0, [pc, #152]	@ (8000c14 <auto_detect_firmware_size+0xa4>)
 8000b7c:	f001 fd62 	bl	8002644 <puts>
  uint32_t start_time = HAL_GetTick();
 8000b80:	f000 fbca 	bl	8001318 <HAL_GetTick>
 8000b84:	4607      	mov	r7, r0
  uint8_t end_sequence[3] = {0};
 8000b86:	4b24      	ldr	r3, [pc, #144]	@ (8000c18 <auto_detect_firmware_size+0xa8>)
 8000b88:	881b      	ldrh	r3, [r3, #0]
 8000b8a:	f8ad 3004 	strh.w	r3, [sp, #4]
 8000b8e:	2500      	movs	r5, #0
 8000b90:	f88d 5006 	strb.w	r5, [sp, #6]
  uint8_t end_index = 0;
 8000b94:	462e      	mov	r6, r5
 8000b96:	e00d      	b.n	8000bb4 <auto_detect_firmware_size+0x44>
      if(received_byte == 'E' && end_index == 0) end_index = 1;
 8000b98:	b9d6      	cbnz	r6, 8000bd0 <auto_detect_firmware_size+0x60>
 8000b9a:	2601      	movs	r6, #1
 8000b9c:	e006      	b.n	8000bac <auto_detect_firmware_size+0x3c>
      else if(received_byte == 'N' && end_index == 1) end_index = 2;
 8000b9e:	2e01      	cmp	r6, #1
 8000ba0:	d118      	bne.n	8000bd4 <auto_detect_firmware_size+0x64>
 8000ba2:	2602      	movs	r6, #2
 8000ba4:	e002      	b.n	8000bac <auto_detect_firmware_size+0x3c>
      else if(received_byte == 'D' && end_index == 2)
 8000ba6:	2e02      	cmp	r6, #2
 8000ba8:	d018      	beq.n	8000bdc <auto_detect_firmware_size+0x6c>
      else end_index = 0;
 8000baa:	4616      	mov	r6, r2
      if(byte_count % 1024 == 0)
 8000bac:	f3c4 0309 	ubfx	r3, r4, #0, #10
 8000bb0:	b1d3      	cbz	r3, 8000be8 <auto_detect_firmware_size+0x78>
      byte_count++;
 8000bb2:	4625      	mov	r5, r4
    status = HAL_UART_Receive(&huart3, &received_byte, 1, 100); // 100ms timeout per byte
 8000bb4:	2364      	movs	r3, #100	@ 0x64
 8000bb6:	2201      	movs	r2, #1
 8000bb8:	f10d 0107 	add.w	r1, sp, #7
 8000bbc:	4817      	ldr	r0, [pc, #92]	@ (8000c1c <auto_detect_firmware_size+0xac>)
 8000bbe:	f001 fb26 	bl	800220e <HAL_UART_Receive>
    if(status == HAL_OK)
 8000bc2:	4602      	mov	r2, r0
 8000bc4:	b9b0      	cbnz	r0, 8000bf4 <auto_detect_firmware_size+0x84>
      byte_count++;
 8000bc6:	1c6c      	adds	r4, r5, #1
      if(received_byte == 'E' && end_index == 0) end_index = 1;
 8000bc8:	f89d 3007 	ldrb.w	r3, [sp, #7]
 8000bcc:	2b45      	cmp	r3, #69	@ 0x45
 8000bce:	d0e3      	beq.n	8000b98 <auto_detect_firmware_size+0x28>
      else if(received_byte == 'N' && end_index == 1) end_index = 2;
 8000bd0:	2b4e      	cmp	r3, #78	@ 0x4e
 8000bd2:	d0e4      	beq.n	8000b9e <auto_detect_firmware_size+0x2e>
      else if(received_byte == 'D' && end_index == 2)
 8000bd4:	2b44      	cmp	r3, #68	@ 0x44
 8000bd6:	d0e6      	beq.n	8000ba6 <auto_detect_firmware_size+0x36>
      else end_index = 0;
 8000bd8:	4616      	mov	r6, r2
 8000bda:	e7e7      	b.n	8000bac <auto_detect_firmware_size+0x3c>
        byte_count -= 3; // Don't count the "END" sequence
 8000bdc:	3d02      	subs	r5, #2
        printf("End sequence detected. Firmware size: %lu bytes\r\n", byte_count);
 8000bde:	4629      	mov	r1, r5
 8000be0:	480f      	ldr	r0, [pc, #60]	@ (8000c20 <auto_detect_firmware_size+0xb0>)
 8000be2:	f001 fcc7 	bl	8002574 <iprintf>
        return byte_count;
 8000be6:	e010      	b.n	8000c0a <auto_detect_firmware_size+0x9a>
        printf("Received %lu bytes...\r\n", byte_count);
 8000be8:	4621      	mov	r1, r4
 8000bea:	480e      	ldr	r0, [pc, #56]	@ (8000c24 <auto_detect_firmware_size+0xb4>)
 8000bec:	f001 fcc2 	bl	8002574 <iprintf>
      byte_count++;
 8000bf0:	4625      	mov	r5, r4
 8000bf2:	e7df      	b.n	8000bb4 <auto_detect_firmware_size+0x44>
      if(HAL_GetTick() - start_time > 30000)
 8000bf4:	f000 fb90 	bl	8001318 <HAL_GetTick>
 8000bf8:	1bc0      	subs	r0, r0, r7
 8000bfa:	f247 5330 	movw	r3, #30000	@ 0x7530
 8000bfe:	4298      	cmp	r0, r3
 8000c00:	d9d8      	bls.n	8000bb4 <auto_detect_firmware_size+0x44>
        printf("Auto-detection timeout. Detected size: %lu bytes\r\n", byte_count);
 8000c02:	4629      	mov	r1, r5
 8000c04:	4808      	ldr	r0, [pc, #32]	@ (8000c28 <auto_detect_firmware_size+0xb8>)
 8000c06:	f001 fcb5 	bl	8002574 <iprintf>
}
 8000c0a:	4628      	mov	r0, r5
 8000c0c:	b003      	add	sp, #12
 8000c0e:	bdf0      	pop	{r4, r5, r6, r7, pc}
 8000c10:	08003438 	.word	0x08003438
 8000c14:	0800345c 	.word	0x0800345c
 8000c18:	08003354 	.word	0x08003354
 8000c1c:	20000094 	.word	0x20000094
 8000c20:	080034ac 	.word	0x080034ac
 8000c24:	080034e0 	.word	0x080034e0
 8000c28:	080034f8 	.word	0x080034f8

08000c2c <write_data_to_flash_app>:
{
 8000c2c:	b5f0      	push	{r4, r5, r6, r7, lr}
 8000c2e:	b087      	sub	sp, #28
 8000c30:	4605      	mov	r5, r0
 8000c32:	460e      	mov	r6, r1
 8000c34:	4614      	mov	r4, r2
    ret = HAL_FLASH_Unlock();
 8000c36:	f000 fc39 	bl	80014ac <HAL_FLASH_Unlock>
    if( ret != HAL_OK )
 8000c3a:	4607      	mov	r7, r0
 8000c3c:	2800      	cmp	r0, #0
 8000c3e:	d134      	bne.n	8000caa <write_data_to_flash_app+0x7e>
    if( is_first_block )
 8000c40:	b9e4      	cbnz	r4, 8000c7c <write_data_to_flash_app+0x50>
{
 8000c42:	2400      	movs	r4, #0
    for(int i = 0; i < data_len/2; i++)
 8000c44:	ebb4 0f56 	cmp.w	r4, r6, lsr #1
 8000c48:	da2e      	bge.n	8000ca8 <write_data_to_flash_app+0x7c>
      uint16_t halfword_data = data[i * 2] | (data[i * 2 + 1] << 8);
 8000c4a:	f815 3014 	ldrb.w	r3, [r5, r4, lsl #1]
 8000c4e:	eb05 0244 	add.w	r2, r5, r4, lsl #1
 8000c52:	7852      	ldrb	r2, [r2, #1]
      ret = HAL_FLASH_Program( FLASH_TYPEPROGRAM_HALFWORD,
 8000c54:	4918      	ldr	r1, [pc, #96]	@ (8000cb8 <write_data_to_flash_app+0x8c>)
 8000c56:	6809      	ldr	r1, [r1, #0]
 8000c58:	ea43 2202 	orr.w	r2, r3, r2, lsl #8
 8000c5c:	2300      	movs	r3, #0
 8000c5e:	f101 6100 	add.w	r1, r1, #134217728	@ 0x8000000
 8000c62:	f501 4188 	add.w	r1, r1, #17408	@ 0x4400
 8000c66:	2001      	movs	r0, #1
 8000c68:	f000 fc78 	bl	800155c <HAL_FLASH_Program>
      if( ret == HAL_OK )
 8000c6c:	4607      	mov	r7, r0
 8000c6e:	b9c0      	cbnz	r0, 8000ca2 <write_data_to_flash_app+0x76>
        application_write_idx += 2;
 8000c70:	4a11      	ldr	r2, [pc, #68]	@ (8000cb8 <write_data_to_flash_app+0x8c>)
 8000c72:	6813      	ldr	r3, [r2, #0]
 8000c74:	3302      	adds	r3, #2
 8000c76:	6013      	str	r3, [r2, #0]
    for(int i = 0; i < data_len/2; i++)
 8000c78:	3401      	adds	r4, #1
 8000c7a:	e7e3      	b.n	8000c44 <write_data_to_flash_app+0x18>
      printf("Erasing the Flash memory...\r\n");
 8000c7c:	480f      	ldr	r0, [pc, #60]	@ (8000cbc <write_data_to_flash_app+0x90>)
 8000c7e:	f001 fce1 	bl	8002644 <puts>
      EraseInitStruct.TypeErase     = FLASH_TYPEERASE_PAGES;
 8000c82:	2300      	movs	r3, #0
 8000c84:	9302      	str	r3, [sp, #8]
      EraseInitStruct.PageAddress   = ETX_APP_START_ADDRESS;
 8000c86:	4b0e      	ldr	r3, [pc, #56]	@ (8000cc0 <write_data_to_flash_app+0x94>)
 8000c88:	9304      	str	r3, [sp, #16]
      EraseInitStruct.NbPages       = 47;                     //47 Pages
 8000c8a:	232f      	movs	r3, #47	@ 0x2f
 8000c8c:	9305      	str	r3, [sp, #20]
      ret = HAL_FLASHEx_Erase( &EraseInitStruct, &SectorError );
 8000c8e:	a901      	add	r1, sp, #4
 8000c90:	a802      	add	r0, sp, #8
 8000c92:	f000 fcd7 	bl	8001644 <HAL_FLASHEx_Erase>
      if( ret != HAL_OK )
 8000c96:	4607      	mov	r7, r0
 8000c98:	b938      	cbnz	r0, 8000caa <write_data_to_flash_app+0x7e>
      application_write_idx = 0;
 8000c9a:	4b07      	ldr	r3, [pc, #28]	@ (8000cb8 <write_data_to_flash_app+0x8c>)
 8000c9c:	2200      	movs	r2, #0
 8000c9e:	601a      	str	r2, [r3, #0]
 8000ca0:	e7cf      	b.n	8000c42 <write_data_to_flash_app+0x16>
        printf("Flash Write Error...HALT!!!\r\n");
 8000ca2:	4808      	ldr	r0, [pc, #32]	@ (8000cc4 <write_data_to_flash_app+0x98>)
 8000ca4:	f001 fcce 	bl	8002644 <puts>
    if( ret != HAL_OK )
 8000ca8:	b117      	cbz	r7, 8000cb0 <write_data_to_flash_app+0x84>
}
 8000caa:	4638      	mov	r0, r7
 8000cac:	b007      	add	sp, #28
 8000cae:	bdf0      	pop	{r4, r5, r6, r7, pc}
    ret = HAL_FLASH_Lock();
 8000cb0:	f000 fc16 	bl	80014e0 <HAL_FLASH_Lock>
 8000cb4:	4607      	mov	r7, r0
    if( ret != HAL_OK )
 8000cb6:	e7f8      	b.n	8000caa <write_data_to_flash_app+0x7e>
 8000cb8:	2000008c 	.word	0x2000008c
 8000cbc:	0800352c 	.word	0x0800352c
 8000cc0:	08004400 	.word	0x08004400
 8000cc4:	0800354c 	.word	0x0800354c

08000cc8 <Firmware_Update>:
{
 8000cc8:	e92d 43f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, lr}
 8000ccc:	f6ad 0d24 	subw	sp, sp, #2084	@ 0x824
  uint8_t block[MAX_BLOCK_SIZE] = { 0 };
 8000cd0:	2400      	movs	r4, #0
 8000cd2:	f8cd 4420 	str.w	r4, [sp, #1056]	@ 0x420
 8000cd6:	f44f 757f 	mov.w	r5, #1020	@ 0x3fc
 8000cda:	462a      	mov	r2, r5
 8000cdc:	4621      	mov	r1, r4
 8000cde:	f20d 4024 	addw	r0, sp, #1060	@ 0x424
 8000ce2:	f001 fd8f 	bl	8002804 <memset>
  uint8_t backup_block[MAX_BLOCK_SIZE] = { 0 }; // For rollback
 8000ce6:	9408      	str	r4, [sp, #32]
 8000ce8:	462a      	mov	r2, r5
 8000cea:	4621      	mov	r1, r4
 8000cec:	a809      	add	r0, sp, #36	@ 0x24
 8000cee:	f001 fd89 	bl	8002804 <memset>
    if( UART_Write_Loop() != 0 )
 8000cf2:	f7ff fe9d 	bl	8000a30 <UART_Write_Loop>
 8000cf6:	b370      	cbz	r0, 8000d56 <Firmware_Update+0x8e>
      printf("=== Enhanced Firmware Update Started ===\r\n");
 8000cf8:	487d      	ldr	r0, [pc, #500]	@ (8000ef0 <Firmware_Update+0x228>)
 8000cfa:	f001 fca3 	bl	8002644 <puts>
      printf("Choose firmware size input method:\r\n");
 8000cfe:	487d      	ldr	r0, [pc, #500]	@ (8000ef4 <Firmware_Update+0x22c>)
 8000d00:	f001 fca0 	bl	8002644 <puts>
      printf("1. Enter size manually (ASCII decimal)\r\n");
 8000d04:	487c      	ldr	r0, [pc, #496]	@ (8000ef8 <Firmware_Update+0x230>)
 8000d06:	f001 fc9d 	bl	8002644 <puts>
      printf("2. Auto-detect from data stream\r\n");
 8000d0a:	487c      	ldr	r0, [pc, #496]	@ (8000efc <Firmware_Update+0x234>)
 8000d0c:	f001 fc9a 	bl	8002644 <puts>
      printf("3. Use default size for GPS clock (20388 bytes)\r\n");
 8000d10:	487b      	ldr	r0, [pc, #492]	@ (8000f00 <Firmware_Update+0x238>)
 8000d12:	f001 fc97 	bl	8002644 <puts>
      uint32_t choice = receive_ascii_number();
 8000d16:	f7ff fecf 	bl	8000ab8 <receive_ascii_number>
      switch(choice)
 8000d1a:	2802      	cmp	r0, #2
 8000d1c:	d036      	beq.n	8000d8c <Firmware_Update+0xc4>
 8000d1e:	2803      	cmp	r0, #3
 8000d20:	d039      	beq.n	8000d96 <Firmware_Update+0xce>
 8000d22:	2801      	cmp	r0, #1
 8000d24:	d007      	beq.n	8000d36 <Firmware_Update+0x6e>
          printf("Invalid choice. Using auto-detection...\r\n");
 8000d26:	4877      	ldr	r0, [pc, #476]	@ (8000f04 <Firmware_Update+0x23c>)
 8000d28:	f001 fc8c 	bl	8002644 <puts>
          application_size = auto_detect_firmware_size();
 8000d2c:	f7ff ff20 	bl	8000b70 <auto_detect_firmware_size>
 8000d30:	4b75      	ldr	r3, [pc, #468]	@ (8000f08 <Firmware_Update+0x240>)
 8000d32:	6018      	str	r0, [r3, #0]
          break;
 8000d34:	e006      	b.n	8000d44 <Firmware_Update+0x7c>
          printf("Enter firmware size in bytes: ");
 8000d36:	4875      	ldr	r0, [pc, #468]	@ (8000f0c <Firmware_Update+0x244>)
 8000d38:	f001 fc1c 	bl	8002574 <iprintf>
          application_size = receive_ascii_number();
 8000d3c:	f7ff febc 	bl	8000ab8 <receive_ascii_number>
 8000d40:	4b71      	ldr	r3, [pc, #452]	@ (8000f08 <Firmware_Update+0x240>)
 8000d42:	6018      	str	r0, [r3, #0]
      if(application_size == 0 || application_size > (47 * 1024))
 8000d44:	4b70      	ldr	r3, [pc, #448]	@ (8000f08 <Firmware_Update+0x240>)
 8000d46:	6819      	ldr	r1, [r3, #0]
 8000d48:	1e4b      	subs	r3, r1, #1
 8000d4a:	f5b3 4f3c 	cmp.w	r3, #48128	@ 0xbc00
 8000d4e:	d32a      	bcc.n	8000da6 <Firmware_Update+0xde>
        printf("Invalid firmware size: %lu bytes (max 48KB)\r\n", application_size);
 8000d50:	486f      	ldr	r0, [pc, #444]	@ (8000f10 <Firmware_Update+0x248>)
 8000d52:	f001 fc0f 	bl	8002574 <iprintf>
    printf("=== Firmware Update Failed ===\r\n");
 8000d56:	486f      	ldr	r0, [pc, #444]	@ (8000f14 <Firmware_Update+0x24c>)
 8000d58:	f001 fc74 	bl	8002644 <puts>
    printf("Initiating rollback procedure...\r\n");
 8000d5c:	486e      	ldr	r0, [pc, #440]	@ (8000f18 <Firmware_Update+0x250>)
 8000d5e:	f001 fc71 	bl	8002644 <puts>
    HAL_StatusTypeDef erase_status = HAL_FLASH_Unlock();
 8000d62:	f000 fba3 	bl	80014ac <HAL_FLASH_Unlock>
    if(erase_status == HAL_OK)
 8000d66:	2800      	cmp	r0, #0
 8000d68:	f000 80ac 	beq.w	8000ec4 <Firmware_Update+0x1fc>
    printf("=== Rollback Complete ===\r\n");
 8000d6c:	486b      	ldr	r0, [pc, #428]	@ (8000f1c <Firmware_Update+0x254>)
 8000d6e:	f001 fc69 	bl	8002644 <puts>
    printf("Please retry firmware update or check your firmware file.\r\n");
 8000d72:	486b      	ldr	r0, [pc, #428]	@ (8000f20 <Firmware_Update+0x258>)
 8000d74:	f001 fc66 	bl	8002644 <puts>
      HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_13); // Blink LED to indicate error
 8000d78:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000d7c:	4869      	ldr	r0, [pc, #420]	@ (8000f24 <Firmware_Update+0x25c>)
 8000d7e:	f000 fdc7 	bl	8001910 <HAL_GPIO_TogglePin>
      HAL_Delay(500);
 8000d82:	f44f 70fa 	mov.w	r0, #500	@ 0x1f4
 8000d86:	f000 facd 	bl	8001324 <HAL_Delay>
    while(1)
 8000d8a:	e7f5      	b.n	8000d78 <Firmware_Update+0xb0>
          application_size = auto_detect_firmware_size();
 8000d8c:	f7ff fef0 	bl	8000b70 <auto_detect_firmware_size>
 8000d90:	4b5d      	ldr	r3, [pc, #372]	@ (8000f08 <Firmware_Update+0x240>)
 8000d92:	6018      	str	r0, [r3, #0]
          break;
 8000d94:	e7d6      	b.n	8000d44 <Firmware_Update+0x7c>
          application_size = 20388; // Your GPS clock size
 8000d96:	f644 71a4 	movw	r1, #20388	@ 0x4fa4
 8000d9a:	4b5b      	ldr	r3, [pc, #364]	@ (8000f08 <Firmware_Update+0x240>)
 8000d9c:	6019      	str	r1, [r3, #0]
          printf("Using default GPS clock size: %lu bytes\r\n", application_size);
 8000d9e:	4862      	ldr	r0, [pc, #392]	@ (8000f28 <Firmware_Update+0x260>)
 8000da0:	f001 fbe8 	bl	8002574 <iprintf>
          break;
 8000da4:	e7ce      	b.n	8000d44 <Firmware_Update+0x7c>
      printf("Confirmed firmware size: %lu bytes\r\n", application_size);
 8000da6:	4861      	ldr	r0, [pc, #388]	@ (8000f2c <Firmware_Update+0x264>)
 8000da8:	f001 fbe4 	bl	8002574 <iprintf>
      printf("Starting automatic data transfer...\r\n");
 8000dac:	4860      	ldr	r0, [pc, #384]	@ (8000f30 <Firmware_Update+0x268>)
 8000dae:	f001 fc49 	bl	8002644 <puts>
      printf("Ready to receive %lu bytes. Send data now...\r\n", application_size);
 8000db2:	4b55      	ldr	r3, [pc, #340]	@ (8000f08 <Firmware_Update+0x240>)
 8000db4:	6819      	ldr	r1, [r3, #0]
 8000db6:	485f      	ldr	r0, [pc, #380]	@ (8000f34 <Firmware_Update+0x26c>)
 8000db8:	f001 fbdc 	bl	8002574 <iprintf>
  uint32_t blocks_written = 0;
 8000dbc:	2500      	movs	r5, #0
  uint32_t i = 0;
 8000dbe:	462e      	mov	r6, r5
  uint32_t current_app_size = 0;
 8000dc0:	462c      	mov	r4, r5
  HAL_StatusTypeDef ex = HAL_OK;
 8000dc2:	46a8      	mov	r8, r5
      while(current_app_size < application_size)
 8000dc4:	e041      	b.n	8000e4a <Firmware_Update+0x182>
          printf("Data reception timeout at byte %lu. Initiating rollback...\r\n", current_app_size);
 8000dc6:	4621      	mov	r1, r4
 8000dc8:	485b      	ldr	r0, [pc, #364]	@ (8000f38 <Firmware_Update+0x270>)
 8000dca:	f001 fbd3 	bl	8002574 <iprintf>
  if(!update_successful)
 8000dce:	e7c2      	b.n	8000d56 <Firmware_Update+0x8e>
          printf("Progress: %lu/%lu bytes (%.1f%%)\r\n",
 8000dd0:	4b4d      	ldr	r3, [pc, #308]	@ (8000f08 <Firmware_Update+0x240>)
 8000dd2:	f8d3 9000 	ldr.w	r9, [r3]
                 (float)current_app_size * 100.0f / application_size);
 8000dd6:	4620      	mov	r0, r4
 8000dd8:	f7ff fc2a 	bl	8000630 <__aeabi_ui2f>
 8000ddc:	4957      	ldr	r1, [pc, #348]	@ (8000f3c <Firmware_Update+0x274>)
 8000dde:	f7ff fc7f 	bl	80006e0 <__aeabi_fmul>
 8000de2:	4606      	mov	r6, r0
 8000de4:	4648      	mov	r0, r9
 8000de6:	f7ff fc23 	bl	8000630 <__aeabi_ui2f>
 8000dea:	4601      	mov	r1, r0
 8000dec:	4630      	mov	r0, r6
 8000dee:	f7ff fd2b 	bl	8000848 <__aeabi_fdiv>
          printf("Progress: %lu/%lu bytes (%.1f%%)\r\n",
 8000df2:	f7ff fb0f 	bl	8000414 <__aeabi_f2d>
 8000df6:	e9cd 0100 	strd	r0, r1, [sp]
 8000dfa:	464a      	mov	r2, r9
 8000dfc:	4621      	mov	r1, r4
 8000dfe:	4850      	ldr	r0, [pc, #320]	@ (8000f40 <Firmware_Update+0x278>)
 8000e00:	f001 fbb8 	bl	8002574 <iprintf>
 8000e04:	e039      	b.n	8000e7a <Firmware_Update+0x1b2>
          printf("Writing block %lu to flash...\r\n", blocks_written);
 8000e06:	4629      	mov	r1, r5
 8000e08:	484e      	ldr	r0, [pc, #312]	@ (8000f44 <Firmware_Update+0x27c>)
 8000e0a:	f001 fbb3 	bl	8002574 <iprintf>
          memcpy(backup_block, block, MAX_BLOCK_SIZE);
 8000e0e:	f44f 6280 	mov.w	r2, #1024	@ 0x400
 8000e12:	f50d 6184 	add.w	r1, sp, #1056	@ 0x420
 8000e16:	a808      	add	r0, sp, #32
 8000e18:	f001 fd6f 	bl	80028fa <memcpy>
          ex = write_data_to_flash_app(block, i, (blocks_written == 0));
 8000e1c:	fab5 f285 	clz	r2, r5
 8000e20:	0952      	lsrs	r2, r2, #5
 8000e22:	b2b9      	uxth	r1, r7
 8000e24:	f50d 6084 	add.w	r0, sp, #1056	@ 0x420
 8000e28:	f7ff ff00 	bl	8000c2c <write_data_to_flash_app>
          if(ex != HAL_OK)
 8000e2c:	4680      	mov	r8, r0
 8000e2e:	bb68      	cbnz	r0, 8000e8c <Firmware_Update+0x1c4>
          printf("Block %lu written successfully\r\n", blocks_written);
 8000e30:	4629      	mov	r1, r5
 8000e32:	4845      	ldr	r0, [pc, #276]	@ (8000f48 <Firmware_Update+0x280>)
 8000e34:	f001 fb9e 	bl	8002574 <iprintf>
          blocks_written++;
 8000e38:	3501      	adds	r5, #1
          memset(block, 0, MAX_BLOCK_SIZE);
 8000e3a:	f44f 6280 	mov.w	r2, #1024	@ 0x400
 8000e3e:	2100      	movs	r1, #0
 8000e40:	f50d 6084 	add.w	r0, sp, #1056	@ 0x420
 8000e44:	f001 fcde 	bl	8002804 <memset>
          i = 0;
 8000e48:	2600      	movs	r6, #0
      while(current_app_size < application_size)
 8000e4a:	4b2f      	ldr	r3, [pc, #188]	@ (8000f08 <Firmware_Update+0x240>)
 8000e4c:	681b      	ldr	r3, [r3, #0]
 8000e4e:	42a3      	cmp	r3, r4
 8000e50:	d920      	bls.n	8000e94 <Firmware_Update+0x1cc>
        HAL_StatusTypeDef rx_status = HAL_UART_Receive(&huart3, &data_byte, 1, 5000);
 8000e52:	f241 3388 	movw	r3, #5000	@ 0x1388
 8000e56:	2201      	movs	r2, #1
 8000e58:	a904      	add	r1, sp, #16
 8000e5a:	483c      	ldr	r0, [pc, #240]	@ (8000f4c <Firmware_Update+0x284>)
 8000e5c:	f001 f9d7 	bl	800220e <HAL_UART_Receive>
        if(rx_status != HAL_OK)
 8000e60:	2800      	cmp	r0, #0
 8000e62:	d1b0      	bne.n	8000dc6 <Firmware_Update+0xfe>
        block[i++] = data_byte;
 8000e64:	1c77      	adds	r7, r6, #1
 8000e66:	f89d 2010 	ldrb.w	r2, [sp, #16]
 8000e6a:	f50d 6384 	add.w	r3, sp, #1056	@ 0x420
 8000e6e:	559a      	strb	r2, [r3, r6]
        current_app_size++;
 8000e70:	3401      	adds	r4, #1
        if(current_app_size % 1024 == 0)
 8000e72:	f3c4 0309 	ubfx	r3, r4, #0, #10
 8000e76:	2b00      	cmp	r3, #0
 8000e78:	d0aa      	beq.n	8000dd0 <Firmware_Update+0x108>
        if(i == MAX_BLOCK_SIZE || current_app_size >= application_size)
 8000e7a:	f5b7 6f80 	cmp.w	r7, #1024	@ 0x400
 8000e7e:	d0c2      	beq.n	8000e06 <Firmware_Update+0x13e>
 8000e80:	4b21      	ldr	r3, [pc, #132]	@ (8000f08 <Firmware_Update+0x240>)
 8000e82:	681b      	ldr	r3, [r3, #0]
 8000e84:	42a3      	cmp	r3, r4
 8000e86:	d9be      	bls.n	8000e06 <Firmware_Update+0x13e>
        block[i++] = data_byte;
 8000e88:	463e      	mov	r6, r7
 8000e8a:	e7de      	b.n	8000e4a <Firmware_Update+0x182>
            printf("Flash write error at block %lu. Initiating rollback...\r\n", blocks_written);
 8000e8c:	4629      	mov	r1, r5
 8000e8e:	4830      	ldr	r0, [pc, #192]	@ (8000f50 <Firmware_Update+0x288>)
 8000e90:	f001 fb70 	bl	8002574 <iprintf>
      if(ex == HAL_OK && current_app_size >= application_size)
 8000e94:	f1b8 0f00 	cmp.w	r8, #0
 8000e98:	f47f af5d 	bne.w	8000d56 <Firmware_Update+0x8e>
 8000e9c:	4b1a      	ldr	r3, [pc, #104]	@ (8000f08 <Firmware_Update+0x240>)
 8000e9e:	681b      	ldr	r3, [r3, #0]
 8000ea0:	42a3      	cmp	r3, r4
 8000ea2:	f63f af58 	bhi.w	8000d56 <Firmware_Update+0x8e>
        printf("=== Firmware Update Completed Successfully ===\r\n");
 8000ea6:	482b      	ldr	r0, [pc, #172]	@ (8000f54 <Firmware_Update+0x28c>)
 8000ea8:	f001 fbcc 	bl	8002644 <puts>
        printf("Total bytes written: %lu\r\n", current_app_size);
 8000eac:	4621      	mov	r1, r4
 8000eae:	482a      	ldr	r0, [pc, #168]	@ (8000f58 <Firmware_Update+0x290>)
 8000eb0:	f001 fb60 	bl	8002574 <iprintf>
        printf("Total blocks written: %lu\r\n", blocks_written);
 8000eb4:	4629      	mov	r1, r5
 8000eb6:	4829      	ldr	r0, [pc, #164]	@ (8000f5c <Firmware_Update+0x294>)
 8000eb8:	f001 fb5c 	bl	8002574 <iprintf>
}
 8000ebc:	f60d 0d24 	addw	sp, sp, #2084	@ 0x824
 8000ec0:	e8bd 83f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, pc}
      EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
 8000ec4:	2300      	movs	r3, #0
 8000ec6:	9304      	str	r3, [sp, #16]
      EraseInitStruct.PageAddress = ETX_APP_START_ADDRESS;
 8000ec8:	4b25      	ldr	r3, [pc, #148]	@ (8000f60 <Firmware_Update+0x298>)
 8000eca:	9306      	str	r3, [sp, #24]
      EraseInitStruct.NbPages = 47;
 8000ecc:	232f      	movs	r3, #47	@ 0x2f
 8000ece:	9307      	str	r3, [sp, #28]
      erase_status = HAL_FLASHEx_Erase(&EraseInitStruct, &SectorError);
 8000ed0:	a903      	add	r1, sp, #12
 8000ed2:	a804      	add	r0, sp, #16
 8000ed4:	f000 fbb6 	bl	8001644 <HAL_FLASHEx_Erase>
 8000ed8:	4604      	mov	r4, r0
      HAL_FLASH_Lock();
 8000eda:	f000 fb01 	bl	80014e0 <HAL_FLASH_Lock>
      if(erase_status == HAL_OK)
 8000ede:	b91c      	cbnz	r4, 8000ee8 <Firmware_Update+0x220>
        printf("Application area erased. System ready for new firmware.\r\n");
 8000ee0:	4820      	ldr	r0, [pc, #128]	@ (8000f64 <Firmware_Update+0x29c>)
 8000ee2:	f001 fbaf 	bl	8002644 <puts>
 8000ee6:	e741      	b.n	8000d6c <Firmware_Update+0xa4>
        printf("Rollback erase failed. Manual intervention required.\r\n");
 8000ee8:	481f      	ldr	r0, [pc, #124]	@ (8000f68 <Firmware_Update+0x2a0>)
 8000eea:	f001 fbab 	bl	8002644 <puts>
 8000eee:	e73d      	b.n	8000d6c <Firmware_Update+0xa4>
 8000ef0:	0800356c 	.word	0x0800356c
 8000ef4:	08003598 	.word	0x08003598
 8000ef8:	080035bc 	.word	0x080035bc
 8000efc:	080035e4 	.word	0x080035e4
 8000f00:	08003608 	.word	0x08003608
 8000f04:	08003688 	.word	0x08003688
 8000f08:	20000090 	.word	0x20000090
 8000f0c:	0800363c 	.word	0x0800363c
 8000f10:	080036b4 	.word	0x080036b4
 8000f14:	080038b0 	.word	0x080038b0
 8000f18:	080038d0 	.word	0x080038d0
 8000f1c:	08003968 	.word	0x08003968
 8000f20:	08003984 	.word	0x08003984
 8000f24:	40010c00 	.word	0x40010c00
 8000f28:	0800365c 	.word	0x0800365c
 8000f2c:	080036e4 	.word	0x080036e4
 8000f30:	0800370c 	.word	0x0800370c
 8000f34:	08003734 	.word	0x08003734
 8000f38:	08003764 	.word	0x08003764
 8000f3c:	42c80000 	.word	0x42c80000
 8000f40:	080037a4 	.word	0x080037a4
 8000f44:	080037c8 	.word	0x080037c8
 8000f48:	08003824 	.word	0x08003824
 8000f4c:	20000094 	.word	0x20000094
 8000f50:	080037e8 	.word	0x080037e8
 8000f54:	08003848 	.word	0x08003848
 8000f58:	08003878 	.word	0x08003878
 8000f5c:	08003894 	.word	0x08003894
 8000f60:	08004400 	.word	0x08004400
 8000f64:	080038f4 	.word	0x080038f4
 8000f68:	08003930 	.word	0x08003930

08000f6c <__io_putchar>:
{
 8000f6c:	b500      	push	{lr}
 8000f6e:	b083      	sub	sp, #12
 8000f70:	9001      	str	r0, [sp, #4]
  HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
 8000f72:	f04f 33ff 	mov.w	r3, #4294967295
 8000f76:	2201      	movs	r2, #1
 8000f78:	a901      	add	r1, sp, #4
 8000f7a:	4803      	ldr	r0, [pc, #12]	@ (8000f88 <__io_putchar+0x1c>)
 8000f7c:	f001 f8e1 	bl	8002142 <HAL_UART_Transmit>
}
 8000f80:	9801      	ldr	r0, [sp, #4]
 8000f82:	b003      	add	sp, #12
 8000f84:	f85d fb04 	ldr.w	pc, [sp], #4
 8000f88:	200000dc 	.word	0x200000dc

08000f8c <Error_Handler>:
  __ASM volatile ("cpsid i" : : : "memory");
 8000f8c:	b672      	cpsid	i
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
 8000f8e:	e7fe      	b.n	8000f8e <Error_Handler+0x2>

08000f90 <MX_USART1_UART_Init>:
{
 8000f90:	b508      	push	{r3, lr}
  huart1.Instance = USART1;
 8000f92:	480a      	ldr	r0, [pc, #40]	@ (8000fbc <MX_USART1_UART_Init+0x2c>)
 8000f94:	4b0a      	ldr	r3, [pc, #40]	@ (8000fc0 <MX_USART1_UART_Init+0x30>)
 8000f96:	6003      	str	r3, [r0, #0]
  huart1.Init.BaudRate = 115200;
 8000f98:	f44f 33e1 	mov.w	r3, #115200	@ 0x1c200
 8000f9c:	6043      	str	r3, [r0, #4]
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
 8000f9e:	2300      	movs	r3, #0
 8000fa0:	6083      	str	r3, [r0, #8]
  huart1.Init.StopBits = UART_STOPBITS_1;
 8000fa2:	60c3      	str	r3, [r0, #12]
  huart1.Init.Parity = UART_PARITY_NONE;
 8000fa4:	6103      	str	r3, [r0, #16]
  huart1.Init.Mode = UART_MODE_TX_RX;
 8000fa6:	220c      	movs	r2, #12
 8000fa8:	6142      	str	r2, [r0, #20]
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
 8000faa:	6183      	str	r3, [r0, #24]
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;
 8000fac:	61c3      	str	r3, [r0, #28]
  if (HAL_UART_Init(&huart1) != HAL_OK)
 8000fae:	f001 f898 	bl	80020e2 <HAL_UART_Init>
 8000fb2:	b900      	cbnz	r0, 8000fb6 <MX_USART1_UART_Init+0x26>
}
 8000fb4:	bd08      	pop	{r3, pc}
    Error_Handler();
 8000fb6:	f7ff ffe9 	bl	8000f8c <Error_Handler>
 8000fba:	bf00      	nop
 8000fbc:	200000dc 	.word	0x200000dc
 8000fc0:	40013800 	.word	0x40013800

08000fc4 <MX_USART3_UART_Init>:
{
 8000fc4:	b508      	push	{r3, lr}
  huart3.Instance = USART3;
 8000fc6:	480a      	ldr	r0, [pc, #40]	@ (8000ff0 <MX_USART3_UART_Init+0x2c>)
 8000fc8:	4b0a      	ldr	r3, [pc, #40]	@ (8000ff4 <MX_USART3_UART_Init+0x30>)
 8000fca:	6003      	str	r3, [r0, #0]
  huart3.Init.BaudRate = 115200;
 8000fcc:	f44f 33e1 	mov.w	r3, #115200	@ 0x1c200
 8000fd0:	6043      	str	r3, [r0, #4]
  huart3.Init.WordLength = UART_WORDLENGTH_8B;
 8000fd2:	2300      	movs	r3, #0
 8000fd4:	6083      	str	r3, [r0, #8]
  huart3.Init.StopBits = UART_STOPBITS_1;
 8000fd6:	60c3      	str	r3, [r0, #12]
  huart3.Init.Parity = UART_PARITY_NONE;
 8000fd8:	6103      	str	r3, [r0, #16]
  huart3.Init.Mode = UART_MODE_TX_RX;
 8000fda:	220c      	movs	r2, #12
 8000fdc:	6142      	str	r2, [r0, #20]
  huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
 8000fde:	6183      	str	r3, [r0, #24]
  huart3.Init.OverSampling = UART_OVERSAMPLING_16;
 8000fe0:	61c3      	str	r3, [r0, #28]
  if (HAL_UART_Init(&huart3) != HAL_OK)
 8000fe2:	f001 f87e 	bl	80020e2 <HAL_UART_Init>
 8000fe6:	b900      	cbnz	r0, 8000fea <MX_USART3_UART_Init+0x26>
}
 8000fe8:	bd08      	pop	{r3, pc}
    Error_Handler();
 8000fea:	f7ff ffcf 	bl	8000f8c <Error_Handler>
 8000fee:	bf00      	nop
 8000ff0:	20000094 	.word	0x20000094
 8000ff4:	40004800 	.word	0x40004800

08000ff8 <SystemClock_Config>:
{
 8000ff8:	b500      	push	{lr}
 8000ffa:	b091      	sub	sp, #68	@ 0x44
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 8000ffc:	2228      	movs	r2, #40	@ 0x28
 8000ffe:	2100      	movs	r1, #0
 8001000:	a806      	add	r0, sp, #24
 8001002:	f001 fbff 	bl	8002804 <memset>
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 8001006:	2300      	movs	r3, #0
 8001008:	9301      	str	r3, [sp, #4]
 800100a:	9302      	str	r3, [sp, #8]
 800100c:	9303      	str	r3, [sp, #12]
 800100e:	9304      	str	r3, [sp, #16]
 8001010:	9305      	str	r3, [sp, #20]
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
 8001012:	2302      	movs	r3, #2
 8001014:	9306      	str	r3, [sp, #24]
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
 8001016:	2301      	movs	r3, #1
 8001018:	930a      	str	r3, [sp, #40]	@ 0x28
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
 800101a:	2310      	movs	r3, #16
 800101c:	930b      	str	r3, [sp, #44]	@ 0x2c
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 800101e:	a806      	add	r0, sp, #24
 8001020:	f000 fc96 	bl	8001950 <HAL_RCC_OscConfig>
 8001024:	b968      	cbnz	r0, 8001042 <SystemClock_Config+0x4a>
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
 8001026:	230f      	movs	r3, #15
 8001028:	9301      	str	r3, [sp, #4]
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
 800102a:	2100      	movs	r1, #0
 800102c:	9102      	str	r1, [sp, #8]
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
 800102e:	9103      	str	r1, [sp, #12]
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
 8001030:	9104      	str	r1, [sp, #16]
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
 8001032:	9105      	str	r1, [sp, #20]
  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
 8001034:	a801      	add	r0, sp, #4
 8001036:	f000 febd 	bl	8001db4 <HAL_RCC_ClockConfig>
 800103a:	b920      	cbnz	r0, 8001046 <SystemClock_Config+0x4e>
}
 800103c:	b011      	add	sp, #68	@ 0x44
 800103e:	f85d fb04 	ldr.w	pc, [sp], #4
    Error_Handler();
 8001042:	f7ff ffa3 	bl	8000f8c <Error_Handler>
    Error_Handler();
 8001046:	f7ff ffa1 	bl	8000f8c <Error_Handler>
	...

0800104c <main>:
{
 800104c:	b508      	push	{r3, lr}
  HAL_Init();
 800104e:	f000 f945 	bl	80012dc <HAL_Init>
  SystemClock_Config();
 8001052:	f7ff ffd1 	bl	8000ff8 <SystemClock_Config>
  MX_GPIO_Init();
 8001056:	f7ff fc93 	bl	8000980 <MX_GPIO_Init>
  MX_USART1_UART_Init();
 800105a:	f7ff ff99 	bl	8000f90 <MX_USART1_UART_Init>
  MX_USART3_UART_Init();
 800105e:	f7ff ffb1 	bl	8000fc4 <MX_USART3_UART_Init>
  printf("Bootloader v%d:%d Started!!!\n", BL_Version[0], BL_Version[1]);
 8001062:	4b05      	ldr	r3, [pc, #20]	@ (8001078 <main+0x2c>)
 8001064:	785a      	ldrb	r2, [r3, #1]
 8001066:	7819      	ldrb	r1, [r3, #0]
 8001068:	4804      	ldr	r0, [pc, #16]	@ (800107c <main+0x30>)
 800106a:	f001 fa83 	bl	8002574 <iprintf>
  Firmware_Update();
 800106e:	f7ff fe2b 	bl	8000cc8 <Firmware_Update>
  goto_application();
 8001072:	f7ff fcb9 	bl	80009e8 <goto_application>
  while (1)
 8001076:	e7fe      	b.n	8001076 <main+0x2a>
 8001078:	20000000 	.word	0x20000000
 800107c:	080039c0 	.word	0x080039c0

08001080 <HAL_MspInit>:
/* USER CODE END 0 */
/**
  * Initializes the Global MSP.
  */
void HAL_MspInit(void)
{
 8001080:	b082      	sub	sp, #8

  /* USER CODE BEGIN MspInit 0 */

  /* USER CODE END MspInit 0 */

  __HAL_RCC_AFIO_CLK_ENABLE();
 8001082:	4b0a      	ldr	r3, [pc, #40]	@ (80010ac <HAL_MspInit+0x2c>)
 8001084:	699a      	ldr	r2, [r3, #24]
 8001086:	f042 0201 	orr.w	r2, r2, #1
 800108a:	619a      	str	r2, [r3, #24]
 800108c:	699a      	ldr	r2, [r3, #24]
 800108e:	f002 0201 	and.w	r2, r2, #1
 8001092:	9200      	str	r2, [sp, #0]
 8001094:	9a00      	ldr	r2, [sp, #0]
  __HAL_RCC_PWR_CLK_ENABLE();
 8001096:	69da      	ldr	r2, [r3, #28]
 8001098:	f042 5280 	orr.w	r2, r2, #268435456	@ 0x10000000
 800109c:	61da      	str	r2, [r3, #28]
 800109e:	69db      	ldr	r3, [r3, #28]
 80010a0:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 80010a4:	9301      	str	r3, [sp, #4]
 80010a6:	9b01      	ldr	r3, [sp, #4]
  /* System interrupt init*/

  /* USER CODE BEGIN MspInit 1 */

  /* USER CODE END MspInit 1 */
}
 80010a8:	b002      	add	sp, #8
 80010aa:	4770      	bx	lr
 80010ac:	40021000 	.word	0x40021000

080010b0 <HAL_UART_MspInit>:
* This function configures the hardware resources used in this example
* @param huart: UART handle pointer
* @retval None
*/
void HAL_UART_MspInit(UART_HandleTypeDef* huart)
{
 80010b0:	b510      	push	{r4, lr}
 80010b2:	b088      	sub	sp, #32
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 80010b4:	2300      	movs	r3, #0
 80010b6:	9304      	str	r3, [sp, #16]
 80010b8:	9305      	str	r3, [sp, #20]
 80010ba:	9306      	str	r3, [sp, #24]
 80010bc:	9307      	str	r3, [sp, #28]
  if(huart->Instance==USART1)
 80010be:	6803      	ldr	r3, [r0, #0]
 80010c0:	4a2d      	ldr	r2, [pc, #180]	@ (8001178 <HAL_UART_MspInit+0xc8>)
 80010c2:	4293      	cmp	r3, r2
 80010c4:	d004      	beq.n	80010d0 <HAL_UART_MspInit+0x20>

  /* USER CODE BEGIN USART1_MspInit 1 */

  /* USER CODE END USART1_MspInit 1 */
  }
  else if(huart->Instance==USART3)
 80010c6:	4a2d      	ldr	r2, [pc, #180]	@ (800117c <HAL_UART_MspInit+0xcc>)
 80010c8:	4293      	cmp	r3, r2
 80010ca:	d02b      	beq.n	8001124 <HAL_UART_MspInit+0x74>
  /* USER CODE BEGIN USART3_MspInit 1 */

  /* USER CODE END USART3_MspInit 1 */
  }

}
 80010cc:	b008      	add	sp, #32
 80010ce:	bd10      	pop	{r4, pc}
    __HAL_RCC_USART1_CLK_ENABLE();
 80010d0:	4b2b      	ldr	r3, [pc, #172]	@ (8001180 <HAL_UART_MspInit+0xd0>)
 80010d2:	699a      	ldr	r2, [r3, #24]
 80010d4:	f442 4280 	orr.w	r2, r2, #16384	@ 0x4000
 80010d8:	619a      	str	r2, [r3, #24]
 80010da:	699a      	ldr	r2, [r3, #24]
 80010dc:	f402 4280 	and.w	r2, r2, #16384	@ 0x4000
 80010e0:	9200      	str	r2, [sp, #0]
 80010e2:	9a00      	ldr	r2, [sp, #0]
    __HAL_RCC_GPIOA_CLK_ENABLE();
 80010e4:	699a      	ldr	r2, [r3, #24]
 80010e6:	f042 0204 	orr.w	r2, r2, #4
 80010ea:	619a      	str	r2, [r3, #24]
 80010ec:	699b      	ldr	r3, [r3, #24]
 80010ee:	f003 0304 	and.w	r3, r3, #4
 80010f2:	9301      	str	r3, [sp, #4]
 80010f4:	9b01      	ldr	r3, [sp, #4]
    GPIO_InitStruct.Pin = GPIO_PIN_9;
 80010f6:	f44f 7300 	mov.w	r3, #512	@ 0x200
 80010fa:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 80010fc:	2302      	movs	r3, #2
 80010fe:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
 8001100:	2303      	movs	r3, #3
 8001102:	9307      	str	r3, [sp, #28]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 8001104:	4c1f      	ldr	r4, [pc, #124]	@ (8001184 <HAL_UART_MspInit+0xd4>)
 8001106:	a904      	add	r1, sp, #16
 8001108:	4620      	mov	r0, r4
 800110a:	f000 faed 	bl	80016e8 <HAL_GPIO_Init>
    GPIO_InitStruct.Pin = GPIO_PIN_10;
 800110e:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 8001112:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 8001114:	2300      	movs	r3, #0
 8001116:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 8001118:	9306      	str	r3, [sp, #24]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 800111a:	a904      	add	r1, sp, #16
 800111c:	4620      	mov	r0, r4
 800111e:	f000 fae3 	bl	80016e8 <HAL_GPIO_Init>
 8001122:	e7d3      	b.n	80010cc <HAL_UART_MspInit+0x1c>
    __HAL_RCC_USART3_CLK_ENABLE();
 8001124:	4b16      	ldr	r3, [pc, #88]	@ (8001180 <HAL_UART_MspInit+0xd0>)
 8001126:	69da      	ldr	r2, [r3, #28]
 8001128:	f442 2280 	orr.w	r2, r2, #262144	@ 0x40000
 800112c:	61da      	str	r2, [r3, #28]
 800112e:	69da      	ldr	r2, [r3, #28]
 8001130:	f402 2280 	and.w	r2, r2, #262144	@ 0x40000
 8001134:	9202      	str	r2, [sp, #8]
 8001136:	9a02      	ldr	r2, [sp, #8]
    __HAL_RCC_GPIOB_CLK_ENABLE();
 8001138:	699a      	ldr	r2, [r3, #24]
 800113a:	f042 0208 	orr.w	r2, r2, #8
 800113e:	619a      	str	r2, [r3, #24]
 8001140:	699b      	ldr	r3, [r3, #24]
 8001142:	f003 0308 	and.w	r3, r3, #8
 8001146:	9303      	str	r3, [sp, #12]
 8001148:	9b03      	ldr	r3, [sp, #12]
    GPIO_InitStruct.Pin = GPIO_PIN_10;
 800114a:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 800114e:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 8001150:	2302      	movs	r3, #2
 8001152:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
 8001154:	2303      	movs	r3, #3
 8001156:	9307      	str	r3, [sp, #28]
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 8001158:	4c0b      	ldr	r4, [pc, #44]	@ (8001188 <HAL_UART_MspInit+0xd8>)
 800115a:	a904      	add	r1, sp, #16
 800115c:	4620      	mov	r0, r4
 800115e:	f000 fac3 	bl	80016e8 <HAL_GPIO_Init>
    GPIO_InitStruct.Pin = GPIO_PIN_11;
 8001162:	f44f 6300 	mov.w	r3, #2048	@ 0x800
 8001166:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 8001168:	2300      	movs	r3, #0
 800116a:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 800116c:	9306      	str	r3, [sp, #24]
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 800116e:	a904      	add	r1, sp, #16
 8001170:	4620      	mov	r0, r4
 8001172:	f000 fab9 	bl	80016e8 <HAL_GPIO_Init>
}
 8001176:	e7a9      	b.n	80010cc <HAL_UART_MspInit+0x1c>
 8001178:	40013800 	.word	0x40013800
 800117c:	40004800 	.word	0x40004800
 8001180:	40021000 	.word	0x40021000
 8001184:	40010800 	.word	0x40010800
 8001188:	40010c00 	.word	0x40010c00

0800118c <NMI_Handler>:
{
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */

  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
  while (1)
 800118c:	e7fe      	b.n	800118c <NMI_Handler>

0800118e <HardFault_Handler>:
void HardFault_Handler(void)
{
  /* USER CODE BEGIN HardFault_IRQn 0 */

  /* USER CODE END HardFault_IRQn 0 */
  while (1)
 800118e:	e7fe      	b.n	800118e <HardFault_Handler>

08001190 <MemManage_Handler>:
void MemManage_Handler(void)
{
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */

  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
 8001190:	e7fe      	b.n	8001190 <MemManage_Handler>

08001192 <BusFault_Handler>:
void BusFault_Handler(void)
{
  /* USER CODE BEGIN BusFault_IRQn 0 */

  /* USER CODE END BusFault_IRQn 0 */
  while (1)
 8001192:	e7fe      	b.n	8001192 <BusFault_Handler>

08001194 <UsageFault_Handler>:
void UsageFault_Handler(void)
{
  /* USER CODE BEGIN UsageFault_IRQn 0 */

  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
 8001194:	e7fe      	b.n	8001194 <UsageFault_Handler>

08001196 <SVC_Handler>:

  /* USER CODE END SVCall_IRQn 0 */
  /* USER CODE BEGIN SVCall_IRQn 1 */

  /* USER CODE END SVCall_IRQn 1 */
}
 8001196:	4770      	bx	lr

08001198 <DebugMon_Handler>:

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}
 8001198:	4770      	bx	lr

0800119a <PendSV_Handler>:

  /* USER CODE END PendSV_IRQn 0 */
  /* USER CODE BEGIN PendSV_IRQn 1 */

  /* USER CODE END PendSV_IRQn 1 */
}
 800119a:	4770      	bx	lr

0800119c <SysTick_Handler>:

/**
  * @brief This function handles System tick timer.
  */
void SysTick_Handler(void)
{
 800119c:	b508      	push	{r3, lr}
  /* USER CODE BEGIN SysTick_IRQn 0 */

  /* USER CODE END SysTick_IRQn 0 */
  HAL_IncTick();
 800119e:	f000 f8af 	bl	8001300 <HAL_IncTick>
  /* USER CODE BEGIN SysTick_IRQn 1 */

  /* USER CODE END SysTick_IRQn 1 */
}
 80011a2:	bd08      	pop	{r3, pc}

080011a4 <_read>:
	_kill(status, -1);
	while (1) {}		/* Make sure we hang here */
}

__attribute__((weak)) int _read(int file, char *ptr, int len)
{
 80011a4:	b570      	push	{r4, r5, r6, lr}
 80011a6:	460c      	mov	r4, r1
 80011a8:	4616      	mov	r6, r2
	int DataIdx;

	for (DataIdx = 0; DataIdx < len; DataIdx++)
 80011aa:	2500      	movs	r5, #0
 80011ac:	e006      	b.n	80011bc <_read+0x18>
	{
		*ptr++ = __io_getchar();
 80011ae:	f3af 8000 	nop.w
 80011b2:	4621      	mov	r1, r4
 80011b4:	f801 0b01 	strb.w	r0, [r1], #1
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 80011b8:	3501      	adds	r5, #1
		*ptr++ = __io_getchar();
 80011ba:	460c      	mov	r4, r1
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 80011bc:	42b5      	cmp	r5, r6
 80011be:	dbf6      	blt.n	80011ae <_read+0xa>
	}

return len;
}
 80011c0:	4630      	mov	r0, r6
 80011c2:	bd70      	pop	{r4, r5, r6, pc}

080011c4 <_write>:

__attribute__((weak)) int _write(int file, char *ptr, int len)
{
 80011c4:	b570      	push	{r4, r5, r6, lr}
 80011c6:	460c      	mov	r4, r1
 80011c8:	4616      	mov	r6, r2
	int DataIdx;

	for (DataIdx = 0; DataIdx < len; DataIdx++)
 80011ca:	2500      	movs	r5, #0
 80011cc:	e004      	b.n	80011d8 <_write+0x14>
	{
		__io_putchar(*ptr++);
 80011ce:	f814 0b01 	ldrb.w	r0, [r4], #1
 80011d2:	f7ff fecb 	bl	8000f6c <__io_putchar>
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 80011d6:	3501      	adds	r5, #1
 80011d8:	42b5      	cmp	r5, r6
 80011da:	dbf8      	blt.n	80011ce <_write+0xa>
	}
	return len;
}
 80011dc:	4630      	mov	r0, r6
 80011de:	bd70      	pop	{r4, r5, r6, pc}

080011e0 <_close>:

int _close(int file)
{
	return -1;
}
 80011e0:	f04f 30ff 	mov.w	r0, #4294967295
 80011e4:	4770      	bx	lr

080011e6 <_fstat>:


int _fstat(int file, struct stat *st)
{
	st->st_mode = S_IFCHR;
 80011e6:	f44f 5300 	mov.w	r3, #8192	@ 0x2000
 80011ea:	604b      	str	r3, [r1, #4]
	return 0;
}
 80011ec:	2000      	movs	r0, #0
 80011ee:	4770      	bx	lr

080011f0 <_isatty>:

int _isatty(int file)
{
	return 1;
}
 80011f0:	2001      	movs	r0, #1
 80011f2:	4770      	bx	lr

080011f4 <_lseek>:

int _lseek(int file, int ptr, int dir)
{
	return 0;
}
 80011f4:	2000      	movs	r0, #0
 80011f6:	4770      	bx	lr

080011f8 <_sbrk>:
 *
 * @param incr Memory size
 * @return Pointer to allocated memory
 */
void *_sbrk(ptrdiff_t incr)
{
 80011f8:	b510      	push	{r4, lr}
 80011fa:	4603      	mov	r3, r0
  extern uint8_t _end; /* Symbol defined in the linker script */
  extern uint8_t _estack; /* Symbol defined in the linker script */
  extern uint32_t _Min_Stack_Size; /* Symbol defined in the linker script */
  const uint32_t stack_limit = (uint32_t)&_estack - (uint32_t)&_Min_Stack_Size;
 80011fc:	4a0c      	ldr	r2, [pc, #48]	@ (8001230 <_sbrk+0x38>)
 80011fe:	490d      	ldr	r1, [pc, #52]	@ (8001234 <_sbrk+0x3c>)
  const uint8_t *max_heap = (uint8_t *)stack_limit;
  uint8_t *prev_heap_end;

  /* Initialize heap end at first call */
  if (NULL == __sbrk_heap_end)
 8001200:	480d      	ldr	r0, [pc, #52]	@ (8001238 <_sbrk+0x40>)
 8001202:	6800      	ldr	r0, [r0, #0]
 8001204:	b140      	cbz	r0, 8001218 <_sbrk+0x20>
  {
    __sbrk_heap_end = &_end;
  }

  /* Protect heap from growing into the reserved MSP stack */
  if (__sbrk_heap_end + incr > max_heap)
 8001206:	480c      	ldr	r0, [pc, #48]	@ (8001238 <_sbrk+0x40>)
 8001208:	6800      	ldr	r0, [r0, #0]
 800120a:	4403      	add	r3, r0
 800120c:	1a52      	subs	r2, r2, r1
 800120e:	4293      	cmp	r3, r2
 8001210:	d806      	bhi.n	8001220 <_sbrk+0x28>
    errno = ENOMEM;
    return (void *)-1;
  }

  prev_heap_end = __sbrk_heap_end;
  __sbrk_heap_end += incr;
 8001212:	4a09      	ldr	r2, [pc, #36]	@ (8001238 <_sbrk+0x40>)
 8001214:	6013      	str	r3, [r2, #0]

  return (void *)prev_heap_end;
}
 8001216:	bd10      	pop	{r4, pc}
    __sbrk_heap_end = &_end;
 8001218:	4807      	ldr	r0, [pc, #28]	@ (8001238 <_sbrk+0x40>)
 800121a:	4c08      	ldr	r4, [pc, #32]	@ (800123c <_sbrk+0x44>)
 800121c:	6004      	str	r4, [r0, #0]
 800121e:	e7f2      	b.n	8001206 <_sbrk+0xe>
    errno = ENOMEM;
 8001220:	f001 fb3e 	bl	80028a0 <__errno>
 8001224:	230c      	movs	r3, #12
 8001226:	6003      	str	r3, [r0, #0]
    return (void *)-1;
 8001228:	f04f 30ff 	mov.w	r0, #4294967295
 800122c:	e7f3      	b.n	8001216 <_sbrk+0x1e>
 800122e:	bf00      	nop
 8001230:	20005000 	.word	0x20005000
 8001234:	00000400 	.word	0x00000400
 8001238:	20000124 	.word	0x20000124
 800123c:	200002a0 	.word	0x200002a0

08001240 <SystemInit>:

  /* Configure the Vector Table location -------------------------------------*/
#if defined(USER_VECT_TAB_ADDRESS)
  SCB->VTOR = VECT_TAB_BASE_ADDRESS | VECT_TAB_OFFSET; /* Vector Table Relocation in Internal SRAM. */
#endif /* USER_VECT_TAB_ADDRESS */
}
 8001240:	4770      	bx	lr
	...

08001244 <Reset_Handler>:
  .weak Reset_Handler
  .type Reset_Handler, %function
Reset_Handler:

/* Copy the data segment initializers from flash to SRAM */
  ldr r0, =_sdata
 8001244:	480c      	ldr	r0, [pc, #48]	@ (8001278 <LoopFillZerobss+0x12>)
  ldr r1, =_edata
 8001246:	490d      	ldr	r1, [pc, #52]	@ (800127c <LoopFillZerobss+0x16>)
  ldr r2, =_sidata
 8001248:	4a0d      	ldr	r2, [pc, #52]	@ (8001280 <LoopFillZerobss+0x1a>)
  movs r3, #0
 800124a:	2300      	movs	r3, #0
  b LoopCopyDataInit
 800124c:	e002      	b.n	8001254 <LoopCopyDataInit>

0800124e <CopyDataInit>:

CopyDataInit:
  ldr r4, [r2, r3]
 800124e:	58d4      	ldr	r4, [r2, r3]
  str r4, [r0, r3]
 8001250:	50c4      	str	r4, [r0, r3]
  adds r3, r3, #4
 8001252:	3304      	adds	r3, #4

08001254 <LoopCopyDataInit>:

LoopCopyDataInit:
  adds r4, r0, r3
 8001254:	18c4      	adds	r4, r0, r3
  cmp r4, r1
 8001256:	428c      	cmp	r4, r1
  bcc CopyDataInit
 8001258:	d3f9      	bcc.n	800124e <CopyDataInit>
  
/* Zero fill the bss segment. */
  ldr r2, =_sbss
 800125a:	4a0a      	ldr	r2, [pc, #40]	@ (8001284 <LoopFillZerobss+0x1e>)
  ldr r4, =_ebss
 800125c:	4c0a      	ldr	r4, [pc, #40]	@ (8001288 <LoopFillZerobss+0x22>)
  movs r3, #0
 800125e:	2300      	movs	r3, #0
  b LoopFillZerobss
 8001260:	e001      	b.n	8001266 <LoopFillZerobss>

08001262 <FillZerobss>:

FillZerobss:
  str  r3, [r2]
 8001262:	6013      	str	r3, [r2, #0]
  adds r2, r2, #4
 8001264:	3204      	adds	r2, #4

08001266 <LoopFillZerobss>:

LoopFillZerobss:
  cmp r2, r4
 8001266:	42a2      	cmp	r2, r4
  bcc FillZerobss
 8001268:	d3fb      	bcc.n	8001262 <FillZerobss>

/* Call the clock system intitialization function.*/
    bl  SystemInit
 800126a:	f7ff ffe9 	bl	8001240 <SystemInit>
/* Call static constructors */
    bl __libc_init_array
 800126e:	f001 fb1d 	bl	80028ac <__libc_init_array>
/* Call the application's entry point.*/
  bl main
 8001272:	f7ff feeb 	bl	800104c <main>
  bx lr
 8001276:	4770      	bx	lr
  ldr r0, =_sdata
 8001278:	20000000 	.word	0x20000000
  ldr r1, =_edata
 800127c:	2000006c 	.word	0x2000006c
  ldr r2, =_sidata
 8001280:	08003b48 	.word	0x08003b48
  ldr r2, =_sbss
 8001284:	20000070 	.word	0x20000070
  ldr r4, =_ebss
 8001288:	2000029c 	.word	0x2000029c

0800128c <ADC1_2_IRQHandler>:
 * @retval : None
*/
    .section .text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
  b Infinite_Loop
 800128c:	e7fe      	b.n	800128c <ADC1_2_IRQHandler>
	...

08001290 <HAL_InitTick>:
  *       implementation  in user file.
  * @param TickPriority Tick interrupt priority.
  * @retval HAL status
  */
__weak HAL_StatusTypeDef HAL_InitTick(uint32_t TickPriority)
{
 8001290:	b510      	push	{r4, lr}
 8001292:	4604      	mov	r4, r0
  /* Configure the SysTick to have interrupt in 1ms time basis*/
  if (HAL_SYSTICK_Config(SystemCoreClock / (1000U / uwTickFreq)) > 0U)
 8001294:	4b0e      	ldr	r3, [pc, #56]	@ (80012d0 <HAL_InitTick+0x40>)
 8001296:	781a      	ldrb	r2, [r3, #0]
 8001298:	f44f 737a 	mov.w	r3, #1000	@ 0x3e8
 800129c:	fbb3 f3f2 	udiv	r3, r3, r2
 80012a0:	4a0c      	ldr	r2, [pc, #48]	@ (80012d4 <HAL_InitTick+0x44>)
 80012a2:	6810      	ldr	r0, [r2, #0]
 80012a4:	fbb0 f0f3 	udiv	r0, r0, r3
 80012a8:	f000 f8a6 	bl	80013f8 <HAL_SYSTICK_Config>
 80012ac:	b968      	cbnz	r0, 80012ca <HAL_InitTick+0x3a>
  {
    return HAL_ERROR;
  }

  /* Configure the SysTick IRQ priority */
  if (TickPriority < (1UL << __NVIC_PRIO_BITS))
 80012ae:	2c0f      	cmp	r4, #15
 80012b0:	d901      	bls.n	80012b6 <HAL_InitTick+0x26>
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
    uwTickPrio = TickPriority;
  }
  else
  {
    return HAL_ERROR;
 80012b2:	2001      	movs	r0, #1
 80012b4:	e00a      	b.n	80012cc <HAL_InitTick+0x3c>
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
 80012b6:	2200      	movs	r2, #0
 80012b8:	4621      	mov	r1, r4
 80012ba:	f04f 30ff 	mov.w	r0, #4294967295
 80012be:	f000 f88b 	bl	80013d8 <HAL_NVIC_SetPriority>
    uwTickPrio = TickPriority;
 80012c2:	4b05      	ldr	r3, [pc, #20]	@ (80012d8 <HAL_InitTick+0x48>)
 80012c4:	601c      	str	r4, [r3, #0]
  }

  /* Return function status */
  return HAL_OK;
 80012c6:	2000      	movs	r0, #0
 80012c8:	e000      	b.n	80012cc <HAL_InitTick+0x3c>
    return HAL_ERROR;
 80012ca:	2001      	movs	r0, #1
}
 80012cc:	bd10      	pop	{r4, pc}
 80012ce:	bf00      	nop
 80012d0:	20000008 	.word	0x20000008
 80012d4:	20000004 	.word	0x20000004
 80012d8:	2000000c 	.word	0x2000000c

080012dc <HAL_Init>:
{
 80012dc:	b508      	push	{r3, lr}
  __HAL_FLASH_PREFETCH_BUFFER_ENABLE();
 80012de:	4a07      	ldr	r2, [pc, #28]	@ (80012fc <HAL_Init+0x20>)
 80012e0:	6813      	ldr	r3, [r2, #0]
 80012e2:	f043 0310 	orr.w	r3, r3, #16
 80012e6:	6013      	str	r3, [r2, #0]
  HAL_NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);
 80012e8:	2003      	movs	r0, #3
 80012ea:	f000 f863 	bl	80013b4 <HAL_NVIC_SetPriorityGrouping>
  HAL_InitTick(TICK_INT_PRIORITY);
 80012ee:	200f      	movs	r0, #15
 80012f0:	f7ff ffce 	bl	8001290 <HAL_InitTick>
  HAL_MspInit();
 80012f4:	f7ff fec4 	bl	8001080 <HAL_MspInit>
}
 80012f8:	2000      	movs	r0, #0
 80012fa:	bd08      	pop	{r3, pc}
 80012fc:	******** 	.word	0x********

08001300 <HAL_IncTick>:
  *      implementations in user file.
  * @retval None
  */
__weak void HAL_IncTick(void)
{
  uwTick += uwTickFreq;
 8001300:	4a03      	ldr	r2, [pc, #12]	@ (8001310 <HAL_IncTick+0x10>)
 8001302:	6811      	ldr	r1, [r2, #0]
 8001304:	4b03      	ldr	r3, [pc, #12]	@ (8001314 <HAL_IncTick+0x14>)
 8001306:	781b      	ldrb	r3, [r3, #0]
 8001308:	440b      	add	r3, r1
 800130a:	6013      	str	r3, [r2, #0]
}
 800130c:	4770      	bx	lr
 800130e:	bf00      	nop
 8001310:	20000128 	.word	0x20000128
 8001314:	20000008 	.word	0x20000008

08001318 <HAL_GetTick>:
  *       implementations in user file.
  * @retval tick value
  */
__weak uint32_t HAL_GetTick(void)
{
  return uwTick;
 8001318:	4b01      	ldr	r3, [pc, #4]	@ (8001320 <HAL_GetTick+0x8>)
 800131a:	6818      	ldr	r0, [r3, #0]
}
 800131c:	4770      	bx	lr
 800131e:	bf00      	nop
 8001320:	20000128 	.word	0x20000128

08001324 <HAL_Delay>:
  *       implementations in user file.
  * @param Delay specifies the delay time length, in milliseconds.
  * @retval None
  */
__weak void HAL_Delay(uint32_t Delay)
{
 8001324:	b538      	push	{r3, r4, r5, lr}
 8001326:	4604      	mov	r4, r0
  uint32_t tickstart = HAL_GetTick();
 8001328:	f7ff fff6 	bl	8001318 <HAL_GetTick>
 800132c:	4605      	mov	r5, r0
  uint32_t wait = Delay;

  /* Add a freq to guarantee minimum wait */
  if (wait < HAL_MAX_DELAY)
 800132e:	f1b4 3fff 	cmp.w	r4, #4294967295
 8001332:	d002      	beq.n	800133a <HAL_Delay+0x16>
  {
    wait += (uint32_t)(uwTickFreq);
 8001334:	4b04      	ldr	r3, [pc, #16]	@ (8001348 <HAL_Delay+0x24>)
 8001336:	781b      	ldrb	r3, [r3, #0]
 8001338:	441c      	add	r4, r3
  }

  while ((HAL_GetTick() - tickstart) < wait)
 800133a:	f7ff ffed 	bl	8001318 <HAL_GetTick>
 800133e:	1b40      	subs	r0, r0, r5
 8001340:	42a0      	cmp	r0, r4
 8001342:	d3fa      	bcc.n	800133a <HAL_Delay+0x16>
  {
  }
}
 8001344:	bd38      	pop	{r3, r4, r5, pc}
 8001346:	bf00      	nop
 8001348:	20000008 	.word	0x20000008

0800134c <__NVIC_SetPriority>:
  \param [in]  priority  Priority to set.
  \note    The priority cannot be set for every processor exception.
 */
__STATIC_INLINE void __NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority)
{
  if ((int32_t)(IRQn) >= 0)
 800134c:	2800      	cmp	r0, #0
 800134e:	db08      	blt.n	8001362 <__NVIC_SetPriority+0x16>
  {
    NVIC->IP[((uint32_t)IRQn)]               = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8001350:	0109      	lsls	r1, r1, #4
 8001352:	b2c9      	uxtb	r1, r1
 8001354:	f100 4060 	add.w	r0, r0, #3758096384	@ 0xe0000000
 8001358:	f500 4061 	add.w	r0, r0, #57600	@ 0xe100
 800135c:	f880 1300 	strb.w	r1, [r0, #768]	@ 0x300
 8001360:	4770      	bx	lr
  }
  else
  {
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8001362:	f000 000f 	and.w	r0, r0, #15
 8001366:	0109      	lsls	r1, r1, #4
 8001368:	b2c9      	uxtb	r1, r1
 800136a:	4b01      	ldr	r3, [pc, #4]	@ (8001370 <__NVIC_SetPriority+0x24>)
 800136c:	5419      	strb	r1, [r3, r0]
  }
}
 800136e:	4770      	bx	lr
 8001370:	e000ed14 	.word	0xe000ed14

08001374 <NVIC_EncodePriority>:
  \param [in]   PreemptPriority  Preemptive priority value (starting from 0).
  \param [in]       SubPriority  Subpriority value (starting from 0).
  \return                        Encoded priority. Value can be used in the function \ref NVIC_SetPriority().
 */
__STATIC_INLINE uint32_t NVIC_EncodePriority (uint32_t PriorityGroup, uint32_t PreemptPriority, uint32_t SubPriority)
{
 8001374:	b500      	push	{lr}
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used          */
 8001376:	f000 0007 	and.w	r0, r0, #7
  uint32_t PreemptPriorityBits;
  uint32_t SubPriorityBits;

  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NVIC_PRIO_BITS) : (uint32_t)(7UL - PriorityGroupTmp);
 800137a:	f1c0 0c07 	rsb	ip, r0, #7
 800137e:	f1bc 0f04 	cmp.w	ip, #4
 8001382:	bf28      	it	cs
 8001384:	f04f 0c04 	movcs.w	ip, #4
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 8001388:	1d03      	adds	r3, r0, #4
 800138a:	2b06      	cmp	r3, #6
 800138c:	d90f      	bls.n	80013ae <NVIC_EncodePriority+0x3a>
 800138e:	1ec3      	subs	r3, r0, #3

  return (
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
 8001390:	f04f 3eff 	mov.w	lr, #4294967295
 8001394:	fa0e f00c 	lsl.w	r0, lr, ip
 8001398:	ea21 0100 	bic.w	r1, r1, r0
 800139c:	4099      	lsls	r1, r3
           ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
 800139e:	fa0e fe03 	lsl.w	lr, lr, r3
 80013a2:	ea22 020e 	bic.w	r2, r2, lr
         );
}
 80013a6:	ea41 0002 	orr.w	r0, r1, r2
 80013aa:	f85d fb04 	ldr.w	pc, [sp], #4
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 80013ae:	2300      	movs	r3, #0
 80013b0:	e7ee      	b.n	8001390 <NVIC_EncodePriority+0x1c>
	...

080013b4 <HAL_NVIC_SetPriorityGrouping>:
  reg_value  =  SCB->AIRCR;                                                   /* read old register configuration    */
 80013b4:	4a07      	ldr	r2, [pc, #28]	@ (80013d4 <HAL_NVIC_SetPriorityGrouping+0x20>)
 80013b6:	68d3      	ldr	r3, [r2, #12]
  reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to change               */
 80013b8:	f423 63e0 	bic.w	r3, r3, #1792	@ 0x700
 80013bc:	041b      	lsls	r3, r3, #16
 80013be:	0c1b      	lsrs	r3, r3, #16
                (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos) );               /* Insert write key and priority group */
 80013c0:	0200      	lsls	r0, r0, #8
 80013c2:	f400 60e0 	and.w	r0, r0, #1792	@ 0x700
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
 80013c6:	4303      	orrs	r3, r0
  reg_value  =  (reg_value                                   |
 80013c8:	f043 63bf 	orr.w	r3, r3, #100139008	@ 0x5f80000
 80013cc:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
  SCB->AIRCR =  reg_value;
 80013d0:	60d3      	str	r3, [r2, #12]
  /* Check the parameters */
  assert_param(IS_NVIC_PRIORITY_GROUP(PriorityGroup));
  
  /* Set the PRIGROUP[10:8] bits according to the PriorityGroup parameter value */
  NVIC_SetPriorityGrouping(PriorityGroup);
}
 80013d2:	4770      	bx	lr
 80013d4:	e000ed00 	.word	0xe000ed00

080013d8 <HAL_NVIC_SetPriority>:
  *         This parameter can be a value between 0 and 15
  *         A lower priority value indicates a higher priority.          
  * @retval None
  */
void HAL_NVIC_SetPriority(IRQn_Type IRQn, uint32_t PreemptPriority, uint32_t SubPriority)
{ 
 80013d8:	b510      	push	{r4, lr}
 80013da:	4604      	mov	r4, r0
  return ((uint32_t)((SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) >> SCB_AIRCR_PRIGROUP_Pos));
 80013dc:	4b05      	ldr	r3, [pc, #20]	@ (80013f4 <HAL_NVIC_SetPriority+0x1c>)
 80013de:	68d8      	ldr	r0, [r3, #12]
  assert_param(IS_NVIC_SUB_PRIORITY(SubPriority));
  assert_param(IS_NVIC_PREEMPTION_PRIORITY(PreemptPriority));
  
  prioritygroup = NVIC_GetPriorityGrouping();
  
  NVIC_SetPriority(IRQn, NVIC_EncodePriority(prioritygroup, PreemptPriority, SubPriority));
 80013e0:	f3c0 2002 	ubfx	r0, r0, #8, #3
 80013e4:	f7ff ffc6 	bl	8001374 <NVIC_EncodePriority>
 80013e8:	4601      	mov	r1, r0
 80013ea:	4620      	mov	r0, r4
 80013ec:	f7ff ffae 	bl	800134c <__NVIC_SetPriority>
}
 80013f0:	bd10      	pop	{r4, pc}
 80013f2:	bf00      	nop
 80013f4:	e000ed00 	.word	0xe000ed00

080013f8 <HAL_SYSTICK_Config>:
           function <b>SysTick_Config</b> is not included. In this case, the file <b><i>device</i>.h</b>
           must contain a vendor-specific implementation of this function.
 */
__STATIC_INLINE uint32_t SysTick_Config(uint32_t ticks)
{
  if ((ticks - 1UL) > SysTick_LOAD_RELOAD_Msk)
 80013f8:	3801      	subs	r0, #1
 80013fa:	f1b0 7f80 	cmp.w	r0, #16777216	@ 0x1000000
 80013fe:	d20b      	bcs.n	8001418 <HAL_SYSTICK_Config+0x20>
  {
    return (1UL);                                                   /* Reload value impossible */
  }

  SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
 8001400:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8001404:	6158      	str	r0, [r3, #20]
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8001406:	4a05      	ldr	r2, [pc, #20]	@ (800141c <HAL_SYSTICK_Config+0x24>)
 8001408:	21f0      	movs	r1, #240	@ 0xf0
 800140a:	f882 1023 	strb.w	r1, [r2, #35]	@ 0x23
  NVIC_SetPriority (SysTick_IRQn, (1UL << __NVIC_PRIO_BITS) - 1UL); /* set Priority for Systick Interrupt */
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
 800140e:	2000      	movs	r0, #0
 8001410:	6198      	str	r0, [r3, #24]
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
 8001412:	2207      	movs	r2, #7
 8001414:	611a      	str	r2, [r3, #16]
                   SysTick_CTRL_TICKINT_Msk   |
                   SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTick Timer */
  return (0UL);                                                     /* Function successful */
 8001416:	4770      	bx	lr
    return (1UL);                                                   /* Reload value impossible */
 8001418:	2001      	movs	r0, #1
  *                  - 1  Function failed.
  */
uint32_t HAL_SYSTICK_Config(uint32_t TicksNumb)
{
   return SysTick_Config(TicksNumb);
}
 800141a:	4770      	bx	lr
 800141c:	e000ed00 	.word	0xe000ed00

******** <FLASH_Program_HalfWord>:
  * @retval None
  */
static void FLASH_Program_HalfWord(uint32_t Address, uint16_t Data)
{
  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 8001420:	4b04      	ldr	r3, [pc, #16]	@ (8001434 <FLASH_Program_HalfWord+0x14>)
 8001422:	2200      	movs	r2, #0
 8001424:	61da      	str	r2, [r3, #28]
#if defined(FLASH_BANK2_END)
  if(Address <= FLASH_BANK1_END)
  {
#endif /* FLASH_BANK2_END */
    /* Proceed to program the new data */
    SET_BIT(FLASH->CR, FLASH_CR_PG);
 8001426:	4a04      	ldr	r2, [pc, #16]	@ (8001438 <FLASH_Program_HalfWord+0x18>)
 8001428:	6913      	ldr	r3, [r2, #16]
 800142a:	f043 0301 	orr.w	r3, r3, #1
 800142e:	6113      	str	r3, [r2, #16]
    SET_BIT(FLASH->CR2, FLASH_CR2_PG);
  }
#endif /* FLASH_BANK2_END */

  /* Write data in the address */
  *(__IO uint16_t*)Address = Data;
 8001430:	8001      	strh	r1, [r0, #0]
}
 8001432:	4770      	bx	lr
 8001434:	******** 	.word	0x********
 8001438:	******** 	.word	0x********

0800143c <FLASH_SetErrorCode>:
  uint32_t flags = 0U;
  
#if defined(FLASH_BANK2_END)
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR) || __HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR_BANK2))
#else
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR))
 800143c:	4b19      	ldr	r3, [pc, #100]	@ (80014a4 <FLASH_SetErrorCode+0x68>)
 800143e:	68db      	ldr	r3, [r3, #12]
 8001440:	f013 0310 	ands.w	r3, r3, #16
 8001444:	d005      	beq.n	8001452 <FLASH_SetErrorCode+0x16>
#endif /* FLASH_BANK2_END */
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_WRP;
 8001446:	4a18      	ldr	r2, [pc, #96]	@ (80014a8 <FLASH_SetErrorCode+0x6c>)
 8001448:	69d3      	ldr	r3, [r2, #28]
 800144a:	f043 0302 	orr.w	r3, r3, #2
 800144e:	61d3      	str	r3, [r2, #28]
#if defined(FLASH_BANK2_END)
    flags |= FLASH_FLAG_WRPERR | FLASH_FLAG_WRPERR_BANK2;
#else
    flags |= FLASH_FLAG_WRPERR;
 8001450:	2310      	movs	r3, #16
#endif /* FLASH_BANK2_END */
  }
#if defined(FLASH_BANK2_END)
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR) || __HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR_BANK2))
#else
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR))
 8001452:	4a14      	ldr	r2, [pc, #80]	@ (80014a4 <FLASH_SetErrorCode+0x68>)
 8001454:	68d2      	ldr	r2, [r2, #12]
 8001456:	f012 0f04 	tst.w	r2, #4
 800145a:	d006      	beq.n	800146a <FLASH_SetErrorCode+0x2e>
#endif /* FLASH_BANK2_END */
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_PROG;
 800145c:	4912      	ldr	r1, [pc, #72]	@ (80014a8 <FLASH_SetErrorCode+0x6c>)
 800145e:	69ca      	ldr	r2, [r1, #28]
 8001460:	f042 0201 	orr.w	r2, r2, #1
 8001464:	61ca      	str	r2, [r1, #28]
#if defined(FLASH_BANK2_END)
    flags |= FLASH_FLAG_PGERR | FLASH_FLAG_PGERR_BANK2;
#else
    flags |= FLASH_FLAG_PGERR;
 8001466:	f043 0304 	orr.w	r3, r3, #4
#endif /* FLASH_BANK2_END */
  }
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR))
 800146a:	4a0e      	ldr	r2, [pc, #56]	@ (80014a4 <FLASH_SetErrorCode+0x68>)
 800146c:	69d2      	ldr	r2, [r2, #28]
 800146e:	f012 0f01 	tst.w	r2, #1
 8001472:	d009      	beq.n	8001488 <FLASH_SetErrorCode+0x4c>
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_OPTV;
 8001474:	490c      	ldr	r1, [pc, #48]	@ (80014a8 <FLASH_SetErrorCode+0x6c>)
 8001476:	69ca      	ldr	r2, [r1, #28]
 8001478:	f042 0204 	orr.w	r2, r2, #4
 800147c:	61ca      	str	r2, [r1, #28]
  __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_OPTVERR);
 800147e:	4909      	ldr	r1, [pc, #36]	@ (80014a4 <FLASH_SetErrorCode+0x68>)
 8001480:	69ca      	ldr	r2, [r1, #28]
 8001482:	f022 0201 	bic.w	r2, r2, #1
 8001486:	61ca      	str	r2, [r1, #28]
  }

  /* Clear FLASH error pending bits */
  __HAL_FLASH_CLEAR_FLAG(flags);
 8001488:	f240 1201 	movw	r2, #257	@ 0x101
 800148c:	4293      	cmp	r3, r2
 800148e:	d002      	beq.n	8001496 <FLASH_SetErrorCode+0x5a>
 8001490:	4a04      	ldr	r2, [pc, #16]	@ (80014a4 <FLASH_SetErrorCode+0x68>)
 8001492:	60d3      	str	r3, [r2, #12]
}  
 8001494:	4770      	bx	lr
  __HAL_FLASH_CLEAR_FLAG(flags);
 8001496:	4a03      	ldr	r2, [pc, #12]	@ (80014a4 <FLASH_SetErrorCode+0x68>)
 8001498:	69d3      	ldr	r3, [r2, #28]
 800149a:	f023 0301 	bic.w	r3, r3, #1
 800149e:	61d3      	str	r3, [r2, #28]
 80014a0:	4770      	bx	lr
 80014a2:	bf00      	nop
 80014a4:	******** 	.word	0x********
 80014a8:	******** 	.word	0x********

080014ac <HAL_FLASH_Unlock>:
  if(READ_BIT(FLASH->CR, FLASH_CR_LOCK) != RESET)
 80014ac:	4b0a      	ldr	r3, [pc, #40]	@ (80014d8 <HAL_FLASH_Unlock+0x2c>)
 80014ae:	691b      	ldr	r3, [r3, #16]
 80014b0:	f013 0f80 	tst.w	r3, #128	@ 0x80
 80014b4:	d00b      	beq.n	80014ce <HAL_FLASH_Unlock+0x22>
    WRITE_REG(FLASH->KEYR, FLASH_KEY1);
 80014b6:	4b08      	ldr	r3, [pc, #32]	@ (80014d8 <HAL_FLASH_Unlock+0x2c>)
 80014b8:	4a08      	ldr	r2, [pc, #32]	@ (80014dc <HAL_FLASH_Unlock+0x30>)
 80014ba:	605a      	str	r2, [r3, #4]
    WRITE_REG(FLASH->KEYR, FLASH_KEY2);
 80014bc:	f102 3288 	add.w	r2, r2, #2290649224	@ 0x88888888
 80014c0:	605a      	str	r2, [r3, #4]
    if(READ_BIT(FLASH->CR, FLASH_CR_LOCK) != RESET)
 80014c2:	691b      	ldr	r3, [r3, #16]
 80014c4:	f013 0f80 	tst.w	r3, #128	@ 0x80
 80014c8:	d103      	bne.n	80014d2 <HAL_FLASH_Unlock+0x26>
  HAL_StatusTypeDef status = HAL_OK;
 80014ca:	2000      	movs	r0, #0
 80014cc:	4770      	bx	lr
 80014ce:	2000      	movs	r0, #0
 80014d0:	4770      	bx	lr
      status = HAL_ERROR;
 80014d2:	2001      	movs	r0, #1
}
 80014d4:	4770      	bx	lr
 80014d6:	bf00      	nop
 80014d8:	******** 	.word	0x********
 80014dc:	45670123 	.word	0x45670123

080014e0 <HAL_FLASH_Lock>:
  SET_BIT(FLASH->CR, FLASH_CR_LOCK);
 80014e0:	4a03      	ldr	r2, [pc, #12]	@ (80014f0 <HAL_FLASH_Lock+0x10>)
 80014e2:	6913      	ldr	r3, [r2, #16]
 80014e4:	f043 0380 	orr.w	r3, r3, #128	@ 0x80
 80014e8:	6113      	str	r3, [r2, #16]
}
 80014ea:	2000      	movs	r0, #0
 80014ec:	4770      	bx	lr
 80014ee:	bf00      	nop
 80014f0:	******** 	.word	0x********

080014f4 <FLASH_WaitForLastOperation>:
{
 80014f4:	b538      	push	{r3, r4, r5, lr}
 80014f6:	4604      	mov	r4, r0
  uint32_t tickstart = HAL_GetTick();
 80014f8:	f7ff ff0e 	bl	8001318 <HAL_GetTick>
 80014fc:	4605      	mov	r5, r0
  while(__HAL_FLASH_GET_FLAG(FLASH_FLAG_BSY)) 
 80014fe:	4b16      	ldr	r3, [pc, #88]	@ (8001558 <FLASH_WaitForLastOperation+0x64>)
 8001500:	68db      	ldr	r3, [r3, #12]
 8001502:	f013 0f01 	tst.w	r3, #1
 8001506:	d00a      	beq.n	800151e <FLASH_WaitForLastOperation+0x2a>
    if (Timeout != HAL_MAX_DELAY)
 8001508:	f1b4 3fff 	cmp.w	r4, #4294967295
 800150c:	d0f7      	beq.n	80014fe <FLASH_WaitForLastOperation+0xa>
      if((Timeout == 0U) || ((HAL_GetTick()-tickstart) > Timeout))
 800150e:	b124      	cbz	r4, 800151a <FLASH_WaitForLastOperation+0x26>
 8001510:	f7ff ff02 	bl	8001318 <HAL_GetTick>
 8001514:	1b40      	subs	r0, r0, r5
 8001516:	42a0      	cmp	r0, r4
 8001518:	d9f1      	bls.n	80014fe <FLASH_WaitForLastOperation+0xa>
        return HAL_TIMEOUT;
 800151a:	2003      	movs	r0, #3
 800151c:	e01b      	b.n	8001556 <FLASH_WaitForLastOperation+0x62>
  if (__HAL_FLASH_GET_FLAG(FLASH_FLAG_EOP))
 800151e:	4b0e      	ldr	r3, [pc, #56]	@ (8001558 <FLASH_WaitForLastOperation+0x64>)
 8001520:	68db      	ldr	r3, [r3, #12]
 8001522:	f013 0f20 	tst.w	r3, #32
 8001526:	d002      	beq.n	800152e <FLASH_WaitForLastOperation+0x3a>
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP);
 8001528:	4b0b      	ldr	r3, [pc, #44]	@ (8001558 <FLASH_WaitForLastOperation+0x64>)
 800152a:	2220      	movs	r2, #32
 800152c:	60da      	str	r2, [r3, #12]
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR)  || 
 800152e:	4b0a      	ldr	r3, [pc, #40]	@ (8001558 <FLASH_WaitForLastOperation+0x64>)
 8001530:	68db      	ldr	r3, [r3, #12]
 8001532:	f013 0f10 	tst.w	r3, #16
 8001536:	d10b      	bne.n	8001550 <FLASH_WaitForLastOperation+0x5c>
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR) || 
 8001538:	4b07      	ldr	r3, [pc, #28]	@ (8001558 <FLASH_WaitForLastOperation+0x64>)
 800153a:	69db      	ldr	r3, [r3, #28]
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR)  || 
 800153c:	f013 0f01 	tst.w	r3, #1
 8001540:	d106      	bne.n	8001550 <FLASH_WaitForLastOperation+0x5c>
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR))
 8001542:	4b05      	ldr	r3, [pc, #20]	@ (8001558 <FLASH_WaitForLastOperation+0x64>)
 8001544:	68db      	ldr	r3, [r3, #12]
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR) || 
 8001546:	f013 0f04 	tst.w	r3, #4
 800154a:	d101      	bne.n	8001550 <FLASH_WaitForLastOperation+0x5c>
  return HAL_OK;
 800154c:	2000      	movs	r0, #0
 800154e:	e002      	b.n	8001556 <FLASH_WaitForLastOperation+0x62>
    FLASH_SetErrorCode();
 8001550:	f7ff ff74 	bl	800143c <FLASH_SetErrorCode>
    return HAL_ERROR;
 8001554:	2001      	movs	r0, #1
}
 8001556:	bd38      	pop	{r3, r4, r5, pc}
 8001558:	******** 	.word	0x********

0800155c <HAL_FLASH_Program>:
{
 800155c:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 8001560:	461e      	mov	r6, r3
  __HAL_LOCK(&pFlash);
 8001562:	4b24      	ldr	r3, [pc, #144]	@ (80015f4 <HAL_FLASH_Program+0x98>)
 8001564:	7e1b      	ldrb	r3, [r3, #24]
 8001566:	2b01      	cmp	r3, #1
 8001568:	d041      	beq.n	80015ee <HAL_FLASH_Program+0x92>
 800156a:	4604      	mov	r4, r0
 800156c:	460f      	mov	r7, r1
 800156e:	4690      	mov	r8, r2
 8001570:	4b20      	ldr	r3, [pc, #128]	@ (80015f4 <HAL_FLASH_Program+0x98>)
 8001572:	2201      	movs	r2, #1
 8001574:	761a      	strb	r2, [r3, #24]
    status = FLASH_WaitForLastOperation(FLASH_TIMEOUT_VALUE);
 8001576:	f24c 3050 	movw	r0, #50000	@ 0xc350
 800157a:	f7ff ffbb 	bl	80014f4 <FLASH_WaitForLastOperation>
  if(status == HAL_OK)
 800157e:	4603      	mov	r3, r0
 8001580:	bb78      	cbnz	r0, 80015e2 <HAL_FLASH_Program+0x86>
    if(TypeProgram == FLASH_TYPEPROGRAM_HALFWORD)
 8001582:	2c01      	cmp	r4, #1
 8001584:	d008      	beq.n	8001598 <HAL_FLASH_Program+0x3c>
    else if(TypeProgram == FLASH_TYPEPROGRAM_WORD)
 8001586:	2c02      	cmp	r4, #2
 8001588:	d003      	beq.n	8001592 <HAL_FLASH_Program+0x36>
      nbiterations = 4U;
 800158a:	f04f 0904 	mov.w	r9, #4
    for (index = 0U; index < nbiterations; index++)
 800158e:	461c      	mov	r4, r3
 8001590:	e007      	b.n	80015a2 <HAL_FLASH_Program+0x46>
      nbiterations = 2U;
 8001592:	f04f 0902 	mov.w	r9, #2
 8001596:	e7fa      	b.n	800158e <HAL_FLASH_Program+0x32>
      nbiterations = 1U;
 8001598:	f04f 0901 	mov.w	r9, #1
 800159c:	e7f7      	b.n	800158e <HAL_FLASH_Program+0x32>
    for (index = 0U; index < nbiterations; index++)
 800159e:	3401      	adds	r4, #1
 80015a0:	b2e4      	uxtb	r4, r4
 80015a2:	454c      	cmp	r4, r9
 80015a4:	d21d      	bcs.n	80015e2 <HAL_FLASH_Program+0x86>
      FLASH_Program_HalfWord((Address + (2U*index)), (uint16_t)(Data >> (16U*index)));
 80015a6:	0121      	lsls	r1, r4, #4
 80015a8:	f1c1 0220 	rsb	r2, r1, #32
 80015ac:	f1a1 0320 	sub.w	r3, r1, #32
 80015b0:	fa28 f101 	lsr.w	r1, r8, r1
 80015b4:	fa06 f202 	lsl.w	r2, r6, r2
 80015b8:	4311      	orrs	r1, r2
 80015ba:	fa26 f303 	lsr.w	r3, r6, r3
 80015be:	4319      	orrs	r1, r3
 80015c0:	b289      	uxth	r1, r1
 80015c2:	eb07 0044 	add.w	r0, r7, r4, lsl #1
 80015c6:	f7ff ff2b 	bl	8001420 <FLASH_Program_HalfWord>
        status = FLASH_WaitForLastOperation(FLASH_TIMEOUT_VALUE);
 80015ca:	f24c 3050 	movw	r0, #50000	@ 0xc350
 80015ce:	f7ff ff91 	bl	80014f4 <FLASH_WaitForLastOperation>
        CLEAR_BIT(FLASH->CR, FLASH_CR_PG);
 80015d2:	4b09      	ldr	r3, [pc, #36]	@ (80015f8 <HAL_FLASH_Program+0x9c>)
 80015d4:	691d      	ldr	r5, [r3, #16]
 80015d6:	f025 0501 	bic.w	r5, r5, #1
 80015da:	611d      	str	r5, [r3, #16]
      if (status != HAL_OK)
 80015dc:	4603      	mov	r3, r0
 80015de:	2800      	cmp	r0, #0
 80015e0:	d0dd      	beq.n	800159e <HAL_FLASH_Program+0x42>
  __HAL_UNLOCK(&pFlash);
 80015e2:	4a04      	ldr	r2, [pc, #16]	@ (80015f4 <HAL_FLASH_Program+0x98>)
 80015e4:	2100      	movs	r1, #0
 80015e6:	7611      	strb	r1, [r2, #24]
}
 80015e8:	4618      	mov	r0, r3
 80015ea:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
  __HAL_LOCK(&pFlash);
 80015ee:	2302      	movs	r3, #2
 80015f0:	e7fa      	b.n	80015e8 <HAL_FLASH_Program+0x8c>
 80015f2:	bf00      	nop
 80015f4:	******** 	.word	0x********
 80015f8:	******** 	.word	0x********

080015fc <FLASH_MassErase>:
{
  /* Check the parameters */
  assert_param(IS_FLASH_BANK(Banks));

  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 80015fc:	4b06      	ldr	r3, [pc, #24]	@ (8001618 <FLASH_MassErase+0x1c>)
 80015fe:	2200      	movs	r2, #0
 8001600:	61da      	str	r2, [r3, #28]
#if !defined(FLASH_BANK2_END)
  /* Prevent unused argument(s) compilation warning */
  UNUSED(Banks);
#endif /* FLASH_BANK2_END */  
    /* Only bank1 will be erased*/
    SET_BIT(FLASH->CR, FLASH_CR_MER);
 8001602:	4b06      	ldr	r3, [pc, #24]	@ (800161c <FLASH_MassErase+0x20>)
 8001604:	691a      	ldr	r2, [r3, #16]
 8001606:	f042 0204 	orr.w	r2, r2, #4
 800160a:	611a      	str	r2, [r3, #16]
    SET_BIT(FLASH->CR, FLASH_CR_STRT);
 800160c:	691a      	ldr	r2, [r3, #16]
 800160e:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8001612:	611a      	str	r2, [r3, #16]
#if defined(FLASH_BANK2_END)
  }
#endif /* FLASH_BANK2_END */
}
 8001614:	4770      	bx	lr
 8001616:	bf00      	nop
 8001618:	******** 	.word	0x********
 800161c:	******** 	.word	0x********

******** <FLASH_PageErase>:
  * @retval None
  */
void FLASH_PageErase(uint32_t PageAddress)
{
  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 8001620:	4b06      	ldr	r3, [pc, #24]	@ (800163c <FLASH_PageErase+0x1c>)
 8001622:	2200      	movs	r2, #0
 8001624:	61da      	str	r2, [r3, #28]
  }
  else
  {
#endif /* FLASH_BANK2_END */
    /* Proceed to erase the page */
    SET_BIT(FLASH->CR, FLASH_CR_PER);
 8001626:	4b06      	ldr	r3, [pc, #24]	@ (8001640 <FLASH_PageErase+0x20>)
 8001628:	691a      	ldr	r2, [r3, #16]
 800162a:	f042 0202 	orr.w	r2, r2, #2
 800162e:	611a      	str	r2, [r3, #16]
    WRITE_REG(FLASH->AR, PageAddress);
 8001630:	6158      	str	r0, [r3, #20]
    SET_BIT(FLASH->CR, FLASH_CR_STRT);
 8001632:	691a      	ldr	r2, [r3, #16]
 8001634:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8001638:	611a      	str	r2, [r3, #16]
#if defined(FLASH_BANK2_END)
  }
#endif /* FLASH_BANK2_END */
}
 800163a:	4770      	bx	lr
 800163c:	******** 	.word	0x********
 8001640:	******** 	.word	0x********

******** <HAL_FLASHEx_Erase>:
  __HAL_LOCK(&pFlash);
 8001644:	4b26      	ldr	r3, [pc, #152]	@ (80016e0 <HAL_FLASHEx_Erase+0x9c>)
 8001646:	7e1b      	ldrb	r3, [r3, #24]
 8001648:	2b01      	cmp	r3, #1
 800164a:	d046      	beq.n	80016da <HAL_FLASHEx_Erase+0x96>
{
 800164c:	b570      	push	{r4, r5, r6, lr}
 800164e:	4605      	mov	r5, r0
 8001650:	460e      	mov	r6, r1
  __HAL_LOCK(&pFlash);
 8001652:	4b23      	ldr	r3, [pc, #140]	@ (80016e0 <HAL_FLASHEx_Erase+0x9c>)
 8001654:	2201      	movs	r2, #1
 8001656:	761a      	strb	r2, [r3, #24]
  if (pEraseInit->TypeErase == FLASH_TYPEERASE_MASSERASE)
 8001658:	6803      	ldr	r3, [r0, #0]
 800165a:	2b02      	cmp	r3, #2
 800165c:	d020      	beq.n	80016a0 <HAL_FLASHEx_Erase+0x5c>
      if (FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE) == HAL_OK)
 800165e:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8001662:	f7ff ff47 	bl	80014f4 <FLASH_WaitForLastOperation>
 8001666:	bb90      	cbnz	r0, 80016ce <HAL_FLASHEx_Erase+0x8a>
        *PageError = 0xFFFFFFFFU;
 8001668:	f04f 33ff 	mov.w	r3, #4294967295
 800166c:	6033      	str	r3, [r6, #0]
        for(address = pEraseInit->PageAddress;
 800166e:	68ac      	ldr	r4, [r5, #8]
  HAL_StatusTypeDef status = HAL_ERROR;
 8001670:	2101      	movs	r1, #1
            address < ((pEraseInit->NbPages * FLASH_PAGE_SIZE) + pEraseInit->PageAddress);
 8001672:	68ea      	ldr	r2, [r5, #12]
 8001674:	68ab      	ldr	r3, [r5, #8]
 8001676:	eb03 2382 	add.w	r3, r3, r2, lsl #10
 800167a:	42a3      	cmp	r3, r4
 800167c:	d928      	bls.n	80016d0 <HAL_FLASHEx_Erase+0x8c>
          FLASH_PageErase(address);
 800167e:	4620      	mov	r0, r4
 8001680:	f7ff ffce 	bl	8001620 <FLASH_PageErase>
          status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE);
 8001684:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8001688:	f7ff ff34 	bl	80014f4 <FLASH_WaitForLastOperation>
          CLEAR_BIT(FLASH->CR, FLASH_CR_PER);
 800168c:	4a15      	ldr	r2, [pc, #84]	@ (80016e4 <HAL_FLASHEx_Erase+0xa0>)
 800168e:	6913      	ldr	r3, [r2, #16]
 8001690:	f023 0302 	bic.w	r3, r3, #2
 8001694:	6113      	str	r3, [r2, #16]
          if (status != HAL_OK)
 8001696:	4601      	mov	r1, r0
 8001698:	b9b8      	cbnz	r0, 80016ca <HAL_FLASHEx_Erase+0x86>
            address += FLASH_PAGE_SIZE)
 800169a:	f504 6480 	add.w	r4, r4, #1024	@ 0x400
 800169e:	e7e8      	b.n	8001672 <HAL_FLASHEx_Erase+0x2e>
      if (FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE) == HAL_OK)
 80016a0:	f24c 3050 	movw	r0, #50000	@ 0xc350
 80016a4:	f7ff ff26 	bl	80014f4 <FLASH_WaitForLastOperation>
 80016a8:	b108      	cbz	r0, 80016ae <HAL_FLASHEx_Erase+0x6a>
  HAL_StatusTypeDef status = HAL_ERROR;
 80016aa:	2101      	movs	r1, #1
 80016ac:	e010      	b.n	80016d0 <HAL_FLASHEx_Erase+0x8c>
        FLASH_MassErase(FLASH_BANK_1);
 80016ae:	2001      	movs	r0, #1
 80016b0:	f7ff ffa4 	bl	80015fc <FLASH_MassErase>
        status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE);
 80016b4:	f24c 3050 	movw	r0, #50000	@ 0xc350
 80016b8:	f7ff ff1c 	bl	80014f4 <FLASH_WaitForLastOperation>
 80016bc:	4601      	mov	r1, r0
        CLEAR_BIT(FLASH->CR, FLASH_CR_MER);
 80016be:	4a09      	ldr	r2, [pc, #36]	@ (80016e4 <HAL_FLASHEx_Erase+0xa0>)
 80016c0:	6913      	ldr	r3, [r2, #16]
 80016c2:	f023 0304 	bic.w	r3, r3, #4
 80016c6:	6113      	str	r3, [r2, #16]
 80016c8:	e002      	b.n	80016d0 <HAL_FLASHEx_Erase+0x8c>
            *PageError = address;
 80016ca:	6034      	str	r4, [r6, #0]
            break;
 80016cc:	e000      	b.n	80016d0 <HAL_FLASHEx_Erase+0x8c>
  HAL_StatusTypeDef status = HAL_ERROR;
 80016ce:	2101      	movs	r1, #1
  __HAL_UNLOCK(&pFlash);
 80016d0:	4b03      	ldr	r3, [pc, #12]	@ (80016e0 <HAL_FLASHEx_Erase+0x9c>)
 80016d2:	2200      	movs	r2, #0
 80016d4:	761a      	strb	r2, [r3, #24]
}
 80016d6:	4608      	mov	r0, r1
 80016d8:	bd70      	pop	{r4, r5, r6, pc}
  __HAL_LOCK(&pFlash);
 80016da:	2102      	movs	r1, #2
}
 80016dc:	4608      	mov	r0, r1
 80016de:	4770      	bx	lr
 80016e0:	******** 	.word	0x********
 80016e4:	******** 	.word	0x********

080016e8 <HAL_GPIO_Init>:
  * @param  GPIO_Init: pointer to a GPIO_InitTypeDef structure that contains
  *         the configuration information for the specified GPIO peripheral.
  * @retval None
  */
void HAL_GPIO_Init(GPIO_TypeDef  *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
 80016e8:	b570      	push	{r4, r5, r6, lr}
 80016ea:	b082      	sub	sp, #8
  uint32_t position = 0x00u;
  uint32_t ioposition;
  uint32_t iocurrent;
  uint32_t temp;
  uint32_t config = 0x00u;
 80016ec:	2400      	movs	r4, #0
  uint32_t position = 0x00u;
 80016ee:	46a4      	mov	ip, r4
  assert_param(IS_GPIO_ALL_INSTANCE(GPIOx));
  assert_param(IS_GPIO_PIN(GPIO_Init->Pin));
  assert_param(IS_GPIO_MODE(GPIO_Init->Mode));

  /* Configure the port pins */
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 80016f0:	e0a2      	b.n	8001838 <HAL_GPIO_Init+0x150>
    {
      /* Check the Alternate function parameters */
      assert_param(IS_GPIO_AF_INSTANCE(GPIOx));

      /* Based on the required mode, filling config variable with MODEy[1:0] and CNFy[3:2] corresponding bits */
      switch (GPIO_Init->Mode)
 80016f2:	4d7e      	ldr	r5, [pc, #504]	@ (80018ec <HAL_GPIO_Init+0x204>)
 80016f4:	42ab      	cmp	r3, r5
 80016f6:	d010      	beq.n	800171a <HAL_GPIO_Init+0x32>
 80016f8:	d907      	bls.n	800170a <HAL_GPIO_Init+0x22>
 80016fa:	4d7d      	ldr	r5, [pc, #500]	@ (80018f0 <HAL_GPIO_Init+0x208>)
 80016fc:	42ab      	cmp	r3, r5
 80016fe:	d00c      	beq.n	800171a <HAL_GPIO_Init+0x32>
 8001700:	f505 3580 	add.w	r5, r5, #65536	@ 0x10000
 8001704:	42ab      	cmp	r3, r5
 8001706:	d008      	beq.n	800171a <HAL_GPIO_Init+0x32>
 8001708:	e013      	b.n	8001732 <HAL_GPIO_Init+0x4a>
 800170a:	f5a5 1580 	sub.w	r5, r5, #1048576	@ 0x100000
 800170e:	42ab      	cmp	r3, r5
 8001710:	d003      	beq.n	800171a <HAL_GPIO_Init+0x32>
 8001712:	f505 2570 	add.w	r5, r5, #983040	@ 0xf0000
 8001716:	42ab      	cmp	r3, r5
 8001718:	d107      	bne.n	800172a <HAL_GPIO_Init+0x42>
        case GPIO_MODE_EVT_RISING:
        case GPIO_MODE_EVT_FALLING:
        case GPIO_MODE_EVT_RISING_FALLING:
          /* Check the GPIO pull parameter */
          assert_param(IS_GPIO_PULL(GPIO_Init->Pull));
          if (GPIO_Init->Pull == GPIO_NOPULL)
 800171a:	688b      	ldr	r3, [r1, #8]
 800171c:	2b00      	cmp	r3, #0
 800171e:	d055      	beq.n	80017cc <HAL_GPIO_Init+0xe4>
          {
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_FLOATING;
          }
          else if (GPIO_Init->Pull == GPIO_PULLUP)
 8001720:	2b01      	cmp	r3, #1
 8001722:	d04e      	beq.n	80017c2 <HAL_GPIO_Init+0xda>
          else /* GPIO_PULLDOWN */
          {
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;

            /* Reset the corresponding ODR bit */
            GPIOx->BRR = ioposition;
 8001724:	6142      	str	r2, [r0, #20]
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;
 8001726:	2408      	movs	r4, #8
 8001728:	e003      	b.n	8001732 <HAL_GPIO_Init+0x4a>
      switch (GPIO_Init->Mode)
 800172a:	f5a5 1580 	sub.w	r5, r5, #1048576	@ 0x100000
 800172e:	42ab      	cmp	r3, r5
 8001730:	d0f3      	beq.n	800171a <HAL_GPIO_Init+0x32>
          break;
      }

      /* Check if the current bit belongs to first half or last half of the pin count number
       in order to address CRH or CRL register*/
      configregister = (iocurrent < GPIO_PIN_8) ? &GPIOx->CRL     : &GPIOx->CRH;
 8001732:	f1be 0fff 	cmp.w	lr, #255	@ 0xff
 8001736:	d84b      	bhi.n	80017d0 <HAL_GPIO_Init+0xe8>
 8001738:	4606      	mov	r6, r0
      registeroffset = (iocurrent < GPIO_PIN_8) ? (position << 2u) : ((position - 8u) << 2u);
 800173a:	ea4f 028c 	mov.w	r2, ip, lsl #2

      /* Apply the new configuration of the pin to the register */
      MODIFY_REG((*configregister), ((GPIO_CRL_MODE0 | GPIO_CRL_CNF0) << registeroffset), (config << registeroffset));
 800173e:	6833      	ldr	r3, [r6, #0]
 8001740:	250f      	movs	r5, #15
 8001742:	4095      	lsls	r5, r2
 8001744:	ea23 0305 	bic.w	r3, r3, r5
 8001748:	fa04 f202 	lsl.w	r2, r4, r2
 800174c:	4313      	orrs	r3, r2
 800174e:	6033      	str	r3, [r6, #0]

      /*--------------------- EXTI Mode Configuration ------------------------*/
      /* Configure the External Interrupt or event for the current IO */
      if ((GPIO_Init->Mode & EXTI_MODE) == EXTI_MODE)
 8001750:	684b      	ldr	r3, [r1, #4]
 8001752:	f013 5f80 	tst.w	r3, #268435456	@ 0x10000000
 8001756:	d06d      	beq.n	8001834 <HAL_GPIO_Init+0x14c>
      {
        /* Enable AFIO Clock */
        __HAL_RCC_AFIO_CLK_ENABLE();
 8001758:	4b66      	ldr	r3, [pc, #408]	@ (80018f4 <HAL_GPIO_Init+0x20c>)
 800175a:	699a      	ldr	r2, [r3, #24]
 800175c:	f042 0201 	orr.w	r2, r2, #1
 8001760:	619a      	str	r2, [r3, #24]
 8001762:	699b      	ldr	r3, [r3, #24]
 8001764:	f003 0301 	and.w	r3, r3, #1
 8001768:	9301      	str	r3, [sp, #4]
 800176a:	9b01      	ldr	r3, [sp, #4]
        temp = AFIO->EXTICR[position >> 2u];
 800176c:	ea4f 029c 	mov.w	r2, ip, lsr #2
 8001770:	1c95      	adds	r5, r2, #2
 8001772:	4b61      	ldr	r3, [pc, #388]	@ (80018f8 <HAL_GPIO_Init+0x210>)
 8001774:	f853 6025 	ldr.w	r6, [r3, r5, lsl #2]
        CLEAR_BIT(temp, (0x0Fu) << (4u * (position & 0x03u)));
 8001778:	f00c 0503 	and.w	r5, ip, #3
 800177c:	00ad      	lsls	r5, r5, #2
 800177e:	230f      	movs	r3, #15
 8001780:	40ab      	lsls	r3, r5
 8001782:	ea26 0603 	bic.w	r6, r6, r3
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 8001786:	4b5d      	ldr	r3, [pc, #372]	@ (80018fc <HAL_GPIO_Init+0x214>)
 8001788:	4298      	cmp	r0, r3
 800178a:	d028      	beq.n	80017de <HAL_GPIO_Init+0xf6>
 800178c:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 8001790:	4298      	cmp	r0, r3
 8001792:	f000 808d 	beq.w	80018b0 <HAL_GPIO_Init+0x1c8>
 8001796:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 800179a:	4298      	cmp	r0, r3
 800179c:	f000 808a 	beq.w	80018b4 <HAL_GPIO_Init+0x1cc>
 80017a0:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 80017a4:	4298      	cmp	r0, r3
 80017a6:	d018      	beq.n	80017da <HAL_GPIO_Init+0xf2>
 80017a8:	2304      	movs	r3, #4
 80017aa:	e019      	b.n	80017e0 <HAL_GPIO_Init+0xf8>
          config = GPIO_Init->Speed + GPIO_CR_CNF_GP_OUTPUT_PP;
 80017ac:	68cc      	ldr	r4, [r1, #12]
          break;
 80017ae:	e7c0      	b.n	8001732 <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_GP_OUTPUT_OD;
 80017b0:	68cc      	ldr	r4, [r1, #12]
 80017b2:	3404      	adds	r4, #4
          break;
 80017b4:	e7bd      	b.n	8001732 <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_AF_OUTPUT_PP;
 80017b6:	68cc      	ldr	r4, [r1, #12]
 80017b8:	3408      	adds	r4, #8
          break;
 80017ba:	e7ba      	b.n	8001732 <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_AF_OUTPUT_OD;
 80017bc:	68cc      	ldr	r4, [r1, #12]
 80017be:	340c      	adds	r4, #12
          break;
 80017c0:	e7b7      	b.n	8001732 <HAL_GPIO_Init+0x4a>
            GPIOx->BSRR = ioposition;
 80017c2:	6102      	str	r2, [r0, #16]
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;
 80017c4:	2408      	movs	r4, #8
 80017c6:	e7b4      	b.n	8001732 <HAL_GPIO_Init+0x4a>
          config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_ANALOG;
 80017c8:	2400      	movs	r4, #0
 80017ca:	e7b2      	b.n	8001732 <HAL_GPIO_Init+0x4a>
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_FLOATING;
 80017cc:	2404      	movs	r4, #4
 80017ce:	e7b0      	b.n	8001732 <HAL_GPIO_Init+0x4a>
      configregister = (iocurrent < GPIO_PIN_8) ? &GPIOx->CRL     : &GPIOx->CRH;
 80017d0:	1d06      	adds	r6, r0, #4
      registeroffset = (iocurrent < GPIO_PIN_8) ? (position << 2u) : ((position - 8u) << 2u);
 80017d2:	f1ac 0208 	sub.w	r2, ip, #8
 80017d6:	0092      	lsls	r2, r2, #2
 80017d8:	e7b1      	b.n	800173e <HAL_GPIO_Init+0x56>
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 80017da:	2303      	movs	r3, #3
 80017dc:	e000      	b.n	80017e0 <HAL_GPIO_Init+0xf8>
 80017de:	2300      	movs	r3, #0
 80017e0:	40ab      	lsls	r3, r5
 80017e2:	4333      	orrs	r3, r6
        AFIO->EXTICR[position >> 2u] = temp;
 80017e4:	3202      	adds	r2, #2
 80017e6:	4d44      	ldr	r5, [pc, #272]	@ (80018f8 <HAL_GPIO_Init+0x210>)
 80017e8:	f845 3022 	str.w	r3, [r5, r2, lsl #2]


        /* Enable or disable the rising trigger */
        if ((GPIO_Init->Mode & RISING_EDGE) == RISING_EDGE)
 80017ec:	684b      	ldr	r3, [r1, #4]
 80017ee:	f413 1f80 	tst.w	r3, #1048576	@ 0x100000
 80017f2:	d061      	beq.n	80018b8 <HAL_GPIO_Init+0x1d0>
        {
          SET_BIT(EXTI->RTSR, iocurrent);
 80017f4:	4a42      	ldr	r2, [pc, #264]	@ (8001900 <HAL_GPIO_Init+0x218>)
 80017f6:	6893      	ldr	r3, [r2, #8]
 80017f8:	ea43 030e 	orr.w	r3, r3, lr
 80017fc:	6093      	str	r3, [r2, #8]
        {
          CLEAR_BIT(EXTI->RTSR, iocurrent);
        }

        /* Enable or disable the falling trigger */
        if ((GPIO_Init->Mode & FALLING_EDGE) == FALLING_EDGE)
 80017fe:	684b      	ldr	r3, [r1, #4]
 8001800:	f413 1f00 	tst.w	r3, #2097152	@ 0x200000
 8001804:	d05e      	beq.n	80018c4 <HAL_GPIO_Init+0x1dc>
        {
          SET_BIT(EXTI->FTSR, iocurrent);
 8001806:	4a3e      	ldr	r2, [pc, #248]	@ (8001900 <HAL_GPIO_Init+0x218>)
 8001808:	68d3      	ldr	r3, [r2, #12]
 800180a:	ea43 030e 	orr.w	r3, r3, lr
 800180e:	60d3      	str	r3, [r2, #12]
        {
          CLEAR_BIT(EXTI->FTSR, iocurrent);
        }

        /* Configure the event mask */
        if ((GPIO_Init->Mode & GPIO_MODE_EVT) == GPIO_MODE_EVT)
 8001810:	684b      	ldr	r3, [r1, #4]
 8001812:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8001816:	d05b      	beq.n	80018d0 <HAL_GPIO_Init+0x1e8>
        {
          SET_BIT(EXTI->EMR, iocurrent);
 8001818:	4a39      	ldr	r2, [pc, #228]	@ (8001900 <HAL_GPIO_Init+0x218>)
 800181a:	6853      	ldr	r3, [r2, #4]
 800181c:	ea43 030e 	orr.w	r3, r3, lr
 8001820:	6053      	str	r3, [r2, #4]
        {
          CLEAR_BIT(EXTI->EMR, iocurrent);
        }

        /* Configure the interrupt mask */
        if ((GPIO_Init->Mode & GPIO_MODE_IT) == GPIO_MODE_IT)
 8001822:	684b      	ldr	r3, [r1, #4]
 8001824:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8001828:	d058      	beq.n	80018dc <HAL_GPIO_Init+0x1f4>
        {
          SET_BIT(EXTI->IMR, iocurrent);
 800182a:	4a35      	ldr	r2, [pc, #212]	@ (8001900 <HAL_GPIO_Init+0x218>)
 800182c:	6813      	ldr	r3, [r2, #0]
 800182e:	ea43 030e 	orr.w	r3, r3, lr
 8001832:	6013      	str	r3, [r2, #0]
          CLEAR_BIT(EXTI->IMR, iocurrent);
        }
      }
    }

	position++;
 8001834:	f10c 0c01 	add.w	ip, ip, #1
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 8001838:	680b      	ldr	r3, [r1, #0]
 800183a:	fa33 f20c 	lsrs.w	r2, r3, ip
 800183e:	d053      	beq.n	80018e8 <HAL_GPIO_Init+0x200>
    ioposition = (0x01uL << position);
 8001840:	2201      	movs	r2, #1
 8001842:	fa02 f20c 	lsl.w	r2, r2, ip
    iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;
 8001846:	ea03 0e02 	and.w	lr, r3, r2
    if (iocurrent == ioposition)
 800184a:	ea32 0303 	bics.w	r3, r2, r3
 800184e:	d1f1      	bne.n	8001834 <HAL_GPIO_Init+0x14c>
      switch (GPIO_Init->Mode)
 8001850:	684b      	ldr	r3, [r1, #4]
 8001852:	2b12      	cmp	r3, #18
 8001854:	f63f af4d 	bhi.w	80016f2 <HAL_GPIO_Init+0xa>
 8001858:	2b12      	cmp	r3, #18
 800185a:	f63f af6a 	bhi.w	8001732 <HAL_GPIO_Init+0x4a>
 800185e:	a501      	add	r5, pc, #4	@ (adr r5, 8001864 <HAL_GPIO_Init+0x17c>)
 8001860:	f855 f023 	ldr.w	pc, [r5, r3, lsl #2]
 8001864:	0800171b 	.word	0x0800171b
 8001868:	080017ad 	.word	0x080017ad
 800186c:	080017b7 	.word	0x080017b7
 8001870:	080017c9 	.word	0x080017c9
 8001874:	08001733 	.word	0x08001733
 8001878:	08001733 	.word	0x08001733
 800187c:	08001733 	.word	0x08001733
 8001880:	08001733 	.word	0x08001733
 8001884:	08001733 	.word	0x08001733
 8001888:	08001733 	.word	0x08001733
 800188c:	08001733 	.word	0x08001733
 8001890:	08001733 	.word	0x08001733
 8001894:	08001733 	.word	0x08001733
 8001898:	08001733 	.word	0x08001733
 800189c:	08001733 	.word	0x08001733
 80018a0:	08001733 	.word	0x08001733
 80018a4:	08001733 	.word	0x08001733
 80018a8:	080017b1 	.word	0x080017b1
 80018ac:	080017bd 	.word	0x080017bd
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 80018b0:	2301      	movs	r3, #1
 80018b2:	e795      	b.n	80017e0 <HAL_GPIO_Init+0xf8>
 80018b4:	2302      	movs	r3, #2
 80018b6:	e793      	b.n	80017e0 <HAL_GPIO_Init+0xf8>
          CLEAR_BIT(EXTI->RTSR, iocurrent);
 80018b8:	4a11      	ldr	r2, [pc, #68]	@ (8001900 <HAL_GPIO_Init+0x218>)
 80018ba:	6893      	ldr	r3, [r2, #8]
 80018bc:	ea23 030e 	bic.w	r3, r3, lr
 80018c0:	6093      	str	r3, [r2, #8]
 80018c2:	e79c      	b.n	80017fe <HAL_GPIO_Init+0x116>
          CLEAR_BIT(EXTI->FTSR, iocurrent);
 80018c4:	4a0e      	ldr	r2, [pc, #56]	@ (8001900 <HAL_GPIO_Init+0x218>)
 80018c6:	68d3      	ldr	r3, [r2, #12]
 80018c8:	ea23 030e 	bic.w	r3, r3, lr
 80018cc:	60d3      	str	r3, [r2, #12]
 80018ce:	e79f      	b.n	8001810 <HAL_GPIO_Init+0x128>
          CLEAR_BIT(EXTI->EMR, iocurrent);
 80018d0:	4a0b      	ldr	r2, [pc, #44]	@ (8001900 <HAL_GPIO_Init+0x218>)
 80018d2:	6853      	ldr	r3, [r2, #4]
 80018d4:	ea23 030e 	bic.w	r3, r3, lr
 80018d8:	6053      	str	r3, [r2, #4]
 80018da:	e7a2      	b.n	8001822 <HAL_GPIO_Init+0x13a>
          CLEAR_BIT(EXTI->IMR, iocurrent);
 80018dc:	4a08      	ldr	r2, [pc, #32]	@ (8001900 <HAL_GPIO_Init+0x218>)
 80018de:	6813      	ldr	r3, [r2, #0]
 80018e0:	ea23 030e 	bic.w	r3, r3, lr
 80018e4:	6013      	str	r3, [r2, #0]
 80018e6:	e7a5      	b.n	8001834 <HAL_GPIO_Init+0x14c>
  }
}
 80018e8:	b002      	add	sp, #8
 80018ea:	bd70      	pop	{r4, r5, r6, pc}
 80018ec:	10220000 	.word	0x10220000
 80018f0:	10310000 	.word	0x10310000
 80018f4:	40021000 	.word	0x40021000
 80018f8:	40010000 	.word	0x40010000
 80018fc:	40010800 	.word	0x40010800
 8001900:	40010400 	.word	0x40010400

08001904 <HAL_GPIO_WritePin>:
{
  /* Check the parameters */
  assert_param(IS_GPIO_PIN(GPIO_Pin));
  assert_param(IS_GPIO_PIN_ACTION(PinState));

  if (PinState != GPIO_PIN_RESET)
 8001904:	b10a      	cbz	r2, 800190a <HAL_GPIO_WritePin+0x6>
  {
    GPIOx->BSRR = GPIO_Pin;
 8001906:	6101      	str	r1, [r0, #16]
 8001908:	4770      	bx	lr
  }
  else
  {
    GPIOx->BSRR = (uint32_t)GPIO_Pin << 16u;
 800190a:	0409      	lsls	r1, r1, #16
 800190c:	6101      	str	r1, [r0, #16]
  }
}
 800190e:	4770      	bx	lr

08001910 <HAL_GPIO_TogglePin>:

  /* Check the parameters */
  assert_param(IS_GPIO_PIN(GPIO_Pin));

  /* get current Output Data Register value */
  odr = GPIOx->ODR;
 8001910:	68c3      	ldr	r3, [r0, #12]

  /* Set selected pins that were at low level, and reset ones that were high */
  GPIOx->BSRR = ((odr & GPIO_Pin) << GPIO_NUMBER) | (~odr & GPIO_Pin);
 8001912:	ea01 0203 	and.w	r2, r1, r3
 8001916:	ea21 0103 	bic.w	r1, r1, r3
 800191a:	ea41 4102 	orr.w	r1, r1, r2, lsl #16
 800191e:	6101      	str	r1, [r0, #16]
}
 8001920:	4770      	bx	lr
	...

08001924 <RCC_Delay>:
  * @brief  This function provides delay (in milliseconds) based on CPU cycles method.
  * @param  mdelay: specifies the delay time length, in milliseconds.
  * @retval None
  */
static void RCC_Delay(uint32_t mdelay)
{
 8001924:	b082      	sub	sp, #8
  __IO uint32_t Delay = mdelay * (SystemCoreClock / 8U / 1000U);
 8001926:	4b08      	ldr	r3, [pc, #32]	@ (8001948 <RCC_Delay+0x24>)
 8001928:	681b      	ldr	r3, [r3, #0]
 800192a:	4a08      	ldr	r2, [pc, #32]	@ (800194c <RCC_Delay+0x28>)
 800192c:	fba2 2303 	umull	r2, r3, r2, r3
 8001930:	0a5b      	lsrs	r3, r3, #9
 8001932:	fb00 f303 	mul.w	r3, r0, r3
 8001936:	9301      	str	r3, [sp, #4]
  do
  {
    __NOP();
 8001938:	bf00      	nop
  }
  while (Delay --);
 800193a:	9b01      	ldr	r3, [sp, #4]
 800193c:	1e5a      	subs	r2, r3, #1
 800193e:	9201      	str	r2, [sp, #4]
 8001940:	2b00      	cmp	r3, #0
 8001942:	d1f9      	bne.n	8001938 <RCC_Delay+0x14>
}
 8001944:	b002      	add	sp, #8
 8001946:	4770      	bx	lr
 8001948:	20000004 	.word	0x20000004
 800194c:	10624dd3 	.word	0x10624dd3

08001950 <HAL_RCC_OscConfig>:
  if (RCC_OscInitStruct == NULL)
 8001950:	2800      	cmp	r0, #0
 8001952:	f000 81f1 	beq.w	8001d38 <HAL_RCC_OscConfig+0x3e8>
{
 8001956:	b570      	push	{r4, r5, r6, lr}
 8001958:	b082      	sub	sp, #8
 800195a:	4604      	mov	r4, r0
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSE) == RCC_OSCILLATORTYPE_HSE)
 800195c:	6803      	ldr	r3, [r0, #0]
 800195e:	f013 0f01 	tst.w	r3, #1
 8001962:	d02c      	beq.n	80019be <HAL_RCC_OscConfig+0x6e>
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSE)
 8001964:	4b99      	ldr	r3, [pc, #612]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001966:	685b      	ldr	r3, [r3, #4]
 8001968:	f003 030c 	and.w	r3, r3, #12
 800196c:	2b04      	cmp	r3, #4
 800196e:	d01d      	beq.n	80019ac <HAL_RCC_OscConfig+0x5c>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSE)))
 8001970:	4b96      	ldr	r3, [pc, #600]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001972:	685b      	ldr	r3, [r3, #4]
 8001974:	f003 030c 	and.w	r3, r3, #12
 8001978:	2b08      	cmp	r3, #8
 800197a:	d012      	beq.n	80019a2 <HAL_RCC_OscConfig+0x52>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 800197c:	6863      	ldr	r3, [r4, #4]
 800197e:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 8001982:	d041      	beq.n	8001a08 <HAL_RCC_OscConfig+0xb8>
 8001984:	2b00      	cmp	r3, #0
 8001986:	d155      	bne.n	8001a34 <HAL_RCC_OscConfig+0xe4>
 8001988:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 800198c:	f503 3304 	add.w	r3, r3, #135168	@ 0x21000
 8001990:	681a      	ldr	r2, [r3, #0]
 8001992:	f422 3280 	bic.w	r2, r2, #65536	@ 0x10000
 8001996:	601a      	str	r2, [r3, #0]
 8001998:	681a      	ldr	r2, [r3, #0]
 800199a:	f422 2280 	bic.w	r2, r2, #262144	@ 0x40000
 800199e:	601a      	str	r2, [r3, #0]
 80019a0:	e037      	b.n	8001a12 <HAL_RCC_OscConfig+0xc2>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSE)))
 80019a2:	4b8a      	ldr	r3, [pc, #552]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 80019a4:	685b      	ldr	r3, [r3, #4]
 80019a6:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 80019aa:	d0e7      	beq.n	800197c <HAL_RCC_OscConfig+0x2c>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET) && (RCC_OscInitStruct->HSEState == RCC_HSE_OFF))
 80019ac:	4b87      	ldr	r3, [pc, #540]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 80019ae:	681b      	ldr	r3, [r3, #0]
 80019b0:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 80019b4:	d003      	beq.n	80019be <HAL_RCC_OscConfig+0x6e>
 80019b6:	6863      	ldr	r3, [r4, #4]
 80019b8:	2b00      	cmp	r3, #0
 80019ba:	f000 81bf 	beq.w	8001d3c <HAL_RCC_OscConfig+0x3ec>
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSI) == RCC_OSCILLATORTYPE_HSI)
 80019be:	6823      	ldr	r3, [r4, #0]
 80019c0:	f013 0f02 	tst.w	r3, #2
 80019c4:	d075      	beq.n	8001ab2 <HAL_RCC_OscConfig+0x162>
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSI)
 80019c6:	4b81      	ldr	r3, [pc, #516]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 80019c8:	685b      	ldr	r3, [r3, #4]
 80019ca:	f013 0f0c 	tst.w	r3, #12
 80019ce:	d05f      	beq.n	8001a90 <HAL_RCC_OscConfig+0x140>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSI_DIV2)))
 80019d0:	4b7e      	ldr	r3, [pc, #504]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 80019d2:	685b      	ldr	r3, [r3, #4]
 80019d4:	f003 030c 	and.w	r3, r3, #12
 80019d8:	2b08      	cmp	r3, #8
 80019da:	d054      	beq.n	8001a86 <HAL_RCC_OscConfig+0x136>
      if (RCC_OscInitStruct->HSIState != RCC_HSI_OFF)
 80019dc:	6923      	ldr	r3, [r4, #16]
 80019de:	2b00      	cmp	r3, #0
 80019e0:	f000 808a 	beq.w	8001af8 <HAL_RCC_OscConfig+0x1a8>
        __HAL_RCC_HSI_ENABLE();
 80019e4:	4b7a      	ldr	r3, [pc, #488]	@ (8001bd0 <HAL_RCC_OscConfig+0x280>)
 80019e6:	2201      	movs	r2, #1
 80019e8:	601a      	str	r2, [r3, #0]
        tickstart = HAL_GetTick();
 80019ea:	f7ff fc95 	bl	8001318 <HAL_GetTick>
 80019ee:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 80019f0:	4b76      	ldr	r3, [pc, #472]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 80019f2:	681b      	ldr	r3, [r3, #0]
 80019f4:	f013 0f02 	tst.w	r3, #2
 80019f8:	d175      	bne.n	8001ae6 <HAL_RCC_OscConfig+0x196>
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 80019fa:	f7ff fc8d 	bl	8001318 <HAL_GetTick>
 80019fe:	1b40      	subs	r0, r0, r5
 8001a00:	2802      	cmp	r0, #2
 8001a02:	d9f5      	bls.n	80019f0 <HAL_RCC_OscConfig+0xa0>
            return HAL_TIMEOUT;
 8001a04:	2003      	movs	r0, #3
 8001a06:	e19e      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8001a08:	4a70      	ldr	r2, [pc, #448]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001a0a:	6813      	ldr	r3, [r2, #0]
 8001a0c:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 8001a10:	6013      	str	r3, [r2, #0]
      if (RCC_OscInitStruct->HSEState != RCC_HSE_OFF)
 8001a12:	6863      	ldr	r3, [r4, #4]
 8001a14:	b343      	cbz	r3, 8001a68 <HAL_RCC_OscConfig+0x118>
        tickstart = HAL_GetTick();
 8001a16:	f7ff fc7f 	bl	8001318 <HAL_GetTick>
 8001a1a:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8001a1c:	4b6b      	ldr	r3, [pc, #428]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001a1e:	681b      	ldr	r3, [r3, #0]
 8001a20:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8001a24:	d1cb      	bne.n	80019be <HAL_RCC_OscConfig+0x6e>
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 8001a26:	f7ff fc77 	bl	8001318 <HAL_GetTick>
 8001a2a:	1b40      	subs	r0, r0, r5
 8001a2c:	2864      	cmp	r0, #100	@ 0x64
 8001a2e:	d9f5      	bls.n	8001a1c <HAL_RCC_OscConfig+0xcc>
            return HAL_TIMEOUT;
 8001a30:	2003      	movs	r0, #3
 8001a32:	e188      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8001a34:	f5b3 2fa0 	cmp.w	r3, #327680	@ 0x50000
 8001a38:	d009      	beq.n	8001a4e <HAL_RCC_OscConfig+0xfe>
 8001a3a:	4b64      	ldr	r3, [pc, #400]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001a3c:	681a      	ldr	r2, [r3, #0]
 8001a3e:	f422 3280 	bic.w	r2, r2, #65536	@ 0x10000
 8001a42:	601a      	str	r2, [r3, #0]
 8001a44:	681a      	ldr	r2, [r3, #0]
 8001a46:	f422 2280 	bic.w	r2, r2, #262144	@ 0x40000
 8001a4a:	601a      	str	r2, [r3, #0]
 8001a4c:	e7e1      	b.n	8001a12 <HAL_RCC_OscConfig+0xc2>
 8001a4e:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 8001a52:	f5a3 333c 	sub.w	r3, r3, #192512	@ 0x2f000
 8001a56:	681a      	ldr	r2, [r3, #0]
 8001a58:	f442 2280 	orr.w	r2, r2, #262144	@ 0x40000
 8001a5c:	601a      	str	r2, [r3, #0]
 8001a5e:	681a      	ldr	r2, [r3, #0]
 8001a60:	f442 3280 	orr.w	r2, r2, #65536	@ 0x10000
 8001a64:	601a      	str	r2, [r3, #0]
 8001a66:	e7d4      	b.n	8001a12 <HAL_RCC_OscConfig+0xc2>
        tickstart = HAL_GetTick();
 8001a68:	f7ff fc56 	bl	8001318 <HAL_GetTick>
 8001a6c:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 8001a6e:	4b57      	ldr	r3, [pc, #348]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001a70:	681b      	ldr	r3, [r3, #0]
 8001a72:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8001a76:	d0a2      	beq.n	80019be <HAL_RCC_OscConfig+0x6e>
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 8001a78:	f7ff fc4e 	bl	8001318 <HAL_GetTick>
 8001a7c:	1b40      	subs	r0, r0, r5
 8001a7e:	2864      	cmp	r0, #100	@ 0x64
 8001a80:	d9f5      	bls.n	8001a6e <HAL_RCC_OscConfig+0x11e>
            return HAL_TIMEOUT;
 8001a82:	2003      	movs	r0, #3
 8001a84:	e15f      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSI_DIV2)))
 8001a86:	4b51      	ldr	r3, [pc, #324]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001a88:	685b      	ldr	r3, [r3, #4]
 8001a8a:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8001a8e:	d1a5      	bne.n	80019dc <HAL_RCC_OscConfig+0x8c>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET) && (RCC_OscInitStruct->HSIState != RCC_HSI_ON))
 8001a90:	4b4e      	ldr	r3, [pc, #312]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001a92:	681b      	ldr	r3, [r3, #0]
 8001a94:	f013 0f02 	tst.w	r3, #2
 8001a98:	d003      	beq.n	8001aa2 <HAL_RCC_OscConfig+0x152>
 8001a9a:	6923      	ldr	r3, [r4, #16]
 8001a9c:	2b01      	cmp	r3, #1
 8001a9e:	f040 814f 	bne.w	8001d40 <HAL_RCC_OscConfig+0x3f0>
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 8001aa2:	4a4a      	ldr	r2, [pc, #296]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001aa4:	6813      	ldr	r3, [r2, #0]
 8001aa6:	f023 03f8 	bic.w	r3, r3, #248	@ 0xf8
 8001aaa:	6961      	ldr	r1, [r4, #20]
 8001aac:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 8001ab0:	6013      	str	r3, [r2, #0]
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 8001ab2:	6823      	ldr	r3, [r4, #0]
 8001ab4:	f013 0f08 	tst.w	r3, #8
 8001ab8:	d033      	beq.n	8001b22 <HAL_RCC_OscConfig+0x1d2>
    if (RCC_OscInitStruct->LSIState != RCC_LSI_OFF)
 8001aba:	69a3      	ldr	r3, [r4, #24]
 8001abc:	2b00      	cmp	r3, #0
 8001abe:	d05c      	beq.n	8001b7a <HAL_RCC_OscConfig+0x22a>
      __HAL_RCC_LSI_ENABLE();
 8001ac0:	4b43      	ldr	r3, [pc, #268]	@ (8001bd0 <HAL_RCC_OscConfig+0x280>)
 8001ac2:	2201      	movs	r2, #1
 8001ac4:	f8c3 2480 	str.w	r2, [r3, #1152]	@ 0x480
      tickstart = HAL_GetTick();
 8001ac8:	f7ff fc26 	bl	8001318 <HAL_GetTick>
 8001acc:	4605      	mov	r5, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 8001ace:	4b3f      	ldr	r3, [pc, #252]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001ad0:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8001ad2:	f013 0f02 	tst.w	r3, #2
 8001ad6:	d121      	bne.n	8001b1c <HAL_RCC_OscConfig+0x1cc>
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 8001ad8:	f7ff fc1e 	bl	8001318 <HAL_GetTick>
 8001adc:	1b40      	subs	r0, r0, r5
 8001ade:	2802      	cmp	r0, #2
 8001ae0:	d9f5      	bls.n	8001ace <HAL_RCC_OscConfig+0x17e>
          return HAL_TIMEOUT;
 8001ae2:	2003      	movs	r0, #3
 8001ae4:	e12f      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 8001ae6:	4a39      	ldr	r2, [pc, #228]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001ae8:	6813      	ldr	r3, [r2, #0]
 8001aea:	f023 03f8 	bic.w	r3, r3, #248	@ 0xf8
 8001aee:	6961      	ldr	r1, [r4, #20]
 8001af0:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 8001af4:	6013      	str	r3, [r2, #0]
 8001af6:	e7dc      	b.n	8001ab2 <HAL_RCC_OscConfig+0x162>
        __HAL_RCC_HSI_DISABLE();
 8001af8:	4b35      	ldr	r3, [pc, #212]	@ (8001bd0 <HAL_RCC_OscConfig+0x280>)
 8001afa:	2200      	movs	r2, #0
 8001afc:	601a      	str	r2, [r3, #0]
        tickstart = HAL_GetTick();
 8001afe:	f7ff fc0b 	bl	8001318 <HAL_GetTick>
 8001b02:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 8001b04:	4b31      	ldr	r3, [pc, #196]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001b06:	681b      	ldr	r3, [r3, #0]
 8001b08:	f013 0f02 	tst.w	r3, #2
 8001b0c:	d0d1      	beq.n	8001ab2 <HAL_RCC_OscConfig+0x162>
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 8001b0e:	f7ff fc03 	bl	8001318 <HAL_GetTick>
 8001b12:	1b40      	subs	r0, r0, r5
 8001b14:	2802      	cmp	r0, #2
 8001b16:	d9f5      	bls.n	8001b04 <HAL_RCC_OscConfig+0x1b4>
            return HAL_TIMEOUT;
 8001b18:	2003      	movs	r0, #3
 8001b1a:	e114      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
      RCC_Delay(1);
 8001b1c:	2001      	movs	r0, #1
 8001b1e:	f7ff ff01 	bl	8001924 <RCC_Delay>
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSE) == RCC_OSCILLATORTYPE_LSE)
 8001b22:	6823      	ldr	r3, [r4, #0]
 8001b24:	f013 0f04 	tst.w	r3, #4
 8001b28:	f000 8096 	beq.w	8001c58 <HAL_RCC_OscConfig+0x308>
    if (__HAL_RCC_PWR_IS_CLK_DISABLED())
 8001b2c:	4b27      	ldr	r3, [pc, #156]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001b2e:	69db      	ldr	r3, [r3, #28]
 8001b30:	f013 5f80 	tst.w	r3, #268435456	@ 0x10000000
 8001b34:	d134      	bne.n	8001ba0 <HAL_RCC_OscConfig+0x250>
      __HAL_RCC_PWR_CLK_ENABLE();
 8001b36:	4b25      	ldr	r3, [pc, #148]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001b38:	69da      	ldr	r2, [r3, #28]
 8001b3a:	f042 5280 	orr.w	r2, r2, #268435456	@ 0x10000000
 8001b3e:	61da      	str	r2, [r3, #28]
 8001b40:	69db      	ldr	r3, [r3, #28]
 8001b42:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8001b46:	9301      	str	r3, [sp, #4]
 8001b48:	9b01      	ldr	r3, [sp, #4]
      pwrclkchanged = SET;
 8001b4a:	2501      	movs	r5, #1
    if (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8001b4c:	4b21      	ldr	r3, [pc, #132]	@ (8001bd4 <HAL_RCC_OscConfig+0x284>)
 8001b4e:	681b      	ldr	r3, [r3, #0]
 8001b50:	f413 7f80 	tst.w	r3, #256	@ 0x100
 8001b54:	d026      	beq.n	8001ba4 <HAL_RCC_OscConfig+0x254>
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 8001b56:	68e3      	ldr	r3, [r4, #12]
 8001b58:	2b01      	cmp	r3, #1
 8001b5a:	d03d      	beq.n	8001bd8 <HAL_RCC_OscConfig+0x288>
 8001b5c:	2b00      	cmp	r3, #0
 8001b5e:	d153      	bne.n	8001c08 <HAL_RCC_OscConfig+0x2b8>
 8001b60:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 8001b64:	f503 3304 	add.w	r3, r3, #135168	@ 0x21000
 8001b68:	6a1a      	ldr	r2, [r3, #32]
 8001b6a:	f022 0201 	bic.w	r2, r2, #1
 8001b6e:	621a      	str	r2, [r3, #32]
 8001b70:	6a1a      	ldr	r2, [r3, #32]
 8001b72:	f022 0204 	bic.w	r2, r2, #4
 8001b76:	621a      	str	r2, [r3, #32]
 8001b78:	e033      	b.n	8001be2 <HAL_RCC_OscConfig+0x292>
      __HAL_RCC_LSI_DISABLE();
 8001b7a:	4b15      	ldr	r3, [pc, #84]	@ (8001bd0 <HAL_RCC_OscConfig+0x280>)
 8001b7c:	2200      	movs	r2, #0
 8001b7e:	f8c3 2480 	str.w	r2, [r3, #1152]	@ 0x480
      tickstart = HAL_GetTick();
 8001b82:	f7ff fbc9 	bl	8001318 <HAL_GetTick>
 8001b86:	4605      	mov	r5, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 8001b88:	4b10      	ldr	r3, [pc, #64]	@ (8001bcc <HAL_RCC_OscConfig+0x27c>)
 8001b8a:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8001b8c:	f013 0f02 	tst.w	r3, #2
 8001b90:	d0c7      	beq.n	8001b22 <HAL_RCC_OscConfig+0x1d2>
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 8001b92:	f7ff fbc1 	bl	8001318 <HAL_GetTick>
 8001b96:	1b40      	subs	r0, r0, r5
 8001b98:	2802      	cmp	r0, #2
 8001b9a:	d9f5      	bls.n	8001b88 <HAL_RCC_OscConfig+0x238>
          return HAL_TIMEOUT;
 8001b9c:	2003      	movs	r0, #3
 8001b9e:	e0d2      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
    FlagStatus       pwrclkchanged = RESET;
 8001ba0:	2500      	movs	r5, #0
 8001ba2:	e7d3      	b.n	8001b4c <HAL_RCC_OscConfig+0x1fc>
      SET_BIT(PWR->CR, PWR_CR_DBP);
 8001ba4:	4a0b      	ldr	r2, [pc, #44]	@ (8001bd4 <HAL_RCC_OscConfig+0x284>)
 8001ba6:	6813      	ldr	r3, [r2, #0]
 8001ba8:	f443 7380 	orr.w	r3, r3, #256	@ 0x100
 8001bac:	6013      	str	r3, [r2, #0]
      tickstart = HAL_GetTick();
 8001bae:	f7ff fbb3 	bl	8001318 <HAL_GetTick>
 8001bb2:	4606      	mov	r6, r0
      while (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8001bb4:	4b07      	ldr	r3, [pc, #28]	@ (8001bd4 <HAL_RCC_OscConfig+0x284>)
 8001bb6:	681b      	ldr	r3, [r3, #0]
 8001bb8:	f413 7f80 	tst.w	r3, #256	@ 0x100
 8001bbc:	d1cb      	bne.n	8001b56 <HAL_RCC_OscConfig+0x206>
        if ((HAL_GetTick() - tickstart) > RCC_DBP_TIMEOUT_VALUE)
 8001bbe:	f7ff fbab 	bl	8001318 <HAL_GetTick>
 8001bc2:	1b80      	subs	r0, r0, r6
 8001bc4:	2864      	cmp	r0, #100	@ 0x64
 8001bc6:	d9f5      	bls.n	8001bb4 <HAL_RCC_OscConfig+0x264>
          return HAL_TIMEOUT;
 8001bc8:	2003      	movs	r0, #3
 8001bca:	e0bc      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
 8001bcc:	40021000 	.word	0x40021000
 8001bd0:	42420000 	.word	0x42420000
 8001bd4:	40007000 	.word	0x40007000
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 8001bd8:	4a5f      	ldr	r2, [pc, #380]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001bda:	6a13      	ldr	r3, [r2, #32]
 8001bdc:	f043 0301 	orr.w	r3, r3, #1
 8001be0:	6213      	str	r3, [r2, #32]
    if (RCC_OscInitStruct->LSEState != RCC_LSE_OFF)
 8001be2:	68e3      	ldr	r3, [r4, #12]
 8001be4:	b333      	cbz	r3, 8001c34 <HAL_RCC_OscConfig+0x2e4>
      tickstart = HAL_GetTick();
 8001be6:	f7ff fb97 	bl	8001318 <HAL_GetTick>
 8001bea:	4606      	mov	r6, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 8001bec:	4b5a      	ldr	r3, [pc, #360]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001bee:	6a1b      	ldr	r3, [r3, #32]
 8001bf0:	f013 0f02 	tst.w	r3, #2
 8001bf4:	d12f      	bne.n	8001c56 <HAL_RCC_OscConfig+0x306>
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 8001bf6:	f7ff fb8f 	bl	8001318 <HAL_GetTick>
 8001bfa:	1b80      	subs	r0, r0, r6
 8001bfc:	f241 3388 	movw	r3, #5000	@ 0x1388
 8001c00:	4298      	cmp	r0, r3
 8001c02:	d9f3      	bls.n	8001bec <HAL_RCC_OscConfig+0x29c>
          return HAL_TIMEOUT;
 8001c04:	2003      	movs	r0, #3
 8001c06:	e09e      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 8001c08:	2b05      	cmp	r3, #5
 8001c0a:	d009      	beq.n	8001c20 <HAL_RCC_OscConfig+0x2d0>
 8001c0c:	4b52      	ldr	r3, [pc, #328]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001c0e:	6a1a      	ldr	r2, [r3, #32]
 8001c10:	f022 0201 	bic.w	r2, r2, #1
 8001c14:	621a      	str	r2, [r3, #32]
 8001c16:	6a1a      	ldr	r2, [r3, #32]
 8001c18:	f022 0204 	bic.w	r2, r2, #4
 8001c1c:	621a      	str	r2, [r3, #32]
 8001c1e:	e7e0      	b.n	8001be2 <HAL_RCC_OscConfig+0x292>
 8001c20:	4b4d      	ldr	r3, [pc, #308]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001c22:	6a1a      	ldr	r2, [r3, #32]
 8001c24:	f042 0204 	orr.w	r2, r2, #4
 8001c28:	621a      	str	r2, [r3, #32]
 8001c2a:	6a1a      	ldr	r2, [r3, #32]
 8001c2c:	f042 0201 	orr.w	r2, r2, #1
 8001c30:	621a      	str	r2, [r3, #32]
 8001c32:	e7d6      	b.n	8001be2 <HAL_RCC_OscConfig+0x292>
      tickstart = HAL_GetTick();
 8001c34:	f7ff fb70 	bl	8001318 <HAL_GetTick>
 8001c38:	4606      	mov	r6, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 8001c3a:	4b47      	ldr	r3, [pc, #284]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001c3c:	6a1b      	ldr	r3, [r3, #32]
 8001c3e:	f013 0f02 	tst.w	r3, #2
 8001c42:	d008      	beq.n	8001c56 <HAL_RCC_OscConfig+0x306>
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 8001c44:	f7ff fb68 	bl	8001318 <HAL_GetTick>
 8001c48:	1b80      	subs	r0, r0, r6
 8001c4a:	f241 3388 	movw	r3, #5000	@ 0x1388
 8001c4e:	4298      	cmp	r0, r3
 8001c50:	d9f3      	bls.n	8001c3a <HAL_RCC_OscConfig+0x2ea>
          return HAL_TIMEOUT;
 8001c52:	2003      	movs	r0, #3
 8001c54:	e077      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
    if (pwrclkchanged == SET)
 8001c56:	b9e5      	cbnz	r5, 8001c92 <HAL_RCC_OscConfig+0x342>
  if ((RCC_OscInitStruct->PLL.PLLState) != RCC_PLL_NONE)
 8001c58:	69e3      	ldr	r3, [r4, #28]
 8001c5a:	2b00      	cmp	r3, #0
 8001c5c:	d072      	beq.n	8001d44 <HAL_RCC_OscConfig+0x3f4>
    if (__HAL_RCC_GET_SYSCLK_SOURCE() != RCC_SYSCLKSOURCE_STATUS_PLLCLK)
 8001c5e:	4a3e      	ldr	r2, [pc, #248]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001c60:	6852      	ldr	r2, [r2, #4]
 8001c62:	f002 020c 	and.w	r2, r2, #12
 8001c66:	2a08      	cmp	r2, #8
 8001c68:	d056      	beq.n	8001d18 <HAL_RCC_OscConfig+0x3c8>
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_ON)
 8001c6a:	2b02      	cmp	r3, #2
 8001c6c:	d017      	beq.n	8001c9e <HAL_RCC_OscConfig+0x34e>
        __HAL_RCC_PLL_DISABLE();
 8001c6e:	4b3b      	ldr	r3, [pc, #236]	@ (8001d5c <HAL_RCC_OscConfig+0x40c>)
 8001c70:	2200      	movs	r2, #0
 8001c72:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 8001c74:	f7ff fb50 	bl	8001318 <HAL_GetTick>
 8001c78:	4604      	mov	r4, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8001c7a:	4b37      	ldr	r3, [pc, #220]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001c7c:	681b      	ldr	r3, [r3, #0]
 8001c7e:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 8001c82:	d047      	beq.n	8001d14 <HAL_RCC_OscConfig+0x3c4>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8001c84:	f7ff fb48 	bl	8001318 <HAL_GetTick>
 8001c88:	1b00      	subs	r0, r0, r4
 8001c8a:	2802      	cmp	r0, #2
 8001c8c:	d9f5      	bls.n	8001c7a <HAL_RCC_OscConfig+0x32a>
            return HAL_TIMEOUT;
 8001c8e:	2003      	movs	r0, #3
 8001c90:	e059      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_PWR_CLK_DISABLE();
 8001c92:	4a31      	ldr	r2, [pc, #196]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001c94:	69d3      	ldr	r3, [r2, #28]
 8001c96:	f023 5380 	bic.w	r3, r3, #268435456	@ 0x10000000
 8001c9a:	61d3      	str	r3, [r2, #28]
 8001c9c:	e7dc      	b.n	8001c58 <HAL_RCC_OscConfig+0x308>
        __HAL_RCC_PLL_DISABLE();
 8001c9e:	4b2f      	ldr	r3, [pc, #188]	@ (8001d5c <HAL_RCC_OscConfig+0x40c>)
 8001ca0:	2200      	movs	r2, #0
 8001ca2:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 8001ca4:	f7ff fb38 	bl	8001318 <HAL_GetTick>
 8001ca8:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8001caa:	4b2b      	ldr	r3, [pc, #172]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001cac:	681b      	ldr	r3, [r3, #0]
 8001cae:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 8001cb2:	d006      	beq.n	8001cc2 <HAL_RCC_OscConfig+0x372>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8001cb4:	f7ff fb30 	bl	8001318 <HAL_GetTick>
 8001cb8:	1b40      	subs	r0, r0, r5
 8001cba:	2802      	cmp	r0, #2
 8001cbc:	d9f5      	bls.n	8001caa <HAL_RCC_OscConfig+0x35a>
            return HAL_TIMEOUT;
 8001cbe:	2003      	movs	r0, #3
 8001cc0:	e041      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
        if (RCC_OscInitStruct->PLL.PLLSource == RCC_PLLSOURCE_HSE)
 8001cc2:	6a23      	ldr	r3, [r4, #32]
 8001cc4:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 8001cc8:	d01a      	beq.n	8001d00 <HAL_RCC_OscConfig+0x3b0>
        __HAL_RCC_PLL_CONFIG(RCC_OscInitStruct->PLL.PLLSource,
 8001cca:	4923      	ldr	r1, [pc, #140]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001ccc:	684b      	ldr	r3, [r1, #4]
 8001cce:	f423 1374 	bic.w	r3, r3, #3997696	@ 0x3d0000
 8001cd2:	6a22      	ldr	r2, [r4, #32]
 8001cd4:	6a60      	ldr	r0, [r4, #36]	@ 0x24
 8001cd6:	4302      	orrs	r2, r0
 8001cd8:	4313      	orrs	r3, r2
 8001cda:	604b      	str	r3, [r1, #4]
        __HAL_RCC_PLL_ENABLE();
 8001cdc:	4b1f      	ldr	r3, [pc, #124]	@ (8001d5c <HAL_RCC_OscConfig+0x40c>)
 8001cde:	2201      	movs	r2, #1
 8001ce0:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 8001ce2:	f7ff fb19 	bl	8001318 <HAL_GetTick>
 8001ce6:	4604      	mov	r4, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  == RESET)
 8001ce8:	4b1b      	ldr	r3, [pc, #108]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001cea:	681b      	ldr	r3, [r3, #0]
 8001cec:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 8001cf0:	d10e      	bne.n	8001d10 <HAL_RCC_OscConfig+0x3c0>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8001cf2:	f7ff fb11 	bl	8001318 <HAL_GetTick>
 8001cf6:	1b00      	subs	r0, r0, r4
 8001cf8:	2802      	cmp	r0, #2
 8001cfa:	d9f5      	bls.n	8001ce8 <HAL_RCC_OscConfig+0x398>
            return HAL_TIMEOUT;
 8001cfc:	2003      	movs	r0, #3
 8001cfe:	e022      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
          __HAL_RCC_HSE_PREDIV_CONFIG(RCC_OscInitStruct->HSEPredivValue);
 8001d00:	4a15      	ldr	r2, [pc, #84]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001d02:	6853      	ldr	r3, [r2, #4]
 8001d04:	f423 3300 	bic.w	r3, r3, #131072	@ 0x20000
 8001d08:	68a1      	ldr	r1, [r4, #8]
 8001d0a:	430b      	orrs	r3, r1
 8001d0c:	6053      	str	r3, [r2, #4]
 8001d0e:	e7dc      	b.n	8001cca <HAL_RCC_OscConfig+0x37a>
  return HAL_OK;
 8001d10:	2000      	movs	r0, #0
 8001d12:	e018      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
 8001d14:	2000      	movs	r0, #0
 8001d16:	e016      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF)
 8001d18:	2b01      	cmp	r3, #1
 8001d1a:	d016      	beq.n	8001d4a <HAL_RCC_OscConfig+0x3fa>
        pll_config = RCC->CFGR;
 8001d1c:	4b0e      	ldr	r3, [pc, #56]	@ (8001d58 <HAL_RCC_OscConfig+0x408>)
 8001d1e:	685b      	ldr	r3, [r3, #4]
        if ((READ_BIT(pll_config, RCC_CFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 8001d20:	f403 3180 	and.w	r1, r3, #65536	@ 0x10000
 8001d24:	6a22      	ldr	r2, [r4, #32]
 8001d26:	4291      	cmp	r1, r2
 8001d28:	d111      	bne.n	8001d4e <HAL_RCC_OscConfig+0x3fe>
            (READ_BIT(pll_config, RCC_CFGR_PLLMULL) != RCC_OscInitStruct->PLL.PLLMUL))
 8001d2a:	f403 1370 	and.w	r3, r3, #3932160	@ 0x3c0000
 8001d2e:	6a62      	ldr	r2, [r4, #36]	@ 0x24
        if ((READ_BIT(pll_config, RCC_CFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 8001d30:	4293      	cmp	r3, r2
 8001d32:	d10e      	bne.n	8001d52 <HAL_RCC_OscConfig+0x402>
  return HAL_OK;
 8001d34:	2000      	movs	r0, #0
 8001d36:	e006      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
    return HAL_ERROR;
 8001d38:	2001      	movs	r0, #1
}
 8001d3a:	4770      	bx	lr
        return HAL_ERROR;
 8001d3c:	2001      	movs	r0, #1
 8001d3e:	e002      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
        return HAL_ERROR;
 8001d40:	2001      	movs	r0, #1
 8001d42:	e000      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
  return HAL_OK;
 8001d44:	2000      	movs	r0, #0
}
 8001d46:	b002      	add	sp, #8
 8001d48:	bd70      	pop	{r4, r5, r6, pc}
        return HAL_ERROR;
 8001d4a:	2001      	movs	r0, #1
 8001d4c:	e7fb      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
          return HAL_ERROR;
 8001d4e:	2001      	movs	r0, #1
 8001d50:	e7f9      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
 8001d52:	2001      	movs	r0, #1
 8001d54:	e7f7      	b.n	8001d46 <HAL_RCC_OscConfig+0x3f6>
 8001d56:	bf00      	nop
 8001d58:	40021000 	.word	0x40021000
 8001d5c:	42420000 	.word	0x42420000

08001d60 <HAL_RCC_GetSysClockFreq>:
  tmpreg = RCC->CFGR;
 8001d60:	4b0f      	ldr	r3, [pc, #60]	@ (8001da0 <HAL_RCC_GetSysClockFreq+0x40>)
 8001d62:	685b      	ldr	r3, [r3, #4]
  switch (tmpreg & RCC_CFGR_SWS)
 8001d64:	f003 020c 	and.w	r2, r3, #12
 8001d68:	2a08      	cmp	r2, #8
 8001d6a:	d001      	beq.n	8001d70 <HAL_RCC_GetSysClockFreq+0x10>
      sysclockfreq = HSE_VALUE;
 8001d6c:	480d      	ldr	r0, [pc, #52]	@ (8001da4 <HAL_RCC_GetSysClockFreq+0x44>)
}
 8001d6e:	4770      	bx	lr
      pllmul = aPLLMULFactorTable[(uint32_t)(tmpreg & RCC_CFGR_PLLMULL) >> RCC_CFGR_PLLMULL_Pos];
 8001d70:	f3c3 4283 	ubfx	r2, r3, #18, #4
 8001d74:	490c      	ldr	r1, [pc, #48]	@ (8001da8 <HAL_RCC_GetSysClockFreq+0x48>)
 8001d76:	5c88      	ldrb	r0, [r1, r2]
      if ((tmpreg & RCC_CFGR_PLLSRC) != RCC_PLLSOURCE_HSI_DIV2)
 8001d78:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8001d7c:	d00b      	beq.n	8001d96 <HAL_RCC_GetSysClockFreq+0x36>
        prediv = aPredivFactorTable[(uint32_t)(RCC->CFGR & RCC_CFGR_PLLXTPRE) >> RCC_CFGR_PLLXTPRE_Pos];
 8001d7e:	4b08      	ldr	r3, [pc, #32]	@ (8001da0 <HAL_RCC_GetSysClockFreq+0x40>)
 8001d80:	685b      	ldr	r3, [r3, #4]
 8001d82:	f3c3 4340 	ubfx	r3, r3, #17, #1
 8001d86:	4a09      	ldr	r2, [pc, #36]	@ (8001dac <HAL_RCC_GetSysClockFreq+0x4c>)
 8001d88:	5cd3      	ldrb	r3, [r2, r3]
        pllclk = (uint32_t)((HSE_VALUE  * pllmul) / prediv);
 8001d8a:	4a06      	ldr	r2, [pc, #24]	@ (8001da4 <HAL_RCC_GetSysClockFreq+0x44>)
 8001d8c:	fb02 f000 	mul.w	r0, r2, r0
 8001d90:	fbb0 f0f3 	udiv	r0, r0, r3
 8001d94:	4770      	bx	lr
        pllclk = (uint32_t)((HSI_VALUE >> 1) * pllmul);
 8001d96:	4b06      	ldr	r3, [pc, #24]	@ (8001db0 <HAL_RCC_GetSysClockFreq+0x50>)
 8001d98:	fb03 f000 	mul.w	r0, r3, r0
 8001d9c:	4770      	bx	lr
 8001d9e:	bf00      	nop
 8001da0:	40021000 	.word	0x40021000
 8001da4:	007a1200 	.word	0x007a1200
 8001da8:	080039fc 	.word	0x080039fc
 8001dac:	080039f8 	.word	0x080039f8
 8001db0:	003d0900 	.word	0x003d0900

08001db4 <HAL_RCC_ClockConfig>:
  if (RCC_ClkInitStruct == NULL)
 8001db4:	2800      	cmp	r0, #0
 8001db6:	f000 80a0 	beq.w	8001efa <HAL_RCC_ClockConfig+0x146>
{
 8001dba:	b570      	push	{r4, r5, r6, lr}
 8001dbc:	460d      	mov	r5, r1
 8001dbe:	4604      	mov	r4, r0
  if (FLatency > __HAL_FLASH_GET_LATENCY())
 8001dc0:	4b52      	ldr	r3, [pc, #328]	@ (8001f0c <HAL_RCC_ClockConfig+0x158>)
 8001dc2:	681b      	ldr	r3, [r3, #0]
 8001dc4:	f003 0307 	and.w	r3, r3, #7
 8001dc8:	428b      	cmp	r3, r1
 8001dca:	d20b      	bcs.n	8001de4 <HAL_RCC_ClockConfig+0x30>
    __HAL_FLASH_SET_LATENCY(FLatency);
 8001dcc:	4a4f      	ldr	r2, [pc, #316]	@ (8001f0c <HAL_RCC_ClockConfig+0x158>)
 8001dce:	6813      	ldr	r3, [r2, #0]
 8001dd0:	f023 0307 	bic.w	r3, r3, #7
 8001dd4:	430b      	orrs	r3, r1
 8001dd6:	6013      	str	r3, [r2, #0]
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 8001dd8:	6813      	ldr	r3, [r2, #0]
 8001dda:	f003 0307 	and.w	r3, r3, #7
 8001dde:	428b      	cmp	r3, r1
 8001de0:	f040 808d 	bne.w	8001efe <HAL_RCC_ClockConfig+0x14a>
if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_HCLK) == RCC_CLOCKTYPE_HCLK)
 8001de4:	6823      	ldr	r3, [r4, #0]
 8001de6:	f013 0f02 	tst.w	r3, #2
 8001dea:	d017      	beq.n	8001e1c <HAL_RCC_ClockConfig+0x68>
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 8001dec:	f013 0f04 	tst.w	r3, #4
 8001df0:	d004      	beq.n	8001dfc <HAL_RCC_ClockConfig+0x48>
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_HCLK_DIV16);
 8001df2:	4a47      	ldr	r2, [pc, #284]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001df4:	6853      	ldr	r3, [r2, #4]
 8001df6:	f443 63e0 	orr.w	r3, r3, #1792	@ 0x700
 8001dfa:	6053      	str	r3, [r2, #4]
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 8001dfc:	6823      	ldr	r3, [r4, #0]
 8001dfe:	f013 0f08 	tst.w	r3, #8
 8001e02:	d004      	beq.n	8001e0e <HAL_RCC_ClockConfig+0x5a>
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, (RCC_HCLK_DIV16 << 3));
 8001e04:	4a42      	ldr	r2, [pc, #264]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001e06:	6853      	ldr	r3, [r2, #4]
 8001e08:	f443 5360 	orr.w	r3, r3, #14336	@ 0x3800
 8001e0c:	6053      	str	r3, [r2, #4]
    MODIFY_REG(RCC->CFGR, RCC_CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
 8001e0e:	4a40      	ldr	r2, [pc, #256]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001e10:	6853      	ldr	r3, [r2, #4]
 8001e12:	f023 03f0 	bic.w	r3, r3, #240	@ 0xf0
 8001e16:	68a1      	ldr	r1, [r4, #8]
 8001e18:	430b      	orrs	r3, r1
 8001e1a:	6053      	str	r3, [r2, #4]
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_SYSCLK) == RCC_CLOCKTYPE_SYSCLK)
 8001e1c:	6823      	ldr	r3, [r4, #0]
 8001e1e:	f013 0f01 	tst.w	r3, #1
 8001e22:	d031      	beq.n	8001e88 <HAL_RCC_ClockConfig+0xd4>
    if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_HSE)
 8001e24:	6863      	ldr	r3, [r4, #4]
 8001e26:	2b01      	cmp	r3, #1
 8001e28:	d020      	beq.n	8001e6c <HAL_RCC_ClockConfig+0xb8>
    else if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_PLLCLK)
 8001e2a:	2b02      	cmp	r3, #2
 8001e2c:	d025      	beq.n	8001e7a <HAL_RCC_ClockConfig+0xc6>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8001e2e:	4a38      	ldr	r2, [pc, #224]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001e30:	6812      	ldr	r2, [r2, #0]
 8001e32:	f012 0f02 	tst.w	r2, #2
 8001e36:	d064      	beq.n	8001f02 <HAL_RCC_ClockConfig+0x14e>
    __HAL_RCC_SYSCLK_CONFIG(RCC_ClkInitStruct->SYSCLKSource);
 8001e38:	4935      	ldr	r1, [pc, #212]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001e3a:	684a      	ldr	r2, [r1, #4]
 8001e3c:	f022 0203 	bic.w	r2, r2, #3
 8001e40:	4313      	orrs	r3, r2
 8001e42:	604b      	str	r3, [r1, #4]
    tickstart = HAL_GetTick();
 8001e44:	f7ff fa68 	bl	8001318 <HAL_GetTick>
 8001e48:	4606      	mov	r6, r0
    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 8001e4a:	4b31      	ldr	r3, [pc, #196]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001e4c:	685b      	ldr	r3, [r3, #4]
 8001e4e:	f003 030c 	and.w	r3, r3, #12
 8001e52:	6862      	ldr	r2, [r4, #4]
 8001e54:	ebb3 0f82 	cmp.w	r3, r2, lsl #2
 8001e58:	d016      	beq.n	8001e88 <HAL_RCC_ClockConfig+0xd4>
      if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
 8001e5a:	f7ff fa5d 	bl	8001318 <HAL_GetTick>
 8001e5e:	1b80      	subs	r0, r0, r6
 8001e60:	f241 3388 	movw	r3, #5000	@ 0x1388
 8001e64:	4298      	cmp	r0, r3
 8001e66:	d9f0      	bls.n	8001e4a <HAL_RCC_ClockConfig+0x96>
        return HAL_TIMEOUT;
 8001e68:	2003      	movs	r0, #3
 8001e6a:	e045      	b.n	8001ef8 <HAL_RCC_ClockConfig+0x144>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8001e6c:	4a28      	ldr	r2, [pc, #160]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001e6e:	6812      	ldr	r2, [r2, #0]
 8001e70:	f412 3f00 	tst.w	r2, #131072	@ 0x20000
 8001e74:	d1e0      	bne.n	8001e38 <HAL_RCC_ClockConfig+0x84>
        return HAL_ERROR;
 8001e76:	2001      	movs	r0, #1
 8001e78:	e03e      	b.n	8001ef8 <HAL_RCC_ClockConfig+0x144>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 8001e7a:	4a25      	ldr	r2, [pc, #148]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001e7c:	6812      	ldr	r2, [r2, #0]
 8001e7e:	f012 7f00 	tst.w	r2, #33554432	@ 0x2000000
 8001e82:	d1d9      	bne.n	8001e38 <HAL_RCC_ClockConfig+0x84>
        return HAL_ERROR;
 8001e84:	2001      	movs	r0, #1
 8001e86:	e037      	b.n	8001ef8 <HAL_RCC_ClockConfig+0x144>
  if (FLatency < __HAL_FLASH_GET_LATENCY())
 8001e88:	4b20      	ldr	r3, [pc, #128]	@ (8001f0c <HAL_RCC_ClockConfig+0x158>)
 8001e8a:	681b      	ldr	r3, [r3, #0]
 8001e8c:	f003 0307 	and.w	r3, r3, #7
 8001e90:	42ab      	cmp	r3, r5
 8001e92:	d90a      	bls.n	8001eaa <HAL_RCC_ClockConfig+0xf6>
    __HAL_FLASH_SET_LATENCY(FLatency);
 8001e94:	4a1d      	ldr	r2, [pc, #116]	@ (8001f0c <HAL_RCC_ClockConfig+0x158>)
 8001e96:	6813      	ldr	r3, [r2, #0]
 8001e98:	f023 0307 	bic.w	r3, r3, #7
 8001e9c:	432b      	orrs	r3, r5
 8001e9e:	6013      	str	r3, [r2, #0]
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 8001ea0:	6813      	ldr	r3, [r2, #0]
 8001ea2:	f003 0307 	and.w	r3, r3, #7
 8001ea6:	42ab      	cmp	r3, r5
 8001ea8:	d12d      	bne.n	8001f06 <HAL_RCC_ClockConfig+0x152>
if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 8001eaa:	6823      	ldr	r3, [r4, #0]
 8001eac:	f013 0f04 	tst.w	r3, #4
 8001eb0:	d006      	beq.n	8001ec0 <HAL_RCC_ClockConfig+0x10c>
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_ClkInitStruct->APB1CLKDivider);
 8001eb2:	4a17      	ldr	r2, [pc, #92]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001eb4:	6853      	ldr	r3, [r2, #4]
 8001eb6:	f423 63e0 	bic.w	r3, r3, #1792	@ 0x700
 8001eba:	68e1      	ldr	r1, [r4, #12]
 8001ebc:	430b      	orrs	r3, r1
 8001ebe:	6053      	str	r3, [r2, #4]
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 8001ec0:	6823      	ldr	r3, [r4, #0]
 8001ec2:	f013 0f08 	tst.w	r3, #8
 8001ec6:	d007      	beq.n	8001ed8 <HAL_RCC_ClockConfig+0x124>
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, ((RCC_ClkInitStruct->APB2CLKDivider) << 3));
 8001ec8:	4a11      	ldr	r2, [pc, #68]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001eca:	6853      	ldr	r3, [r2, #4]
 8001ecc:	f423 5360 	bic.w	r3, r3, #14336	@ 0x3800
 8001ed0:	6921      	ldr	r1, [r4, #16]
 8001ed2:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 8001ed6:	6053      	str	r3, [r2, #4]
  SystemCoreClock = HAL_RCC_GetSysClockFreq() >> AHBPrescTable[(RCC->CFGR & RCC_CFGR_HPRE) >> RCC_CFGR_HPRE_Pos];
 8001ed8:	f7ff ff42 	bl	8001d60 <HAL_RCC_GetSysClockFreq>
 8001edc:	4b0c      	ldr	r3, [pc, #48]	@ (8001f10 <HAL_RCC_ClockConfig+0x15c>)
 8001ede:	685b      	ldr	r3, [r3, #4]
 8001ee0:	f3c3 1303 	ubfx	r3, r3, #4, #4
 8001ee4:	4a0b      	ldr	r2, [pc, #44]	@ (8001f14 <HAL_RCC_ClockConfig+0x160>)
 8001ee6:	5cd3      	ldrb	r3, [r2, r3]
 8001ee8:	40d8      	lsrs	r0, r3
 8001eea:	4b0b      	ldr	r3, [pc, #44]	@ (8001f18 <HAL_RCC_ClockConfig+0x164>)
 8001eec:	6018      	str	r0, [r3, #0]
  HAL_InitTick(uwTickPrio);
 8001eee:	4b0b      	ldr	r3, [pc, #44]	@ (8001f1c <HAL_RCC_ClockConfig+0x168>)
 8001ef0:	6818      	ldr	r0, [r3, #0]
 8001ef2:	f7ff f9cd 	bl	8001290 <HAL_InitTick>
  return HAL_OK;
 8001ef6:	2000      	movs	r0, #0
}
 8001ef8:	bd70      	pop	{r4, r5, r6, pc}
    return HAL_ERROR;
 8001efa:	2001      	movs	r0, #1
}
 8001efc:	4770      	bx	lr
    return HAL_ERROR;
 8001efe:	2001      	movs	r0, #1
 8001f00:	e7fa      	b.n	8001ef8 <HAL_RCC_ClockConfig+0x144>
        return HAL_ERROR;
 8001f02:	2001      	movs	r0, #1
 8001f04:	e7f8      	b.n	8001ef8 <HAL_RCC_ClockConfig+0x144>
    return HAL_ERROR;
 8001f06:	2001      	movs	r0, #1
 8001f08:	e7f6      	b.n	8001ef8 <HAL_RCC_ClockConfig+0x144>
 8001f0a:	bf00      	nop
 8001f0c:	******** 	.word	0x********
 8001f10:	40021000 	.word	0x40021000
 8001f14:	080039e8 	.word	0x080039e8
 8001f18:	20000004 	.word	0x20000004
 8001f1c:	2000000c 	.word	0x2000000c

08001f20 <HAL_RCC_GetHCLKFreq>:
}
 8001f20:	4b01      	ldr	r3, [pc, #4]	@ (8001f28 <HAL_RCC_GetHCLKFreq+0x8>)
 8001f22:	6818      	ldr	r0, [r3, #0]
 8001f24:	4770      	bx	lr
 8001f26:	bf00      	nop
 8001f28:	20000004 	.word	0x20000004

08001f2c <HAL_RCC_GetPCLK1Freq>:
{
 8001f2c:	b508      	push	{r3, lr}
  return (HAL_RCC_GetHCLKFreq() >> APBPrescTable[(RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos]);
 8001f2e:	f7ff fff7 	bl	8001f20 <HAL_RCC_GetHCLKFreq>
 8001f32:	4b04      	ldr	r3, [pc, #16]	@ (8001f44 <HAL_RCC_GetPCLK1Freq+0x18>)
 8001f34:	685b      	ldr	r3, [r3, #4]
 8001f36:	f3c3 2302 	ubfx	r3, r3, #8, #3
 8001f3a:	4a03      	ldr	r2, [pc, #12]	@ (8001f48 <HAL_RCC_GetPCLK1Freq+0x1c>)
 8001f3c:	5cd3      	ldrb	r3, [r2, r3]
}
 8001f3e:	40d8      	lsrs	r0, r3
 8001f40:	bd08      	pop	{r3, pc}
 8001f42:	bf00      	nop
 8001f44:	40021000 	.word	0x40021000
 8001f48:	080039e0 	.word	0x080039e0

08001f4c <HAL_RCC_GetPCLK2Freq>:
{
 8001f4c:	b508      	push	{r3, lr}
  return (HAL_RCC_GetHCLKFreq() >> APBPrescTable[(RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos]);
 8001f4e:	f7ff ffe7 	bl	8001f20 <HAL_RCC_GetHCLKFreq>
 8001f52:	4b04      	ldr	r3, [pc, #16]	@ (8001f64 <HAL_RCC_GetPCLK2Freq+0x18>)
 8001f54:	685b      	ldr	r3, [r3, #4]
 8001f56:	f3c3 23c2 	ubfx	r3, r3, #11, #3
 8001f5a:	4a03      	ldr	r2, [pc, #12]	@ (8001f68 <HAL_RCC_GetPCLK2Freq+0x1c>)
 8001f5c:	5cd3      	ldrb	r3, [r2, r3]
}
 8001f5e:	40d8      	lsrs	r0, r3
 8001f60:	bd08      	pop	{r3, pc}
 8001f62:	bf00      	nop
 8001f64:	40021000 	.word	0x40021000
 8001f68:	080039e0 	.word	0x080039e0

08001f6c <UART_EndRxTransfer>:
  * @retval None
  */
static void UART_EndRxTransfer(UART_HandleTypeDef *huart)
{
  /* Disable RXNE, PE and ERR (Frame error, noise error, overrun error) interrupts */
  ATOMIC_CLEAR_BIT(huart->Instance->CR1, (USART_CR1_RXNEIE | USART_CR1_PEIE));
 8001f6c:	6802      	ldr	r2, [r0, #0]
 */
__STATIC_FORCEINLINE uint32_t __LDREXW(volatile uint32_t *addr)
{
    uint32_t result;

   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 8001f6e:	f102 030c 	add.w	r3, r2, #12
 8001f72:	e853 3f00 	ldrex	r3, [r3]
 8001f76:	f423 7390 	bic.w	r3, r3, #288	@ 0x120
 */
__STATIC_FORCEINLINE uint32_t __STREXW(uint32_t value, volatile uint32_t *addr)
{
   uint32_t result;

   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 8001f7a:	320c      	adds	r2, #12
 8001f7c:	e842 3100 	strex	r1, r3, [r2]
 8001f80:	2900      	cmp	r1, #0
 8001f82:	d1f3      	bne.n	8001f6c <UART_EndRxTransfer>
  ATOMIC_CLEAR_BIT(huart->Instance->CR3, USART_CR3_EIE);
 8001f84:	6802      	ldr	r2, [r0, #0]
   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 8001f86:	f102 0314 	add.w	r3, r2, #20
 8001f8a:	e853 3f00 	ldrex	r3, [r3]
 8001f8e:	f023 0301 	bic.w	r3, r3, #1
   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 8001f92:	3214      	adds	r2, #20
 8001f94:	e842 3100 	strex	r1, r3, [r2]
 8001f98:	2900      	cmp	r1, #0
 8001f9a:	d1f3      	bne.n	8001f84 <UART_EndRxTransfer+0x18>

  /* In case of reception waiting for IDLE event, disable also the IDLE IE interrupt source */
  if (huart->ReceptionType == HAL_UART_RECEPTION_TOIDLE)
 8001f9c:	6b03      	ldr	r3, [r0, #48]	@ 0x30
 8001f9e:	2b01      	cmp	r3, #1
 8001fa0:	d005      	beq.n	8001fae <UART_EndRxTransfer+0x42>
  {
    ATOMIC_CLEAR_BIT(huart->Instance->CR1, USART_CR1_IDLEIE);
  }

  /* At end of Rx process, restore huart->RxState to Ready */
  huart->RxState = HAL_UART_STATE_READY;
 8001fa2:	2320      	movs	r3, #32
 8001fa4:	f880 3042 	strb.w	r3, [r0, #66]	@ 0x42
  huart->ReceptionType = HAL_UART_RECEPTION_STANDARD;
 8001fa8:	2300      	movs	r3, #0
 8001faa:	6303      	str	r3, [r0, #48]	@ 0x30
}
 8001fac:	4770      	bx	lr
    ATOMIC_CLEAR_BIT(huart->Instance->CR1, USART_CR1_IDLEIE);
 8001fae:	6802      	ldr	r2, [r0, #0]
   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 8001fb0:	f102 030c 	add.w	r3, r2, #12
 8001fb4:	e853 3f00 	ldrex	r3, [r3]
 8001fb8:	f023 0310 	bic.w	r3, r3, #16
   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 8001fbc:	320c      	adds	r2, #12
 8001fbe:	e842 3100 	strex	r1, r3, [r2]
 8001fc2:	2900      	cmp	r1, #0
 8001fc4:	d1f3      	bne.n	8001fae <UART_EndRxTransfer+0x42>
 8001fc6:	e7ec      	b.n	8001fa2 <UART_EndRxTransfer+0x36>

08001fc8 <UART_SetConfig>:
  * @param  huart  Pointer to a UART_HandleTypeDef structure that contains
  *                the configuration information for the specified UART module.
  * @retval None
  */
static void UART_SetConfig(UART_HandleTypeDef *huart)
{
 8001fc8:	b510      	push	{r4, lr}
 8001fca:	4604      	mov	r4, r0
  assert_param(IS_UART_MODE(huart->Init.Mode));

  /*-------------------------- USART CR2 Configuration -----------------------*/
  /* Configure the UART Stop Bits: Set STOP[13:12] bits
     according to huart->Init.StopBits value */
  MODIFY_REG(huart->Instance->CR2, USART_CR2_STOP, huart->Init.StopBits);
 8001fcc:	6802      	ldr	r2, [r0, #0]
 8001fce:	6913      	ldr	r3, [r2, #16]
 8001fd0:	f423 5340 	bic.w	r3, r3, #12288	@ 0x3000
 8001fd4:	68c1      	ldr	r1, [r0, #12]
 8001fd6:	430b      	orrs	r3, r1
 8001fd8:	6113      	str	r3, [r2, #16]
  tmpreg = (uint32_t)huart->Init.WordLength | huart->Init.Parity | huart->Init.Mode | huart->Init.OverSampling;
  MODIFY_REG(huart->Instance->CR1,
             (uint32_t)(USART_CR1_M | USART_CR1_PCE | USART_CR1_PS | USART_CR1_TE | USART_CR1_RE | USART_CR1_OVER8),
             tmpreg);
#else
  tmpreg = (uint32_t)huart->Init.WordLength | huart->Init.Parity | huart->Init.Mode;
 8001fda:	6883      	ldr	r3, [r0, #8]
 8001fdc:	6902      	ldr	r2, [r0, #16]
 8001fde:	4313      	orrs	r3, r2
 8001fe0:	6942      	ldr	r2, [r0, #20]
 8001fe2:	431a      	orrs	r2, r3
  MODIFY_REG(huart->Instance->CR1,
 8001fe4:	6801      	ldr	r1, [r0, #0]
 8001fe6:	68cb      	ldr	r3, [r1, #12]
 8001fe8:	f423 53b0 	bic.w	r3, r3, #5632	@ 0x1600
 8001fec:	f023 030c 	bic.w	r3, r3, #12
 8001ff0:	4313      	orrs	r3, r2
 8001ff2:	60cb      	str	r3, [r1, #12]
             tmpreg);
#endif /* USART_CR1_OVER8 */

  /*-------------------------- USART CR3 Configuration -----------------------*/
  /* Configure the UART HFC: Set CTSE and RTSE bits according to huart->Init.HwFlowCtl value */
  MODIFY_REG(huart->Instance->CR3, (USART_CR3_RTSE | USART_CR3_CTSE), huart->Init.HwFlowCtl);
 8001ff4:	6802      	ldr	r2, [r0, #0]
 8001ff6:	6953      	ldr	r3, [r2, #20]
 8001ff8:	f423 7340 	bic.w	r3, r3, #768	@ 0x300
 8001ffc:	6981      	ldr	r1, [r0, #24]
 8001ffe:	430b      	orrs	r3, r1
 8002000:	6153      	str	r3, [r2, #20]


  if(huart->Instance == USART1)
 8002002:	6802      	ldr	r2, [r0, #0]
 8002004:	4b13      	ldr	r3, [pc, #76]	@ (8002054 <UART_SetConfig+0x8c>)
 8002006:	429a      	cmp	r2, r3
 8002008:	d020      	beq.n	800204c <UART_SetConfig+0x84>
  {
    pclk = HAL_RCC_GetPCLK2Freq();
  }
  else
  {
    pclk = HAL_RCC_GetPCLK1Freq();
 800200a:	f7ff ff8f 	bl	8001f2c <HAL_RCC_GetPCLK1Freq>
 800200e:	4602      	mov	r2, r0
  else
  {
    huart->Instance->BRR = UART_BRR_SAMPLING16(pclk, huart->Init.BaudRate);
  }
#else
  huart->Instance->BRR = UART_BRR_SAMPLING16(pclk, huart->Init.BaudRate);
 8002010:	eb02 0282 	add.w	r2, r2, r2, lsl #2
 8002014:	eb02 0282 	add.w	r2, r2, r2, lsl #2
 8002018:	6863      	ldr	r3, [r4, #4]
 800201a:	009b      	lsls	r3, r3, #2
 800201c:	fbb2 f2f3 	udiv	r2, r2, r3
 8002020:	480d      	ldr	r0, [pc, #52]	@ (8002058 <UART_SetConfig+0x90>)
 8002022:	fba0 3102 	umull	r3, r1, r0, r2
 8002026:	0949      	lsrs	r1, r1, #5
 8002028:	2364      	movs	r3, #100	@ 0x64
 800202a:	fb03 2311 	mls	r3, r3, r1, r2
 800202e:	011b      	lsls	r3, r3, #4
 8002030:	3332      	adds	r3, #50	@ 0x32
 8002032:	fba0 0303 	umull	r0, r3, r0, r3
 8002036:	095b      	lsrs	r3, r3, #5
 8002038:	f003 02f0 	and.w	r2, r3, #240	@ 0xf0
 800203c:	eb02 1201 	add.w	r2, r2, r1, lsl #4
 8002040:	f003 030f 	and.w	r3, r3, #15
 8002044:	6821      	ldr	r1, [r4, #0]
 8002046:	4413      	add	r3, r2
 8002048:	608b      	str	r3, [r1, #8]
#endif /* USART_CR1_OVER8 */
}
 800204a:	bd10      	pop	{r4, pc}
    pclk = HAL_RCC_GetPCLK2Freq();
 800204c:	f7ff ff7e 	bl	8001f4c <HAL_RCC_GetPCLK2Freq>
 8002050:	4602      	mov	r2, r0
 8002052:	e7dd      	b.n	8002010 <UART_SetConfig+0x48>
 8002054:	40013800 	.word	0x40013800
 8002058:	51eb851f 	.word	0x51eb851f

0800205c <UART_WaitOnFlagUntilTimeout>:
{
 800205c:	e92d 43f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, lr}
 8002060:	b083      	sub	sp, #12
 8002062:	4605      	mov	r5, r0
 8002064:	460e      	mov	r6, r1
 8002066:	4617      	mov	r7, r2
 8002068:	4699      	mov	r9, r3
 800206a:	f8dd 8028 	ldr.w	r8, [sp, #40]	@ 0x28
  while ((__HAL_UART_GET_FLAG(huart, Flag) ? SET : RESET) == Status)
 800206e:	682b      	ldr	r3, [r5, #0]
 8002070:	681c      	ldr	r4, [r3, #0]
 8002072:	ea36 0404 	bics.w	r4, r6, r4
 8002076:	bf0c      	ite	eq
 8002078:	2401      	moveq	r4, #1
 800207a:	2400      	movne	r4, #0
 800207c:	42bc      	cmp	r4, r7
 800207e:	d128      	bne.n	80020d2 <UART_WaitOnFlagUntilTimeout+0x76>
    if (Timeout != HAL_MAX_DELAY)
 8002080:	f1b8 3fff 	cmp.w	r8, #4294967295
 8002084:	d0f3      	beq.n	800206e <UART_WaitOnFlagUntilTimeout+0x12>
      if (((HAL_GetTick() - Tickstart) > Timeout) || (Timeout == 0U))
 8002086:	f7ff f947 	bl	8001318 <HAL_GetTick>
 800208a:	eba0 0009 	sub.w	r0, r0, r9
 800208e:	4540      	cmp	r0, r8
 8002090:	d823      	bhi.n	80020da <UART_WaitOnFlagUntilTimeout+0x7e>
 8002092:	f1b8 0f00 	cmp.w	r8, #0
 8002096:	d022      	beq.n	80020de <UART_WaitOnFlagUntilTimeout+0x82>
      if ((READ_BIT(huart->Instance->CR1, USART_CR1_RE) != 0U) && (Flag != UART_FLAG_TXE) && (Flag != UART_FLAG_TC))
 8002098:	682b      	ldr	r3, [r5, #0]
 800209a:	68da      	ldr	r2, [r3, #12]
 800209c:	f012 0f04 	tst.w	r2, #4
 80020a0:	d0e5      	beq.n	800206e <UART_WaitOnFlagUntilTimeout+0x12>
 80020a2:	2e80      	cmp	r6, #128	@ 0x80
 80020a4:	d0e3      	beq.n	800206e <UART_WaitOnFlagUntilTimeout+0x12>
 80020a6:	2e40      	cmp	r6, #64	@ 0x40
 80020a8:	d0e1      	beq.n	800206e <UART_WaitOnFlagUntilTimeout+0x12>
        if (__HAL_UART_GET_FLAG(huart, UART_FLAG_ORE) == SET)
 80020aa:	681a      	ldr	r2, [r3, #0]
 80020ac:	f012 0f08 	tst.w	r2, #8
 80020b0:	d0dd      	beq.n	800206e <UART_WaitOnFlagUntilTimeout+0x12>
          __HAL_UART_CLEAR_OREFLAG(huart);
 80020b2:	2400      	movs	r4, #0
 80020b4:	9401      	str	r4, [sp, #4]
 80020b6:	681a      	ldr	r2, [r3, #0]
 80020b8:	9201      	str	r2, [sp, #4]
 80020ba:	685b      	ldr	r3, [r3, #4]
 80020bc:	9301      	str	r3, [sp, #4]
 80020be:	9b01      	ldr	r3, [sp, #4]
          UART_EndRxTransfer(huart);
 80020c0:	4628      	mov	r0, r5
 80020c2:	f7ff ff53 	bl	8001f6c <UART_EndRxTransfer>
          huart->ErrorCode = HAL_UART_ERROR_ORE;
 80020c6:	2308      	movs	r3, #8
 80020c8:	646b      	str	r3, [r5, #68]	@ 0x44
          __HAL_UNLOCK(huart);
 80020ca:	f885 4040 	strb.w	r4, [r5, #64]	@ 0x40
          return HAL_ERROR;
 80020ce:	2001      	movs	r0, #1
 80020d0:	e000      	b.n	80020d4 <UART_WaitOnFlagUntilTimeout+0x78>
  return HAL_OK;
 80020d2:	2000      	movs	r0, #0
}
 80020d4:	b003      	add	sp, #12
 80020d6:	e8bd 83f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, pc}
        return HAL_TIMEOUT;
 80020da:	2003      	movs	r0, #3
 80020dc:	e7fa      	b.n	80020d4 <UART_WaitOnFlagUntilTimeout+0x78>
 80020de:	2003      	movs	r0, #3
 80020e0:	e7f8      	b.n	80020d4 <UART_WaitOnFlagUntilTimeout+0x78>

080020e2 <HAL_UART_Init>:
  if (huart == NULL)
 80020e2:	b360      	cbz	r0, 800213e <HAL_UART_Init+0x5c>
{
 80020e4:	b510      	push	{r4, lr}
 80020e6:	4604      	mov	r4, r0
  if (huart->gState == HAL_UART_STATE_RESET)
 80020e8:	f890 3041 	ldrb.w	r3, [r0, #65]	@ 0x41
 80020ec:	b313      	cbz	r3, 8002134 <HAL_UART_Init+0x52>
  huart->gState = HAL_UART_STATE_BUSY;
 80020ee:	2324      	movs	r3, #36	@ 0x24
 80020f0:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
  __HAL_UART_DISABLE(huart);
 80020f4:	6822      	ldr	r2, [r4, #0]
 80020f6:	68d3      	ldr	r3, [r2, #12]
 80020f8:	f423 5300 	bic.w	r3, r3, #8192	@ 0x2000
 80020fc:	60d3      	str	r3, [r2, #12]
  UART_SetConfig(huart);
 80020fe:	4620      	mov	r0, r4
 8002100:	f7ff ff62 	bl	8001fc8 <UART_SetConfig>
  CLEAR_BIT(huart->Instance->CR2, (USART_CR2_LINEN | USART_CR2_CLKEN));
 8002104:	6822      	ldr	r2, [r4, #0]
 8002106:	6913      	ldr	r3, [r2, #16]
 8002108:	f423 4390 	bic.w	r3, r3, #18432	@ 0x4800
 800210c:	6113      	str	r3, [r2, #16]
  CLEAR_BIT(huart->Instance->CR3, (USART_CR3_SCEN | USART_CR3_HDSEL | USART_CR3_IREN));
 800210e:	6822      	ldr	r2, [r4, #0]
 8002110:	6953      	ldr	r3, [r2, #20]
 8002112:	f023 032a 	bic.w	r3, r3, #42	@ 0x2a
 8002116:	6153      	str	r3, [r2, #20]
  __HAL_UART_ENABLE(huart);
 8002118:	6822      	ldr	r2, [r4, #0]
 800211a:	68d3      	ldr	r3, [r2, #12]
 800211c:	f443 5300 	orr.w	r3, r3, #8192	@ 0x2000
 8002120:	60d3      	str	r3, [r2, #12]
  huart->ErrorCode = HAL_UART_ERROR_NONE;
 8002122:	2000      	movs	r0, #0
 8002124:	6460      	str	r0, [r4, #68]	@ 0x44
  huart->gState = HAL_UART_STATE_READY;
 8002126:	2320      	movs	r3, #32
 8002128:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
  huart->RxState = HAL_UART_STATE_READY;
 800212c:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
  huart->RxEventType = HAL_UART_RXEVENT_TC;
 8002130:	6360      	str	r0, [r4, #52]	@ 0x34
}
 8002132:	bd10      	pop	{r4, pc}
    huart->Lock = HAL_UNLOCKED;
 8002134:	f880 3040 	strb.w	r3, [r0, #64]	@ 0x40
    HAL_UART_MspInit(huart);
 8002138:	f7fe ffba 	bl	80010b0 <HAL_UART_MspInit>
 800213c:	e7d7      	b.n	80020ee <HAL_UART_Init+0xc>
    return HAL_ERROR;
 800213e:	2001      	movs	r0, #1
}
 8002140:	4770      	bx	lr

08002142 <HAL_UART_Transmit>:
{
 8002142:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 8002146:	b082      	sub	sp, #8
 8002148:	461e      	mov	r6, r3
  if (huart->gState == HAL_UART_STATE_READY)
 800214a:	f890 3041 	ldrb.w	r3, [r0, #65]	@ 0x41
 800214e:	b2db      	uxtb	r3, r3
 8002150:	2b20      	cmp	r3, #32
 8002152:	d156      	bne.n	8002202 <HAL_UART_Transmit+0xc0>
 8002154:	4604      	mov	r4, r0
 8002156:	460d      	mov	r5, r1
 8002158:	4690      	mov	r8, r2
    if ((pData == NULL) || (Size == 0U))
 800215a:	2900      	cmp	r1, #0
 800215c:	d055      	beq.n	800220a <HAL_UART_Transmit+0xc8>
 800215e:	b90a      	cbnz	r2, 8002164 <HAL_UART_Transmit+0x22>
      return  HAL_ERROR;
 8002160:	2001      	movs	r0, #1
 8002162:	e04f      	b.n	8002204 <HAL_UART_Transmit+0xc2>
    huart->ErrorCode = HAL_UART_ERROR_NONE;
 8002164:	2300      	movs	r3, #0
 8002166:	6443      	str	r3, [r0, #68]	@ 0x44
    huart->gState = HAL_UART_STATE_BUSY_TX;
 8002168:	2321      	movs	r3, #33	@ 0x21
 800216a:	f880 3041 	strb.w	r3, [r0, #65]	@ 0x41
    tickstart = HAL_GetTick();
 800216e:	f7ff f8d3 	bl	8001318 <HAL_GetTick>
 8002172:	4607      	mov	r7, r0
    huart->TxXferSize = Size;
 8002174:	f8a4 8024 	strh.w	r8, [r4, #36]	@ 0x24
    huart->TxXferCount = Size;
 8002178:	f8a4 8026 	strh.w	r8, [r4, #38]	@ 0x26
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 800217c:	68a3      	ldr	r3, [r4, #8]
 800217e:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 8002182:	d002      	beq.n	800218a <HAL_UART_Transmit+0x48>
      pdata16bits = NULL;
 8002184:	f04f 0800 	mov.w	r8, #0
 8002188:	e014      	b.n	80021b4 <HAL_UART_Transmit+0x72>
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 800218a:	6923      	ldr	r3, [r4, #16]
 800218c:	b32b      	cbz	r3, 80021da <HAL_UART_Transmit+0x98>
      pdata16bits = NULL;
 800218e:	f04f 0800 	mov.w	r8, #0
 8002192:	e00f      	b.n	80021b4 <HAL_UART_Transmit+0x72>
        huart->gState = HAL_UART_STATE_READY;
 8002194:	2320      	movs	r3, #32
 8002196:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
        return HAL_TIMEOUT;
 800219a:	2003      	movs	r0, #3
 800219c:	e032      	b.n	8002204 <HAL_UART_Transmit+0xc2>
        huart->Instance->DR = (uint16_t)(*pdata16bits & 0x01FFU);
 800219e:	f838 3b02 	ldrh.w	r3, [r8], #2
 80021a2:	6822      	ldr	r2, [r4, #0]
 80021a4:	f3c3 0308 	ubfx	r3, r3, #0, #9
 80021a8:	6053      	str	r3, [r2, #4]
      huart->TxXferCount--;
 80021aa:	8ce2      	ldrh	r2, [r4, #38]	@ 0x26
 80021ac:	b292      	uxth	r2, r2
 80021ae:	3a01      	subs	r2, #1
 80021b0:	b292      	uxth	r2, r2
 80021b2:	84e2      	strh	r2, [r4, #38]	@ 0x26
    while (huart->TxXferCount > 0U)
 80021b4:	8ce3      	ldrh	r3, [r4, #38]	@ 0x26
 80021b6:	b29b      	uxth	r3, r3
 80021b8:	b193      	cbz	r3, 80021e0 <HAL_UART_Transmit+0x9e>
      if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_TXE, RESET, tickstart, Timeout) != HAL_OK)
 80021ba:	9600      	str	r6, [sp, #0]
 80021bc:	463b      	mov	r3, r7
 80021be:	2200      	movs	r2, #0
 80021c0:	2180      	movs	r1, #128	@ 0x80
 80021c2:	4620      	mov	r0, r4
 80021c4:	f7ff ff4a 	bl	800205c <UART_WaitOnFlagUntilTimeout>
 80021c8:	2800      	cmp	r0, #0
 80021ca:	d1e3      	bne.n	8002194 <HAL_UART_Transmit+0x52>
      if (pdata8bits == NULL)
 80021cc:	2d00      	cmp	r5, #0
 80021ce:	d0e6      	beq.n	800219e <HAL_UART_Transmit+0x5c>
        huart->Instance->DR = (uint8_t)(*pdata8bits & 0xFFU);
 80021d0:	f815 2b01 	ldrb.w	r2, [r5], #1
 80021d4:	6823      	ldr	r3, [r4, #0]
 80021d6:	605a      	str	r2, [r3, #4]
        pdata8bits++;
 80021d8:	e7e7      	b.n	80021aa <HAL_UART_Transmit+0x68>
      pdata16bits = (const uint16_t *) pData;
 80021da:	46a8      	mov	r8, r5
      pdata8bits  = NULL;
 80021dc:	2500      	movs	r5, #0
 80021de:	e7e9      	b.n	80021b4 <HAL_UART_Transmit+0x72>
    if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_TC, RESET, tickstart, Timeout) != HAL_OK)
 80021e0:	9600      	str	r6, [sp, #0]
 80021e2:	463b      	mov	r3, r7
 80021e4:	2200      	movs	r2, #0
 80021e6:	2140      	movs	r1, #64	@ 0x40
 80021e8:	4620      	mov	r0, r4
 80021ea:	f7ff ff37 	bl	800205c <UART_WaitOnFlagUntilTimeout>
 80021ee:	b918      	cbnz	r0, 80021f8 <HAL_UART_Transmit+0xb6>
    huart->gState = HAL_UART_STATE_READY;
 80021f0:	2320      	movs	r3, #32
 80021f2:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
    return HAL_OK;
 80021f6:	e005      	b.n	8002204 <HAL_UART_Transmit+0xc2>
      huart->gState = HAL_UART_STATE_READY;
 80021f8:	2320      	movs	r3, #32
 80021fa:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
      return HAL_TIMEOUT;
 80021fe:	2003      	movs	r0, #3
 8002200:	e000      	b.n	8002204 <HAL_UART_Transmit+0xc2>
    return HAL_BUSY;
 8002202:	2002      	movs	r0, #2
}
 8002204:	b002      	add	sp, #8
 8002206:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
      return  HAL_ERROR;
 800220a:	2001      	movs	r0, #1
 800220c:	e7fa      	b.n	8002204 <HAL_UART_Transmit+0xc2>

0800220e <HAL_UART_Receive>:
{
 800220e:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 8002212:	b082      	sub	sp, #8
 8002214:	461e      	mov	r6, r3
  if (huart->RxState == HAL_UART_STATE_READY)
 8002216:	f890 3042 	ldrb.w	r3, [r0, #66]	@ 0x42
 800221a:	b2db      	uxtb	r3, r3
 800221c:	2b20      	cmp	r3, #32
 800221e:	d159      	bne.n	80022d4 <HAL_UART_Receive+0xc6>
 8002220:	4604      	mov	r4, r0
 8002222:	460d      	mov	r5, r1
 8002224:	4690      	mov	r8, r2
    if ((pData == NULL) || (Size == 0U))
 8002226:	2900      	cmp	r1, #0
 8002228:	d058      	beq.n	80022dc <HAL_UART_Receive+0xce>
 800222a:	b90a      	cbnz	r2, 8002230 <HAL_UART_Receive+0x22>
      return  HAL_ERROR;
 800222c:	2001      	movs	r0, #1
 800222e:	e052      	b.n	80022d6 <HAL_UART_Receive+0xc8>
    huart->ErrorCode = HAL_UART_ERROR_NONE;
 8002230:	2300      	movs	r3, #0
 8002232:	6443      	str	r3, [r0, #68]	@ 0x44
    huart->RxState = HAL_UART_STATE_BUSY_RX;
 8002234:	2222      	movs	r2, #34	@ 0x22
 8002236:	f880 2042 	strb.w	r2, [r0, #66]	@ 0x42
    huart->ReceptionType = HAL_UART_RECEPTION_STANDARD;
 800223a:	6303      	str	r3, [r0, #48]	@ 0x30
    tickstart = HAL_GetTick();
 800223c:	f7ff f86c 	bl	8001318 <HAL_GetTick>
 8002240:	4607      	mov	r7, r0
    huart->RxXferSize = Size;
 8002242:	f8a4 802c 	strh.w	r8, [r4, #44]	@ 0x2c
    huart->RxXferCount = Size;
 8002246:	f8a4 802e 	strh.w	r8, [r4, #46]	@ 0x2e
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 800224a:	68a3      	ldr	r3, [r4, #8]
 800224c:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 8002250:	d002      	beq.n	8002258 <HAL_UART_Receive+0x4a>
      pdata16bits = NULL;
 8002252:	f04f 0800 	mov.w	r8, #0
 8002256:	e01c      	b.n	8002292 <HAL_UART_Receive+0x84>
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 8002258:	6923      	ldr	r3, [r4, #16]
 800225a:	b113      	cbz	r3, 8002262 <HAL_UART_Receive+0x54>
      pdata16bits = NULL;
 800225c:	f04f 0800 	mov.w	r8, #0
 8002260:	e017      	b.n	8002292 <HAL_UART_Receive+0x84>
      pdata16bits = (uint16_t *) pData;
 8002262:	46a8      	mov	r8, r5
      pdata8bits  = NULL;
 8002264:	2500      	movs	r5, #0
 8002266:	e014      	b.n	8002292 <HAL_UART_Receive+0x84>
        huart->RxState = HAL_UART_STATE_READY;
 8002268:	2320      	movs	r3, #32
 800226a:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
        return HAL_TIMEOUT;
 800226e:	2003      	movs	r0, #3
 8002270:	e031      	b.n	80022d6 <HAL_UART_Receive+0xc8>
        *pdata16bits = (uint16_t)(huart->Instance->DR & 0x01FF);
 8002272:	6823      	ldr	r3, [r4, #0]
 8002274:	685b      	ldr	r3, [r3, #4]
 8002276:	f3c3 0308 	ubfx	r3, r3, #0, #9
 800227a:	f828 3b02 	strh.w	r3, [r8], #2
        pdata16bits++;
 800227e:	e003      	b.n	8002288 <HAL_UART_Receive+0x7a>
          *pdata8bits = (uint8_t)(huart->Instance->DR & (uint8_t)0x00FF);
 8002280:	6823      	ldr	r3, [r4, #0]
 8002282:	685b      	ldr	r3, [r3, #4]
 8002284:	702b      	strb	r3, [r5, #0]
        pdata8bits++;
 8002286:	3501      	adds	r5, #1
      huart->RxXferCount--;
 8002288:	8de2      	ldrh	r2, [r4, #46]	@ 0x2e
 800228a:	b292      	uxth	r2, r2
 800228c:	3a01      	subs	r2, #1
 800228e:	b292      	uxth	r2, r2
 8002290:	85e2      	strh	r2, [r4, #46]	@ 0x2e
    while (huart->RxXferCount > 0U)
 8002292:	8de3      	ldrh	r3, [r4, #46]	@ 0x2e
 8002294:	b29b      	uxth	r3, r3
 8002296:	b1c3      	cbz	r3, 80022ca <HAL_UART_Receive+0xbc>
      if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_RXNE, RESET, tickstart, Timeout) != HAL_OK)
 8002298:	9600      	str	r6, [sp, #0]
 800229a:	463b      	mov	r3, r7
 800229c:	2200      	movs	r2, #0
 800229e:	2120      	movs	r1, #32
 80022a0:	4620      	mov	r0, r4
 80022a2:	f7ff fedb 	bl	800205c <UART_WaitOnFlagUntilTimeout>
 80022a6:	2800      	cmp	r0, #0
 80022a8:	d1de      	bne.n	8002268 <HAL_UART_Receive+0x5a>
      if (pdata8bits == NULL)
 80022aa:	2d00      	cmp	r5, #0
 80022ac:	d0e1      	beq.n	8002272 <HAL_UART_Receive+0x64>
        if ((huart->Init.WordLength == UART_WORDLENGTH_9B) || ((huart->Init.WordLength == UART_WORDLENGTH_8B) && (huart->Init.Parity == UART_PARITY_NONE)))
 80022ae:	68a3      	ldr	r3, [r4, #8]
 80022b0:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 80022b4:	d0e4      	beq.n	8002280 <HAL_UART_Receive+0x72>
 80022b6:	b913      	cbnz	r3, 80022be <HAL_UART_Receive+0xb0>
 80022b8:	6923      	ldr	r3, [r4, #16]
 80022ba:	2b00      	cmp	r3, #0
 80022bc:	d0e0      	beq.n	8002280 <HAL_UART_Receive+0x72>
          *pdata8bits = (uint8_t)(huart->Instance->DR & (uint8_t)0x007F);
 80022be:	6823      	ldr	r3, [r4, #0]
 80022c0:	685b      	ldr	r3, [r3, #4]
 80022c2:	f003 037f 	and.w	r3, r3, #127	@ 0x7f
 80022c6:	702b      	strb	r3, [r5, #0]
 80022c8:	e7dd      	b.n	8002286 <HAL_UART_Receive+0x78>
    huart->RxState = HAL_UART_STATE_READY;
 80022ca:	2320      	movs	r3, #32
 80022cc:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
    return HAL_OK;
 80022d0:	2000      	movs	r0, #0
 80022d2:	e000      	b.n	80022d6 <HAL_UART_Receive+0xc8>
    return HAL_BUSY;
 80022d4:	2002      	movs	r0, #2
}
 80022d6:	b002      	add	sp, #8
 80022d8:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
      return  HAL_ERROR;
 80022dc:	2001      	movs	r0, #1
 80022de:	e7fa      	b.n	80022d6 <HAL_UART_Receive+0xc8>

080022e0 <atoi>:
 80022e0:	220a      	movs	r2, #10
 80022e2:	2100      	movs	r1, #0
 80022e4:	f000 b87a 	b.w	80023dc <strtol>

080022e8 <_strtol_l.constprop.0>:
 80022e8:	2b24      	cmp	r3, #36	@ 0x24
 80022ea:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
 80022ee:	4686      	mov	lr, r0
 80022f0:	4690      	mov	r8, r2
 80022f2:	d801      	bhi.n	80022f8 <_strtol_l.constprop.0+0x10>
 80022f4:	2b01      	cmp	r3, #1
 80022f6:	d106      	bne.n	8002306 <_strtol_l.constprop.0+0x1e>
 80022f8:	f000 fad2 	bl	80028a0 <__errno>
 80022fc:	2316      	movs	r3, #22
 80022fe:	6003      	str	r3, [r0, #0]
 8002300:	2000      	movs	r0, #0
 8002302:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 8002306:	460d      	mov	r5, r1
 8002308:	4833      	ldr	r0, [pc, #204]	@ (80023d8 <_strtol_l.constprop.0+0xf0>)
 800230a:	462a      	mov	r2, r5
 800230c:	f815 4b01 	ldrb.w	r4, [r5], #1
 8002310:	5d06      	ldrb	r6, [r0, r4]
 8002312:	f016 0608 	ands.w	r6, r6, #8
 8002316:	d1f8      	bne.n	800230a <_strtol_l.constprop.0+0x22>
 8002318:	2c2d      	cmp	r4, #45	@ 0x2d
 800231a:	d12d      	bne.n	8002378 <_strtol_l.constprop.0+0x90>
 800231c:	2601      	movs	r6, #1
 800231e:	782c      	ldrb	r4, [r5, #0]
 8002320:	1c95      	adds	r5, r2, #2
 8002322:	f033 0210 	bics.w	r2, r3, #16
 8002326:	d109      	bne.n	800233c <_strtol_l.constprop.0+0x54>
 8002328:	2c30      	cmp	r4, #48	@ 0x30
 800232a:	d12a      	bne.n	8002382 <_strtol_l.constprop.0+0x9a>
 800232c:	782a      	ldrb	r2, [r5, #0]
 800232e:	f002 02df 	and.w	r2, r2, #223	@ 0xdf
 8002332:	2a58      	cmp	r2, #88	@ 0x58
 8002334:	d125      	bne.n	8002382 <_strtol_l.constprop.0+0x9a>
 8002336:	2310      	movs	r3, #16
 8002338:	786c      	ldrb	r4, [r5, #1]
 800233a:	3502      	adds	r5, #2
 800233c:	2200      	movs	r2, #0
 800233e:	f106 4c00 	add.w	ip, r6, #2147483648	@ 0x80000000
 8002342:	f10c 3cff 	add.w	ip, ip, #4294967295
 8002346:	fbbc f9f3 	udiv	r9, ip, r3
 800234a:	4610      	mov	r0, r2
 800234c:	fb03 ca19 	mls	sl, r3, r9, ip
 8002350:	f1a4 0730 	sub.w	r7, r4, #48	@ 0x30
 8002354:	2f09      	cmp	r7, #9
 8002356:	d81b      	bhi.n	8002390 <_strtol_l.constprop.0+0xa8>
 8002358:	463c      	mov	r4, r7
 800235a:	42a3      	cmp	r3, r4
 800235c:	dd27      	ble.n	80023ae <_strtol_l.constprop.0+0xc6>
 800235e:	1c57      	adds	r7, r2, #1
 8002360:	d007      	beq.n	8002372 <_strtol_l.constprop.0+0x8a>
 8002362:	4581      	cmp	r9, r0
 8002364:	d320      	bcc.n	80023a8 <_strtol_l.constprop.0+0xc0>
 8002366:	d101      	bne.n	800236c <_strtol_l.constprop.0+0x84>
 8002368:	45a2      	cmp	sl, r4
 800236a:	db1d      	blt.n	80023a8 <_strtol_l.constprop.0+0xc0>
 800236c:	2201      	movs	r2, #1
 800236e:	fb00 4003 	mla	r0, r0, r3, r4
 8002372:	f815 4b01 	ldrb.w	r4, [r5], #1
 8002376:	e7eb      	b.n	8002350 <_strtol_l.constprop.0+0x68>
 8002378:	2c2b      	cmp	r4, #43	@ 0x2b
 800237a:	bf04      	itt	eq
 800237c:	782c      	ldrbeq	r4, [r5, #0]
 800237e:	1c95      	addeq	r5, r2, #2
 8002380:	e7cf      	b.n	8002322 <_strtol_l.constprop.0+0x3a>
 8002382:	2b00      	cmp	r3, #0
 8002384:	d1da      	bne.n	800233c <_strtol_l.constprop.0+0x54>
 8002386:	2c30      	cmp	r4, #48	@ 0x30
 8002388:	bf0c      	ite	eq
 800238a:	2308      	moveq	r3, #8
 800238c:	230a      	movne	r3, #10
 800238e:	e7d5      	b.n	800233c <_strtol_l.constprop.0+0x54>
 8002390:	f1a4 0741 	sub.w	r7, r4, #65	@ 0x41
 8002394:	2f19      	cmp	r7, #25
 8002396:	d801      	bhi.n	800239c <_strtol_l.constprop.0+0xb4>
 8002398:	3c37      	subs	r4, #55	@ 0x37
 800239a:	e7de      	b.n	800235a <_strtol_l.constprop.0+0x72>
 800239c:	f1a4 0761 	sub.w	r7, r4, #97	@ 0x61
 80023a0:	2f19      	cmp	r7, #25
 80023a2:	d804      	bhi.n	80023ae <_strtol_l.constprop.0+0xc6>
 80023a4:	3c57      	subs	r4, #87	@ 0x57
 80023a6:	e7d8      	b.n	800235a <_strtol_l.constprop.0+0x72>
 80023a8:	f04f 32ff 	mov.w	r2, #4294967295
 80023ac:	e7e1      	b.n	8002372 <_strtol_l.constprop.0+0x8a>
 80023ae:	1c53      	adds	r3, r2, #1
 80023b0:	d108      	bne.n	80023c4 <_strtol_l.constprop.0+0xdc>
 80023b2:	2322      	movs	r3, #34	@ 0x22
 80023b4:	4660      	mov	r0, ip
 80023b6:	f8ce 3000 	str.w	r3, [lr]
 80023ba:	f1b8 0f00 	cmp.w	r8, #0
 80023be:	d0a0      	beq.n	8002302 <_strtol_l.constprop.0+0x1a>
 80023c0:	1e69      	subs	r1, r5, #1
 80023c2:	e006      	b.n	80023d2 <_strtol_l.constprop.0+0xea>
 80023c4:	b106      	cbz	r6, 80023c8 <_strtol_l.constprop.0+0xe0>
 80023c6:	4240      	negs	r0, r0
 80023c8:	f1b8 0f00 	cmp.w	r8, #0
 80023cc:	d099      	beq.n	8002302 <_strtol_l.constprop.0+0x1a>
 80023ce:	2a00      	cmp	r2, #0
 80023d0:	d1f6      	bne.n	80023c0 <_strtol_l.constprop.0+0xd8>
 80023d2:	f8c8 1000 	str.w	r1, [r8]
 80023d6:	e794      	b.n	8002302 <_strtol_l.constprop.0+0x1a>
 80023d8:	08003a0d 	.word	0x08003a0d

080023dc <strtol>:
 80023dc:	4613      	mov	r3, r2
 80023de:	460a      	mov	r2, r1
 80023e0:	4601      	mov	r1, r0
 80023e2:	4802      	ldr	r0, [pc, #8]	@ (80023ec <strtol+0x10>)
 80023e4:	6800      	ldr	r0, [r0, #0]
 80023e6:	f7ff bf7f 	b.w	80022e8 <_strtol_l.constprop.0>
 80023ea:	bf00      	nop
 80023ec:	2000001c 	.word	0x2000001c

080023f0 <std>:
 80023f0:	2300      	movs	r3, #0
 80023f2:	b510      	push	{r4, lr}
 80023f4:	4604      	mov	r4, r0
 80023f6:	e9c0 3300 	strd	r3, r3, [r0]
 80023fa:	e9c0 3304 	strd	r3, r3, [r0, #16]
 80023fe:	6083      	str	r3, [r0, #8]
 8002400:	8181      	strh	r1, [r0, #12]
 8002402:	6643      	str	r3, [r0, #100]	@ 0x64
 8002404:	81c2      	strh	r2, [r0, #14]
 8002406:	6183      	str	r3, [r0, #24]
 8002408:	4619      	mov	r1, r3
 800240a:	2208      	movs	r2, #8
 800240c:	305c      	adds	r0, #92	@ 0x5c
 800240e:	f000 f9f9 	bl	8002804 <memset>
 8002412:	4b0d      	ldr	r3, [pc, #52]	@ (8002448 <std+0x58>)
 8002414:	6224      	str	r4, [r4, #32]
 8002416:	6263      	str	r3, [r4, #36]	@ 0x24
 8002418:	4b0c      	ldr	r3, [pc, #48]	@ (800244c <std+0x5c>)
 800241a:	62a3      	str	r3, [r4, #40]	@ 0x28
 800241c:	4b0c      	ldr	r3, [pc, #48]	@ (8002450 <std+0x60>)
 800241e:	62e3      	str	r3, [r4, #44]	@ 0x2c
 8002420:	4b0c      	ldr	r3, [pc, #48]	@ (8002454 <std+0x64>)
 8002422:	6323      	str	r3, [r4, #48]	@ 0x30
 8002424:	4b0c      	ldr	r3, [pc, #48]	@ (8002458 <std+0x68>)
 8002426:	429c      	cmp	r4, r3
 8002428:	d006      	beq.n	8002438 <std+0x48>
 800242a:	f103 0268 	add.w	r2, r3, #104	@ 0x68
 800242e:	4294      	cmp	r4, r2
 8002430:	d002      	beq.n	8002438 <std+0x48>
 8002432:	33d0      	adds	r3, #208	@ 0xd0
 8002434:	429c      	cmp	r4, r3
 8002436:	d105      	bne.n	8002444 <std+0x54>
 8002438:	f104 0058 	add.w	r0, r4, #88	@ 0x58
 800243c:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 8002440:	f000 ba58 	b.w	80028f4 <__retarget_lock_init_recursive>
 8002444:	bd10      	pop	{r4, pc}
 8002446:	bf00      	nop
 8002448:	08002655 	.word	0x08002655
 800244c:	08002677 	.word	0x08002677
 8002450:	080026af 	.word	0x080026af
 8002454:	080026d3 	.word	0x080026d3
 8002458:	20000150 	.word	0x20000150

0800245c <stdio_exit_handler>:
 800245c:	4a02      	ldr	r2, [pc, #8]	@ (8002468 <stdio_exit_handler+0xc>)
 800245e:	4903      	ldr	r1, [pc, #12]	@ (800246c <stdio_exit_handler+0x10>)
 8002460:	4803      	ldr	r0, [pc, #12]	@ (8002470 <stdio_exit_handler+0x14>)
 8002462:	f000 b869 	b.w	8002538 <_fwalk_sglue>
 8002466:	bf00      	nop
 8002468:	20000010 	.word	0x20000010
 800246c:	080031a9 	.word	0x080031a9
 8002470:	20000020 	.word	0x20000020

08002474 <cleanup_stdio>:
 8002474:	6841      	ldr	r1, [r0, #4]
 8002476:	4b0c      	ldr	r3, [pc, #48]	@ (80024a8 <cleanup_stdio+0x34>)
 8002478:	b510      	push	{r4, lr}
 800247a:	4299      	cmp	r1, r3
 800247c:	4604      	mov	r4, r0
 800247e:	d001      	beq.n	8002484 <cleanup_stdio+0x10>
 8002480:	f000 fe92 	bl	80031a8 <_fflush_r>
 8002484:	68a1      	ldr	r1, [r4, #8]
 8002486:	4b09      	ldr	r3, [pc, #36]	@ (80024ac <cleanup_stdio+0x38>)
 8002488:	4299      	cmp	r1, r3
 800248a:	d002      	beq.n	8002492 <cleanup_stdio+0x1e>
 800248c:	4620      	mov	r0, r4
 800248e:	f000 fe8b 	bl	80031a8 <_fflush_r>
 8002492:	68e1      	ldr	r1, [r4, #12]
 8002494:	4b06      	ldr	r3, [pc, #24]	@ (80024b0 <cleanup_stdio+0x3c>)
 8002496:	4299      	cmp	r1, r3
 8002498:	d004      	beq.n	80024a4 <cleanup_stdio+0x30>
 800249a:	4620      	mov	r0, r4
 800249c:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 80024a0:	f000 be82 	b.w	80031a8 <_fflush_r>
 80024a4:	bd10      	pop	{r4, pc}
 80024a6:	bf00      	nop
 80024a8:	20000150 	.word	0x20000150
 80024ac:	200001b8 	.word	0x200001b8
 80024b0:	20000220 	.word	0x20000220

080024b4 <global_stdio_init.part.0>:
 80024b4:	b510      	push	{r4, lr}
 80024b6:	4b0b      	ldr	r3, [pc, #44]	@ (80024e4 <global_stdio_init.part.0+0x30>)
 80024b8:	4c0b      	ldr	r4, [pc, #44]	@ (80024e8 <global_stdio_init.part.0+0x34>)
 80024ba:	4a0c      	ldr	r2, [pc, #48]	@ (80024ec <global_stdio_init.part.0+0x38>)
 80024bc:	4620      	mov	r0, r4
 80024be:	601a      	str	r2, [r3, #0]
 80024c0:	2104      	movs	r1, #4
 80024c2:	2200      	movs	r2, #0
 80024c4:	f7ff ff94 	bl	80023f0 <std>
 80024c8:	f104 0068 	add.w	r0, r4, #104	@ 0x68
 80024cc:	2201      	movs	r2, #1
 80024ce:	2109      	movs	r1, #9
 80024d0:	f7ff ff8e 	bl	80023f0 <std>
 80024d4:	f104 00d0 	add.w	r0, r4, #208	@ 0xd0
 80024d8:	2202      	movs	r2, #2
 80024da:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 80024de:	2112      	movs	r1, #18
 80024e0:	f7ff bf86 	b.w	80023f0 <std>
 80024e4:	20000288 	.word	0x20000288
 80024e8:	20000150 	.word	0x20000150
 80024ec:	0800245d 	.word	0x0800245d

080024f0 <__sfp_lock_acquire>:
 80024f0:	4801      	ldr	r0, [pc, #4]	@ (80024f8 <__sfp_lock_acquire+0x8>)
 80024f2:	f000 ba00 	b.w	80028f6 <__retarget_lock_acquire_recursive>
 80024f6:	bf00      	nop
 80024f8:	20000291 	.word	0x20000291

080024fc <__sfp_lock_release>:
 80024fc:	4801      	ldr	r0, [pc, #4]	@ (8002504 <__sfp_lock_release+0x8>)
 80024fe:	f000 b9fb 	b.w	80028f8 <__retarget_lock_release_recursive>
 8002502:	bf00      	nop
 8002504:	20000291 	.word	0x20000291

08002508 <__sinit>:
 8002508:	b510      	push	{r4, lr}
 800250a:	4604      	mov	r4, r0
 800250c:	f7ff fff0 	bl	80024f0 <__sfp_lock_acquire>
 8002510:	6a23      	ldr	r3, [r4, #32]
 8002512:	b11b      	cbz	r3, 800251c <__sinit+0x14>
 8002514:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 8002518:	f7ff bff0 	b.w	80024fc <__sfp_lock_release>
 800251c:	4b04      	ldr	r3, [pc, #16]	@ (8002530 <__sinit+0x28>)
 800251e:	6223      	str	r3, [r4, #32]
 8002520:	4b04      	ldr	r3, [pc, #16]	@ (8002534 <__sinit+0x2c>)
 8002522:	681b      	ldr	r3, [r3, #0]
 8002524:	2b00      	cmp	r3, #0
 8002526:	d1f5      	bne.n	8002514 <__sinit+0xc>
 8002528:	f7ff ffc4 	bl	80024b4 <global_stdio_init.part.0>
 800252c:	e7f2      	b.n	8002514 <__sinit+0xc>
 800252e:	bf00      	nop
 8002530:	08002475 	.word	0x08002475
 8002534:	20000288 	.word	0x20000288

08002538 <_fwalk_sglue>:
 8002538:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 800253c:	4607      	mov	r7, r0
 800253e:	4688      	mov	r8, r1
 8002540:	4614      	mov	r4, r2
 8002542:	2600      	movs	r6, #0
 8002544:	e9d4 9501 	ldrd	r9, r5, [r4, #4]
 8002548:	f1b9 0901 	subs.w	r9, r9, #1
 800254c:	d505      	bpl.n	800255a <_fwalk_sglue+0x22>
 800254e:	6824      	ldr	r4, [r4, #0]
 8002550:	2c00      	cmp	r4, #0
 8002552:	d1f7      	bne.n	8002544 <_fwalk_sglue+0xc>
 8002554:	4630      	mov	r0, r6
 8002556:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
 800255a:	89ab      	ldrh	r3, [r5, #12]
 800255c:	2b01      	cmp	r3, #1
 800255e:	d907      	bls.n	8002570 <_fwalk_sglue+0x38>
 8002560:	f9b5 300e 	ldrsh.w	r3, [r5, #14]
 8002564:	3301      	adds	r3, #1
 8002566:	d003      	beq.n	8002570 <_fwalk_sglue+0x38>
 8002568:	4629      	mov	r1, r5
 800256a:	4638      	mov	r0, r7
 800256c:	47c0      	blx	r8
 800256e:	4306      	orrs	r6, r0
 8002570:	3568      	adds	r5, #104	@ 0x68
 8002572:	e7e9      	b.n	8002548 <_fwalk_sglue+0x10>

08002574 <iprintf>:
 8002574:	b40f      	push	{r0, r1, r2, r3}
 8002576:	b507      	push	{r0, r1, r2, lr}
 8002578:	4906      	ldr	r1, [pc, #24]	@ (8002594 <iprintf+0x20>)
 800257a:	ab04      	add	r3, sp, #16
 800257c:	6808      	ldr	r0, [r1, #0]
 800257e:	f853 2b04 	ldr.w	r2, [r3], #4
 8002582:	6881      	ldr	r1, [r0, #8]
 8002584:	9301      	str	r3, [sp, #4]
 8002586:	f000 fae5 	bl	8002b54 <_vfiprintf_r>
 800258a:	b003      	add	sp, #12
 800258c:	f85d eb04 	ldr.w	lr, [sp], #4
 8002590:	b004      	add	sp, #16
 8002592:	4770      	bx	lr
 8002594:	2000001c 	.word	0x2000001c

08002598 <_puts_r>:
 8002598:	6a03      	ldr	r3, [r0, #32]
 800259a:	b570      	push	{r4, r5, r6, lr}
 800259c:	4605      	mov	r5, r0
 800259e:	460e      	mov	r6, r1
 80025a0:	6884      	ldr	r4, [r0, #8]
 80025a2:	b90b      	cbnz	r3, 80025a8 <_puts_r+0x10>
 80025a4:	f7ff ffb0 	bl	8002508 <__sinit>
 80025a8:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 80025aa:	07db      	lsls	r3, r3, #31
 80025ac:	d405      	bmi.n	80025ba <_puts_r+0x22>
 80025ae:	89a3      	ldrh	r3, [r4, #12]
 80025b0:	0598      	lsls	r0, r3, #22
 80025b2:	d402      	bmi.n	80025ba <_puts_r+0x22>
 80025b4:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 80025b6:	f000 f99e 	bl	80028f6 <__retarget_lock_acquire_recursive>
 80025ba:	89a3      	ldrh	r3, [r4, #12]
 80025bc:	0719      	lsls	r1, r3, #28
 80025be:	d502      	bpl.n	80025c6 <_puts_r+0x2e>
 80025c0:	6923      	ldr	r3, [r4, #16]
 80025c2:	2b00      	cmp	r3, #0
 80025c4:	d135      	bne.n	8002632 <_puts_r+0x9a>
 80025c6:	4621      	mov	r1, r4
 80025c8:	4628      	mov	r0, r5
 80025ca:	f000 f8c5 	bl	8002758 <__swsetup_r>
 80025ce:	b380      	cbz	r0, 8002632 <_puts_r+0x9a>
 80025d0:	f04f 35ff 	mov.w	r5, #4294967295
 80025d4:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 80025d6:	07da      	lsls	r2, r3, #31
 80025d8:	d405      	bmi.n	80025e6 <_puts_r+0x4e>
 80025da:	89a3      	ldrh	r3, [r4, #12]
 80025dc:	059b      	lsls	r3, r3, #22
 80025de:	d402      	bmi.n	80025e6 <_puts_r+0x4e>
 80025e0:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 80025e2:	f000 f989 	bl	80028f8 <__retarget_lock_release_recursive>
 80025e6:	4628      	mov	r0, r5
 80025e8:	bd70      	pop	{r4, r5, r6, pc}
 80025ea:	2b00      	cmp	r3, #0
 80025ec:	da04      	bge.n	80025f8 <_puts_r+0x60>
 80025ee:	69a2      	ldr	r2, [r4, #24]
 80025f0:	429a      	cmp	r2, r3
 80025f2:	dc17      	bgt.n	8002624 <_puts_r+0x8c>
 80025f4:	290a      	cmp	r1, #10
 80025f6:	d015      	beq.n	8002624 <_puts_r+0x8c>
 80025f8:	6823      	ldr	r3, [r4, #0]
 80025fa:	1c5a      	adds	r2, r3, #1
 80025fc:	6022      	str	r2, [r4, #0]
 80025fe:	7019      	strb	r1, [r3, #0]
 8002600:	68a3      	ldr	r3, [r4, #8]
 8002602:	f816 1f01 	ldrb.w	r1, [r6, #1]!
 8002606:	3b01      	subs	r3, #1
 8002608:	60a3      	str	r3, [r4, #8]
 800260a:	2900      	cmp	r1, #0
 800260c:	d1ed      	bne.n	80025ea <_puts_r+0x52>
 800260e:	2b00      	cmp	r3, #0
 8002610:	da11      	bge.n	8002636 <_puts_r+0x9e>
 8002612:	4622      	mov	r2, r4
 8002614:	210a      	movs	r1, #10
 8002616:	4628      	mov	r0, r5
 8002618:	f000 f85f 	bl	80026da <__swbuf_r>
 800261c:	3001      	adds	r0, #1
 800261e:	d0d7      	beq.n	80025d0 <_puts_r+0x38>
 8002620:	250a      	movs	r5, #10
 8002622:	e7d7      	b.n	80025d4 <_puts_r+0x3c>
 8002624:	4622      	mov	r2, r4
 8002626:	4628      	mov	r0, r5
 8002628:	f000 f857 	bl	80026da <__swbuf_r>
 800262c:	3001      	adds	r0, #1
 800262e:	d1e7      	bne.n	8002600 <_puts_r+0x68>
 8002630:	e7ce      	b.n	80025d0 <_puts_r+0x38>
 8002632:	3e01      	subs	r6, #1
 8002634:	e7e4      	b.n	8002600 <_puts_r+0x68>
 8002636:	6823      	ldr	r3, [r4, #0]
 8002638:	1c5a      	adds	r2, r3, #1
 800263a:	6022      	str	r2, [r4, #0]
 800263c:	220a      	movs	r2, #10
 800263e:	701a      	strb	r2, [r3, #0]
 8002640:	e7ee      	b.n	8002620 <_puts_r+0x88>
	...

08002644 <puts>:
 8002644:	4b02      	ldr	r3, [pc, #8]	@ (8002650 <puts+0xc>)
 8002646:	4601      	mov	r1, r0
 8002648:	6818      	ldr	r0, [r3, #0]
 800264a:	f7ff bfa5 	b.w	8002598 <_puts_r>
 800264e:	bf00      	nop
 8002650:	2000001c 	.word	0x2000001c

08002654 <__sread>:
 8002654:	b510      	push	{r4, lr}
 8002656:	460c      	mov	r4, r1
 8002658:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 800265c:	f000 f8fc 	bl	8002858 <_read_r>
 8002660:	2800      	cmp	r0, #0
 8002662:	bfab      	itete	ge
 8002664:	6d63      	ldrge	r3, [r4, #84]	@ 0x54
 8002666:	89a3      	ldrhlt	r3, [r4, #12]
 8002668:	181b      	addge	r3, r3, r0
 800266a:	f423 5380 	biclt.w	r3, r3, #4096	@ 0x1000
 800266e:	bfac      	ite	ge
 8002670:	6563      	strge	r3, [r4, #84]	@ 0x54
 8002672:	81a3      	strhlt	r3, [r4, #12]
 8002674:	bd10      	pop	{r4, pc}

08002676 <__swrite>:
 8002676:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 800267a:	461f      	mov	r7, r3
 800267c:	898b      	ldrh	r3, [r1, #12]
 800267e:	4605      	mov	r5, r0
 8002680:	05db      	lsls	r3, r3, #23
 8002682:	460c      	mov	r4, r1
 8002684:	4616      	mov	r6, r2
 8002686:	d505      	bpl.n	8002694 <__swrite+0x1e>
 8002688:	2302      	movs	r3, #2
 800268a:	2200      	movs	r2, #0
 800268c:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8002690:	f000 f8d0 	bl	8002834 <_lseek_r>
 8002694:	89a3      	ldrh	r3, [r4, #12]
 8002696:	4632      	mov	r2, r6
 8002698:	f423 5380 	bic.w	r3, r3, #4096	@ 0x1000
 800269c:	81a3      	strh	r3, [r4, #12]
 800269e:	4628      	mov	r0, r5
 80026a0:	463b      	mov	r3, r7
 80026a2:	f9b4 100e 	ldrsh.w	r1, [r4, #14]
 80026a6:	e8bd 41f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, lr}
 80026aa:	f000 b8e7 	b.w	800287c <_write_r>

080026ae <__sseek>:
 80026ae:	b510      	push	{r4, lr}
 80026b0:	460c      	mov	r4, r1
 80026b2:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 80026b6:	f000 f8bd 	bl	8002834 <_lseek_r>
 80026ba:	1c43      	adds	r3, r0, #1
 80026bc:	89a3      	ldrh	r3, [r4, #12]
 80026be:	bf15      	itete	ne
 80026c0:	6560      	strne	r0, [r4, #84]	@ 0x54
 80026c2:	f423 5380 	biceq.w	r3, r3, #4096	@ 0x1000
 80026c6:	f443 5380 	orrne.w	r3, r3, #4096	@ 0x1000
 80026ca:	81a3      	strheq	r3, [r4, #12]
 80026cc:	bf18      	it	ne
 80026ce:	81a3      	strhne	r3, [r4, #12]
 80026d0:	bd10      	pop	{r4, pc}

080026d2 <__sclose>:
 80026d2:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 80026d6:	f000 b89d 	b.w	8002814 <_close_r>

080026da <__swbuf_r>:
 80026da:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 80026dc:	460e      	mov	r6, r1
 80026de:	4614      	mov	r4, r2
 80026e0:	4605      	mov	r5, r0
 80026e2:	b118      	cbz	r0, 80026ec <__swbuf_r+0x12>
 80026e4:	6a03      	ldr	r3, [r0, #32]
 80026e6:	b90b      	cbnz	r3, 80026ec <__swbuf_r+0x12>
 80026e8:	f7ff ff0e 	bl	8002508 <__sinit>
 80026ec:	69a3      	ldr	r3, [r4, #24]
 80026ee:	60a3      	str	r3, [r4, #8]
 80026f0:	89a3      	ldrh	r3, [r4, #12]
 80026f2:	071a      	lsls	r2, r3, #28
 80026f4:	d501      	bpl.n	80026fa <__swbuf_r+0x20>
 80026f6:	6923      	ldr	r3, [r4, #16]
 80026f8:	b943      	cbnz	r3, 800270c <__swbuf_r+0x32>
 80026fa:	4621      	mov	r1, r4
 80026fc:	4628      	mov	r0, r5
 80026fe:	f000 f82b 	bl	8002758 <__swsetup_r>
 8002702:	b118      	cbz	r0, 800270c <__swbuf_r+0x32>
 8002704:	f04f 37ff 	mov.w	r7, #4294967295
 8002708:	4638      	mov	r0, r7
 800270a:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
 800270c:	6823      	ldr	r3, [r4, #0]
 800270e:	6922      	ldr	r2, [r4, #16]
 8002710:	b2f6      	uxtb	r6, r6
 8002712:	1a98      	subs	r0, r3, r2
 8002714:	6963      	ldr	r3, [r4, #20]
 8002716:	4637      	mov	r7, r6
 8002718:	4283      	cmp	r3, r0
 800271a:	dc05      	bgt.n	8002728 <__swbuf_r+0x4e>
 800271c:	4621      	mov	r1, r4
 800271e:	4628      	mov	r0, r5
 8002720:	f000 fd42 	bl	80031a8 <_fflush_r>
 8002724:	2800      	cmp	r0, #0
 8002726:	d1ed      	bne.n	8002704 <__swbuf_r+0x2a>
 8002728:	68a3      	ldr	r3, [r4, #8]
 800272a:	3b01      	subs	r3, #1
 800272c:	60a3      	str	r3, [r4, #8]
 800272e:	6823      	ldr	r3, [r4, #0]
 8002730:	1c5a      	adds	r2, r3, #1
 8002732:	6022      	str	r2, [r4, #0]
 8002734:	701e      	strb	r6, [r3, #0]
 8002736:	6962      	ldr	r2, [r4, #20]
 8002738:	1c43      	adds	r3, r0, #1
 800273a:	429a      	cmp	r2, r3
 800273c:	d004      	beq.n	8002748 <__swbuf_r+0x6e>
 800273e:	89a3      	ldrh	r3, [r4, #12]
 8002740:	07db      	lsls	r3, r3, #31
 8002742:	d5e1      	bpl.n	8002708 <__swbuf_r+0x2e>
 8002744:	2e0a      	cmp	r6, #10
 8002746:	d1df      	bne.n	8002708 <__swbuf_r+0x2e>
 8002748:	4621      	mov	r1, r4
 800274a:	4628      	mov	r0, r5
 800274c:	f000 fd2c 	bl	80031a8 <_fflush_r>
 8002750:	2800      	cmp	r0, #0
 8002752:	d0d9      	beq.n	8002708 <__swbuf_r+0x2e>
 8002754:	e7d6      	b.n	8002704 <__swbuf_r+0x2a>
	...

08002758 <__swsetup_r>:
 8002758:	b538      	push	{r3, r4, r5, lr}
 800275a:	4b29      	ldr	r3, [pc, #164]	@ (8002800 <__swsetup_r+0xa8>)
 800275c:	4605      	mov	r5, r0
 800275e:	6818      	ldr	r0, [r3, #0]
 8002760:	460c      	mov	r4, r1
 8002762:	b118      	cbz	r0, 800276c <__swsetup_r+0x14>
 8002764:	6a03      	ldr	r3, [r0, #32]
 8002766:	b90b      	cbnz	r3, 800276c <__swsetup_r+0x14>
 8002768:	f7ff fece 	bl	8002508 <__sinit>
 800276c:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8002770:	0719      	lsls	r1, r3, #28
 8002772:	d422      	bmi.n	80027ba <__swsetup_r+0x62>
 8002774:	06da      	lsls	r2, r3, #27
 8002776:	d407      	bmi.n	8002788 <__swsetup_r+0x30>
 8002778:	2209      	movs	r2, #9
 800277a:	602a      	str	r2, [r5, #0]
 800277c:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 8002780:	f04f 30ff 	mov.w	r0, #4294967295
 8002784:	81a3      	strh	r3, [r4, #12]
 8002786:	e033      	b.n	80027f0 <__swsetup_r+0x98>
 8002788:	0758      	lsls	r0, r3, #29
 800278a:	d512      	bpl.n	80027b2 <__swsetup_r+0x5a>
 800278c:	6b61      	ldr	r1, [r4, #52]	@ 0x34
 800278e:	b141      	cbz	r1, 80027a2 <__swsetup_r+0x4a>
 8002790:	f104 0344 	add.w	r3, r4, #68	@ 0x44
 8002794:	4299      	cmp	r1, r3
 8002796:	d002      	beq.n	800279e <__swsetup_r+0x46>
 8002798:	4628      	mov	r0, r5
 800279a:	f000 f8bd 	bl	8002918 <_free_r>
 800279e:	2300      	movs	r3, #0
 80027a0:	6363      	str	r3, [r4, #52]	@ 0x34
 80027a2:	89a3      	ldrh	r3, [r4, #12]
 80027a4:	f023 0324 	bic.w	r3, r3, #36	@ 0x24
 80027a8:	81a3      	strh	r3, [r4, #12]
 80027aa:	2300      	movs	r3, #0
 80027ac:	6063      	str	r3, [r4, #4]
 80027ae:	6923      	ldr	r3, [r4, #16]
 80027b0:	6023      	str	r3, [r4, #0]
 80027b2:	89a3      	ldrh	r3, [r4, #12]
 80027b4:	f043 0308 	orr.w	r3, r3, #8
 80027b8:	81a3      	strh	r3, [r4, #12]
 80027ba:	6923      	ldr	r3, [r4, #16]
 80027bc:	b94b      	cbnz	r3, 80027d2 <__swsetup_r+0x7a>
 80027be:	89a3      	ldrh	r3, [r4, #12]
 80027c0:	f403 7320 	and.w	r3, r3, #640	@ 0x280
 80027c4:	f5b3 7f00 	cmp.w	r3, #512	@ 0x200
 80027c8:	d003      	beq.n	80027d2 <__swsetup_r+0x7a>
 80027ca:	4621      	mov	r1, r4
 80027cc:	4628      	mov	r0, r5
 80027ce:	f000 fd38 	bl	8003242 <__smakebuf_r>
 80027d2:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 80027d6:	f013 0201 	ands.w	r2, r3, #1
 80027da:	d00a      	beq.n	80027f2 <__swsetup_r+0x9a>
 80027dc:	2200      	movs	r2, #0
 80027de:	60a2      	str	r2, [r4, #8]
 80027e0:	6962      	ldr	r2, [r4, #20]
 80027e2:	4252      	negs	r2, r2
 80027e4:	61a2      	str	r2, [r4, #24]
 80027e6:	6922      	ldr	r2, [r4, #16]
 80027e8:	b942      	cbnz	r2, 80027fc <__swsetup_r+0xa4>
 80027ea:	f013 0080 	ands.w	r0, r3, #128	@ 0x80
 80027ee:	d1c5      	bne.n	800277c <__swsetup_r+0x24>
 80027f0:	bd38      	pop	{r3, r4, r5, pc}
 80027f2:	0799      	lsls	r1, r3, #30
 80027f4:	bf58      	it	pl
 80027f6:	6962      	ldrpl	r2, [r4, #20]
 80027f8:	60a2      	str	r2, [r4, #8]
 80027fa:	e7f4      	b.n	80027e6 <__swsetup_r+0x8e>
 80027fc:	2000      	movs	r0, #0
 80027fe:	e7f7      	b.n	80027f0 <__swsetup_r+0x98>
 8002800:	2000001c 	.word	0x2000001c

08002804 <memset>:
 8002804:	4603      	mov	r3, r0
 8002806:	4402      	add	r2, r0
 8002808:	4293      	cmp	r3, r2
 800280a:	d100      	bne.n	800280e <memset+0xa>
 800280c:	4770      	bx	lr
 800280e:	f803 1b01 	strb.w	r1, [r3], #1
 8002812:	e7f9      	b.n	8002808 <memset+0x4>

08002814 <_close_r>:
 8002814:	b538      	push	{r3, r4, r5, lr}
 8002816:	2300      	movs	r3, #0
 8002818:	4d05      	ldr	r5, [pc, #20]	@ (8002830 <_close_r+0x1c>)
 800281a:	4604      	mov	r4, r0
 800281c:	4608      	mov	r0, r1
 800281e:	602b      	str	r3, [r5, #0]
 8002820:	f7fe fcde 	bl	80011e0 <_close>
 8002824:	1c43      	adds	r3, r0, #1
 8002826:	d102      	bne.n	800282e <_close_r+0x1a>
 8002828:	682b      	ldr	r3, [r5, #0]
 800282a:	b103      	cbz	r3, 800282e <_close_r+0x1a>
 800282c:	6023      	str	r3, [r4, #0]
 800282e:	bd38      	pop	{r3, r4, r5, pc}
 8002830:	2000028c 	.word	0x2000028c

08002834 <_lseek_r>:
 8002834:	b538      	push	{r3, r4, r5, lr}
 8002836:	4604      	mov	r4, r0
 8002838:	4608      	mov	r0, r1
 800283a:	4611      	mov	r1, r2
 800283c:	2200      	movs	r2, #0
 800283e:	4d05      	ldr	r5, [pc, #20]	@ (8002854 <_lseek_r+0x20>)
 8002840:	602a      	str	r2, [r5, #0]
 8002842:	461a      	mov	r2, r3
 8002844:	f7fe fcd6 	bl	80011f4 <_lseek>
 8002848:	1c43      	adds	r3, r0, #1
 800284a:	d102      	bne.n	8002852 <_lseek_r+0x1e>
 800284c:	682b      	ldr	r3, [r5, #0]
 800284e:	b103      	cbz	r3, 8002852 <_lseek_r+0x1e>
 8002850:	6023      	str	r3, [r4, #0]
 8002852:	bd38      	pop	{r3, r4, r5, pc}
 8002854:	2000028c 	.word	0x2000028c

08002858 <_read_r>:
 8002858:	b538      	push	{r3, r4, r5, lr}
 800285a:	4604      	mov	r4, r0
 800285c:	4608      	mov	r0, r1
 800285e:	4611      	mov	r1, r2
 8002860:	2200      	movs	r2, #0
 8002862:	4d05      	ldr	r5, [pc, #20]	@ (8002878 <_read_r+0x20>)
 8002864:	602a      	str	r2, [r5, #0]
 8002866:	461a      	mov	r2, r3
 8002868:	f7fe fc9c 	bl	80011a4 <_read>
 800286c:	1c43      	adds	r3, r0, #1
 800286e:	d102      	bne.n	8002876 <_read_r+0x1e>
 8002870:	682b      	ldr	r3, [r5, #0]
 8002872:	b103      	cbz	r3, 8002876 <_read_r+0x1e>
 8002874:	6023      	str	r3, [r4, #0]
 8002876:	bd38      	pop	{r3, r4, r5, pc}
 8002878:	2000028c 	.word	0x2000028c

0800287c <_write_r>:
 800287c:	b538      	push	{r3, r4, r5, lr}
 800287e:	4604      	mov	r4, r0
 8002880:	4608      	mov	r0, r1
 8002882:	4611      	mov	r1, r2
 8002884:	2200      	movs	r2, #0
 8002886:	4d05      	ldr	r5, [pc, #20]	@ (800289c <_write_r+0x20>)
 8002888:	602a      	str	r2, [r5, #0]
 800288a:	461a      	mov	r2, r3
 800288c:	f7fe fc9a 	bl	80011c4 <_write>
 8002890:	1c43      	adds	r3, r0, #1
 8002892:	d102      	bne.n	800289a <_write_r+0x1e>
 8002894:	682b      	ldr	r3, [r5, #0]
 8002896:	b103      	cbz	r3, 800289a <_write_r+0x1e>
 8002898:	6023      	str	r3, [r4, #0]
 800289a:	bd38      	pop	{r3, r4, r5, pc}
 800289c:	2000028c 	.word	0x2000028c

080028a0 <__errno>:
 80028a0:	4b01      	ldr	r3, [pc, #4]	@ (80028a8 <__errno+0x8>)
 80028a2:	6818      	ldr	r0, [r3, #0]
 80028a4:	4770      	bx	lr
 80028a6:	bf00      	nop
 80028a8:	2000001c 	.word	0x2000001c

080028ac <__libc_init_array>:
 80028ac:	b570      	push	{r4, r5, r6, lr}
 80028ae:	2600      	movs	r6, #0
 80028b0:	4d0c      	ldr	r5, [pc, #48]	@ (80028e4 <__libc_init_array+0x38>)
 80028b2:	4c0d      	ldr	r4, [pc, #52]	@ (80028e8 <__libc_init_array+0x3c>)
 80028b4:	1b64      	subs	r4, r4, r5
 80028b6:	10a4      	asrs	r4, r4, #2
 80028b8:	42a6      	cmp	r6, r4
 80028ba:	d109      	bne.n	80028d0 <__libc_init_array+0x24>
 80028bc:	f000 fd3e 	bl	800333c <_init>
 80028c0:	2600      	movs	r6, #0
 80028c2:	4d0a      	ldr	r5, [pc, #40]	@ (80028ec <__libc_init_array+0x40>)
 80028c4:	4c0a      	ldr	r4, [pc, #40]	@ (80028f0 <__libc_init_array+0x44>)
 80028c6:	1b64      	subs	r4, r4, r5
 80028c8:	10a4      	asrs	r4, r4, #2
 80028ca:	42a6      	cmp	r6, r4
 80028cc:	d105      	bne.n	80028da <__libc_init_array+0x2e>
 80028ce:	bd70      	pop	{r4, r5, r6, pc}
 80028d0:	f855 3b04 	ldr.w	r3, [r5], #4
 80028d4:	4798      	blx	r3
 80028d6:	3601      	adds	r6, #1
 80028d8:	e7ee      	b.n	80028b8 <__libc_init_array+0xc>
 80028da:	f855 3b04 	ldr.w	r3, [r5], #4
 80028de:	4798      	blx	r3
 80028e0:	3601      	adds	r6, #1
 80028e2:	e7f2      	b.n	80028ca <__libc_init_array+0x1e>
 80028e4:	08003b40 	.word	0x08003b40
 80028e8:	08003b40 	.word	0x08003b40
 80028ec:	08003b40 	.word	0x08003b40
 80028f0:	08003b44 	.word	0x08003b44

080028f4 <__retarget_lock_init_recursive>:
 80028f4:	4770      	bx	lr

080028f6 <__retarget_lock_acquire_recursive>:
 80028f6:	4770      	bx	lr

080028f8 <__retarget_lock_release_recursive>:
 80028f8:	4770      	bx	lr

080028fa <memcpy>:
 80028fa:	440a      	add	r2, r1
 80028fc:	4291      	cmp	r1, r2
 80028fe:	f100 33ff 	add.w	r3, r0, #4294967295
 8002902:	d100      	bne.n	8002906 <memcpy+0xc>
 8002904:	4770      	bx	lr
 8002906:	b510      	push	{r4, lr}
 8002908:	f811 4b01 	ldrb.w	r4, [r1], #1
 800290c:	4291      	cmp	r1, r2
 800290e:	f803 4f01 	strb.w	r4, [r3, #1]!
 8002912:	d1f9      	bne.n	8002908 <memcpy+0xe>
 8002914:	bd10      	pop	{r4, pc}
	...

08002918 <_free_r>:
 8002918:	b538      	push	{r3, r4, r5, lr}
 800291a:	4605      	mov	r5, r0
 800291c:	2900      	cmp	r1, #0
 800291e:	d040      	beq.n	80029a2 <_free_r+0x8a>
 8002920:	f851 3c04 	ldr.w	r3, [r1, #-4]
 8002924:	1f0c      	subs	r4, r1, #4
 8002926:	2b00      	cmp	r3, #0
 8002928:	bfb8      	it	lt
 800292a:	18e4      	addlt	r4, r4, r3
 800292c:	f000 f8de 	bl	8002aec <__malloc_lock>
 8002930:	4a1c      	ldr	r2, [pc, #112]	@ (80029a4 <_free_r+0x8c>)
 8002932:	6813      	ldr	r3, [r2, #0]
 8002934:	b933      	cbnz	r3, 8002944 <_free_r+0x2c>
 8002936:	6063      	str	r3, [r4, #4]
 8002938:	6014      	str	r4, [r2, #0]
 800293a:	4628      	mov	r0, r5
 800293c:	e8bd 4038 	ldmia.w	sp!, {r3, r4, r5, lr}
 8002940:	f000 b8da 	b.w	8002af8 <__malloc_unlock>
 8002944:	42a3      	cmp	r3, r4
 8002946:	d908      	bls.n	800295a <_free_r+0x42>
 8002948:	6820      	ldr	r0, [r4, #0]
 800294a:	1821      	adds	r1, r4, r0
 800294c:	428b      	cmp	r3, r1
 800294e:	bf01      	itttt	eq
 8002950:	6819      	ldreq	r1, [r3, #0]
 8002952:	685b      	ldreq	r3, [r3, #4]
 8002954:	1809      	addeq	r1, r1, r0
 8002956:	6021      	streq	r1, [r4, #0]
 8002958:	e7ed      	b.n	8002936 <_free_r+0x1e>
 800295a:	461a      	mov	r2, r3
 800295c:	685b      	ldr	r3, [r3, #4]
 800295e:	b10b      	cbz	r3, 8002964 <_free_r+0x4c>
 8002960:	42a3      	cmp	r3, r4
 8002962:	d9fa      	bls.n	800295a <_free_r+0x42>
 8002964:	6811      	ldr	r1, [r2, #0]
 8002966:	1850      	adds	r0, r2, r1
 8002968:	42a0      	cmp	r0, r4
 800296a:	d10b      	bne.n	8002984 <_free_r+0x6c>
 800296c:	6820      	ldr	r0, [r4, #0]
 800296e:	4401      	add	r1, r0
 8002970:	1850      	adds	r0, r2, r1
 8002972:	4283      	cmp	r3, r0
 8002974:	6011      	str	r1, [r2, #0]
 8002976:	d1e0      	bne.n	800293a <_free_r+0x22>
 8002978:	6818      	ldr	r0, [r3, #0]
 800297a:	685b      	ldr	r3, [r3, #4]
 800297c:	4408      	add	r0, r1
 800297e:	6010      	str	r0, [r2, #0]
 8002980:	6053      	str	r3, [r2, #4]
 8002982:	e7da      	b.n	800293a <_free_r+0x22>
 8002984:	d902      	bls.n	800298c <_free_r+0x74>
 8002986:	230c      	movs	r3, #12
 8002988:	602b      	str	r3, [r5, #0]
 800298a:	e7d6      	b.n	800293a <_free_r+0x22>
 800298c:	6820      	ldr	r0, [r4, #0]
 800298e:	1821      	adds	r1, r4, r0
 8002990:	428b      	cmp	r3, r1
 8002992:	bf01      	itttt	eq
 8002994:	6819      	ldreq	r1, [r3, #0]
 8002996:	685b      	ldreq	r3, [r3, #4]
 8002998:	1809      	addeq	r1, r1, r0
 800299a:	6021      	streq	r1, [r4, #0]
 800299c:	6063      	str	r3, [r4, #4]
 800299e:	6054      	str	r4, [r2, #4]
 80029a0:	e7cb      	b.n	800293a <_free_r+0x22>
 80029a2:	bd38      	pop	{r3, r4, r5, pc}
 80029a4:	20000298 	.word	0x20000298

080029a8 <sbrk_aligned>:
 80029a8:	b570      	push	{r4, r5, r6, lr}
 80029aa:	4e0f      	ldr	r6, [pc, #60]	@ (80029e8 <sbrk_aligned+0x40>)
 80029ac:	460c      	mov	r4, r1
 80029ae:	6831      	ldr	r1, [r6, #0]
 80029b0:	4605      	mov	r5, r0
 80029b2:	b911      	cbnz	r1, 80029ba <sbrk_aligned+0x12>
 80029b4:	f000 fca4 	bl	8003300 <_sbrk_r>
 80029b8:	6030      	str	r0, [r6, #0]
 80029ba:	4621      	mov	r1, r4
 80029bc:	4628      	mov	r0, r5
 80029be:	f000 fc9f 	bl	8003300 <_sbrk_r>
 80029c2:	1c43      	adds	r3, r0, #1
 80029c4:	d103      	bne.n	80029ce <sbrk_aligned+0x26>
 80029c6:	f04f 34ff 	mov.w	r4, #4294967295
 80029ca:	4620      	mov	r0, r4
 80029cc:	bd70      	pop	{r4, r5, r6, pc}
 80029ce:	1cc4      	adds	r4, r0, #3
 80029d0:	f024 0403 	bic.w	r4, r4, #3
 80029d4:	42a0      	cmp	r0, r4
 80029d6:	d0f8      	beq.n	80029ca <sbrk_aligned+0x22>
 80029d8:	1a21      	subs	r1, r4, r0
 80029da:	4628      	mov	r0, r5
 80029dc:	f000 fc90 	bl	8003300 <_sbrk_r>
 80029e0:	3001      	adds	r0, #1
 80029e2:	d1f2      	bne.n	80029ca <sbrk_aligned+0x22>
 80029e4:	e7ef      	b.n	80029c6 <sbrk_aligned+0x1e>
 80029e6:	bf00      	nop
 80029e8:	20000294 	.word	0x20000294

080029ec <_malloc_r>:
 80029ec:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 80029f0:	1ccd      	adds	r5, r1, #3
 80029f2:	f025 0503 	bic.w	r5, r5, #3
 80029f6:	3508      	adds	r5, #8
 80029f8:	2d0c      	cmp	r5, #12
 80029fa:	bf38      	it	cc
 80029fc:	250c      	movcc	r5, #12
 80029fe:	2d00      	cmp	r5, #0
 8002a00:	4606      	mov	r6, r0
 8002a02:	db01      	blt.n	8002a08 <_malloc_r+0x1c>
 8002a04:	42a9      	cmp	r1, r5
 8002a06:	d904      	bls.n	8002a12 <_malloc_r+0x26>
 8002a08:	230c      	movs	r3, #12
 8002a0a:	6033      	str	r3, [r6, #0]
 8002a0c:	2000      	movs	r0, #0
 8002a0e:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
 8002a12:	f8df 80d4 	ldr.w	r8, [pc, #212]	@ 8002ae8 <_malloc_r+0xfc>
 8002a16:	f000 f869 	bl	8002aec <__malloc_lock>
 8002a1a:	f8d8 3000 	ldr.w	r3, [r8]
 8002a1e:	461c      	mov	r4, r3
 8002a20:	bb44      	cbnz	r4, 8002a74 <_malloc_r+0x88>
 8002a22:	4629      	mov	r1, r5
 8002a24:	4630      	mov	r0, r6
 8002a26:	f7ff ffbf 	bl	80029a8 <sbrk_aligned>
 8002a2a:	1c43      	adds	r3, r0, #1
 8002a2c:	4604      	mov	r4, r0
 8002a2e:	d158      	bne.n	8002ae2 <_malloc_r+0xf6>
 8002a30:	f8d8 4000 	ldr.w	r4, [r8]
 8002a34:	4627      	mov	r7, r4
 8002a36:	2f00      	cmp	r7, #0
 8002a38:	d143      	bne.n	8002ac2 <_malloc_r+0xd6>
 8002a3a:	2c00      	cmp	r4, #0
 8002a3c:	d04b      	beq.n	8002ad6 <_malloc_r+0xea>
 8002a3e:	6823      	ldr	r3, [r4, #0]
 8002a40:	4639      	mov	r1, r7
 8002a42:	4630      	mov	r0, r6
 8002a44:	eb04 0903 	add.w	r9, r4, r3
 8002a48:	f000 fc5a 	bl	8003300 <_sbrk_r>
 8002a4c:	4581      	cmp	r9, r0
 8002a4e:	d142      	bne.n	8002ad6 <_malloc_r+0xea>
 8002a50:	6821      	ldr	r1, [r4, #0]
 8002a52:	4630      	mov	r0, r6
 8002a54:	1a6d      	subs	r5, r5, r1
 8002a56:	4629      	mov	r1, r5
 8002a58:	f7ff ffa6 	bl	80029a8 <sbrk_aligned>
 8002a5c:	3001      	adds	r0, #1
 8002a5e:	d03a      	beq.n	8002ad6 <_malloc_r+0xea>
 8002a60:	6823      	ldr	r3, [r4, #0]
 8002a62:	442b      	add	r3, r5
 8002a64:	6023      	str	r3, [r4, #0]
 8002a66:	f8d8 3000 	ldr.w	r3, [r8]
 8002a6a:	685a      	ldr	r2, [r3, #4]
 8002a6c:	bb62      	cbnz	r2, 8002ac8 <_malloc_r+0xdc>
 8002a6e:	f8c8 7000 	str.w	r7, [r8]
 8002a72:	e00f      	b.n	8002a94 <_malloc_r+0xa8>
 8002a74:	6822      	ldr	r2, [r4, #0]
 8002a76:	1b52      	subs	r2, r2, r5
 8002a78:	d420      	bmi.n	8002abc <_malloc_r+0xd0>
 8002a7a:	2a0b      	cmp	r2, #11
 8002a7c:	d917      	bls.n	8002aae <_malloc_r+0xc2>
 8002a7e:	1961      	adds	r1, r4, r5
 8002a80:	42a3      	cmp	r3, r4
 8002a82:	6025      	str	r5, [r4, #0]
 8002a84:	bf18      	it	ne
 8002a86:	6059      	strne	r1, [r3, #4]
 8002a88:	6863      	ldr	r3, [r4, #4]
 8002a8a:	bf08      	it	eq
 8002a8c:	f8c8 1000 	streq.w	r1, [r8]
 8002a90:	5162      	str	r2, [r4, r5]
 8002a92:	604b      	str	r3, [r1, #4]
 8002a94:	4630      	mov	r0, r6
 8002a96:	f000 f82f 	bl	8002af8 <__malloc_unlock>
 8002a9a:	f104 000b 	add.w	r0, r4, #11
 8002a9e:	1d23      	adds	r3, r4, #4
 8002aa0:	f020 0007 	bic.w	r0, r0, #7
 8002aa4:	1ac2      	subs	r2, r0, r3
 8002aa6:	bf1c      	itt	ne
 8002aa8:	1a1b      	subne	r3, r3, r0
 8002aaa:	50a3      	strne	r3, [r4, r2]
 8002aac:	e7af      	b.n	8002a0e <_malloc_r+0x22>
 8002aae:	6862      	ldr	r2, [r4, #4]
 8002ab0:	42a3      	cmp	r3, r4
 8002ab2:	bf0c      	ite	eq
 8002ab4:	f8c8 2000 	streq.w	r2, [r8]
 8002ab8:	605a      	strne	r2, [r3, #4]
 8002aba:	e7eb      	b.n	8002a94 <_malloc_r+0xa8>
 8002abc:	4623      	mov	r3, r4
 8002abe:	6864      	ldr	r4, [r4, #4]
 8002ac0:	e7ae      	b.n	8002a20 <_malloc_r+0x34>
 8002ac2:	463c      	mov	r4, r7
 8002ac4:	687f      	ldr	r7, [r7, #4]
 8002ac6:	e7b6      	b.n	8002a36 <_malloc_r+0x4a>
 8002ac8:	461a      	mov	r2, r3
 8002aca:	685b      	ldr	r3, [r3, #4]
 8002acc:	42a3      	cmp	r3, r4
 8002ace:	d1fb      	bne.n	8002ac8 <_malloc_r+0xdc>
 8002ad0:	2300      	movs	r3, #0
 8002ad2:	6053      	str	r3, [r2, #4]
 8002ad4:	e7de      	b.n	8002a94 <_malloc_r+0xa8>
 8002ad6:	230c      	movs	r3, #12
 8002ad8:	4630      	mov	r0, r6
 8002ada:	6033      	str	r3, [r6, #0]
 8002adc:	f000 f80c 	bl	8002af8 <__malloc_unlock>
 8002ae0:	e794      	b.n	8002a0c <_malloc_r+0x20>
 8002ae2:	6005      	str	r5, [r0, #0]
 8002ae4:	e7d6      	b.n	8002a94 <_malloc_r+0xa8>
 8002ae6:	bf00      	nop
 8002ae8:	20000298 	.word	0x20000298

08002aec <__malloc_lock>:
 8002aec:	4801      	ldr	r0, [pc, #4]	@ (8002af4 <__malloc_lock+0x8>)
 8002aee:	f7ff bf02 	b.w	80028f6 <__retarget_lock_acquire_recursive>
 8002af2:	bf00      	nop
 8002af4:	20000290 	.word	0x20000290

08002af8 <__malloc_unlock>:
 8002af8:	4801      	ldr	r0, [pc, #4]	@ (8002b00 <__malloc_unlock+0x8>)
 8002afa:	f7ff befd 	b.w	80028f8 <__retarget_lock_release_recursive>
 8002afe:	bf00      	nop
 8002b00:	20000290 	.word	0x20000290

08002b04 <__sfputc_r>:
 8002b04:	6893      	ldr	r3, [r2, #8]
 8002b06:	b410      	push	{r4}
 8002b08:	3b01      	subs	r3, #1
 8002b0a:	2b00      	cmp	r3, #0
 8002b0c:	6093      	str	r3, [r2, #8]
 8002b0e:	da07      	bge.n	8002b20 <__sfputc_r+0x1c>
 8002b10:	6994      	ldr	r4, [r2, #24]
 8002b12:	42a3      	cmp	r3, r4
 8002b14:	db01      	blt.n	8002b1a <__sfputc_r+0x16>
 8002b16:	290a      	cmp	r1, #10
 8002b18:	d102      	bne.n	8002b20 <__sfputc_r+0x1c>
 8002b1a:	bc10      	pop	{r4}
 8002b1c:	f7ff bddd 	b.w	80026da <__swbuf_r>
 8002b20:	6813      	ldr	r3, [r2, #0]
 8002b22:	1c58      	adds	r0, r3, #1
 8002b24:	6010      	str	r0, [r2, #0]
 8002b26:	7019      	strb	r1, [r3, #0]
 8002b28:	4608      	mov	r0, r1
 8002b2a:	bc10      	pop	{r4}
 8002b2c:	4770      	bx	lr

08002b2e <__sfputs_r>:
 8002b2e:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8002b30:	4606      	mov	r6, r0
 8002b32:	460f      	mov	r7, r1
 8002b34:	4614      	mov	r4, r2
 8002b36:	18d5      	adds	r5, r2, r3
 8002b38:	42ac      	cmp	r4, r5
 8002b3a:	d101      	bne.n	8002b40 <__sfputs_r+0x12>
 8002b3c:	2000      	movs	r0, #0
 8002b3e:	e007      	b.n	8002b50 <__sfputs_r+0x22>
 8002b40:	463a      	mov	r2, r7
 8002b42:	4630      	mov	r0, r6
 8002b44:	f814 1b01 	ldrb.w	r1, [r4], #1
 8002b48:	f7ff ffdc 	bl	8002b04 <__sfputc_r>
 8002b4c:	1c43      	adds	r3, r0, #1
 8002b4e:	d1f3      	bne.n	8002b38 <__sfputs_r+0xa>
 8002b50:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
	...

08002b54 <_vfiprintf_r>:
 8002b54:	e92d 4ff0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
 8002b58:	460d      	mov	r5, r1
 8002b5a:	4614      	mov	r4, r2
 8002b5c:	4698      	mov	r8, r3
 8002b5e:	4606      	mov	r6, r0
 8002b60:	b09d      	sub	sp, #116	@ 0x74
 8002b62:	b118      	cbz	r0, 8002b6c <_vfiprintf_r+0x18>
 8002b64:	6a03      	ldr	r3, [r0, #32]
 8002b66:	b90b      	cbnz	r3, 8002b6c <_vfiprintf_r+0x18>
 8002b68:	f7ff fcce 	bl	8002508 <__sinit>
 8002b6c:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 8002b6e:	07d9      	lsls	r1, r3, #31
 8002b70:	d405      	bmi.n	8002b7e <_vfiprintf_r+0x2a>
 8002b72:	89ab      	ldrh	r3, [r5, #12]
 8002b74:	059a      	lsls	r2, r3, #22
 8002b76:	d402      	bmi.n	8002b7e <_vfiprintf_r+0x2a>
 8002b78:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 8002b7a:	f7ff febc 	bl	80028f6 <__retarget_lock_acquire_recursive>
 8002b7e:	89ab      	ldrh	r3, [r5, #12]
 8002b80:	071b      	lsls	r3, r3, #28
 8002b82:	d501      	bpl.n	8002b88 <_vfiprintf_r+0x34>
 8002b84:	692b      	ldr	r3, [r5, #16]
 8002b86:	b99b      	cbnz	r3, 8002bb0 <_vfiprintf_r+0x5c>
 8002b88:	4629      	mov	r1, r5
 8002b8a:	4630      	mov	r0, r6
 8002b8c:	f7ff fde4 	bl	8002758 <__swsetup_r>
 8002b90:	b170      	cbz	r0, 8002bb0 <_vfiprintf_r+0x5c>
 8002b92:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 8002b94:	07dc      	lsls	r4, r3, #31
 8002b96:	d504      	bpl.n	8002ba2 <_vfiprintf_r+0x4e>
 8002b98:	f04f 30ff 	mov.w	r0, #4294967295
 8002b9c:	b01d      	add	sp, #116	@ 0x74
 8002b9e:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
 8002ba2:	89ab      	ldrh	r3, [r5, #12]
 8002ba4:	0598      	lsls	r0, r3, #22
 8002ba6:	d4f7      	bmi.n	8002b98 <_vfiprintf_r+0x44>
 8002ba8:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 8002baa:	f7ff fea5 	bl	80028f8 <__retarget_lock_release_recursive>
 8002bae:	e7f3      	b.n	8002b98 <_vfiprintf_r+0x44>
 8002bb0:	2300      	movs	r3, #0
 8002bb2:	9309      	str	r3, [sp, #36]	@ 0x24
 8002bb4:	2320      	movs	r3, #32
 8002bb6:	f88d 3029 	strb.w	r3, [sp, #41]	@ 0x29
 8002bba:	2330      	movs	r3, #48	@ 0x30
 8002bbc:	f04f 0901 	mov.w	r9, #1
 8002bc0:	f8cd 800c 	str.w	r8, [sp, #12]
 8002bc4:	f8df 81a8 	ldr.w	r8, [pc, #424]	@ 8002d70 <_vfiprintf_r+0x21c>
 8002bc8:	f88d 302a 	strb.w	r3, [sp, #42]	@ 0x2a
 8002bcc:	4623      	mov	r3, r4
 8002bce:	469a      	mov	sl, r3
 8002bd0:	f813 2b01 	ldrb.w	r2, [r3], #1
 8002bd4:	b10a      	cbz	r2, 8002bda <_vfiprintf_r+0x86>
 8002bd6:	2a25      	cmp	r2, #37	@ 0x25
 8002bd8:	d1f9      	bne.n	8002bce <_vfiprintf_r+0x7a>
 8002bda:	ebba 0b04 	subs.w	fp, sl, r4
 8002bde:	d00b      	beq.n	8002bf8 <_vfiprintf_r+0xa4>
 8002be0:	465b      	mov	r3, fp
 8002be2:	4622      	mov	r2, r4
 8002be4:	4629      	mov	r1, r5
 8002be6:	4630      	mov	r0, r6
 8002be8:	f7ff ffa1 	bl	8002b2e <__sfputs_r>
 8002bec:	3001      	adds	r0, #1
 8002bee:	f000 80a7 	beq.w	8002d40 <_vfiprintf_r+0x1ec>
 8002bf2:	9a09      	ldr	r2, [sp, #36]	@ 0x24
 8002bf4:	445a      	add	r2, fp
 8002bf6:	9209      	str	r2, [sp, #36]	@ 0x24
 8002bf8:	f89a 3000 	ldrb.w	r3, [sl]
 8002bfc:	2b00      	cmp	r3, #0
 8002bfe:	f000 809f 	beq.w	8002d40 <_vfiprintf_r+0x1ec>
 8002c02:	2300      	movs	r3, #0
 8002c04:	f04f 32ff 	mov.w	r2, #4294967295
 8002c08:	e9cd 2305 	strd	r2, r3, [sp, #20]
 8002c0c:	f10a 0a01 	add.w	sl, sl, #1
 8002c10:	9304      	str	r3, [sp, #16]
 8002c12:	9307      	str	r3, [sp, #28]
 8002c14:	f88d 3053 	strb.w	r3, [sp, #83]	@ 0x53
 8002c18:	931a      	str	r3, [sp, #104]	@ 0x68
 8002c1a:	4654      	mov	r4, sl
 8002c1c:	2205      	movs	r2, #5
 8002c1e:	f814 1b01 	ldrb.w	r1, [r4], #1
 8002c22:	4853      	ldr	r0, [pc, #332]	@ (8002d70 <_vfiprintf_r+0x21c>)
 8002c24:	f000 fb7c 	bl	8003320 <memchr>
 8002c28:	9a04      	ldr	r2, [sp, #16]
 8002c2a:	b9d8      	cbnz	r0, 8002c64 <_vfiprintf_r+0x110>
 8002c2c:	06d1      	lsls	r1, r2, #27
 8002c2e:	bf44      	itt	mi
 8002c30:	2320      	movmi	r3, #32
 8002c32:	f88d 3053 	strbmi.w	r3, [sp, #83]	@ 0x53
 8002c36:	0713      	lsls	r3, r2, #28
 8002c38:	bf44      	itt	mi
 8002c3a:	232b      	movmi	r3, #43	@ 0x2b
 8002c3c:	f88d 3053 	strbmi.w	r3, [sp, #83]	@ 0x53
 8002c40:	f89a 3000 	ldrb.w	r3, [sl]
 8002c44:	2b2a      	cmp	r3, #42	@ 0x2a
 8002c46:	d015      	beq.n	8002c74 <_vfiprintf_r+0x120>
 8002c48:	4654      	mov	r4, sl
 8002c4a:	2000      	movs	r0, #0
 8002c4c:	f04f 0c0a 	mov.w	ip, #10
 8002c50:	9a07      	ldr	r2, [sp, #28]
 8002c52:	4621      	mov	r1, r4
 8002c54:	f811 3b01 	ldrb.w	r3, [r1], #1
 8002c58:	3b30      	subs	r3, #48	@ 0x30
 8002c5a:	2b09      	cmp	r3, #9
 8002c5c:	d94b      	bls.n	8002cf6 <_vfiprintf_r+0x1a2>
 8002c5e:	b1b0      	cbz	r0, 8002c8e <_vfiprintf_r+0x13a>
 8002c60:	9207      	str	r2, [sp, #28]
 8002c62:	e014      	b.n	8002c8e <_vfiprintf_r+0x13a>
 8002c64:	eba0 0308 	sub.w	r3, r0, r8
 8002c68:	fa09 f303 	lsl.w	r3, r9, r3
 8002c6c:	4313      	orrs	r3, r2
 8002c6e:	46a2      	mov	sl, r4
 8002c70:	9304      	str	r3, [sp, #16]
 8002c72:	e7d2      	b.n	8002c1a <_vfiprintf_r+0xc6>
 8002c74:	9b03      	ldr	r3, [sp, #12]
 8002c76:	1d19      	adds	r1, r3, #4
 8002c78:	681b      	ldr	r3, [r3, #0]
 8002c7a:	9103      	str	r1, [sp, #12]
 8002c7c:	2b00      	cmp	r3, #0
 8002c7e:	bfbb      	ittet	lt
 8002c80:	425b      	neglt	r3, r3
 8002c82:	f042 0202 	orrlt.w	r2, r2, #2
 8002c86:	9307      	strge	r3, [sp, #28]
 8002c88:	9307      	strlt	r3, [sp, #28]
 8002c8a:	bfb8      	it	lt
 8002c8c:	9204      	strlt	r2, [sp, #16]
 8002c8e:	7823      	ldrb	r3, [r4, #0]
 8002c90:	2b2e      	cmp	r3, #46	@ 0x2e
 8002c92:	d10a      	bne.n	8002caa <_vfiprintf_r+0x156>
 8002c94:	7863      	ldrb	r3, [r4, #1]
 8002c96:	2b2a      	cmp	r3, #42	@ 0x2a
 8002c98:	d132      	bne.n	8002d00 <_vfiprintf_r+0x1ac>
 8002c9a:	9b03      	ldr	r3, [sp, #12]
 8002c9c:	3402      	adds	r4, #2
 8002c9e:	1d1a      	adds	r2, r3, #4
 8002ca0:	681b      	ldr	r3, [r3, #0]
 8002ca2:	9203      	str	r2, [sp, #12]
 8002ca4:	ea43 73e3 	orr.w	r3, r3, r3, asr #31
 8002ca8:	9305      	str	r3, [sp, #20]
 8002caa:	f8df a0c8 	ldr.w	sl, [pc, #200]	@ 8002d74 <_vfiprintf_r+0x220>
 8002cae:	2203      	movs	r2, #3
 8002cb0:	4650      	mov	r0, sl
 8002cb2:	7821      	ldrb	r1, [r4, #0]
 8002cb4:	f000 fb34 	bl	8003320 <memchr>
 8002cb8:	b138      	cbz	r0, 8002cca <_vfiprintf_r+0x176>
 8002cba:	2240      	movs	r2, #64	@ 0x40
 8002cbc:	9b04      	ldr	r3, [sp, #16]
 8002cbe:	eba0 000a 	sub.w	r0, r0, sl
 8002cc2:	4082      	lsls	r2, r0
 8002cc4:	4313      	orrs	r3, r2
 8002cc6:	3401      	adds	r4, #1
 8002cc8:	9304      	str	r3, [sp, #16]
 8002cca:	f814 1b01 	ldrb.w	r1, [r4], #1
 8002cce:	2206      	movs	r2, #6
 8002cd0:	4829      	ldr	r0, [pc, #164]	@ (8002d78 <_vfiprintf_r+0x224>)
 8002cd2:	f88d 1028 	strb.w	r1, [sp, #40]	@ 0x28
 8002cd6:	f000 fb23 	bl	8003320 <memchr>
 8002cda:	2800      	cmp	r0, #0
 8002cdc:	d03f      	beq.n	8002d5e <_vfiprintf_r+0x20a>
 8002cde:	4b27      	ldr	r3, [pc, #156]	@ (8002d7c <_vfiprintf_r+0x228>)
 8002ce0:	bb1b      	cbnz	r3, 8002d2a <_vfiprintf_r+0x1d6>
 8002ce2:	9b03      	ldr	r3, [sp, #12]
 8002ce4:	3307      	adds	r3, #7
 8002ce6:	f023 0307 	bic.w	r3, r3, #7
 8002cea:	3308      	adds	r3, #8
 8002cec:	9303      	str	r3, [sp, #12]
 8002cee:	9b09      	ldr	r3, [sp, #36]	@ 0x24
 8002cf0:	443b      	add	r3, r7
 8002cf2:	9309      	str	r3, [sp, #36]	@ 0x24
 8002cf4:	e76a      	b.n	8002bcc <_vfiprintf_r+0x78>
 8002cf6:	460c      	mov	r4, r1
 8002cf8:	2001      	movs	r0, #1
 8002cfa:	fb0c 3202 	mla	r2, ip, r2, r3
 8002cfe:	e7a8      	b.n	8002c52 <_vfiprintf_r+0xfe>
 8002d00:	2300      	movs	r3, #0
 8002d02:	f04f 0c0a 	mov.w	ip, #10
 8002d06:	4619      	mov	r1, r3
 8002d08:	3401      	adds	r4, #1
 8002d0a:	9305      	str	r3, [sp, #20]
 8002d0c:	4620      	mov	r0, r4
 8002d0e:	f810 2b01 	ldrb.w	r2, [r0], #1
 8002d12:	3a30      	subs	r2, #48	@ 0x30
 8002d14:	2a09      	cmp	r2, #9
 8002d16:	d903      	bls.n	8002d20 <_vfiprintf_r+0x1cc>
 8002d18:	2b00      	cmp	r3, #0
 8002d1a:	d0c6      	beq.n	8002caa <_vfiprintf_r+0x156>
 8002d1c:	9105      	str	r1, [sp, #20]
 8002d1e:	e7c4      	b.n	8002caa <_vfiprintf_r+0x156>
 8002d20:	4604      	mov	r4, r0
 8002d22:	2301      	movs	r3, #1
 8002d24:	fb0c 2101 	mla	r1, ip, r1, r2
 8002d28:	e7f0      	b.n	8002d0c <_vfiprintf_r+0x1b8>
 8002d2a:	ab03      	add	r3, sp, #12
 8002d2c:	9300      	str	r3, [sp, #0]
 8002d2e:	462a      	mov	r2, r5
 8002d30:	4630      	mov	r0, r6
 8002d32:	4b13      	ldr	r3, [pc, #76]	@ (8002d80 <_vfiprintf_r+0x22c>)
 8002d34:	a904      	add	r1, sp, #16
 8002d36:	f3af 8000 	nop.w
 8002d3a:	4607      	mov	r7, r0
 8002d3c:	1c78      	adds	r0, r7, #1
 8002d3e:	d1d6      	bne.n	8002cee <_vfiprintf_r+0x19a>
 8002d40:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 8002d42:	07d9      	lsls	r1, r3, #31
 8002d44:	d405      	bmi.n	8002d52 <_vfiprintf_r+0x1fe>
 8002d46:	89ab      	ldrh	r3, [r5, #12]
 8002d48:	059a      	lsls	r2, r3, #22
 8002d4a:	d402      	bmi.n	8002d52 <_vfiprintf_r+0x1fe>
 8002d4c:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 8002d4e:	f7ff fdd3 	bl	80028f8 <__retarget_lock_release_recursive>
 8002d52:	89ab      	ldrh	r3, [r5, #12]
 8002d54:	065b      	lsls	r3, r3, #25
 8002d56:	f53f af1f 	bmi.w	8002b98 <_vfiprintf_r+0x44>
 8002d5a:	9809      	ldr	r0, [sp, #36]	@ 0x24
 8002d5c:	e71e      	b.n	8002b9c <_vfiprintf_r+0x48>
 8002d5e:	ab03      	add	r3, sp, #12
 8002d60:	9300      	str	r3, [sp, #0]
 8002d62:	462a      	mov	r2, r5
 8002d64:	4630      	mov	r0, r6
 8002d66:	4b06      	ldr	r3, [pc, #24]	@ (8002d80 <_vfiprintf_r+0x22c>)
 8002d68:	a904      	add	r1, sp, #16
 8002d6a:	f000 f87d 	bl	8002e68 <_printf_i>
 8002d6e:	e7e4      	b.n	8002d3a <_vfiprintf_r+0x1e6>
 8002d70:	08003b0d 	.word	0x08003b0d
 8002d74:	08003b13 	.word	0x08003b13
 8002d78:	08003b17 	.word	0x08003b17
 8002d7c:	00000000 	.word	0x00000000
 8002d80:	08002b2f 	.word	0x08002b2f

08002d84 <_printf_common>:
 8002d84:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
 8002d88:	4616      	mov	r6, r2
 8002d8a:	4698      	mov	r8, r3
 8002d8c:	688a      	ldr	r2, [r1, #8]
 8002d8e:	690b      	ldr	r3, [r1, #16]
 8002d90:	4607      	mov	r7, r0
 8002d92:	4293      	cmp	r3, r2
 8002d94:	bfb8      	it	lt
 8002d96:	4613      	movlt	r3, r2
 8002d98:	6033      	str	r3, [r6, #0]
 8002d9a:	f891 2043 	ldrb.w	r2, [r1, #67]	@ 0x43
 8002d9e:	460c      	mov	r4, r1
 8002da0:	f8dd 9020 	ldr.w	r9, [sp, #32]
 8002da4:	b10a      	cbz	r2, 8002daa <_printf_common+0x26>
 8002da6:	3301      	adds	r3, #1
 8002da8:	6033      	str	r3, [r6, #0]
 8002daa:	6823      	ldr	r3, [r4, #0]
 8002dac:	0699      	lsls	r1, r3, #26
 8002dae:	bf42      	ittt	mi
 8002db0:	6833      	ldrmi	r3, [r6, #0]
 8002db2:	3302      	addmi	r3, #2
 8002db4:	6033      	strmi	r3, [r6, #0]
 8002db6:	6825      	ldr	r5, [r4, #0]
 8002db8:	f015 0506 	ands.w	r5, r5, #6
 8002dbc:	d106      	bne.n	8002dcc <_printf_common+0x48>
 8002dbe:	f104 0a19 	add.w	sl, r4, #25
 8002dc2:	68e3      	ldr	r3, [r4, #12]
 8002dc4:	6832      	ldr	r2, [r6, #0]
 8002dc6:	1a9b      	subs	r3, r3, r2
 8002dc8:	42ab      	cmp	r3, r5
 8002dca:	dc2b      	bgt.n	8002e24 <_printf_common+0xa0>
 8002dcc:	f894 3043 	ldrb.w	r3, [r4, #67]	@ 0x43
 8002dd0:	6822      	ldr	r2, [r4, #0]
 8002dd2:	3b00      	subs	r3, #0
 8002dd4:	bf18      	it	ne
 8002dd6:	2301      	movne	r3, #1
 8002dd8:	0692      	lsls	r2, r2, #26
 8002dda:	d430      	bmi.n	8002e3e <_printf_common+0xba>
 8002ddc:	4641      	mov	r1, r8
 8002dde:	4638      	mov	r0, r7
 8002de0:	f104 0243 	add.w	r2, r4, #67	@ 0x43
 8002de4:	47c8      	blx	r9
 8002de6:	3001      	adds	r0, #1
 8002de8:	d023      	beq.n	8002e32 <_printf_common+0xae>
 8002dea:	6823      	ldr	r3, [r4, #0]
 8002dec:	6922      	ldr	r2, [r4, #16]
 8002dee:	f003 0306 	and.w	r3, r3, #6
 8002df2:	2b04      	cmp	r3, #4
 8002df4:	bf14      	ite	ne
 8002df6:	2500      	movne	r5, #0
 8002df8:	6833      	ldreq	r3, [r6, #0]
 8002dfa:	f04f 0600 	mov.w	r6, #0
 8002dfe:	bf08      	it	eq
 8002e00:	68e5      	ldreq	r5, [r4, #12]
 8002e02:	f104 041a 	add.w	r4, r4, #26
 8002e06:	bf08      	it	eq
 8002e08:	1aed      	subeq	r5, r5, r3
 8002e0a:	f854 3c12 	ldr.w	r3, [r4, #-18]
 8002e0e:	bf08      	it	eq
 8002e10:	ea25 75e5 	biceq.w	r5, r5, r5, asr #31
 8002e14:	4293      	cmp	r3, r2
 8002e16:	bfc4      	itt	gt
 8002e18:	1a9b      	subgt	r3, r3, r2
 8002e1a:	18ed      	addgt	r5, r5, r3
 8002e1c:	42b5      	cmp	r5, r6
 8002e1e:	d11a      	bne.n	8002e56 <_printf_common+0xd2>
 8002e20:	2000      	movs	r0, #0
 8002e22:	e008      	b.n	8002e36 <_printf_common+0xb2>
 8002e24:	2301      	movs	r3, #1
 8002e26:	4652      	mov	r2, sl
 8002e28:	4641      	mov	r1, r8
 8002e2a:	4638      	mov	r0, r7
 8002e2c:	47c8      	blx	r9
 8002e2e:	3001      	adds	r0, #1
 8002e30:	d103      	bne.n	8002e3a <_printf_common+0xb6>
 8002e32:	f04f 30ff 	mov.w	r0, #4294967295
 8002e36:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 8002e3a:	3501      	adds	r5, #1
 8002e3c:	e7c1      	b.n	8002dc2 <_printf_common+0x3e>
 8002e3e:	2030      	movs	r0, #48	@ 0x30
 8002e40:	18e1      	adds	r1, r4, r3
 8002e42:	f881 0043 	strb.w	r0, [r1, #67]	@ 0x43
 8002e46:	1c5a      	adds	r2, r3, #1
 8002e48:	f894 1045 	ldrb.w	r1, [r4, #69]	@ 0x45
 8002e4c:	4422      	add	r2, r4
 8002e4e:	3302      	adds	r3, #2
 8002e50:	f882 1043 	strb.w	r1, [r2, #67]	@ 0x43
 8002e54:	e7c2      	b.n	8002ddc <_printf_common+0x58>
 8002e56:	2301      	movs	r3, #1
 8002e58:	4622      	mov	r2, r4
 8002e5a:	4641      	mov	r1, r8
 8002e5c:	4638      	mov	r0, r7
 8002e5e:	47c8      	blx	r9
 8002e60:	3001      	adds	r0, #1
 8002e62:	d0e6      	beq.n	8002e32 <_printf_common+0xae>
 8002e64:	3601      	adds	r6, #1
 8002e66:	e7d9      	b.n	8002e1c <_printf_common+0x98>

08002e68 <_printf_i>:
 8002e68:	e92d 47ff 	stmdb	sp!, {r0, r1, r2, r3, r4, r5, r6, r7, r8, r9, sl, lr}
 8002e6c:	7e0f      	ldrb	r7, [r1, #24]
 8002e6e:	4691      	mov	r9, r2
 8002e70:	2f78      	cmp	r7, #120	@ 0x78
 8002e72:	4680      	mov	r8, r0
 8002e74:	460c      	mov	r4, r1
 8002e76:	469a      	mov	sl, r3
 8002e78:	9e0c      	ldr	r6, [sp, #48]	@ 0x30
 8002e7a:	f101 0243 	add.w	r2, r1, #67	@ 0x43
 8002e7e:	d807      	bhi.n	8002e90 <_printf_i+0x28>
 8002e80:	2f62      	cmp	r7, #98	@ 0x62
 8002e82:	d80a      	bhi.n	8002e9a <_printf_i+0x32>
 8002e84:	2f00      	cmp	r7, #0
 8002e86:	f000 80d3 	beq.w	8003030 <_printf_i+0x1c8>
 8002e8a:	2f58      	cmp	r7, #88	@ 0x58
 8002e8c:	f000 80ba 	beq.w	8003004 <_printf_i+0x19c>
 8002e90:	f104 0642 	add.w	r6, r4, #66	@ 0x42
 8002e94:	f884 7042 	strb.w	r7, [r4, #66]	@ 0x42
 8002e98:	e03a      	b.n	8002f10 <_printf_i+0xa8>
 8002e9a:	f1a7 0363 	sub.w	r3, r7, #99	@ 0x63
 8002e9e:	2b15      	cmp	r3, #21
 8002ea0:	d8f6      	bhi.n	8002e90 <_printf_i+0x28>
 8002ea2:	a101      	add	r1, pc, #4	@ (adr r1, 8002ea8 <_printf_i+0x40>)
 8002ea4:	f851 f023 	ldr.w	pc, [r1, r3, lsl #2]
 8002ea8:	08002f01 	.word	0x08002f01
 8002eac:	08002f15 	.word	0x08002f15
 8002eb0:	08002e91 	.word	0x08002e91
 8002eb4:	08002e91 	.word	0x08002e91
 8002eb8:	08002e91 	.word	0x08002e91
 8002ebc:	08002e91 	.word	0x08002e91
 8002ec0:	08002f15 	.word	0x08002f15
 8002ec4:	08002e91 	.word	0x08002e91
 8002ec8:	08002e91 	.word	0x08002e91
 8002ecc:	08002e91 	.word	0x08002e91
 8002ed0:	08002e91 	.word	0x08002e91
 8002ed4:	08003017 	.word	0x08003017
 8002ed8:	08002f3f 	.word	0x08002f3f
 8002edc:	08002fd1 	.word	0x08002fd1
 8002ee0:	08002e91 	.word	0x08002e91
 8002ee4:	08002e91 	.word	0x08002e91
 8002ee8:	08003039 	.word	0x08003039
 8002eec:	08002e91 	.word	0x08002e91
 8002ef0:	08002f3f 	.word	0x08002f3f
 8002ef4:	08002e91 	.word	0x08002e91
 8002ef8:	08002e91 	.word	0x08002e91
 8002efc:	08002fd9 	.word	0x08002fd9
 8002f00:	6833      	ldr	r3, [r6, #0]
 8002f02:	1d1a      	adds	r2, r3, #4
 8002f04:	681b      	ldr	r3, [r3, #0]
 8002f06:	6032      	str	r2, [r6, #0]
 8002f08:	f104 0642 	add.w	r6, r4, #66	@ 0x42
 8002f0c:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
 8002f10:	2301      	movs	r3, #1
 8002f12:	e09e      	b.n	8003052 <_printf_i+0x1ea>
 8002f14:	6833      	ldr	r3, [r6, #0]
 8002f16:	6820      	ldr	r0, [r4, #0]
 8002f18:	1d19      	adds	r1, r3, #4
 8002f1a:	6031      	str	r1, [r6, #0]
 8002f1c:	0606      	lsls	r6, r0, #24
 8002f1e:	d501      	bpl.n	8002f24 <_printf_i+0xbc>
 8002f20:	681d      	ldr	r5, [r3, #0]
 8002f22:	e003      	b.n	8002f2c <_printf_i+0xc4>
 8002f24:	0645      	lsls	r5, r0, #25
 8002f26:	d5fb      	bpl.n	8002f20 <_printf_i+0xb8>
 8002f28:	f9b3 5000 	ldrsh.w	r5, [r3]
 8002f2c:	2d00      	cmp	r5, #0
 8002f2e:	da03      	bge.n	8002f38 <_printf_i+0xd0>
 8002f30:	232d      	movs	r3, #45	@ 0x2d
 8002f32:	426d      	negs	r5, r5
 8002f34:	f884 3043 	strb.w	r3, [r4, #67]	@ 0x43
 8002f38:	230a      	movs	r3, #10
 8002f3a:	4859      	ldr	r0, [pc, #356]	@ (80030a0 <_printf_i+0x238>)
 8002f3c:	e011      	b.n	8002f62 <_printf_i+0xfa>
 8002f3e:	6821      	ldr	r1, [r4, #0]
 8002f40:	6833      	ldr	r3, [r6, #0]
 8002f42:	0608      	lsls	r0, r1, #24
 8002f44:	f853 5b04 	ldr.w	r5, [r3], #4
 8002f48:	d402      	bmi.n	8002f50 <_printf_i+0xe8>
 8002f4a:	0649      	lsls	r1, r1, #25
 8002f4c:	bf48      	it	mi
 8002f4e:	b2ad      	uxthmi	r5, r5
 8002f50:	2f6f      	cmp	r7, #111	@ 0x6f
 8002f52:	6033      	str	r3, [r6, #0]
 8002f54:	bf14      	ite	ne
 8002f56:	230a      	movne	r3, #10
 8002f58:	2308      	moveq	r3, #8
 8002f5a:	4851      	ldr	r0, [pc, #324]	@ (80030a0 <_printf_i+0x238>)
 8002f5c:	2100      	movs	r1, #0
 8002f5e:	f884 1043 	strb.w	r1, [r4, #67]	@ 0x43
 8002f62:	6866      	ldr	r6, [r4, #4]
 8002f64:	2e00      	cmp	r6, #0
 8002f66:	bfa8      	it	ge
 8002f68:	6821      	ldrge	r1, [r4, #0]
 8002f6a:	60a6      	str	r6, [r4, #8]
 8002f6c:	bfa4      	itt	ge
 8002f6e:	f021 0104 	bicge.w	r1, r1, #4
 8002f72:	6021      	strge	r1, [r4, #0]
 8002f74:	b90d      	cbnz	r5, 8002f7a <_printf_i+0x112>
 8002f76:	2e00      	cmp	r6, #0
 8002f78:	d04b      	beq.n	8003012 <_printf_i+0x1aa>
 8002f7a:	4616      	mov	r6, r2
 8002f7c:	fbb5 f1f3 	udiv	r1, r5, r3
 8002f80:	fb03 5711 	mls	r7, r3, r1, r5
 8002f84:	5dc7      	ldrb	r7, [r0, r7]
 8002f86:	f806 7d01 	strb.w	r7, [r6, #-1]!
 8002f8a:	462f      	mov	r7, r5
 8002f8c:	42bb      	cmp	r3, r7
 8002f8e:	460d      	mov	r5, r1
 8002f90:	d9f4      	bls.n	8002f7c <_printf_i+0x114>
 8002f92:	2b08      	cmp	r3, #8
 8002f94:	d10b      	bne.n	8002fae <_printf_i+0x146>
 8002f96:	6823      	ldr	r3, [r4, #0]
 8002f98:	07df      	lsls	r7, r3, #31
 8002f9a:	d508      	bpl.n	8002fae <_printf_i+0x146>
 8002f9c:	6923      	ldr	r3, [r4, #16]
 8002f9e:	6861      	ldr	r1, [r4, #4]
 8002fa0:	4299      	cmp	r1, r3
 8002fa2:	bfde      	ittt	le
 8002fa4:	2330      	movle	r3, #48	@ 0x30
 8002fa6:	f806 3c01 	strble.w	r3, [r6, #-1]
 8002faa:	f106 36ff 	addle.w	r6, r6, #4294967295
 8002fae:	1b92      	subs	r2, r2, r6
 8002fb0:	6122      	str	r2, [r4, #16]
 8002fb2:	464b      	mov	r3, r9
 8002fb4:	4621      	mov	r1, r4
 8002fb6:	4640      	mov	r0, r8
 8002fb8:	f8cd a000 	str.w	sl, [sp]
 8002fbc:	aa03      	add	r2, sp, #12
 8002fbe:	f7ff fee1 	bl	8002d84 <_printf_common>
 8002fc2:	3001      	adds	r0, #1
 8002fc4:	d14a      	bne.n	800305c <_printf_i+0x1f4>
 8002fc6:	f04f 30ff 	mov.w	r0, #4294967295
 8002fca:	b004      	add	sp, #16
 8002fcc:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 8002fd0:	6823      	ldr	r3, [r4, #0]
 8002fd2:	f043 0320 	orr.w	r3, r3, #32
 8002fd6:	6023      	str	r3, [r4, #0]
 8002fd8:	2778      	movs	r7, #120	@ 0x78
 8002fda:	4832      	ldr	r0, [pc, #200]	@ (80030a4 <_printf_i+0x23c>)
 8002fdc:	f884 7045 	strb.w	r7, [r4, #69]	@ 0x45
 8002fe0:	6823      	ldr	r3, [r4, #0]
 8002fe2:	6831      	ldr	r1, [r6, #0]
 8002fe4:	061f      	lsls	r7, r3, #24
 8002fe6:	f851 5b04 	ldr.w	r5, [r1], #4
 8002fea:	d402      	bmi.n	8002ff2 <_printf_i+0x18a>
 8002fec:	065f      	lsls	r7, r3, #25
 8002fee:	bf48      	it	mi
 8002ff0:	b2ad      	uxthmi	r5, r5
 8002ff2:	6031      	str	r1, [r6, #0]
 8002ff4:	07d9      	lsls	r1, r3, #31
 8002ff6:	bf44      	itt	mi
 8002ff8:	f043 0320 	orrmi.w	r3, r3, #32
 8002ffc:	6023      	strmi	r3, [r4, #0]
 8002ffe:	b11d      	cbz	r5, 8003008 <_printf_i+0x1a0>
 8003000:	2310      	movs	r3, #16
 8003002:	e7ab      	b.n	8002f5c <_printf_i+0xf4>
 8003004:	4826      	ldr	r0, [pc, #152]	@ (80030a0 <_printf_i+0x238>)
 8003006:	e7e9      	b.n	8002fdc <_printf_i+0x174>
 8003008:	6823      	ldr	r3, [r4, #0]
 800300a:	f023 0320 	bic.w	r3, r3, #32
 800300e:	6023      	str	r3, [r4, #0]
 8003010:	e7f6      	b.n	8003000 <_printf_i+0x198>
 8003012:	4616      	mov	r6, r2
 8003014:	e7bd      	b.n	8002f92 <_printf_i+0x12a>
 8003016:	6833      	ldr	r3, [r6, #0]
 8003018:	6825      	ldr	r5, [r4, #0]
 800301a:	1d18      	adds	r0, r3, #4
 800301c:	6961      	ldr	r1, [r4, #20]
 800301e:	6030      	str	r0, [r6, #0]
 8003020:	062e      	lsls	r6, r5, #24
 8003022:	681b      	ldr	r3, [r3, #0]
 8003024:	d501      	bpl.n	800302a <_printf_i+0x1c2>
 8003026:	6019      	str	r1, [r3, #0]
 8003028:	e002      	b.n	8003030 <_printf_i+0x1c8>
 800302a:	0668      	lsls	r0, r5, #25
 800302c:	d5fb      	bpl.n	8003026 <_printf_i+0x1be>
 800302e:	8019      	strh	r1, [r3, #0]
 8003030:	2300      	movs	r3, #0
 8003032:	4616      	mov	r6, r2
 8003034:	6123      	str	r3, [r4, #16]
 8003036:	e7bc      	b.n	8002fb2 <_printf_i+0x14a>
 8003038:	6833      	ldr	r3, [r6, #0]
 800303a:	2100      	movs	r1, #0
 800303c:	1d1a      	adds	r2, r3, #4
 800303e:	6032      	str	r2, [r6, #0]
 8003040:	681e      	ldr	r6, [r3, #0]
 8003042:	6862      	ldr	r2, [r4, #4]
 8003044:	4630      	mov	r0, r6
 8003046:	f000 f96b 	bl	8003320 <memchr>
 800304a:	b108      	cbz	r0, 8003050 <_printf_i+0x1e8>
 800304c:	1b80      	subs	r0, r0, r6
 800304e:	6060      	str	r0, [r4, #4]
 8003050:	6863      	ldr	r3, [r4, #4]
 8003052:	6123      	str	r3, [r4, #16]
 8003054:	2300      	movs	r3, #0
 8003056:	f884 3043 	strb.w	r3, [r4, #67]	@ 0x43
 800305a:	e7aa      	b.n	8002fb2 <_printf_i+0x14a>
 800305c:	4632      	mov	r2, r6
 800305e:	4649      	mov	r1, r9
 8003060:	4640      	mov	r0, r8
 8003062:	6923      	ldr	r3, [r4, #16]
 8003064:	47d0      	blx	sl
 8003066:	3001      	adds	r0, #1
 8003068:	d0ad      	beq.n	8002fc6 <_printf_i+0x15e>
 800306a:	6823      	ldr	r3, [r4, #0]
 800306c:	079b      	lsls	r3, r3, #30
 800306e:	d413      	bmi.n	8003098 <_printf_i+0x230>
 8003070:	68e0      	ldr	r0, [r4, #12]
 8003072:	9b03      	ldr	r3, [sp, #12]
 8003074:	4298      	cmp	r0, r3
 8003076:	bfb8      	it	lt
 8003078:	4618      	movlt	r0, r3
 800307a:	e7a6      	b.n	8002fca <_printf_i+0x162>
 800307c:	2301      	movs	r3, #1
 800307e:	4632      	mov	r2, r6
 8003080:	4649      	mov	r1, r9
 8003082:	4640      	mov	r0, r8
 8003084:	47d0      	blx	sl
 8003086:	3001      	adds	r0, #1
 8003088:	d09d      	beq.n	8002fc6 <_printf_i+0x15e>
 800308a:	3501      	adds	r5, #1
 800308c:	68e3      	ldr	r3, [r4, #12]
 800308e:	9903      	ldr	r1, [sp, #12]
 8003090:	1a5b      	subs	r3, r3, r1
 8003092:	42ab      	cmp	r3, r5
 8003094:	dcf2      	bgt.n	800307c <_printf_i+0x214>
 8003096:	e7eb      	b.n	8003070 <_printf_i+0x208>
 8003098:	2500      	movs	r5, #0
 800309a:	f104 0619 	add.w	r6, r4, #25
 800309e:	e7f5      	b.n	800308c <_printf_i+0x224>
 80030a0:	08003b1e 	.word	0x08003b1e
 80030a4:	08003b2f 	.word	0x08003b2f

080030a8 <__sflush_r>:
 80030a8:	f9b1 200c 	ldrsh.w	r2, [r1, #12]
 80030ac:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 80030ae:	0716      	lsls	r6, r2, #28
 80030b0:	4605      	mov	r5, r0
 80030b2:	460c      	mov	r4, r1
 80030b4:	d454      	bmi.n	8003160 <__sflush_r+0xb8>
 80030b6:	684b      	ldr	r3, [r1, #4]
 80030b8:	2b00      	cmp	r3, #0
 80030ba:	dc02      	bgt.n	80030c2 <__sflush_r+0x1a>
 80030bc:	6c0b      	ldr	r3, [r1, #64]	@ 0x40
 80030be:	2b00      	cmp	r3, #0
 80030c0:	dd48      	ble.n	8003154 <__sflush_r+0xac>
 80030c2:	6ae6      	ldr	r6, [r4, #44]	@ 0x2c
 80030c4:	2e00      	cmp	r6, #0
 80030c6:	d045      	beq.n	8003154 <__sflush_r+0xac>
 80030c8:	2300      	movs	r3, #0
 80030ca:	f412 5280 	ands.w	r2, r2, #4096	@ 0x1000
 80030ce:	682f      	ldr	r7, [r5, #0]
 80030d0:	6a21      	ldr	r1, [r4, #32]
 80030d2:	602b      	str	r3, [r5, #0]
 80030d4:	d030      	beq.n	8003138 <__sflush_r+0x90>
 80030d6:	6d62      	ldr	r2, [r4, #84]	@ 0x54
 80030d8:	89a3      	ldrh	r3, [r4, #12]
 80030da:	0759      	lsls	r1, r3, #29
 80030dc:	d505      	bpl.n	80030ea <__sflush_r+0x42>
 80030de:	6863      	ldr	r3, [r4, #4]
 80030e0:	1ad2      	subs	r2, r2, r3
 80030e2:	6b63      	ldr	r3, [r4, #52]	@ 0x34
 80030e4:	b10b      	cbz	r3, 80030ea <__sflush_r+0x42>
 80030e6:	6c23      	ldr	r3, [r4, #64]	@ 0x40
 80030e8:	1ad2      	subs	r2, r2, r3
 80030ea:	2300      	movs	r3, #0
 80030ec:	4628      	mov	r0, r5
 80030ee:	6ae6      	ldr	r6, [r4, #44]	@ 0x2c
 80030f0:	6a21      	ldr	r1, [r4, #32]
 80030f2:	47b0      	blx	r6
 80030f4:	1c43      	adds	r3, r0, #1
 80030f6:	89a3      	ldrh	r3, [r4, #12]
 80030f8:	d106      	bne.n	8003108 <__sflush_r+0x60>
 80030fa:	6829      	ldr	r1, [r5, #0]
 80030fc:	291d      	cmp	r1, #29
 80030fe:	d82b      	bhi.n	8003158 <__sflush_r+0xb0>
 8003100:	4a28      	ldr	r2, [pc, #160]	@ (80031a4 <__sflush_r+0xfc>)
 8003102:	410a      	asrs	r2, r1
 8003104:	07d6      	lsls	r6, r2, #31
 8003106:	d427      	bmi.n	8003158 <__sflush_r+0xb0>
 8003108:	2200      	movs	r2, #0
 800310a:	6062      	str	r2, [r4, #4]
 800310c:	6922      	ldr	r2, [r4, #16]
 800310e:	04d9      	lsls	r1, r3, #19
 8003110:	6022      	str	r2, [r4, #0]
 8003112:	d504      	bpl.n	800311e <__sflush_r+0x76>
 8003114:	1c42      	adds	r2, r0, #1
 8003116:	d101      	bne.n	800311c <__sflush_r+0x74>
 8003118:	682b      	ldr	r3, [r5, #0]
 800311a:	b903      	cbnz	r3, 800311e <__sflush_r+0x76>
 800311c:	6560      	str	r0, [r4, #84]	@ 0x54
 800311e:	6b61      	ldr	r1, [r4, #52]	@ 0x34
 8003120:	602f      	str	r7, [r5, #0]
 8003122:	b1b9      	cbz	r1, 8003154 <__sflush_r+0xac>
 8003124:	f104 0344 	add.w	r3, r4, #68	@ 0x44
 8003128:	4299      	cmp	r1, r3
 800312a:	d002      	beq.n	8003132 <__sflush_r+0x8a>
 800312c:	4628      	mov	r0, r5
 800312e:	f7ff fbf3 	bl	8002918 <_free_r>
 8003132:	2300      	movs	r3, #0
 8003134:	6363      	str	r3, [r4, #52]	@ 0x34
 8003136:	e00d      	b.n	8003154 <__sflush_r+0xac>
 8003138:	2301      	movs	r3, #1
 800313a:	4628      	mov	r0, r5
 800313c:	47b0      	blx	r6
 800313e:	4602      	mov	r2, r0
 8003140:	1c50      	adds	r0, r2, #1
 8003142:	d1c9      	bne.n	80030d8 <__sflush_r+0x30>
 8003144:	682b      	ldr	r3, [r5, #0]
 8003146:	2b00      	cmp	r3, #0
 8003148:	d0c6      	beq.n	80030d8 <__sflush_r+0x30>
 800314a:	2b1d      	cmp	r3, #29
 800314c:	d001      	beq.n	8003152 <__sflush_r+0xaa>
 800314e:	2b16      	cmp	r3, #22
 8003150:	d11d      	bne.n	800318e <__sflush_r+0xe6>
 8003152:	602f      	str	r7, [r5, #0]
 8003154:	2000      	movs	r0, #0
 8003156:	e021      	b.n	800319c <__sflush_r+0xf4>
 8003158:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 800315c:	b21b      	sxth	r3, r3
 800315e:	e01a      	b.n	8003196 <__sflush_r+0xee>
 8003160:	690f      	ldr	r7, [r1, #16]
 8003162:	2f00      	cmp	r7, #0
 8003164:	d0f6      	beq.n	8003154 <__sflush_r+0xac>
 8003166:	0793      	lsls	r3, r2, #30
 8003168:	bf18      	it	ne
 800316a:	2300      	movne	r3, #0
 800316c:	680e      	ldr	r6, [r1, #0]
 800316e:	bf08      	it	eq
 8003170:	694b      	ldreq	r3, [r1, #20]
 8003172:	1bf6      	subs	r6, r6, r7
 8003174:	600f      	str	r7, [r1, #0]
 8003176:	608b      	str	r3, [r1, #8]
 8003178:	2e00      	cmp	r6, #0
 800317a:	ddeb      	ble.n	8003154 <__sflush_r+0xac>
 800317c:	4633      	mov	r3, r6
 800317e:	463a      	mov	r2, r7
 8003180:	4628      	mov	r0, r5
 8003182:	6a21      	ldr	r1, [r4, #32]
 8003184:	f8d4 c028 	ldr.w	ip, [r4, #40]	@ 0x28
 8003188:	47e0      	blx	ip
 800318a:	2800      	cmp	r0, #0
 800318c:	dc07      	bgt.n	800319e <__sflush_r+0xf6>
 800318e:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8003192:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 8003196:	f04f 30ff 	mov.w	r0, #4294967295
 800319a:	81a3      	strh	r3, [r4, #12]
 800319c:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
 800319e:	4407      	add	r7, r0
 80031a0:	1a36      	subs	r6, r6, r0
 80031a2:	e7e9      	b.n	8003178 <__sflush_r+0xd0>
 80031a4:	dfbffffe 	.word	0xdfbffffe

080031a8 <_fflush_r>:
 80031a8:	b538      	push	{r3, r4, r5, lr}
 80031aa:	690b      	ldr	r3, [r1, #16]
 80031ac:	4605      	mov	r5, r0
 80031ae:	460c      	mov	r4, r1
 80031b0:	b913      	cbnz	r3, 80031b8 <_fflush_r+0x10>
 80031b2:	2500      	movs	r5, #0
 80031b4:	4628      	mov	r0, r5
 80031b6:	bd38      	pop	{r3, r4, r5, pc}
 80031b8:	b118      	cbz	r0, 80031c2 <_fflush_r+0x1a>
 80031ba:	6a03      	ldr	r3, [r0, #32]
 80031bc:	b90b      	cbnz	r3, 80031c2 <_fflush_r+0x1a>
 80031be:	f7ff f9a3 	bl	8002508 <__sinit>
 80031c2:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 80031c6:	2b00      	cmp	r3, #0
 80031c8:	d0f3      	beq.n	80031b2 <_fflush_r+0xa>
 80031ca:	6e62      	ldr	r2, [r4, #100]	@ 0x64
 80031cc:	07d0      	lsls	r0, r2, #31
 80031ce:	d404      	bmi.n	80031da <_fflush_r+0x32>
 80031d0:	0599      	lsls	r1, r3, #22
 80031d2:	d402      	bmi.n	80031da <_fflush_r+0x32>
 80031d4:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 80031d6:	f7ff fb8e 	bl	80028f6 <__retarget_lock_acquire_recursive>
 80031da:	4628      	mov	r0, r5
 80031dc:	4621      	mov	r1, r4
 80031de:	f7ff ff63 	bl	80030a8 <__sflush_r>
 80031e2:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 80031e4:	4605      	mov	r5, r0
 80031e6:	07da      	lsls	r2, r3, #31
 80031e8:	d4e4      	bmi.n	80031b4 <_fflush_r+0xc>
 80031ea:	89a3      	ldrh	r3, [r4, #12]
 80031ec:	059b      	lsls	r3, r3, #22
 80031ee:	d4e1      	bmi.n	80031b4 <_fflush_r+0xc>
 80031f0:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 80031f2:	f7ff fb81 	bl	80028f8 <__retarget_lock_release_recursive>
 80031f6:	e7dd      	b.n	80031b4 <_fflush_r+0xc>

080031f8 <__swhatbuf_r>:
 80031f8:	b570      	push	{r4, r5, r6, lr}
 80031fa:	460c      	mov	r4, r1
 80031fc:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8003200:	4615      	mov	r5, r2
 8003202:	2900      	cmp	r1, #0
 8003204:	461e      	mov	r6, r3
 8003206:	b096      	sub	sp, #88	@ 0x58
 8003208:	da0c      	bge.n	8003224 <__swhatbuf_r+0x2c>
 800320a:	89a3      	ldrh	r3, [r4, #12]
 800320c:	2100      	movs	r1, #0
 800320e:	f013 0f80 	tst.w	r3, #128	@ 0x80
 8003212:	bf14      	ite	ne
 8003214:	2340      	movne	r3, #64	@ 0x40
 8003216:	f44f 6380 	moveq.w	r3, #1024	@ 0x400
 800321a:	2000      	movs	r0, #0
 800321c:	6031      	str	r1, [r6, #0]
 800321e:	602b      	str	r3, [r5, #0]
 8003220:	b016      	add	sp, #88	@ 0x58
 8003222:	bd70      	pop	{r4, r5, r6, pc}
 8003224:	466a      	mov	r2, sp
 8003226:	f000 f849 	bl	80032bc <_fstat_r>
 800322a:	2800      	cmp	r0, #0
 800322c:	dbed      	blt.n	800320a <__swhatbuf_r+0x12>
 800322e:	9901      	ldr	r1, [sp, #4]
 8003230:	f401 4170 	and.w	r1, r1, #61440	@ 0xf000
 8003234:	f5a1 5300 	sub.w	r3, r1, #8192	@ 0x2000
 8003238:	4259      	negs	r1, r3
 800323a:	4159      	adcs	r1, r3
 800323c:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 8003240:	e7eb      	b.n	800321a <__swhatbuf_r+0x22>

08003242 <__smakebuf_r>:
 8003242:	898b      	ldrh	r3, [r1, #12]
 8003244:	b5f7      	push	{r0, r1, r2, r4, r5, r6, r7, lr}
 8003246:	079d      	lsls	r5, r3, #30
 8003248:	4606      	mov	r6, r0
 800324a:	460c      	mov	r4, r1
 800324c:	d507      	bpl.n	800325e <__smakebuf_r+0x1c>
 800324e:	f104 0347 	add.w	r3, r4, #71	@ 0x47
 8003252:	6023      	str	r3, [r4, #0]
 8003254:	6123      	str	r3, [r4, #16]
 8003256:	2301      	movs	r3, #1
 8003258:	6163      	str	r3, [r4, #20]
 800325a:	b003      	add	sp, #12
 800325c:	bdf0      	pop	{r4, r5, r6, r7, pc}
 800325e:	466a      	mov	r2, sp
 8003260:	ab01      	add	r3, sp, #4
 8003262:	f7ff ffc9 	bl	80031f8 <__swhatbuf_r>
 8003266:	9f00      	ldr	r7, [sp, #0]
 8003268:	4605      	mov	r5, r0
 800326a:	4639      	mov	r1, r7
 800326c:	4630      	mov	r0, r6
 800326e:	f7ff fbbd 	bl	80029ec <_malloc_r>
 8003272:	b948      	cbnz	r0, 8003288 <__smakebuf_r+0x46>
 8003274:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8003278:	059a      	lsls	r2, r3, #22
 800327a:	d4ee      	bmi.n	800325a <__smakebuf_r+0x18>
 800327c:	f023 0303 	bic.w	r3, r3, #3
 8003280:	f043 0302 	orr.w	r3, r3, #2
 8003284:	81a3      	strh	r3, [r4, #12]
 8003286:	e7e2      	b.n	800324e <__smakebuf_r+0xc>
 8003288:	89a3      	ldrh	r3, [r4, #12]
 800328a:	e9c4 0704 	strd	r0, r7, [r4, #16]
 800328e:	f043 0380 	orr.w	r3, r3, #128	@ 0x80
 8003292:	81a3      	strh	r3, [r4, #12]
 8003294:	9b01      	ldr	r3, [sp, #4]
 8003296:	6020      	str	r0, [r4, #0]
 8003298:	b15b      	cbz	r3, 80032b2 <__smakebuf_r+0x70>
 800329a:	4630      	mov	r0, r6
 800329c:	f9b4 100e 	ldrsh.w	r1, [r4, #14]
 80032a0:	f000 f81e 	bl	80032e0 <_isatty_r>
 80032a4:	b128      	cbz	r0, 80032b2 <__smakebuf_r+0x70>
 80032a6:	89a3      	ldrh	r3, [r4, #12]
 80032a8:	f023 0303 	bic.w	r3, r3, #3
 80032ac:	f043 0301 	orr.w	r3, r3, #1
 80032b0:	81a3      	strh	r3, [r4, #12]
 80032b2:	89a3      	ldrh	r3, [r4, #12]
 80032b4:	431d      	orrs	r5, r3
 80032b6:	81a5      	strh	r5, [r4, #12]
 80032b8:	e7cf      	b.n	800325a <__smakebuf_r+0x18>
	...

080032bc <_fstat_r>:
 80032bc:	b538      	push	{r3, r4, r5, lr}
 80032be:	2300      	movs	r3, #0
 80032c0:	4d06      	ldr	r5, [pc, #24]	@ (80032dc <_fstat_r+0x20>)
 80032c2:	4604      	mov	r4, r0
 80032c4:	4608      	mov	r0, r1
 80032c6:	4611      	mov	r1, r2
 80032c8:	602b      	str	r3, [r5, #0]
 80032ca:	f7fd ff8c 	bl	80011e6 <_fstat>
 80032ce:	1c43      	adds	r3, r0, #1
 80032d0:	d102      	bne.n	80032d8 <_fstat_r+0x1c>
 80032d2:	682b      	ldr	r3, [r5, #0]
 80032d4:	b103      	cbz	r3, 80032d8 <_fstat_r+0x1c>
 80032d6:	6023      	str	r3, [r4, #0]
 80032d8:	bd38      	pop	{r3, r4, r5, pc}
 80032da:	bf00      	nop
 80032dc:	2000028c 	.word	0x2000028c

080032e0 <_isatty_r>:
 80032e0:	b538      	push	{r3, r4, r5, lr}
 80032e2:	2300      	movs	r3, #0
 80032e4:	4d05      	ldr	r5, [pc, #20]	@ (80032fc <_isatty_r+0x1c>)
 80032e6:	4604      	mov	r4, r0
 80032e8:	4608      	mov	r0, r1
 80032ea:	602b      	str	r3, [r5, #0]
 80032ec:	f7fd ff80 	bl	80011f0 <_isatty>
 80032f0:	1c43      	adds	r3, r0, #1
 80032f2:	d102      	bne.n	80032fa <_isatty_r+0x1a>
 80032f4:	682b      	ldr	r3, [r5, #0]
 80032f6:	b103      	cbz	r3, 80032fa <_isatty_r+0x1a>
 80032f8:	6023      	str	r3, [r4, #0]
 80032fa:	bd38      	pop	{r3, r4, r5, pc}
 80032fc:	2000028c 	.word	0x2000028c

08003300 <_sbrk_r>:
 8003300:	b538      	push	{r3, r4, r5, lr}
 8003302:	2300      	movs	r3, #0
 8003304:	4d05      	ldr	r5, [pc, #20]	@ (800331c <_sbrk_r+0x1c>)
 8003306:	4604      	mov	r4, r0
 8003308:	4608      	mov	r0, r1
 800330a:	602b      	str	r3, [r5, #0]
 800330c:	f7fd ff74 	bl	80011f8 <_sbrk>
 8003310:	1c43      	adds	r3, r0, #1
 8003312:	d102      	bne.n	800331a <_sbrk_r+0x1a>
 8003314:	682b      	ldr	r3, [r5, #0]
 8003316:	b103      	cbz	r3, 800331a <_sbrk_r+0x1a>
 8003318:	6023      	str	r3, [r4, #0]
 800331a:	bd38      	pop	{r3, r4, r5, pc}
 800331c:	2000028c 	.word	0x2000028c

08003320 <memchr>:
 8003320:	4603      	mov	r3, r0
 8003322:	b510      	push	{r4, lr}
 8003324:	b2c9      	uxtb	r1, r1
 8003326:	4402      	add	r2, r0
 8003328:	4293      	cmp	r3, r2
 800332a:	4618      	mov	r0, r3
 800332c:	d101      	bne.n	8003332 <memchr+0x12>
 800332e:	2000      	movs	r0, #0
 8003330:	e003      	b.n	800333a <memchr+0x1a>
 8003332:	7804      	ldrb	r4, [r0, #0]
 8003334:	3301      	adds	r3, #1
 8003336:	428c      	cmp	r4, r1
 8003338:	d1f6      	bne.n	8003328 <memchr+0x8>
 800333a:	bd10      	pop	{r4, pc}

0800333c <_init>:
 800333c:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 800333e:	bf00      	nop
 8003340:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8003342:	bc08      	pop	{r3}
 8003344:	469e      	mov	lr, r3
 8003346:	4770      	bx	lr

08003348 <_fini>:
 8003348:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 800334a:	bf00      	nop
 800334c:	bcf8      	pop	{r3, r4, r5, r6, r7}
 800334e:	bc08      	pop	{r3}
 8003350:	469e      	mov	lr, r3
 8003352:	4770      	bx	lr
