
Bootloader.elf:     file format elf32-littlearm

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .isr_vector   0000010c  08000000  08000000  00001000  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  1 .text         000029f4  0800010c  0800010c  0000110c  2**2
                  CONTENTS, ALL<PERSON>, LOAD, READONLY, CODE
  2 .rodata       000007ec  08002b00  08002b00  00003b00  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  3 .ARM.extab    00000000  080032ec  080032ec  0000506c  2**0
                  CONTENTS
  4 .ARM          00000000  080032ec  080032ec  0000506c  2**0
                  CONTENTS
  5 .preinit_array 00000000  080032ec  080032ec  0000506c  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  6 .init_array   00000004  080032ec  080032ec  000042ec  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  7 .fini_array   00000004  080032f0  080032f0  000042f0  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .data         0000006c  20000000  080032f4  00005000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  9 .bss          0000022c  20000070  08003360  00005070  2**3
                  ALLOC
 10 ._user_heap_stack 00000604  2000029c  08003360  0000529c  2**0
                  ALLOC
 11 .ARM.attributes 00000029  00000000  00000000  0000506c  2**0
                  CONTENTS, READONLY
 12 .debug_info   0000a12f  00000000  00000000  00005095  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 13 .debug_abbrev 00001c18  00000000  00000000  0000f1c4  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 14 .debug_loclists 0000447d  00000000  00000000  00010ddc  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 15 .debug_aranges 000007d8  00000000  00000000  00015260  2**3
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 16 .debug_rnglists 0000062f  00000000  00000000  00015a38  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 17 .debug_macro  0000238c  00000000  00000000  00016067  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 18 .debug_line   0000c871  00000000  00000000  000183f3  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 19 .debug_str    000843d7  00000000  00000000  00024c64  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 20 .comment      00000043  00000000  00000000  000a903b  2**0
                  CONTENTS, READONLY
 21 .debug_frame  00001bf0  00000000  00000000  000a9080  2**2
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 22 .debug_line_str 00000096  00000000  00000000  000aac70  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS

Disassembly of section .text:

0800010c <__do_global_dtors_aux>:
 800010c:	b510      	push	{r4, lr}
 800010e:	4c05      	ldr	r4, [pc, #20]	@ (8000124 <__do_global_dtors_aux+0x18>)
 8000110:	7823      	ldrb	r3, [r4, #0]
 8000112:	b933      	cbnz	r3, 8000122 <__do_global_dtors_aux+0x16>
 8000114:	4b04      	ldr	r3, [pc, #16]	@ (8000128 <__do_global_dtors_aux+0x1c>)
 8000116:	b113      	cbz	r3, 800011e <__do_global_dtors_aux+0x12>
 8000118:	4804      	ldr	r0, [pc, #16]	@ (800012c <__do_global_dtors_aux+0x20>)
 800011a:	f3af 8000 	nop.w
 800011e:	2301      	movs	r3, #1
 8000120:	7023      	strb	r3, [r4, #0]
 8000122:	bd10      	pop	{r4, pc}
 8000124:	20000070 	.word	0x20000070
 8000128:	00000000 	.word	0x00000000
 800012c:	08002ae8 	.word	0x08002ae8

08000130 <frame_dummy>:
 8000130:	b508      	push	{r3, lr}
 8000132:	4b03      	ldr	r3, [pc, #12]	@ (8000140 <frame_dummy+0x10>)
 8000134:	b11b      	cbz	r3, 800013e <frame_dummy+0xe>
 8000136:	4903      	ldr	r1, [pc, #12]	@ (8000144 <frame_dummy+0x14>)
 8000138:	4803      	ldr	r0, [pc, #12]	@ (8000148 <frame_dummy+0x18>)
 800013a:	f3af 8000 	nop.w
 800013e:	bd08      	pop	{r3, pc}
 8000140:	00000000 	.word	0x00000000
 8000144:	20000074 	.word	0x20000074
 8000148:	08002ae8 	.word	0x08002ae8

0800014c <MX_GPIO_Init>:
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
 800014c:	b530      	push	{r4, r5, lr}
 800014e:	b087      	sub	sp, #28
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 8000150:	2400      	movs	r4, #0
 8000152:	9402      	str	r4, [sp, #8]
 8000154:	9403      	str	r4, [sp, #12]
 8000156:	9404      	str	r4, [sp, #16]
 8000158:	9405      	str	r4, [sp, #20]
/* USER CODE BEGIN MX_GPIO_Init_1 */
/* USER CODE END MX_GPIO_Init_1 */

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOB_CLK_ENABLE();
 800015a:	4b14      	ldr	r3, [pc, #80]	@ (80001ac <MX_GPIO_Init+0x60>)
 800015c:	699a      	ldr	r2, [r3, #24]
 800015e:	f042 0208 	orr.w	r2, r2, #8
 8000162:	619a      	str	r2, [r3, #24]
 8000164:	699a      	ldr	r2, [r3, #24]
 8000166:	f002 0208 	and.w	r2, r2, #8
 800016a:	9200      	str	r2, [sp, #0]
 800016c:	9a00      	ldr	r2, [sp, #0]
  __HAL_RCC_GPIOA_CLK_ENABLE();
 800016e:	699a      	ldr	r2, [r3, #24]
 8000170:	f042 0204 	orr.w	r2, r2, #4
 8000174:	619a      	str	r2, [r3, #24]
 8000176:	699b      	ldr	r3, [r3, #24]
 8000178:	f003 0304 	and.w	r3, r3, #4
 800017c:	9301      	str	r3, [sp, #4]
 800017e:	9b01      	ldr	r3, [sp, #4]

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET);
 8000180:	4d0b      	ldr	r5, [pc, #44]	@ (80001b0 <MX_GPIO_Init+0x64>)
 8000182:	4622      	mov	r2, r4
 8000184:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000188:	4628      	mov	r0, r5
 800018a:	f000 ff91 	bl	80010b0 <HAL_GPIO_WritePin>

  /*Configure GPIO pin : PB13 */
  GPIO_InitStruct.Pin = GPIO_PIN_13;
 800018e:	f44f 5300 	mov.w	r3, #8192	@ 0x2000
 8000192:	9302      	str	r3, [sp, #8]
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 8000194:	2301      	movs	r3, #1
 8000196:	9303      	str	r3, [sp, #12]
  GPIO_InitStruct.Pull = GPIO_NOPULL;
 8000198:	9404      	str	r4, [sp, #16]
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 800019a:	2302      	movs	r3, #2
 800019c:	9305      	str	r3, [sp, #20]
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 800019e:	a902      	add	r1, sp, #8
 80001a0:	4628      	mov	r0, r5
 80001a2:	f000 fe77 	bl	8000e94 <HAL_GPIO_Init>

/* USER CODE BEGIN MX_GPIO_Init_2 */
/* USER CODE END MX_GPIO_Init_2 */
}
 80001a6:	b007      	add	sp, #28
 80001a8:	bd30      	pop	{r4, r5, pc}
 80001aa:	bf00      	nop
 80001ac:	40021000 	.word	0x40021000
 80001b0:	40010c00 	.word	0x40010c00

080001b4 <goto_application>:
    }
  }
}

static void goto_application( void )
{
 80001b4:	b510      	push	{r4, lr}
	printf("Gonna Jump to Application...\n");
 80001b6:	480d      	ldr	r0, [pc, #52]	@ (80001ec <goto_application+0x38>)
 80001b8:	f001 fe1a 	bl	8001df0 <puts>
	void (*app_reset_handler)(void) = (void*)(*((volatile uint32_t*)(ETX_APP_START_ADDRESS + 4U)));
 80001bc:	4b0c      	ldr	r3, [pc, #48]	@ (80001f0 <goto_application+0x3c>)
 80001be:	f8d3 4404 	ldr.w	r4, [r3, #1028]	@ 0x404

	if( app_reset_handler == (void*)0xFFFFFFFF )
 80001c2:	f1b4 3fff 	cmp.w	r4, #4294967295
 80001c6:	d00c      	beq.n	80001e2 <goto_application+0x2e>
	{
	  printf("Invalid Application... HALT!!!\r\n");
	  while(1);
	}

	__set_MSP(*(volatile uint32_t*) ETX_APP_START_ADDRESS);
 80001c8:	4b09      	ldr	r3, [pc, #36]	@ (80001f0 <goto_application+0x3c>)
 80001ca:	f8d3 3400 	ldr.w	r3, [r3, #1024]	@ 0x400
  \details Assigns the given value to the Main Stack Pointer (MSP).
  \param [in]    topOfMainStack  Main Stack Pointer value to set
 */
__STATIC_FORCEINLINE void __set_MSP(uint32_t topOfMainStack)
{
  __ASM volatile ("MSR msp, %0" : : "r" (topOfMainStack) : );
 80001ce:	f383 8808 	msr	MSP, r3

	// Turn OFF the Led to tell the user that Bootloader is not running
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET );
 80001d2:	2200      	movs	r2, #0
 80001d4:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 80001d8:	4806      	ldr	r0, [pc, #24]	@ (80001f4 <goto_application+0x40>)
 80001da:	f000 ff69 	bl	80010b0 <HAL_GPIO_WritePin>

	app_reset_handler();    //call the app reset handler
 80001de:	47a0      	blx	r4
}
 80001e0:	bd10      	pop	{r4, pc}
	  printf("Invalid Application... HALT!!!\r\n");
 80001e2:	4805      	ldr	r0, [pc, #20]	@ (80001f8 <goto_application+0x44>)
 80001e4:	f001 fe04 	bl	8001df0 <puts>
	  while(1);
 80001e8:	e7fe      	b.n	80001e8 <goto_application+0x34>
 80001ea:	bf00      	nop
 80001ec:	08002b04 	.word	0x08002b04
 80001f0:	08004000 	.word	0x08004000
 80001f4:	40010c00 	.word	0x40010c00
 80001f8:	08002b24 	.word	0x08002b24

080001fc <UART_Write_Loop>:
{
 80001fc:	b530      	push	{r4, r5, lr}
 80001fe:	b083      	sub	sp, #12
  printf("\n Press 'o' to start Firmware Update...\r\n");
 8000200:	481b      	ldr	r0, [pc, #108]	@ (8000270 <UART_Write_Loop+0x74>)
 8000202:	f001 fdf5 	bl	8001df0 <puts>
  char tx = '.';
 8000206:	232e      	movs	r3, #46	@ 0x2e
 8000208:	f88d 3007 	strb.w	r3, [sp, #7]
  char rx = '0';
 800020c:	2330      	movs	r3, #48	@ 0x30
 800020e:	f88d 3006 	strb.w	r3, [sp, #6]
  int count = 0;
 8000212:	2500      	movs	r5, #0
 8000214:	e005      	b.n	8000222 <UART_Write_Loop+0x26>
    if( count == 250 )  // Changed from 100 to 250 to make it 5 seconds (250 * 20ms = 5000ms = 5 seconds)
 8000216:	2dfa      	cmp	r5, #250	@ 0xfa
 8000218:	d024      	beq.n	8000264 <UART_Write_Loop+0x68>
    count++;
 800021a:	3501      	adds	r5, #1
    HAL_Delay(20);              //20ms delay
 800021c:	2014      	movs	r0, #20
 800021e:	f000 fc57 	bl	8000ad0 <HAL_Delay>
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_13);
 8000222:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000226:	4813      	ldr	r0, [pc, #76]	@ (8000274 <UART_Write_Loop+0x78>)
 8000228:	f000 ff48 	bl	80010bc <HAL_GPIO_TogglePin>
    HAL_UART_Transmit(&huart3, (uint8_t *)&tx, 1, HAL_MAX_DELAY);
 800022c:	4c12      	ldr	r4, [pc, #72]	@ (8000278 <UART_Write_Loop+0x7c>)
 800022e:	f04f 33ff 	mov.w	r3, #4294967295
 8000232:	2201      	movs	r2, #1
 8000234:	f10d 0107 	add.w	r1, sp, #7
 8000238:	4620      	mov	r0, r4
 800023a:	f001 fb58 	bl	80018ee <HAL_UART_Transmit>
    ex = HAL_UART_Receive(&huart3, (uint8_t *)&rx, 1, 20);  // Changed from 10ms to 20ms to give Arduino more time to respond
 800023e:	2314      	movs	r3, #20
 8000240:	2201      	movs	r2, #1
 8000242:	f10d 0106 	add.w	r1, sp, #6
 8000246:	4620      	mov	r0, r4
 8000248:	f001 fbb7 	bl	80019ba <HAL_UART_Receive>
    if( ( ex == HAL_OK ) && ( rx == 'o' ) )
 800024c:	2800      	cmp	r0, #0
 800024e:	d1e2      	bne.n	8000216 <UART_Write_Loop+0x1a>
 8000250:	f89d 3006 	ldrb.w	r3, [sp, #6]
 8000254:	2b6f      	cmp	r3, #111	@ 0x6f
 8000256:	d1de      	bne.n	8000216 <UART_Write_Loop+0x1a>
      printf("Firmware Update Started\r\n");
 8000258:	4808      	ldr	r0, [pc, #32]	@ (800027c <UART_Write_Loop+0x80>)
 800025a:	f001 fdc9 	bl	8001df0 <puts>
      ret = 1;
 800025e:	2001      	movs	r0, #1
}
 8000260:	b003      	add	sp, #12
 8000262:	bd30      	pop	{r4, r5, pc}
      printf("No Data Received for Firmware Update\r\n");
 8000264:	4806      	ldr	r0, [pc, #24]	@ (8000280 <UART_Write_Loop+0x84>)
 8000266:	f001 fdc3 	bl	8001df0 <puts>
  int ret = 0;
 800026a:	2000      	movs	r0, #0
      break;
 800026c:	e7f8      	b.n	8000260 <UART_Write_Loop+0x64>
 800026e:	bf00      	nop
 8000270:	08002b44 	.word	0x08002b44
 8000274:	40010c00 	.word	0x40010c00
 8000278:	20000094 	.word	0x20000094
 800027c:	08002b70 	.word	0x08002b70
 8000280:	08002b8c 	.word	0x08002b8c

08000284 <receive_ascii_number>:
{
 8000284:	b510      	push	{r4, lr}
 8000286:	b086      	sub	sp, #24
  char buffer[16] = {0};
 8000288:	2400      	movs	r4, #0
 800028a:	9402      	str	r4, [sp, #8]
 800028c:	9403      	str	r4, [sp, #12]
 800028e:	9404      	str	r4, [sp, #16]
 8000290:	9405      	str	r4, [sp, #20]
  printf("Enter number: ");
 8000292:	4825      	ldr	r0, [pc, #148]	@ (8000328 <receive_ascii_number+0xa4>)
 8000294:	f001 fd44 	bl	8001d20 <iprintf>
  while(index < 15)
 8000298:	e015      	b.n	80002c6 <receive_ascii_number+0x42>
      printf("Timeout waiting for input\r\n");
 800029a:	4824      	ldr	r0, [pc, #144]	@ (800032c <receive_ascii_number+0xa8>)
 800029c:	f001 fda8 	bl	8001df0 <puts>
      return 0;
 80002a0:	2000      	movs	r0, #0
 80002a2:	e00c      	b.n	80002be <receive_ascii_number+0x3a>
      printf("\r\n");
 80002a4:	4822      	ldr	r0, [pc, #136]	@ (8000330 <receive_ascii_number+0xac>)
 80002a6:	f001 fda3 	bl	8001df0 <puts>
  buffer[index] = '\0';
 80002aa:	f104 0318 	add.w	r3, r4, #24
 80002ae:	eb0d 0403 	add.w	r4, sp, r3
 80002b2:	2300      	movs	r3, #0
 80002b4:	f804 3c10 	strb.w	r3, [r4, #-16]
  return atoi(buffer);
 80002b8:	a802      	add	r0, sp, #8
 80002ba:	f001 fbe7 	bl	8001a8c <atoi>
}
 80002be:	b006      	add	sp, #24
 80002c0:	bd10      	pop	{r4, pc}
    else if(received_char == '\b' && index > 0) // Backspace
 80002c2:	2b08      	cmp	r3, #8
 80002c4:	d027      	beq.n	8000316 <receive_ascii_number+0x92>
  while(index < 15)
 80002c6:	2c0e      	cmp	r4, #14
 80002c8:	d8ef      	bhi.n	80002aa <receive_ascii_number+0x26>
    status = HAL_UART_Receive(&huart3, &received_char, 1, 10000); // 10 second timeout
 80002ca:	f242 7310 	movw	r3, #10000	@ 0x2710
 80002ce:	2201      	movs	r2, #1
 80002d0:	f10d 0107 	add.w	r1, sp, #7
 80002d4:	4817      	ldr	r0, [pc, #92]	@ (8000334 <receive_ascii_number+0xb0>)
 80002d6:	f001 fb70 	bl	80019ba <HAL_UART_Receive>
    if(status != HAL_OK)
 80002da:	2800      	cmp	r0, #0
 80002dc:	d1dd      	bne.n	800029a <receive_ascii_number+0x16>
    HAL_UART_Transmit(&huart3, &received_char, 1, HAL_MAX_DELAY);
 80002de:	f04f 33ff 	mov.w	r3, #4294967295
 80002e2:	2201      	movs	r2, #1
 80002e4:	f10d 0107 	add.w	r1, sp, #7
 80002e8:	4812      	ldr	r0, [pc, #72]	@ (8000334 <receive_ascii_number+0xb0>)
 80002ea:	f001 fb00 	bl	80018ee <HAL_UART_Transmit>
    if(received_char == '\r' || received_char == '\n')
 80002ee:	f89d 3007 	ldrb.w	r3, [sp, #7]
 80002f2:	2b0d      	cmp	r3, #13
 80002f4:	d0d6      	beq.n	80002a4 <receive_ascii_number+0x20>
 80002f6:	2b0a      	cmp	r3, #10
 80002f8:	d0d4      	beq.n	80002a4 <receive_ascii_number+0x20>
    else if(received_char >= '0' && received_char <= '9')
 80002fa:	f1a3 0230 	sub.w	r2, r3, #48	@ 0x30
 80002fe:	b2d2      	uxtb	r2, r2
 8000300:	2a09      	cmp	r2, #9
 8000302:	d8de      	bhi.n	80002c2 <receive_ascii_number+0x3e>
      buffer[index++] = received_char;
 8000304:	1c62      	adds	r2, r4, #1
 8000306:	f104 0118 	add.w	r1, r4, #24
 800030a:	eb0d 0401 	add.w	r4, sp, r1
 800030e:	f804 3c10 	strb.w	r3, [r4, #-16]
 8000312:	b2d4      	uxtb	r4, r2
 8000314:	e7d7      	b.n	80002c6 <receive_ascii_number+0x42>
    else if(received_char == '\b' && index > 0) // Backspace
 8000316:	2c00      	cmp	r4, #0
 8000318:	d0d5      	beq.n	80002c6 <receive_ascii_number+0x42>
      index--;
 800031a:	3c01      	subs	r4, #1
 800031c:	b2e4      	uxtb	r4, r4
      printf(" \b"); // Erase character on terminal
 800031e:	4806      	ldr	r0, [pc, #24]	@ (8000338 <receive_ascii_number+0xb4>)
 8000320:	f001 fcfe 	bl	8001d20 <iprintf>
 8000324:	e7cf      	b.n	80002c6 <receive_ascii_number+0x42>
 8000326:	bf00      	nop
 8000328:	08002bb4 	.word	0x08002bb4
 800032c:	08002bc4 	.word	0x08002bc4
 8000330:	08002c54 	.word	0x08002c54
 8000334:	20000094 	.word	0x20000094
 8000338:	08002be0 	.word	0x08002be0

0800033c <auto_detect_firmware_size>:
{
 800033c:	b5f0      	push	{r4, r5, r6, r7, lr}
 800033e:	b083      	sub	sp, #12
  printf("Auto-detecting firmware size...\r\n");
 8000340:	4826      	ldr	r0, [pc, #152]	@ (80003dc <auto_detect_firmware_size+0xa0>)
 8000342:	f001 fd55 	bl	8001df0 <puts>
  printf("Send firmware data continuously (end with 'END' or timeout after 30 seconds)\r\n");
 8000346:	4826      	ldr	r0, [pc, #152]	@ (80003e0 <auto_detect_firmware_size+0xa4>)
 8000348:	f001 fd52 	bl	8001df0 <puts>
  uint32_t start_time = HAL_GetTick();
 800034c:	f000 fbba 	bl	8000ac4 <HAL_GetTick>
 8000350:	4607      	mov	r7, r0
  uint8_t end_sequence[3] = {0};
 8000352:	4b24      	ldr	r3, [pc, #144]	@ (80003e4 <auto_detect_firmware_size+0xa8>)
 8000354:	881b      	ldrh	r3, [r3, #0]
 8000356:	f8ad 3004 	strh.w	r3, [sp, #4]
 800035a:	2500      	movs	r5, #0
 800035c:	f88d 5006 	strb.w	r5, [sp, #6]
  uint8_t end_index = 0;
 8000360:	462e      	mov	r6, r5
 8000362:	e00d      	b.n	8000380 <auto_detect_firmware_size+0x44>
      if(received_byte == 'E' && end_index == 0) end_index = 1;
 8000364:	b9d6      	cbnz	r6, 800039c <auto_detect_firmware_size+0x60>
 8000366:	2601      	movs	r6, #1
 8000368:	e006      	b.n	8000378 <auto_detect_firmware_size+0x3c>
      else if(received_byte == 'N' && end_index == 1) end_index = 2;
 800036a:	2e01      	cmp	r6, #1
 800036c:	d118      	bne.n	80003a0 <auto_detect_firmware_size+0x64>
 800036e:	2602      	movs	r6, #2
 8000370:	e002      	b.n	8000378 <auto_detect_firmware_size+0x3c>
      else if(received_byte == 'D' && end_index == 2)
 8000372:	2e02      	cmp	r6, #2
 8000374:	d018      	beq.n	80003a8 <auto_detect_firmware_size+0x6c>
      else end_index = 0;
 8000376:	4616      	mov	r6, r2
      if(byte_count % 1024 == 0)
 8000378:	f3c4 0309 	ubfx	r3, r4, #0, #10
 800037c:	b1d3      	cbz	r3, 80003b4 <auto_detect_firmware_size+0x78>
      byte_count++;
 800037e:	4625      	mov	r5, r4
    status = HAL_UART_Receive(&huart3, &received_byte, 1, 100); // 100ms timeout per byte
 8000380:	2364      	movs	r3, #100	@ 0x64
 8000382:	2201      	movs	r2, #1
 8000384:	f10d 0107 	add.w	r1, sp, #7
 8000388:	4817      	ldr	r0, [pc, #92]	@ (80003e8 <auto_detect_firmware_size+0xac>)
 800038a:	f001 fb16 	bl	80019ba <HAL_UART_Receive>
    if(status == HAL_OK)
 800038e:	4602      	mov	r2, r0
 8000390:	b9b0      	cbnz	r0, 80003c0 <auto_detect_firmware_size+0x84>
      byte_count++;
 8000392:	1c6c      	adds	r4, r5, #1
      if(received_byte == 'E' && end_index == 0) end_index = 1;
 8000394:	f89d 3007 	ldrb.w	r3, [sp, #7]
 8000398:	2b45      	cmp	r3, #69	@ 0x45
 800039a:	d0e3      	beq.n	8000364 <auto_detect_firmware_size+0x28>
      else if(received_byte == 'N' && end_index == 1) end_index = 2;
 800039c:	2b4e      	cmp	r3, #78	@ 0x4e
 800039e:	d0e4      	beq.n	800036a <auto_detect_firmware_size+0x2e>
      else if(received_byte == 'D' && end_index == 2)
 80003a0:	2b44      	cmp	r3, #68	@ 0x44
 80003a2:	d0e6      	beq.n	8000372 <auto_detect_firmware_size+0x36>
      else end_index = 0;
 80003a4:	4616      	mov	r6, r2
 80003a6:	e7e7      	b.n	8000378 <auto_detect_firmware_size+0x3c>
        byte_count -= 3; // Don't count the "END" sequence
 80003a8:	3d02      	subs	r5, #2
        printf("End sequence detected. Firmware size: %lu bytes\r\n", byte_count);
 80003aa:	4629      	mov	r1, r5
 80003ac:	480f      	ldr	r0, [pc, #60]	@ (80003ec <auto_detect_firmware_size+0xb0>)
 80003ae:	f001 fcb7 	bl	8001d20 <iprintf>
        return byte_count;
 80003b2:	e010      	b.n	80003d6 <auto_detect_firmware_size+0x9a>
        printf("Received %lu bytes...\r\n", byte_count);
 80003b4:	4621      	mov	r1, r4
 80003b6:	480e      	ldr	r0, [pc, #56]	@ (80003f0 <auto_detect_firmware_size+0xb4>)
 80003b8:	f001 fcb2 	bl	8001d20 <iprintf>
      byte_count++;
 80003bc:	4625      	mov	r5, r4
 80003be:	e7df      	b.n	8000380 <auto_detect_firmware_size+0x44>
      if(HAL_GetTick() - start_time > 30000)
 80003c0:	f000 fb80 	bl	8000ac4 <HAL_GetTick>
 80003c4:	1bc0      	subs	r0, r0, r7
 80003c6:	f247 5330 	movw	r3, #30000	@ 0x7530
 80003ca:	4298      	cmp	r0, r3
 80003cc:	d9d8      	bls.n	8000380 <auto_detect_firmware_size+0x44>
        printf("Auto-detection timeout. Detected size: %lu bytes\r\n", byte_count);
 80003ce:	4629      	mov	r1, r5
 80003d0:	4808      	ldr	r0, [pc, #32]	@ (80003f4 <auto_detect_firmware_size+0xb8>)
 80003d2:	f001 fca5 	bl	8001d20 <iprintf>
}
 80003d6:	4628      	mov	r0, r5
 80003d8:	b003      	add	sp, #12
 80003da:	bdf0      	pop	{r4, r5, r6, r7, pc}
 80003dc:	08002be4 	.word	0x08002be4
 80003e0:	08002c08 	.word	0x08002c08
 80003e4:	08002b00 	.word	0x08002b00
 80003e8:	20000094 	.word	0x20000094
 80003ec:	08002c58 	.word	0x08002c58
 80003f0:	08002c8c 	.word	0x08002c8c
 80003f4:	08002ca4 	.word	0x08002ca4

080003f8 <write_data_to_flash_app>:
{
 80003f8:	b5f0      	push	{r4, r5, r6, r7, lr}
 80003fa:	b087      	sub	sp, #28
 80003fc:	4605      	mov	r5, r0
 80003fe:	460e      	mov	r6, r1
 8000400:	4614      	mov	r4, r2
    ret = HAL_FLASH_Unlock();
 8000402:	f000 fc29 	bl	8000c58 <HAL_FLASH_Unlock>
    if( ret != HAL_OK )
 8000406:	4607      	mov	r7, r0
 8000408:	2800      	cmp	r0, #0
 800040a:	d134      	bne.n	8000476 <write_data_to_flash_app+0x7e>
    if( is_first_block )
 800040c:	b9e4      	cbnz	r4, 8000448 <write_data_to_flash_app+0x50>
{
 800040e:	2400      	movs	r4, #0
    for(int i = 0; i < data_len/2; i++)
 8000410:	ebb4 0f56 	cmp.w	r4, r6, lsr #1
 8000414:	da2e      	bge.n	8000474 <write_data_to_flash_app+0x7c>
      uint16_t halfword_data = data[i * 2] | (data[i * 2 + 1] << 8);
 8000416:	f815 3014 	ldrb.w	r3, [r5, r4, lsl #1]
 800041a:	eb05 0244 	add.w	r2, r5, r4, lsl #1
 800041e:	7852      	ldrb	r2, [r2, #1]
      ret = HAL_FLASH_Program( FLASH_TYPEPROGRAM_HALFWORD,
 8000420:	4918      	ldr	r1, [pc, #96]	@ (8000484 <write_data_to_flash_app+0x8c>)
 8000422:	6809      	ldr	r1, [r1, #0]
 8000424:	ea43 2202 	orr.w	r2, r3, r2, lsl #8
 8000428:	2300      	movs	r3, #0
 800042a:	f101 6100 	add.w	r1, r1, #134217728	@ 0x8000000
 800042e:	f501 4188 	add.w	r1, r1, #17408	@ 0x4400
 8000432:	2001      	movs	r0, #1
 8000434:	f000 fc68 	bl	8000d08 <HAL_FLASH_Program>
      if( ret == HAL_OK )
 8000438:	4607      	mov	r7, r0
 800043a:	b9c0      	cbnz	r0, 800046e <write_data_to_flash_app+0x76>
        application_write_idx += 2;
 800043c:	4a11      	ldr	r2, [pc, #68]	@ (8000484 <write_data_to_flash_app+0x8c>)
 800043e:	6813      	ldr	r3, [r2, #0]
 8000440:	3302      	adds	r3, #2
 8000442:	6013      	str	r3, [r2, #0]
    for(int i = 0; i < data_len/2; i++)
 8000444:	3401      	adds	r4, #1
 8000446:	e7e3      	b.n	8000410 <write_data_to_flash_app+0x18>
      printf("Erasing the Flash memory...\r\n");
 8000448:	480f      	ldr	r0, [pc, #60]	@ (8000488 <write_data_to_flash_app+0x90>)
 800044a:	f001 fcd1 	bl	8001df0 <puts>
      EraseInitStruct.TypeErase     = FLASH_TYPEERASE_PAGES;
 800044e:	2300      	movs	r3, #0
 8000450:	9302      	str	r3, [sp, #8]
      EraseInitStruct.PageAddress   = ETX_APP_START_ADDRESS;
 8000452:	4b0e      	ldr	r3, [pc, #56]	@ (800048c <write_data_to_flash_app+0x94>)
 8000454:	9304      	str	r3, [sp, #16]
      EraseInitStruct.NbPages       = 47;                     //47 Pages
 8000456:	232f      	movs	r3, #47	@ 0x2f
 8000458:	9305      	str	r3, [sp, #20]
      ret = HAL_FLASHEx_Erase( &EraseInitStruct, &SectorError );
 800045a:	a901      	add	r1, sp, #4
 800045c:	a802      	add	r0, sp, #8
 800045e:	f000 fcc7 	bl	8000df0 <HAL_FLASHEx_Erase>
      if( ret != HAL_OK )
 8000462:	4607      	mov	r7, r0
 8000464:	b938      	cbnz	r0, 8000476 <write_data_to_flash_app+0x7e>
      application_write_idx = 0;
 8000466:	4b07      	ldr	r3, [pc, #28]	@ (8000484 <write_data_to_flash_app+0x8c>)
 8000468:	2200      	movs	r2, #0
 800046a:	601a      	str	r2, [r3, #0]
 800046c:	e7cf      	b.n	800040e <write_data_to_flash_app+0x16>
        printf("Flash Write Error...HALT!!!\r\n");
 800046e:	4808      	ldr	r0, [pc, #32]	@ (8000490 <write_data_to_flash_app+0x98>)
 8000470:	f001 fcbe 	bl	8001df0 <puts>
    if( ret != HAL_OK )
 8000474:	b117      	cbz	r7, 800047c <write_data_to_flash_app+0x84>
}
 8000476:	4638      	mov	r0, r7
 8000478:	b007      	add	sp, #28
 800047a:	bdf0      	pop	{r4, r5, r6, r7, pc}
    ret = HAL_FLASH_Lock();
 800047c:	f000 fc06 	bl	8000c8c <HAL_FLASH_Lock>
 8000480:	4607      	mov	r7, r0
    if( ret != HAL_OK )
 8000482:	e7f8      	b.n	8000476 <write_data_to_flash_app+0x7e>
 8000484:	2000008c 	.word	0x2000008c
 8000488:	08002cd8 	.word	0x08002cd8
 800048c:	08004400 	.word	0x08004400
 8000490:	08002cf8 	.word	0x08002cf8

08000494 <Firmware_Update>:
{
 8000494:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 8000498:	f6ad 0d18 	subw	sp, sp, #2072	@ 0x818
  uint8_t block[MAX_BLOCK_SIZE] = { 0 };
 800049c:	2400      	movs	r4, #0
 800049e:	f8cd 4418 	str.w	r4, [sp, #1048]	@ 0x418
 80004a2:	f44f 757f 	mov.w	r5, #1020	@ 0x3fc
 80004a6:	462a      	mov	r2, r5
 80004a8:	4621      	mov	r1, r4
 80004aa:	f20d 401c 	addw	r0, sp, #1052	@ 0x41c
 80004ae:	f001 fd7f 	bl	8001fb0 <memset>
  uint8_t backup_block[MAX_BLOCK_SIZE] = { 0 }; // For rollback
 80004b2:	9406      	str	r4, [sp, #24]
 80004b4:	462a      	mov	r2, r5
 80004b6:	4621      	mov	r1, r4
 80004b8:	a807      	add	r0, sp, #28
 80004ba:	f001 fd79 	bl	8001fb0 <memset>
    if( UART_Write_Loop() != 0 )
 80004be:	f7ff fe9d 	bl	80001fc <UART_Write_Loop>
 80004c2:	b370      	cbz	r0, 8000522 <Firmware_Update+0x8e>
      printf("=== Enhanced Firmware Update Started ===\r\n");
 80004c4:	4876      	ldr	r0, [pc, #472]	@ (80006a0 <Firmware_Update+0x20c>)
 80004c6:	f001 fc93 	bl	8001df0 <puts>
      printf("Choose firmware size input method:\r\n");
 80004ca:	4876      	ldr	r0, [pc, #472]	@ (80006a4 <Firmware_Update+0x210>)
 80004cc:	f001 fc90 	bl	8001df0 <puts>
      printf("1. Enter size manually (ASCII decimal)\r\n");
 80004d0:	4875      	ldr	r0, [pc, #468]	@ (80006a8 <Firmware_Update+0x214>)
 80004d2:	f001 fc8d 	bl	8001df0 <puts>
      printf("2. Auto-detect from data stream\r\n");
 80004d6:	4875      	ldr	r0, [pc, #468]	@ (80006ac <Firmware_Update+0x218>)
 80004d8:	f001 fc8a 	bl	8001df0 <puts>
      printf("3. Use default size for GPS clock (20388 bytes)\r\n");
 80004dc:	4874      	ldr	r0, [pc, #464]	@ (80006b0 <Firmware_Update+0x21c>)
 80004de:	f001 fc87 	bl	8001df0 <puts>
      uint32_t choice = receive_ascii_number();
 80004e2:	f7ff fecf 	bl	8000284 <receive_ascii_number>
      switch(choice)
 80004e6:	2802      	cmp	r0, #2
 80004e8:	d036      	beq.n	8000558 <Firmware_Update+0xc4>
 80004ea:	2803      	cmp	r0, #3
 80004ec:	d039      	beq.n	8000562 <Firmware_Update+0xce>
 80004ee:	2801      	cmp	r0, #1
 80004f0:	d007      	beq.n	8000502 <Firmware_Update+0x6e>
          printf("Invalid choice. Using auto-detection...\r\n");
 80004f2:	4870      	ldr	r0, [pc, #448]	@ (80006b4 <Firmware_Update+0x220>)
 80004f4:	f001 fc7c 	bl	8001df0 <puts>
          application_size = auto_detect_firmware_size();
 80004f8:	f7ff ff20 	bl	800033c <auto_detect_firmware_size>
 80004fc:	4b6e      	ldr	r3, [pc, #440]	@ (80006b8 <Firmware_Update+0x224>)
 80004fe:	6018      	str	r0, [r3, #0]
          break;
 8000500:	e006      	b.n	8000510 <Firmware_Update+0x7c>
          printf("Enter firmware size in bytes: ");
 8000502:	486e      	ldr	r0, [pc, #440]	@ (80006bc <Firmware_Update+0x228>)
 8000504:	f001 fc0c 	bl	8001d20 <iprintf>
          application_size = receive_ascii_number();
 8000508:	f7ff febc 	bl	8000284 <receive_ascii_number>
 800050c:	4b6a      	ldr	r3, [pc, #424]	@ (80006b8 <Firmware_Update+0x224>)
 800050e:	6018      	str	r0, [r3, #0]
      if(application_size == 0 || application_size > (47 * 1024))
 8000510:	4b69      	ldr	r3, [pc, #420]	@ (80006b8 <Firmware_Update+0x224>)
 8000512:	6819      	ldr	r1, [r3, #0]
 8000514:	1e4b      	subs	r3, r1, #1
 8000516:	f5b3 4f3c 	cmp.w	r3, #48128	@ 0xbc00
 800051a:	d32a      	bcc.n	8000572 <Firmware_Update+0xde>
        printf("Invalid firmware size: %lu bytes (max 48KB)\r\n", application_size);
 800051c:	4868      	ldr	r0, [pc, #416]	@ (80006c0 <Firmware_Update+0x22c>)
 800051e:	f001 fbff 	bl	8001d20 <iprintf>
    printf("=== Firmware Update Failed ===\r\n");
 8000522:	4868      	ldr	r0, [pc, #416]	@ (80006c4 <Firmware_Update+0x230>)
 8000524:	f001 fc64 	bl	8001df0 <puts>
    printf("Initiating rollback procedure...\r\n");
 8000528:	4867      	ldr	r0, [pc, #412]	@ (80006c8 <Firmware_Update+0x234>)
 800052a:	f001 fc61 	bl	8001df0 <puts>
    HAL_StatusTypeDef erase_status = HAL_FLASH_Unlock();
 800052e:	f000 fb93 	bl	8000c58 <HAL_FLASH_Unlock>
    if(erase_status == HAL_OK)
 8000532:	2800      	cmp	r0, #0
 8000534:	f000 809d 	beq.w	8000672 <Firmware_Update+0x1de>
    printf("=== Rollback Complete ===\r\n");
 8000538:	4864      	ldr	r0, [pc, #400]	@ (80006cc <Firmware_Update+0x238>)
 800053a:	f001 fc59 	bl	8001df0 <puts>
    printf("Please retry firmware update or check your firmware file.\r\n");
 800053e:	4864      	ldr	r0, [pc, #400]	@ (80006d0 <Firmware_Update+0x23c>)
 8000540:	f001 fc56 	bl	8001df0 <puts>
      HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_13); // Blink LED to indicate error
 8000544:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000548:	4862      	ldr	r0, [pc, #392]	@ (80006d4 <Firmware_Update+0x240>)
 800054a:	f000 fdb7 	bl	80010bc <HAL_GPIO_TogglePin>
      HAL_Delay(500);
 800054e:	f44f 70fa 	mov.w	r0, #500	@ 0x1f4
 8000552:	f000 fabd 	bl	8000ad0 <HAL_Delay>
    while(1)
 8000556:	e7f5      	b.n	8000544 <Firmware_Update+0xb0>
          application_size = auto_detect_firmware_size();
 8000558:	f7ff fef0 	bl	800033c <auto_detect_firmware_size>
 800055c:	4b56      	ldr	r3, [pc, #344]	@ (80006b8 <Firmware_Update+0x224>)
 800055e:	6018      	str	r0, [r3, #0]
          break;
 8000560:	e7d6      	b.n	8000510 <Firmware_Update+0x7c>
          application_size = 20388; // Your GPS clock size
 8000562:	f644 71a4 	movw	r1, #20388	@ 0x4fa4
 8000566:	4b54      	ldr	r3, [pc, #336]	@ (80006b8 <Firmware_Update+0x224>)
 8000568:	6019      	str	r1, [r3, #0]
          printf("Using default GPS clock size: %lu bytes\r\n", application_size);
 800056a:	485b      	ldr	r0, [pc, #364]	@ (80006d8 <Firmware_Update+0x244>)
 800056c:	f001 fbd8 	bl	8001d20 <iprintf>
          break;
 8000570:	e7ce      	b.n	8000510 <Firmware_Update+0x7c>
      printf("Confirmed firmware size: %lu bytes\r\n", application_size);
 8000572:	485a      	ldr	r0, [pc, #360]	@ (80006dc <Firmware_Update+0x248>)
 8000574:	f001 fbd4 	bl	8001d20 <iprintf>
      printf("Starting automatic data transfer...\r\n");
 8000578:	4859      	ldr	r0, [pc, #356]	@ (80006e0 <Firmware_Update+0x24c>)
 800057a:	f001 fc39 	bl	8001df0 <puts>
      printf("Ready to receive %lu bytes. Send data now...\r\n", application_size);
 800057e:	4b4e      	ldr	r3, [pc, #312]	@ (80006b8 <Firmware_Update+0x224>)
 8000580:	6819      	ldr	r1, [r3, #0]
 8000582:	4858      	ldr	r0, [pc, #352]	@ (80006e4 <Firmware_Update+0x250>)
 8000584:	f001 fbcc 	bl	8001d20 <iprintf>
  uint32_t blocks_written = 0;
 8000588:	2500      	movs	r5, #0
  uint32_t i = 0;
 800058a:	462e      	mov	r6, r5
  uint32_t current_app_size = 0;
 800058c:	462c      	mov	r4, r5
  HAL_StatusTypeDef ex = HAL_OK;
 800058e:	46a8      	mov	r8, r5
      while(current_app_size < application_size)
 8000590:	e032      	b.n	80005f8 <Firmware_Update+0x164>
          printf("Data reception timeout at byte %lu. Initiating rollback...\r\n", current_app_size);
 8000592:	4621      	mov	r1, r4
 8000594:	4854      	ldr	r0, [pc, #336]	@ (80006e8 <Firmware_Update+0x254>)
 8000596:	f001 fbc3 	bl	8001d20 <iprintf>
  if(!update_successful)
 800059a:	e7c2      	b.n	8000522 <Firmware_Update+0x8e>
          uint32_t progress_percent = (current_app_size * 100) / application_size;
 800059c:	2364      	movs	r3, #100	@ 0x64
 800059e:	fb04 f303 	mul.w	r3, r4, r3
 80005a2:	4a45      	ldr	r2, [pc, #276]	@ (80006b8 <Firmware_Update+0x224>)
 80005a4:	6812      	ldr	r2, [r2, #0]
          printf("Progress: %lu/%lu bytes (%lu%%)\r\n",
 80005a6:	fbb3 f3f2 	udiv	r3, r3, r2
 80005aa:	4621      	mov	r1, r4
 80005ac:	484f      	ldr	r0, [pc, #316]	@ (80006ec <Firmware_Update+0x258>)
 80005ae:	f001 fbb7 	bl	8001d20 <iprintf>
 80005b2:	e039      	b.n	8000628 <Firmware_Update+0x194>
          printf("Writing block %lu to flash...\r\n", blocks_written);
 80005b4:	4629      	mov	r1, r5
 80005b6:	484e      	ldr	r0, [pc, #312]	@ (80006f0 <Firmware_Update+0x25c>)
 80005b8:	f001 fbb2 	bl	8001d20 <iprintf>
          memcpy(backup_block, block, MAX_BLOCK_SIZE);
 80005bc:	f44f 6280 	mov.w	r2, #1024	@ 0x400
 80005c0:	f50d 6183 	add.w	r1, sp, #1048	@ 0x418
 80005c4:	a806      	add	r0, sp, #24
 80005c6:	f001 fd6e 	bl	80020a6 <memcpy>
          ex = write_data_to_flash_app(block, i, (blocks_written == 0));
 80005ca:	fab5 f285 	clz	r2, r5
 80005ce:	0952      	lsrs	r2, r2, #5
 80005d0:	b2b9      	uxth	r1, r7
 80005d2:	f50d 6083 	add.w	r0, sp, #1048	@ 0x418
 80005d6:	f7ff ff0f 	bl	80003f8 <write_data_to_flash_app>
          if(ex != HAL_OK)
 80005da:	4680      	mov	r8, r0
 80005dc:	bb68      	cbnz	r0, 800063a <Firmware_Update+0x1a6>
          printf("Block %lu written successfully\r\n", blocks_written);
 80005de:	4629      	mov	r1, r5
 80005e0:	4844      	ldr	r0, [pc, #272]	@ (80006f4 <Firmware_Update+0x260>)
 80005e2:	f001 fb9d 	bl	8001d20 <iprintf>
          blocks_written++;
 80005e6:	3501      	adds	r5, #1
          memset(block, 0, MAX_BLOCK_SIZE);
 80005e8:	f44f 6280 	mov.w	r2, #1024	@ 0x400
 80005ec:	2100      	movs	r1, #0
 80005ee:	f50d 6083 	add.w	r0, sp, #1048	@ 0x418
 80005f2:	f001 fcdd 	bl	8001fb0 <memset>
          i = 0;
 80005f6:	2600      	movs	r6, #0
      while(current_app_size < application_size)
 80005f8:	4b2f      	ldr	r3, [pc, #188]	@ (80006b8 <Firmware_Update+0x224>)
 80005fa:	681b      	ldr	r3, [r3, #0]
 80005fc:	42a3      	cmp	r3, r4
 80005fe:	d920      	bls.n	8000642 <Firmware_Update+0x1ae>
        HAL_StatusTypeDef rx_status = HAL_UART_Receive(&huart3, &data_byte, 1, 5000);
 8000600:	f241 3388 	movw	r3, #5000	@ 0x1388
 8000604:	2201      	movs	r2, #1
 8000606:	a902      	add	r1, sp, #8
 8000608:	483b      	ldr	r0, [pc, #236]	@ (80006f8 <Firmware_Update+0x264>)
 800060a:	f001 f9d6 	bl	80019ba <HAL_UART_Receive>
        if(rx_status != HAL_OK)
 800060e:	2800      	cmp	r0, #0
 8000610:	d1bf      	bne.n	8000592 <Firmware_Update+0xfe>
        block[i++] = data_byte;
 8000612:	1c77      	adds	r7, r6, #1
 8000614:	f89d 2008 	ldrb.w	r2, [sp, #8]
 8000618:	f50d 6383 	add.w	r3, sp, #1048	@ 0x418
 800061c:	559a      	strb	r2, [r3, r6]
        current_app_size++;
 800061e:	3401      	adds	r4, #1
        if(current_app_size % 1024 == 0)
 8000620:	f3c4 0309 	ubfx	r3, r4, #0, #10
 8000624:	2b00      	cmp	r3, #0
 8000626:	d0b9      	beq.n	800059c <Firmware_Update+0x108>
        if(i == MAX_BLOCK_SIZE || current_app_size >= application_size)
 8000628:	f5b7 6f80 	cmp.w	r7, #1024	@ 0x400
 800062c:	d0c2      	beq.n	80005b4 <Firmware_Update+0x120>
 800062e:	4b22      	ldr	r3, [pc, #136]	@ (80006b8 <Firmware_Update+0x224>)
 8000630:	681b      	ldr	r3, [r3, #0]
 8000632:	42a3      	cmp	r3, r4
 8000634:	d9be      	bls.n	80005b4 <Firmware_Update+0x120>
        block[i++] = data_byte;
 8000636:	463e      	mov	r6, r7
 8000638:	e7de      	b.n	80005f8 <Firmware_Update+0x164>
            printf("Flash write error at block %lu. Initiating rollback...\r\n", blocks_written);
 800063a:	4629      	mov	r1, r5
 800063c:	482f      	ldr	r0, [pc, #188]	@ (80006fc <Firmware_Update+0x268>)
 800063e:	f001 fb6f 	bl	8001d20 <iprintf>
      if(ex == HAL_OK && current_app_size >= application_size)
 8000642:	f1b8 0f00 	cmp.w	r8, #0
 8000646:	f47f af6c 	bne.w	8000522 <Firmware_Update+0x8e>
 800064a:	4b1b      	ldr	r3, [pc, #108]	@ (80006b8 <Firmware_Update+0x224>)
 800064c:	681b      	ldr	r3, [r3, #0]
 800064e:	42a3      	cmp	r3, r4
 8000650:	f63f af67 	bhi.w	8000522 <Firmware_Update+0x8e>
        printf("=== Firmware Update Completed Successfully ===\r\n");
 8000654:	482a      	ldr	r0, [pc, #168]	@ (8000700 <Firmware_Update+0x26c>)
 8000656:	f001 fbcb 	bl	8001df0 <puts>
        printf("Total bytes written: %lu\r\n", current_app_size);
 800065a:	4621      	mov	r1, r4
 800065c:	4829      	ldr	r0, [pc, #164]	@ (8000704 <Firmware_Update+0x270>)
 800065e:	f001 fb5f 	bl	8001d20 <iprintf>
        printf("Total blocks written: %lu\r\n", blocks_written);
 8000662:	4629      	mov	r1, r5
 8000664:	4828      	ldr	r0, [pc, #160]	@ (8000708 <Firmware_Update+0x274>)
 8000666:	f001 fb5b 	bl	8001d20 <iprintf>
}
 800066a:	f60d 0d18 	addw	sp, sp, #2072	@ 0x818
 800066e:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
      EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
 8000672:	2300      	movs	r3, #0
 8000674:	9302      	str	r3, [sp, #8]
      EraseInitStruct.PageAddress = ETX_APP_START_ADDRESS;
 8000676:	4b25      	ldr	r3, [pc, #148]	@ (800070c <Firmware_Update+0x278>)
 8000678:	9304      	str	r3, [sp, #16]
      EraseInitStruct.NbPages = 47;
 800067a:	232f      	movs	r3, #47	@ 0x2f
 800067c:	9305      	str	r3, [sp, #20]
      erase_status = HAL_FLASHEx_Erase(&EraseInitStruct, &SectorError);
 800067e:	a901      	add	r1, sp, #4
 8000680:	a802      	add	r0, sp, #8
 8000682:	f000 fbb5 	bl	8000df0 <HAL_FLASHEx_Erase>
 8000686:	4604      	mov	r4, r0
      HAL_FLASH_Lock();
 8000688:	f000 fb00 	bl	8000c8c <HAL_FLASH_Lock>
      if(erase_status == HAL_OK)
 800068c:	b91c      	cbnz	r4, 8000696 <Firmware_Update+0x202>
        printf("Application area erased. System ready for new firmware.\r\n");
 800068e:	4820      	ldr	r0, [pc, #128]	@ (8000710 <Firmware_Update+0x27c>)
 8000690:	f001 fbae 	bl	8001df0 <puts>
 8000694:	e750      	b.n	8000538 <Firmware_Update+0xa4>
        printf("Rollback erase failed. Manual intervention required.\r\n");
 8000696:	481f      	ldr	r0, [pc, #124]	@ (8000714 <Firmware_Update+0x280>)
 8000698:	f001 fbaa 	bl	8001df0 <puts>
 800069c:	e74c      	b.n	8000538 <Firmware_Update+0xa4>
 800069e:	bf00      	nop
 80006a0:	08002d18 	.word	0x08002d18
 80006a4:	08002d44 	.word	0x08002d44
 80006a8:	08002d68 	.word	0x08002d68
 80006ac:	08002d90 	.word	0x08002d90
 80006b0:	08002db4 	.word	0x08002db4
 80006b4:	08002e34 	.word	0x08002e34
 80006b8:	20000090 	.word	0x20000090
 80006bc:	08002de8 	.word	0x08002de8
 80006c0:	08002e60 	.word	0x08002e60
 80006c4:	0800305c 	.word	0x0800305c
 80006c8:	0800307c 	.word	0x0800307c
 80006cc:	08003114 	.word	0x08003114
 80006d0:	08003130 	.word	0x08003130
 80006d4:	40010c00 	.word	0x40010c00
 80006d8:	08002e08 	.word	0x08002e08
 80006dc:	08002e90 	.word	0x08002e90
 80006e0:	08002eb8 	.word	0x08002eb8
 80006e4:	08002ee0 	.word	0x08002ee0
 80006e8:	08002f10 	.word	0x08002f10
 80006ec:	08002f50 	.word	0x08002f50
 80006f0:	08002f74 	.word	0x08002f74
 80006f4:	08002fd0 	.word	0x08002fd0
 80006f8:	20000094 	.word	0x20000094
 80006fc:	08002f94 	.word	0x08002f94
 8000700:	08002ff4 	.word	0x08002ff4
 8000704:	08003024 	.word	0x08003024
 8000708:	08003040 	.word	0x08003040
 800070c:	08004400 	.word	0x08004400
 8000710:	080030a0 	.word	0x080030a0
 8000714:	080030dc 	.word	0x080030dc

08000718 <__io_putchar>:
{
 8000718:	b500      	push	{lr}
 800071a:	b083      	sub	sp, #12
 800071c:	9001      	str	r0, [sp, #4]
  HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
 800071e:	f04f 33ff 	mov.w	r3, #4294967295
 8000722:	2201      	movs	r2, #1
 8000724:	a901      	add	r1, sp, #4
 8000726:	4803      	ldr	r0, [pc, #12]	@ (8000734 <__io_putchar+0x1c>)
 8000728:	f001 f8e1 	bl	80018ee <HAL_UART_Transmit>
}
 800072c:	9801      	ldr	r0, [sp, #4]
 800072e:	b003      	add	sp, #12
 8000730:	f85d fb04 	ldr.w	pc, [sp], #4
 8000734:	200000dc 	.word	0x200000dc

08000738 <Error_Handler>:
  __ASM volatile ("cpsid i" : : : "memory");
 8000738:	b672      	cpsid	i
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
 800073a:	e7fe      	b.n	800073a <Error_Handler+0x2>

0800073c <MX_USART1_UART_Init>:
{
 800073c:	b508      	push	{r3, lr}
  huart1.Instance = USART1;
 800073e:	480a      	ldr	r0, [pc, #40]	@ (8000768 <MX_USART1_UART_Init+0x2c>)
 8000740:	4b0a      	ldr	r3, [pc, #40]	@ (800076c <MX_USART1_UART_Init+0x30>)
 8000742:	6003      	str	r3, [r0, #0]
  huart1.Init.BaudRate = 115200;
 8000744:	f44f 33e1 	mov.w	r3, #115200	@ 0x1c200
 8000748:	6043      	str	r3, [r0, #4]
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
 800074a:	2300      	movs	r3, #0
 800074c:	6083      	str	r3, [r0, #8]
  huart1.Init.StopBits = UART_STOPBITS_1;
 800074e:	60c3      	str	r3, [r0, #12]
  huart1.Init.Parity = UART_PARITY_NONE;
 8000750:	6103      	str	r3, [r0, #16]
  huart1.Init.Mode = UART_MODE_TX_RX;
 8000752:	220c      	movs	r2, #12
 8000754:	6142      	str	r2, [r0, #20]
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
 8000756:	6183      	str	r3, [r0, #24]
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;
 8000758:	61c3      	str	r3, [r0, #28]
  if (HAL_UART_Init(&huart1) != HAL_OK)
 800075a:	f001 f898 	bl	800188e <HAL_UART_Init>
 800075e:	b900      	cbnz	r0, 8000762 <MX_USART1_UART_Init+0x26>
}
 8000760:	bd08      	pop	{r3, pc}
    Error_Handler();
 8000762:	f7ff ffe9 	bl	8000738 <Error_Handler>
 8000766:	bf00      	nop
 8000768:	200000dc 	.word	0x200000dc
 800076c:	40013800 	.word	0x40013800

08000770 <MX_USART3_UART_Init>:
{
 8000770:	b508      	push	{r3, lr}
  huart3.Instance = USART3;
 8000772:	480a      	ldr	r0, [pc, #40]	@ (800079c <MX_USART3_UART_Init+0x2c>)
 8000774:	4b0a      	ldr	r3, [pc, #40]	@ (80007a0 <MX_USART3_UART_Init+0x30>)
 8000776:	6003      	str	r3, [r0, #0]
  huart3.Init.BaudRate = 115200;
 8000778:	f44f 33e1 	mov.w	r3, #115200	@ 0x1c200
 800077c:	6043      	str	r3, [r0, #4]
  huart3.Init.WordLength = UART_WORDLENGTH_8B;
 800077e:	2300      	movs	r3, #0
 8000780:	6083      	str	r3, [r0, #8]
  huart3.Init.StopBits = UART_STOPBITS_1;
 8000782:	60c3      	str	r3, [r0, #12]
  huart3.Init.Parity = UART_PARITY_NONE;
 8000784:	6103      	str	r3, [r0, #16]
  huart3.Init.Mode = UART_MODE_TX_RX;
 8000786:	220c      	movs	r2, #12
 8000788:	6142      	str	r2, [r0, #20]
  huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
 800078a:	6183      	str	r3, [r0, #24]
  huart3.Init.OverSampling = UART_OVERSAMPLING_16;
 800078c:	61c3      	str	r3, [r0, #28]
  if (HAL_UART_Init(&huart3) != HAL_OK)
 800078e:	f001 f87e 	bl	800188e <HAL_UART_Init>
 8000792:	b900      	cbnz	r0, 8000796 <MX_USART3_UART_Init+0x26>
}
 8000794:	bd08      	pop	{r3, pc}
    Error_Handler();
 8000796:	f7ff ffcf 	bl	8000738 <Error_Handler>
 800079a:	bf00      	nop
 800079c:	20000094 	.word	0x20000094
 80007a0:	40004800 	.word	0x40004800

080007a4 <SystemClock_Config>:
{
 80007a4:	b500      	push	{lr}
 80007a6:	b091      	sub	sp, #68	@ 0x44
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 80007a8:	2228      	movs	r2, #40	@ 0x28
 80007aa:	2100      	movs	r1, #0
 80007ac:	a806      	add	r0, sp, #24
 80007ae:	f001 fbff 	bl	8001fb0 <memset>
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 80007b2:	2300      	movs	r3, #0
 80007b4:	9301      	str	r3, [sp, #4]
 80007b6:	9302      	str	r3, [sp, #8]
 80007b8:	9303      	str	r3, [sp, #12]
 80007ba:	9304      	str	r3, [sp, #16]
 80007bc:	9305      	str	r3, [sp, #20]
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
 80007be:	2302      	movs	r3, #2
 80007c0:	9306      	str	r3, [sp, #24]
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
 80007c2:	2301      	movs	r3, #1
 80007c4:	930a      	str	r3, [sp, #40]	@ 0x28
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
 80007c6:	2310      	movs	r3, #16
 80007c8:	930b      	str	r3, [sp, #44]	@ 0x2c
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 80007ca:	a806      	add	r0, sp, #24
 80007cc:	f000 fc96 	bl	80010fc <HAL_RCC_OscConfig>
 80007d0:	b968      	cbnz	r0, 80007ee <SystemClock_Config+0x4a>
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
 80007d2:	230f      	movs	r3, #15
 80007d4:	9301      	str	r3, [sp, #4]
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
 80007d6:	2100      	movs	r1, #0
 80007d8:	9102      	str	r1, [sp, #8]
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
 80007da:	9103      	str	r1, [sp, #12]
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
 80007dc:	9104      	str	r1, [sp, #16]
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
 80007de:	9105      	str	r1, [sp, #20]
  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
 80007e0:	a801      	add	r0, sp, #4
 80007e2:	f000 febd 	bl	8001560 <HAL_RCC_ClockConfig>
 80007e6:	b920      	cbnz	r0, 80007f2 <SystemClock_Config+0x4e>
}
 80007e8:	b011      	add	sp, #68	@ 0x44
 80007ea:	f85d fb04 	ldr.w	pc, [sp], #4
    Error_Handler();
 80007ee:	f7ff ffa3 	bl	8000738 <Error_Handler>
    Error_Handler();
 80007f2:	f7ff ffa1 	bl	8000738 <Error_Handler>
	...

080007f8 <main>:
{
 80007f8:	b508      	push	{r3, lr}
  HAL_Init();
 80007fa:	f000 f945 	bl	8000a88 <HAL_Init>
  SystemClock_Config();
 80007fe:	f7ff ffd1 	bl	80007a4 <SystemClock_Config>
  MX_GPIO_Init();
 8000802:	f7ff fca3 	bl	800014c <MX_GPIO_Init>
  MX_USART1_UART_Init();
 8000806:	f7ff ff99 	bl	800073c <MX_USART1_UART_Init>
  MX_USART3_UART_Init();
 800080a:	f7ff ffb1 	bl	8000770 <MX_USART3_UART_Init>
  printf("Bootloader v%d:%d Started!!!\n", BL_Version[0], BL_Version[1]);
 800080e:	4b05      	ldr	r3, [pc, #20]	@ (8000824 <main+0x2c>)
 8000810:	785a      	ldrb	r2, [r3, #1]
 8000812:	7819      	ldrb	r1, [r3, #0]
 8000814:	4804      	ldr	r0, [pc, #16]	@ (8000828 <main+0x30>)
 8000816:	f001 fa83 	bl	8001d20 <iprintf>
  Firmware_Update();
 800081a:	f7ff fe3b 	bl	8000494 <Firmware_Update>
  goto_application();
 800081e:	f7ff fcc9 	bl	80001b4 <goto_application>
  while (1)
 8000822:	e7fe      	b.n	8000822 <main+0x2a>
 8000824:	20000000 	.word	0x20000000
 8000828:	0800316c 	.word	0x0800316c

0800082c <HAL_MspInit>:
/* USER CODE END 0 */
/**
  * Initializes the Global MSP.
  */
void HAL_MspInit(void)
{
 800082c:	b082      	sub	sp, #8

  /* USER CODE BEGIN MspInit 0 */

  /* USER CODE END MspInit 0 */

  __HAL_RCC_AFIO_CLK_ENABLE();
 800082e:	4b0a      	ldr	r3, [pc, #40]	@ (8000858 <HAL_MspInit+0x2c>)
 8000830:	699a      	ldr	r2, [r3, #24]
 8000832:	f042 0201 	orr.w	r2, r2, #1
 8000836:	619a      	str	r2, [r3, #24]
 8000838:	699a      	ldr	r2, [r3, #24]
 800083a:	f002 0201 	and.w	r2, r2, #1
 800083e:	9200      	str	r2, [sp, #0]
 8000840:	9a00      	ldr	r2, [sp, #0]
  __HAL_RCC_PWR_CLK_ENABLE();
 8000842:	69da      	ldr	r2, [r3, #28]
 8000844:	f042 5280 	orr.w	r2, r2, #268435456	@ 0x10000000
 8000848:	61da      	str	r2, [r3, #28]
 800084a:	69db      	ldr	r3, [r3, #28]
 800084c:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8000850:	9301      	str	r3, [sp, #4]
 8000852:	9b01      	ldr	r3, [sp, #4]
  /* System interrupt init*/

  /* USER CODE BEGIN MspInit 1 */

  /* USER CODE END MspInit 1 */
}
 8000854:	b002      	add	sp, #8
 8000856:	4770      	bx	lr
 8000858:	40021000 	.word	0x40021000

0800085c <HAL_UART_MspInit>:
* This function configures the hardware resources used in this example
* @param huart: UART handle pointer
* @retval None
*/
void HAL_UART_MspInit(UART_HandleTypeDef* huart)
{
 800085c:	b510      	push	{r4, lr}
 800085e:	b088      	sub	sp, #32
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 8000860:	2300      	movs	r3, #0
 8000862:	9304      	str	r3, [sp, #16]
 8000864:	9305      	str	r3, [sp, #20]
 8000866:	9306      	str	r3, [sp, #24]
 8000868:	9307      	str	r3, [sp, #28]
  if(huart->Instance==USART1)
 800086a:	6803      	ldr	r3, [r0, #0]
 800086c:	4a2d      	ldr	r2, [pc, #180]	@ (8000924 <HAL_UART_MspInit+0xc8>)
 800086e:	4293      	cmp	r3, r2
 8000870:	d004      	beq.n	800087c <HAL_UART_MspInit+0x20>

  /* USER CODE BEGIN USART1_MspInit 1 */

  /* USER CODE END USART1_MspInit 1 */
  }
  else if(huart->Instance==USART3)
 8000872:	4a2d      	ldr	r2, [pc, #180]	@ (8000928 <HAL_UART_MspInit+0xcc>)
 8000874:	4293      	cmp	r3, r2
 8000876:	d02b      	beq.n	80008d0 <HAL_UART_MspInit+0x74>
  /* USER CODE BEGIN USART3_MspInit 1 */

  /* USER CODE END USART3_MspInit 1 */
  }

}
 8000878:	b008      	add	sp, #32
 800087a:	bd10      	pop	{r4, pc}
    __HAL_RCC_USART1_CLK_ENABLE();
 800087c:	4b2b      	ldr	r3, [pc, #172]	@ (800092c <HAL_UART_MspInit+0xd0>)
 800087e:	699a      	ldr	r2, [r3, #24]
 8000880:	f442 4280 	orr.w	r2, r2, #16384	@ 0x4000
 8000884:	619a      	str	r2, [r3, #24]
 8000886:	699a      	ldr	r2, [r3, #24]
 8000888:	f402 4280 	and.w	r2, r2, #16384	@ 0x4000
 800088c:	9200      	str	r2, [sp, #0]
 800088e:	9a00      	ldr	r2, [sp, #0]
    __HAL_RCC_GPIOA_CLK_ENABLE();
 8000890:	699a      	ldr	r2, [r3, #24]
 8000892:	f042 0204 	orr.w	r2, r2, #4
 8000896:	619a      	str	r2, [r3, #24]
 8000898:	699b      	ldr	r3, [r3, #24]
 800089a:	f003 0304 	and.w	r3, r3, #4
 800089e:	9301      	str	r3, [sp, #4]
 80008a0:	9b01      	ldr	r3, [sp, #4]
    GPIO_InitStruct.Pin = GPIO_PIN_9;
 80008a2:	f44f 7300 	mov.w	r3, #512	@ 0x200
 80008a6:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 80008a8:	2302      	movs	r3, #2
 80008aa:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
 80008ac:	2303      	movs	r3, #3
 80008ae:	9307      	str	r3, [sp, #28]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 80008b0:	4c1f      	ldr	r4, [pc, #124]	@ (8000930 <HAL_UART_MspInit+0xd4>)
 80008b2:	a904      	add	r1, sp, #16
 80008b4:	4620      	mov	r0, r4
 80008b6:	f000 faed 	bl	8000e94 <HAL_GPIO_Init>
    GPIO_InitStruct.Pin = GPIO_PIN_10;
 80008ba:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 80008be:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 80008c0:	2300      	movs	r3, #0
 80008c2:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 80008c4:	9306      	str	r3, [sp, #24]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 80008c6:	a904      	add	r1, sp, #16
 80008c8:	4620      	mov	r0, r4
 80008ca:	f000 fae3 	bl	8000e94 <HAL_GPIO_Init>
 80008ce:	e7d3      	b.n	8000878 <HAL_UART_MspInit+0x1c>
    __HAL_RCC_USART3_CLK_ENABLE();
 80008d0:	4b16      	ldr	r3, [pc, #88]	@ (800092c <HAL_UART_MspInit+0xd0>)
 80008d2:	69da      	ldr	r2, [r3, #28]
 80008d4:	f442 2280 	orr.w	r2, r2, #262144	@ 0x40000
 80008d8:	61da      	str	r2, [r3, #28]
 80008da:	69da      	ldr	r2, [r3, #28]
 80008dc:	f402 2280 	and.w	r2, r2, #262144	@ 0x40000
 80008e0:	9202      	str	r2, [sp, #8]
 80008e2:	9a02      	ldr	r2, [sp, #8]
    __HAL_RCC_GPIOB_CLK_ENABLE();
 80008e4:	699a      	ldr	r2, [r3, #24]
 80008e6:	f042 0208 	orr.w	r2, r2, #8
 80008ea:	619a      	str	r2, [r3, #24]
 80008ec:	699b      	ldr	r3, [r3, #24]
 80008ee:	f003 0308 	and.w	r3, r3, #8
 80008f2:	9303      	str	r3, [sp, #12]
 80008f4:	9b03      	ldr	r3, [sp, #12]
    GPIO_InitStruct.Pin = GPIO_PIN_10;
 80008f6:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 80008fa:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 80008fc:	2302      	movs	r3, #2
 80008fe:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
 8000900:	2303      	movs	r3, #3
 8000902:	9307      	str	r3, [sp, #28]
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 8000904:	4c0b      	ldr	r4, [pc, #44]	@ (8000934 <HAL_UART_MspInit+0xd8>)
 8000906:	a904      	add	r1, sp, #16
 8000908:	4620      	mov	r0, r4
 800090a:	f000 fac3 	bl	8000e94 <HAL_GPIO_Init>
    GPIO_InitStruct.Pin = GPIO_PIN_11;
 800090e:	f44f 6300 	mov.w	r3, #2048	@ 0x800
 8000912:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 8000914:	2300      	movs	r3, #0
 8000916:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 8000918:	9306      	str	r3, [sp, #24]
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 800091a:	a904      	add	r1, sp, #16
 800091c:	4620      	mov	r0, r4
 800091e:	f000 fab9 	bl	8000e94 <HAL_GPIO_Init>
}
 8000922:	e7a9      	b.n	8000878 <HAL_UART_MspInit+0x1c>
 8000924:	40013800 	.word	0x40013800
 8000928:	40004800 	.word	0x40004800
 800092c:	40021000 	.word	0x40021000
 8000930:	40010800 	.word	0x40010800
 8000934:	40010c00 	.word	0x40010c00

08000938 <NMI_Handler>:
{
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */

  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
  while (1)
 8000938:	e7fe      	b.n	8000938 <NMI_Handler>

0800093a <HardFault_Handler>:
void HardFault_Handler(void)
{
  /* USER CODE BEGIN HardFault_IRQn 0 */

  /* USER CODE END HardFault_IRQn 0 */
  while (1)
 800093a:	e7fe      	b.n	800093a <HardFault_Handler>

0800093c <MemManage_Handler>:
void MemManage_Handler(void)
{
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */

  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
 800093c:	e7fe      	b.n	800093c <MemManage_Handler>

0800093e <BusFault_Handler>:
void BusFault_Handler(void)
{
  /* USER CODE BEGIN BusFault_IRQn 0 */

  /* USER CODE END BusFault_IRQn 0 */
  while (1)
 800093e:	e7fe      	b.n	800093e <BusFault_Handler>

08000940 <UsageFault_Handler>:
void UsageFault_Handler(void)
{
  /* USER CODE BEGIN UsageFault_IRQn 0 */

  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
 8000940:	e7fe      	b.n	8000940 <UsageFault_Handler>

08000942 <SVC_Handler>:

  /* USER CODE END SVCall_IRQn 0 */
  /* USER CODE BEGIN SVCall_IRQn 1 */

  /* USER CODE END SVCall_IRQn 1 */
}
 8000942:	4770      	bx	lr

08000944 <DebugMon_Handler>:

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}
 8000944:	4770      	bx	lr

08000946 <PendSV_Handler>:

  /* USER CODE END PendSV_IRQn 0 */
  /* USER CODE BEGIN PendSV_IRQn 1 */

  /* USER CODE END PendSV_IRQn 1 */
}
 8000946:	4770      	bx	lr

08000948 <SysTick_Handler>:

/**
  * @brief This function handles System tick timer.
  */
void SysTick_Handler(void)
{
 8000948:	b508      	push	{r3, lr}
  /* USER CODE BEGIN SysTick_IRQn 0 */

  /* USER CODE END SysTick_IRQn 0 */
  HAL_IncTick();
 800094a:	f000 f8af 	bl	8000aac <HAL_IncTick>
  /* USER CODE BEGIN SysTick_IRQn 1 */

  /* USER CODE END SysTick_IRQn 1 */
}
 800094e:	bd08      	pop	{r3, pc}

08000950 <_read>:
	_kill(status, -1);
	while (1) {}		/* Make sure we hang here */
}

__attribute__((weak)) int _read(int file, char *ptr, int len)
{
 8000950:	b570      	push	{r4, r5, r6, lr}
 8000952:	460c      	mov	r4, r1
 8000954:	4616      	mov	r6, r2
	int DataIdx;

	for (DataIdx = 0; DataIdx < len; DataIdx++)
 8000956:	2500      	movs	r5, #0
 8000958:	e006      	b.n	8000968 <_read+0x18>
	{
		*ptr++ = __io_getchar();
 800095a:	f3af 8000 	nop.w
 800095e:	4621      	mov	r1, r4
 8000960:	f801 0b01 	strb.w	r0, [r1], #1
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 8000964:	3501      	adds	r5, #1
		*ptr++ = __io_getchar();
 8000966:	460c      	mov	r4, r1
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 8000968:	42b5      	cmp	r5, r6
 800096a:	dbf6      	blt.n	800095a <_read+0xa>
	}

return len;
}
 800096c:	4630      	mov	r0, r6
 800096e:	bd70      	pop	{r4, r5, r6, pc}

08000970 <_write>:

__attribute__((weak)) int _write(int file, char *ptr, int len)
{
 8000970:	b570      	push	{r4, r5, r6, lr}
 8000972:	460c      	mov	r4, r1
 8000974:	4616      	mov	r6, r2
	int DataIdx;

	for (DataIdx = 0; DataIdx < len; DataIdx++)
 8000976:	2500      	movs	r5, #0
 8000978:	e004      	b.n	8000984 <_write+0x14>
	{
		__io_putchar(*ptr++);
 800097a:	f814 0b01 	ldrb.w	r0, [r4], #1
 800097e:	f7ff fecb 	bl	8000718 <__io_putchar>
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 8000982:	3501      	adds	r5, #1
 8000984:	42b5      	cmp	r5, r6
 8000986:	dbf8      	blt.n	800097a <_write+0xa>
	}
	return len;
}
 8000988:	4630      	mov	r0, r6
 800098a:	bd70      	pop	{r4, r5, r6, pc}

0800098c <_close>:

int _close(int file)
{
	return -1;
}
 800098c:	f04f 30ff 	mov.w	r0, #4294967295
 8000990:	4770      	bx	lr

08000992 <_fstat>:


int _fstat(int file, struct stat *st)
{
	st->st_mode = S_IFCHR;
 8000992:	f44f 5300 	mov.w	r3, #8192	@ 0x2000
 8000996:	604b      	str	r3, [r1, #4]
	return 0;
}
 8000998:	2000      	movs	r0, #0
 800099a:	4770      	bx	lr

0800099c <_isatty>:

int _isatty(int file)
{
	return 1;
}
 800099c:	2001      	movs	r0, #1
 800099e:	4770      	bx	lr

080009a0 <_lseek>:

int _lseek(int file, int ptr, int dir)
{
	return 0;
}
 80009a0:	2000      	movs	r0, #0
 80009a2:	4770      	bx	lr

080009a4 <_sbrk>:
 *
 * @param incr Memory size
 * @return Pointer to allocated memory
 */
void *_sbrk(ptrdiff_t incr)
{
 80009a4:	b510      	push	{r4, lr}
 80009a6:	4603      	mov	r3, r0
  extern uint8_t _end; /* Symbol defined in the linker script */
  extern uint8_t _estack; /* Symbol defined in the linker script */
  extern uint32_t _Min_Stack_Size; /* Symbol defined in the linker script */
  const uint32_t stack_limit = (uint32_t)&_estack - (uint32_t)&_Min_Stack_Size;
 80009a8:	4a0c      	ldr	r2, [pc, #48]	@ (80009dc <_sbrk+0x38>)
 80009aa:	490d      	ldr	r1, [pc, #52]	@ (80009e0 <_sbrk+0x3c>)
  const uint8_t *max_heap = (uint8_t *)stack_limit;
  uint8_t *prev_heap_end;

  /* Initialize heap end at first call */
  if (NULL == __sbrk_heap_end)
 80009ac:	480d      	ldr	r0, [pc, #52]	@ (80009e4 <_sbrk+0x40>)
 80009ae:	6800      	ldr	r0, [r0, #0]
 80009b0:	b140      	cbz	r0, 80009c4 <_sbrk+0x20>
  {
    __sbrk_heap_end = &_end;
  }

  /* Protect heap from growing into the reserved MSP stack */
  if (__sbrk_heap_end + incr > max_heap)
 80009b2:	480c      	ldr	r0, [pc, #48]	@ (80009e4 <_sbrk+0x40>)
 80009b4:	6800      	ldr	r0, [r0, #0]
 80009b6:	4403      	add	r3, r0
 80009b8:	1a52      	subs	r2, r2, r1
 80009ba:	4293      	cmp	r3, r2
 80009bc:	d806      	bhi.n	80009cc <_sbrk+0x28>
    errno = ENOMEM;
    return (void *)-1;
  }

  prev_heap_end = __sbrk_heap_end;
  __sbrk_heap_end += incr;
 80009be:	4a09      	ldr	r2, [pc, #36]	@ (80009e4 <_sbrk+0x40>)
 80009c0:	6013      	str	r3, [r2, #0]

  return (void *)prev_heap_end;
}
 80009c2:	bd10      	pop	{r4, pc}
    __sbrk_heap_end = &_end;
 80009c4:	4807      	ldr	r0, [pc, #28]	@ (80009e4 <_sbrk+0x40>)
 80009c6:	4c08      	ldr	r4, [pc, #32]	@ (80009e8 <_sbrk+0x44>)
 80009c8:	6004      	str	r4, [r0, #0]
 80009ca:	e7f2      	b.n	80009b2 <_sbrk+0xe>
    errno = ENOMEM;
 80009cc:	f001 fb3e 	bl	800204c <__errno>
 80009d0:	230c      	movs	r3, #12
 80009d2:	6003      	str	r3, [r0, #0]
    return (void *)-1;
 80009d4:	f04f 30ff 	mov.w	r0, #4294967295
 80009d8:	e7f3      	b.n	80009c2 <_sbrk+0x1e>
 80009da:	bf00      	nop
 80009dc:	20005000 	.word	0x20005000
 80009e0:	00000400 	.word	0x00000400
 80009e4:	20000124 	.word	0x20000124
 80009e8:	200002a0 	.word	0x200002a0

080009ec <SystemInit>:

  /* Configure the Vector Table location -------------------------------------*/
#if defined(USER_VECT_TAB_ADDRESS)
  SCB->VTOR = VECT_TAB_BASE_ADDRESS | VECT_TAB_OFFSET; /* Vector Table Relocation in Internal SRAM. */
#endif /* USER_VECT_TAB_ADDRESS */
}
 80009ec:	4770      	bx	lr
	...

080009f0 <Reset_Handler>:
  .weak Reset_Handler
  .type Reset_Handler, %function
Reset_Handler:

/* Copy the data segment initializers from flash to SRAM */
  ldr r0, =_sdata
 80009f0:	480c      	ldr	r0, [pc, #48]	@ (8000a24 <LoopFillZerobss+0x12>)
  ldr r1, =_edata
 80009f2:	490d      	ldr	r1, [pc, #52]	@ (8000a28 <LoopFillZerobss+0x16>)
  ldr r2, =_sidata
 80009f4:	4a0d      	ldr	r2, [pc, #52]	@ (8000a2c <LoopFillZerobss+0x1a>)
  movs r3, #0
 80009f6:	2300      	movs	r3, #0
  b LoopCopyDataInit
 80009f8:	e002      	b.n	8000a00 <LoopCopyDataInit>

080009fa <CopyDataInit>:

CopyDataInit:
  ldr r4, [r2, r3]
 80009fa:	58d4      	ldr	r4, [r2, r3]
  str r4, [r0, r3]
 80009fc:	50c4      	str	r4, [r0, r3]
  adds r3, r3, #4
 80009fe:	3304      	adds	r3, #4

08000a00 <LoopCopyDataInit>:

LoopCopyDataInit:
  adds r4, r0, r3
 8000a00:	18c4      	adds	r4, r0, r3
  cmp r4, r1
 8000a02:	428c      	cmp	r4, r1
  bcc CopyDataInit
 8000a04:	d3f9      	bcc.n	80009fa <CopyDataInit>
  
/* Zero fill the bss segment. */
  ldr r2, =_sbss
 8000a06:	4a0a      	ldr	r2, [pc, #40]	@ (8000a30 <LoopFillZerobss+0x1e>)
  ldr r4, =_ebss
 8000a08:	4c0a      	ldr	r4, [pc, #40]	@ (8000a34 <LoopFillZerobss+0x22>)
  movs r3, #0
 8000a0a:	2300      	movs	r3, #0
  b LoopFillZerobss
 8000a0c:	e001      	b.n	8000a12 <LoopFillZerobss>

08000a0e <FillZerobss>:

FillZerobss:
  str  r3, [r2]
 8000a0e:	6013      	str	r3, [r2, #0]
  adds r2, r2, #4
 8000a10:	3204      	adds	r2, #4

08000a12 <LoopFillZerobss>:

LoopFillZerobss:
  cmp r2, r4
 8000a12:	42a2      	cmp	r2, r4
  bcc FillZerobss
 8000a14:	d3fb      	bcc.n	8000a0e <FillZerobss>

/* Call the clock system intitialization function.*/
    bl  SystemInit
 8000a16:	f7ff ffe9 	bl	80009ec <SystemInit>
/* Call static constructors */
    bl __libc_init_array
 8000a1a:	f001 fb1d 	bl	8002058 <__libc_init_array>
/* Call the application's entry point.*/
  bl main
 8000a1e:	f7ff feeb 	bl	80007f8 <main>
  bx lr
 8000a22:	4770      	bx	lr
  ldr r0, =_sdata
 8000a24:	20000000 	.word	0x20000000
  ldr r1, =_edata
 8000a28:	2000006c 	.word	0x2000006c
  ldr r2, =_sidata
 8000a2c:	080032f4 	.word	0x080032f4
  ldr r2, =_sbss
 8000a30:	20000070 	.word	0x20000070
  ldr r4, =_ebss
 8000a34:	2000029c 	.word	0x2000029c

08000a38 <ADC1_2_IRQHandler>:
 * @retval : None
*/
    .section .text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
  b Infinite_Loop
 8000a38:	e7fe      	b.n	8000a38 <ADC1_2_IRQHandler>
	...

08000a3c <HAL_InitTick>:
  *       implementation  in user file.
  * @param TickPriority Tick interrupt priority.
  * @retval HAL status
  */
__weak HAL_StatusTypeDef HAL_InitTick(uint32_t TickPriority)
{
 8000a3c:	b510      	push	{r4, lr}
 8000a3e:	4604      	mov	r4, r0
  /* Configure the SysTick to have interrupt in 1ms time basis*/
  if (HAL_SYSTICK_Config(SystemCoreClock / (1000U / uwTickFreq)) > 0U)
 8000a40:	4b0e      	ldr	r3, [pc, #56]	@ (8000a7c <HAL_InitTick+0x40>)
 8000a42:	781a      	ldrb	r2, [r3, #0]
 8000a44:	f44f 737a 	mov.w	r3, #1000	@ 0x3e8
 8000a48:	fbb3 f3f2 	udiv	r3, r3, r2
 8000a4c:	4a0c      	ldr	r2, [pc, #48]	@ (8000a80 <HAL_InitTick+0x44>)
 8000a4e:	6810      	ldr	r0, [r2, #0]
 8000a50:	fbb0 f0f3 	udiv	r0, r0, r3
 8000a54:	f000 f8a6 	bl	8000ba4 <HAL_SYSTICK_Config>
 8000a58:	b968      	cbnz	r0, 8000a76 <HAL_InitTick+0x3a>
  {
    return HAL_ERROR;
  }

  /* Configure the SysTick IRQ priority */
  if (TickPriority < (1UL << __NVIC_PRIO_BITS))
 8000a5a:	2c0f      	cmp	r4, #15
 8000a5c:	d901      	bls.n	8000a62 <HAL_InitTick+0x26>
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
    uwTickPrio = TickPriority;
  }
  else
  {
    return HAL_ERROR;
 8000a5e:	2001      	movs	r0, #1
 8000a60:	e00a      	b.n	8000a78 <HAL_InitTick+0x3c>
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
 8000a62:	2200      	movs	r2, #0
 8000a64:	4621      	mov	r1, r4
 8000a66:	f04f 30ff 	mov.w	r0, #4294967295
 8000a6a:	f000 f88b 	bl	8000b84 <HAL_NVIC_SetPriority>
    uwTickPrio = TickPriority;
 8000a6e:	4b05      	ldr	r3, [pc, #20]	@ (8000a84 <HAL_InitTick+0x48>)
 8000a70:	601c      	str	r4, [r3, #0]
  }

  /* Return function status */
  return HAL_OK;
 8000a72:	2000      	movs	r0, #0
 8000a74:	e000      	b.n	8000a78 <HAL_InitTick+0x3c>
    return HAL_ERROR;
 8000a76:	2001      	movs	r0, #1
}
 8000a78:	bd10      	pop	{r4, pc}
 8000a7a:	bf00      	nop
 8000a7c:	20000008 	.word	0x20000008
 8000a80:	20000004 	.word	0x20000004
 8000a84:	2000000c 	.word	0x2000000c

08000a88 <HAL_Init>:
{
 8000a88:	b508      	push	{r3, lr}
  __HAL_FLASH_PREFETCH_BUFFER_ENABLE();
 8000a8a:	4a07      	ldr	r2, [pc, #28]	@ (8000aa8 <HAL_Init+0x20>)
 8000a8c:	6813      	ldr	r3, [r2, #0]
 8000a8e:	f043 0310 	orr.w	r3, r3, #16
 8000a92:	6013      	str	r3, [r2, #0]
  HAL_NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);
 8000a94:	2003      	movs	r0, #3
 8000a96:	f000 f863 	bl	8000b60 <HAL_NVIC_SetPriorityGrouping>
  HAL_InitTick(TICK_INT_PRIORITY);
 8000a9a:	200f      	movs	r0, #15
 8000a9c:	f7ff ffce 	bl	8000a3c <HAL_InitTick>
  HAL_MspInit();
 8000aa0:	f7ff fec4 	bl	800082c <HAL_MspInit>
}
 8000aa4:	2000      	movs	r0, #0
 8000aa6:	bd08      	pop	{r3, pc}
 8000aa8:	******** 	.word	0x********

08000aac <HAL_IncTick>:
  *      implementations in user file.
  * @retval None
  */
__weak void HAL_IncTick(void)
{
  uwTick += uwTickFreq;
 8000aac:	4a03      	ldr	r2, [pc, #12]	@ (8000abc <HAL_IncTick+0x10>)
 8000aae:	6811      	ldr	r1, [r2, #0]
 8000ab0:	4b03      	ldr	r3, [pc, #12]	@ (8000ac0 <HAL_IncTick+0x14>)
 8000ab2:	781b      	ldrb	r3, [r3, #0]
 8000ab4:	440b      	add	r3, r1
 8000ab6:	6013      	str	r3, [r2, #0]
}
 8000ab8:	4770      	bx	lr
 8000aba:	bf00      	nop
 8000abc:	20000128 	.word	0x20000128
 8000ac0:	20000008 	.word	0x20000008

08000ac4 <HAL_GetTick>:
  *       implementations in user file.
  * @retval tick value
  */
__weak uint32_t HAL_GetTick(void)
{
  return uwTick;
 8000ac4:	4b01      	ldr	r3, [pc, #4]	@ (8000acc <HAL_GetTick+0x8>)
 8000ac6:	6818      	ldr	r0, [r3, #0]
}
 8000ac8:	4770      	bx	lr
 8000aca:	bf00      	nop
 8000acc:	20000128 	.word	0x20000128

08000ad0 <HAL_Delay>:
  *       implementations in user file.
  * @param Delay specifies the delay time length, in milliseconds.
  * @retval None
  */
__weak void HAL_Delay(uint32_t Delay)
{
 8000ad0:	b538      	push	{r3, r4, r5, lr}
 8000ad2:	4604      	mov	r4, r0
  uint32_t tickstart = HAL_GetTick();
 8000ad4:	f7ff fff6 	bl	8000ac4 <HAL_GetTick>
 8000ad8:	4605      	mov	r5, r0
  uint32_t wait = Delay;

  /* Add a freq to guarantee minimum wait */
  if (wait < HAL_MAX_DELAY)
 8000ada:	f1b4 3fff 	cmp.w	r4, #4294967295
 8000ade:	d002      	beq.n	8000ae6 <HAL_Delay+0x16>
  {
    wait += (uint32_t)(uwTickFreq);
 8000ae0:	4b04      	ldr	r3, [pc, #16]	@ (8000af4 <HAL_Delay+0x24>)
 8000ae2:	781b      	ldrb	r3, [r3, #0]
 8000ae4:	441c      	add	r4, r3
  }

  while ((HAL_GetTick() - tickstart) < wait)
 8000ae6:	f7ff ffed 	bl	8000ac4 <HAL_GetTick>
 8000aea:	1b40      	subs	r0, r0, r5
 8000aec:	42a0      	cmp	r0, r4
 8000aee:	d3fa      	bcc.n	8000ae6 <HAL_Delay+0x16>
  {
  }
}
 8000af0:	bd38      	pop	{r3, r4, r5, pc}
 8000af2:	bf00      	nop
 8000af4:	20000008 	.word	0x20000008

08000af8 <__NVIC_SetPriority>:
  \param [in]  priority  Priority to set.
  \note    The priority cannot be set for every processor exception.
 */
__STATIC_INLINE void __NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority)
{
  if ((int32_t)(IRQn) >= 0)
 8000af8:	2800      	cmp	r0, #0
 8000afa:	db08      	blt.n	8000b0e <__NVIC_SetPriority+0x16>
  {
    NVIC->IP[((uint32_t)IRQn)]               = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000afc:	0109      	lsls	r1, r1, #4
 8000afe:	b2c9      	uxtb	r1, r1
 8000b00:	f100 4060 	add.w	r0, r0, #3758096384	@ 0xe0000000
 8000b04:	f500 4061 	add.w	r0, r0, #57600	@ 0xe100
 8000b08:	f880 1300 	strb.w	r1, [r0, #768]	@ 0x300
 8000b0c:	4770      	bx	lr
  }
  else
  {
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000b0e:	f000 000f 	and.w	r0, r0, #15
 8000b12:	0109      	lsls	r1, r1, #4
 8000b14:	b2c9      	uxtb	r1, r1
 8000b16:	4b01      	ldr	r3, [pc, #4]	@ (8000b1c <__NVIC_SetPriority+0x24>)
 8000b18:	5419      	strb	r1, [r3, r0]
  }
}
 8000b1a:	4770      	bx	lr
 8000b1c:	e000ed14 	.word	0xe000ed14

08000b20 <NVIC_EncodePriority>:
  \param [in]   PreemptPriority  Preemptive priority value (starting from 0).
  \param [in]       SubPriority  Subpriority value (starting from 0).
  \return                        Encoded priority. Value can be used in the function \ref NVIC_SetPriority().
 */
__STATIC_INLINE uint32_t NVIC_EncodePriority (uint32_t PriorityGroup, uint32_t PreemptPriority, uint32_t SubPriority)
{
 8000b20:	b500      	push	{lr}
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used          */
 8000b22:	f000 0007 	and.w	r0, r0, #7
  uint32_t PreemptPriorityBits;
  uint32_t SubPriorityBits;

  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NVIC_PRIO_BITS) : (uint32_t)(7UL - PriorityGroupTmp);
 8000b26:	f1c0 0c07 	rsb	ip, r0, #7
 8000b2a:	f1bc 0f04 	cmp.w	ip, #4
 8000b2e:	bf28      	it	cs
 8000b30:	f04f 0c04 	movcs.w	ip, #4
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 8000b34:	1d03      	adds	r3, r0, #4
 8000b36:	2b06      	cmp	r3, #6
 8000b38:	d90f      	bls.n	8000b5a <NVIC_EncodePriority+0x3a>
 8000b3a:	1ec3      	subs	r3, r0, #3

  return (
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
 8000b3c:	f04f 3eff 	mov.w	lr, #4294967295
 8000b40:	fa0e f00c 	lsl.w	r0, lr, ip
 8000b44:	ea21 0100 	bic.w	r1, r1, r0
 8000b48:	4099      	lsls	r1, r3
           ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
 8000b4a:	fa0e fe03 	lsl.w	lr, lr, r3
 8000b4e:	ea22 020e 	bic.w	r2, r2, lr
         );
}
 8000b52:	ea41 0002 	orr.w	r0, r1, r2
 8000b56:	f85d fb04 	ldr.w	pc, [sp], #4
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 8000b5a:	2300      	movs	r3, #0
 8000b5c:	e7ee      	b.n	8000b3c <NVIC_EncodePriority+0x1c>
	...

08000b60 <HAL_NVIC_SetPriorityGrouping>:
  reg_value  =  SCB->AIRCR;                                                   /* read old register configuration    */
 8000b60:	4a07      	ldr	r2, [pc, #28]	@ (8000b80 <HAL_NVIC_SetPriorityGrouping+0x20>)
 8000b62:	68d3      	ldr	r3, [r2, #12]
  reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to change               */
 8000b64:	f423 63e0 	bic.w	r3, r3, #1792	@ 0x700
 8000b68:	041b      	lsls	r3, r3, #16
 8000b6a:	0c1b      	lsrs	r3, r3, #16
                (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos) );               /* Insert write key and priority group */
 8000b6c:	0200      	lsls	r0, r0, #8
 8000b6e:	f400 60e0 	and.w	r0, r0, #1792	@ 0x700
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
 8000b72:	4303      	orrs	r3, r0
  reg_value  =  (reg_value                                   |
 8000b74:	f043 63bf 	orr.w	r3, r3, #100139008	@ 0x5f80000
 8000b78:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
  SCB->AIRCR =  reg_value;
 8000b7c:	60d3      	str	r3, [r2, #12]
  /* Check the parameters */
  assert_param(IS_NVIC_PRIORITY_GROUP(PriorityGroup));
  
  /* Set the PRIGROUP[10:8] bits according to the PriorityGroup parameter value */
  NVIC_SetPriorityGrouping(PriorityGroup);
}
 8000b7e:	4770      	bx	lr
 8000b80:	e000ed00 	.word	0xe000ed00

08000b84 <HAL_NVIC_SetPriority>:
  *         This parameter can be a value between 0 and 15
  *         A lower priority value indicates a higher priority.          
  * @retval None
  */
void HAL_NVIC_SetPriority(IRQn_Type IRQn, uint32_t PreemptPriority, uint32_t SubPriority)
{ 
 8000b84:	b510      	push	{r4, lr}
 8000b86:	4604      	mov	r4, r0
  return ((uint32_t)((SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) >> SCB_AIRCR_PRIGROUP_Pos));
 8000b88:	4b05      	ldr	r3, [pc, #20]	@ (8000ba0 <HAL_NVIC_SetPriority+0x1c>)
 8000b8a:	68d8      	ldr	r0, [r3, #12]
  assert_param(IS_NVIC_SUB_PRIORITY(SubPriority));
  assert_param(IS_NVIC_PREEMPTION_PRIORITY(PreemptPriority));
  
  prioritygroup = NVIC_GetPriorityGrouping();
  
  NVIC_SetPriority(IRQn, NVIC_EncodePriority(prioritygroup, PreemptPriority, SubPriority));
 8000b8c:	f3c0 2002 	ubfx	r0, r0, #8, #3
 8000b90:	f7ff ffc6 	bl	8000b20 <NVIC_EncodePriority>
 8000b94:	4601      	mov	r1, r0
 8000b96:	4620      	mov	r0, r4
 8000b98:	f7ff ffae 	bl	8000af8 <__NVIC_SetPriority>
}
 8000b9c:	bd10      	pop	{r4, pc}
 8000b9e:	bf00      	nop
 8000ba0:	e000ed00 	.word	0xe000ed00

08000ba4 <HAL_SYSTICK_Config>:
           function <b>SysTick_Config</b> is not included. In this case, the file <b><i>device</i>.h</b>
           must contain a vendor-specific implementation of this function.
 */
__STATIC_INLINE uint32_t SysTick_Config(uint32_t ticks)
{
  if ((ticks - 1UL) > SysTick_LOAD_RELOAD_Msk)
 8000ba4:	3801      	subs	r0, #1
 8000ba6:	f1b0 7f80 	cmp.w	r0, #16777216	@ 0x1000000
 8000baa:	d20b      	bcs.n	8000bc4 <HAL_SYSTICK_Config+0x20>
  {
    return (1UL);                                                   /* Reload value impossible */
  }

  SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
 8000bac:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8000bb0:	6158      	str	r0, [r3, #20]
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000bb2:	4a05      	ldr	r2, [pc, #20]	@ (8000bc8 <HAL_SYSTICK_Config+0x24>)
 8000bb4:	21f0      	movs	r1, #240	@ 0xf0
 8000bb6:	f882 1023 	strb.w	r1, [r2, #35]	@ 0x23
  NVIC_SetPriority (SysTick_IRQn, (1UL << __NVIC_PRIO_BITS) - 1UL); /* set Priority for Systick Interrupt */
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
 8000bba:	2000      	movs	r0, #0
 8000bbc:	6198      	str	r0, [r3, #24]
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
 8000bbe:	2207      	movs	r2, #7
 8000bc0:	611a      	str	r2, [r3, #16]
                   SysTick_CTRL_TICKINT_Msk   |
                   SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTick Timer */
  return (0UL);                                                     /* Function successful */
 8000bc2:	4770      	bx	lr
    return (1UL);                                                   /* Reload value impossible */
 8000bc4:	2001      	movs	r0, #1
  *                  - 1  Function failed.
  */
uint32_t HAL_SYSTICK_Config(uint32_t TicksNumb)
{
   return SysTick_Config(TicksNumb);
}
 8000bc6:	4770      	bx	lr
 8000bc8:	e000ed00 	.word	0xe000ed00

08000bcc <FLASH_Program_HalfWord>:
  * @retval None
  */
static void FLASH_Program_HalfWord(uint32_t Address, uint16_t Data)
{
  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 8000bcc:	4b04      	ldr	r3, [pc, #16]	@ (8000be0 <FLASH_Program_HalfWord+0x14>)
 8000bce:	2200      	movs	r2, #0
 8000bd0:	61da      	str	r2, [r3, #28]
#if defined(FLASH_BANK2_END)
  if(Address <= FLASH_BANK1_END)
  {
#endif /* FLASH_BANK2_END */
    /* Proceed to program the new data */
    SET_BIT(FLASH->CR, FLASH_CR_PG);
 8000bd2:	4a04      	ldr	r2, [pc, #16]	@ (8000be4 <FLASH_Program_HalfWord+0x18>)
 8000bd4:	6913      	ldr	r3, [r2, #16]
 8000bd6:	f043 0301 	orr.w	r3, r3, #1
 8000bda:	6113      	str	r3, [r2, #16]
    SET_BIT(FLASH->CR2, FLASH_CR2_PG);
  }
#endif /* FLASH_BANK2_END */

  /* Write data in the address */
  *(__IO uint16_t*)Address = Data;
 8000bdc:	8001      	strh	r1, [r0, #0]
}
 8000bde:	4770      	bx	lr
 8000be0:	******** 	.word	0x********
 8000be4:	******** 	.word	0x********

08000be8 <FLASH_SetErrorCode>:
  uint32_t flags = 0U;
  
#if defined(FLASH_BANK2_END)
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR) || __HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR_BANK2))
#else
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR))
 8000be8:	4b19      	ldr	r3, [pc, #100]	@ (8000c50 <FLASH_SetErrorCode+0x68>)
 8000bea:	68db      	ldr	r3, [r3, #12]
 8000bec:	f013 0310 	ands.w	r3, r3, #16
 8000bf0:	d005      	beq.n	8000bfe <FLASH_SetErrorCode+0x16>
#endif /* FLASH_BANK2_END */
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_WRP;
 8000bf2:	4a18      	ldr	r2, [pc, #96]	@ (8000c54 <FLASH_SetErrorCode+0x6c>)
 8000bf4:	69d3      	ldr	r3, [r2, #28]
 8000bf6:	f043 0302 	orr.w	r3, r3, #2
 8000bfa:	61d3      	str	r3, [r2, #28]
#if defined(FLASH_BANK2_END)
    flags |= FLASH_FLAG_WRPERR | FLASH_FLAG_WRPERR_BANK2;
#else
    flags |= FLASH_FLAG_WRPERR;
 8000bfc:	2310      	movs	r3, #16
#endif /* FLASH_BANK2_END */
  }
#if defined(FLASH_BANK2_END)
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR) || __HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR_BANK2))
#else
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR))
 8000bfe:	4a14      	ldr	r2, [pc, #80]	@ (8000c50 <FLASH_SetErrorCode+0x68>)
 8000c00:	68d2      	ldr	r2, [r2, #12]
 8000c02:	f012 0f04 	tst.w	r2, #4
 8000c06:	d006      	beq.n	8000c16 <FLASH_SetErrorCode+0x2e>
#endif /* FLASH_BANK2_END */
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_PROG;
 8000c08:	4912      	ldr	r1, [pc, #72]	@ (8000c54 <FLASH_SetErrorCode+0x6c>)
 8000c0a:	69ca      	ldr	r2, [r1, #28]
 8000c0c:	f042 0201 	orr.w	r2, r2, #1
 8000c10:	61ca      	str	r2, [r1, #28]
#if defined(FLASH_BANK2_END)
    flags |= FLASH_FLAG_PGERR | FLASH_FLAG_PGERR_BANK2;
#else
    flags |= FLASH_FLAG_PGERR;
 8000c12:	f043 0304 	orr.w	r3, r3, #4
#endif /* FLASH_BANK2_END */
  }
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR))
 8000c16:	4a0e      	ldr	r2, [pc, #56]	@ (8000c50 <FLASH_SetErrorCode+0x68>)
 8000c18:	69d2      	ldr	r2, [r2, #28]
 8000c1a:	f012 0f01 	tst.w	r2, #1
 8000c1e:	d009      	beq.n	8000c34 <FLASH_SetErrorCode+0x4c>
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_OPTV;
 8000c20:	490c      	ldr	r1, [pc, #48]	@ (8000c54 <FLASH_SetErrorCode+0x6c>)
 8000c22:	69ca      	ldr	r2, [r1, #28]
 8000c24:	f042 0204 	orr.w	r2, r2, #4
 8000c28:	61ca      	str	r2, [r1, #28]
  __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_OPTVERR);
 8000c2a:	4909      	ldr	r1, [pc, #36]	@ (8000c50 <FLASH_SetErrorCode+0x68>)
 8000c2c:	69ca      	ldr	r2, [r1, #28]
 8000c2e:	f022 0201 	bic.w	r2, r2, #1
 8000c32:	61ca      	str	r2, [r1, #28]
  }

  /* Clear FLASH error pending bits */
  __HAL_FLASH_CLEAR_FLAG(flags);
 8000c34:	f240 1201 	movw	r2, #257	@ 0x101
 8000c38:	4293      	cmp	r3, r2
 8000c3a:	d002      	beq.n	8000c42 <FLASH_SetErrorCode+0x5a>
 8000c3c:	4a04      	ldr	r2, [pc, #16]	@ (8000c50 <FLASH_SetErrorCode+0x68>)
 8000c3e:	60d3      	str	r3, [r2, #12]
}  
 8000c40:	4770      	bx	lr
  __HAL_FLASH_CLEAR_FLAG(flags);
 8000c42:	4a03      	ldr	r2, [pc, #12]	@ (8000c50 <FLASH_SetErrorCode+0x68>)
 8000c44:	69d3      	ldr	r3, [r2, #28]
 8000c46:	f023 0301 	bic.w	r3, r3, #1
 8000c4a:	61d3      	str	r3, [r2, #28]
 8000c4c:	4770      	bx	lr
 8000c4e:	bf00      	nop
 8000c50:	******** 	.word	0x********
 8000c54:	******** 	.word	0x********

08000c58 <HAL_FLASH_Unlock>:
  if(READ_BIT(FLASH->CR, FLASH_CR_LOCK) != RESET)
 8000c58:	4b0a      	ldr	r3, [pc, #40]	@ (8000c84 <HAL_FLASH_Unlock+0x2c>)
 8000c5a:	691b      	ldr	r3, [r3, #16]
 8000c5c:	f013 0f80 	tst.w	r3, #128	@ 0x80
 8000c60:	d00b      	beq.n	8000c7a <HAL_FLASH_Unlock+0x22>
    WRITE_REG(FLASH->KEYR, FLASH_KEY1);
 8000c62:	4b08      	ldr	r3, [pc, #32]	@ (8000c84 <HAL_FLASH_Unlock+0x2c>)
 8000c64:	4a08      	ldr	r2, [pc, #32]	@ (8000c88 <HAL_FLASH_Unlock+0x30>)
 8000c66:	605a      	str	r2, [r3, #4]
    WRITE_REG(FLASH->KEYR, FLASH_KEY2);
 8000c68:	f102 3288 	add.w	r2, r2, #2290649224	@ 0x88888888
 8000c6c:	605a      	str	r2, [r3, #4]
    if(READ_BIT(FLASH->CR, FLASH_CR_LOCK) != RESET)
 8000c6e:	691b      	ldr	r3, [r3, #16]
 8000c70:	f013 0f80 	tst.w	r3, #128	@ 0x80
 8000c74:	d103      	bne.n	8000c7e <HAL_FLASH_Unlock+0x26>
  HAL_StatusTypeDef status = HAL_OK;
 8000c76:	2000      	movs	r0, #0
 8000c78:	4770      	bx	lr
 8000c7a:	2000      	movs	r0, #0
 8000c7c:	4770      	bx	lr
      status = HAL_ERROR;
 8000c7e:	2001      	movs	r0, #1
}
 8000c80:	4770      	bx	lr
 8000c82:	bf00      	nop
 8000c84:	******** 	.word	0x********
 8000c88:	45670123 	.word	0x45670123

08000c8c <HAL_FLASH_Lock>:
  SET_BIT(FLASH->CR, FLASH_CR_LOCK);
 8000c8c:	4a03      	ldr	r2, [pc, #12]	@ (8000c9c <HAL_FLASH_Lock+0x10>)
 8000c8e:	6913      	ldr	r3, [r2, #16]
 8000c90:	f043 0380 	orr.w	r3, r3, #128	@ 0x80
 8000c94:	6113      	str	r3, [r2, #16]
}
 8000c96:	2000      	movs	r0, #0
 8000c98:	4770      	bx	lr
 8000c9a:	bf00      	nop
 8000c9c:	******** 	.word	0x********

08000ca0 <FLASH_WaitForLastOperation>:
{
 8000ca0:	b538      	push	{r3, r4, r5, lr}
 8000ca2:	4604      	mov	r4, r0
  uint32_t tickstart = HAL_GetTick();
 8000ca4:	f7ff ff0e 	bl	8000ac4 <HAL_GetTick>
 8000ca8:	4605      	mov	r5, r0
  while(__HAL_FLASH_GET_FLAG(FLASH_FLAG_BSY)) 
 8000caa:	4b16      	ldr	r3, [pc, #88]	@ (8000d04 <FLASH_WaitForLastOperation+0x64>)
 8000cac:	68db      	ldr	r3, [r3, #12]
 8000cae:	f013 0f01 	tst.w	r3, #1
 8000cb2:	d00a      	beq.n	8000cca <FLASH_WaitForLastOperation+0x2a>
    if (Timeout != HAL_MAX_DELAY)
 8000cb4:	f1b4 3fff 	cmp.w	r4, #4294967295
 8000cb8:	d0f7      	beq.n	8000caa <FLASH_WaitForLastOperation+0xa>
      if((Timeout == 0U) || ((HAL_GetTick()-tickstart) > Timeout))
 8000cba:	b124      	cbz	r4, 8000cc6 <FLASH_WaitForLastOperation+0x26>
 8000cbc:	f7ff ff02 	bl	8000ac4 <HAL_GetTick>
 8000cc0:	1b40      	subs	r0, r0, r5
 8000cc2:	42a0      	cmp	r0, r4
 8000cc4:	d9f1      	bls.n	8000caa <FLASH_WaitForLastOperation+0xa>
        return HAL_TIMEOUT;
 8000cc6:	2003      	movs	r0, #3
 8000cc8:	e01b      	b.n	8000d02 <FLASH_WaitForLastOperation+0x62>
  if (__HAL_FLASH_GET_FLAG(FLASH_FLAG_EOP))
 8000cca:	4b0e      	ldr	r3, [pc, #56]	@ (8000d04 <FLASH_WaitForLastOperation+0x64>)
 8000ccc:	68db      	ldr	r3, [r3, #12]
 8000cce:	f013 0f20 	tst.w	r3, #32
 8000cd2:	d002      	beq.n	8000cda <FLASH_WaitForLastOperation+0x3a>
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP);
 8000cd4:	4b0b      	ldr	r3, [pc, #44]	@ (8000d04 <FLASH_WaitForLastOperation+0x64>)
 8000cd6:	2220      	movs	r2, #32
 8000cd8:	60da      	str	r2, [r3, #12]
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR)  || 
 8000cda:	4b0a      	ldr	r3, [pc, #40]	@ (8000d04 <FLASH_WaitForLastOperation+0x64>)
 8000cdc:	68db      	ldr	r3, [r3, #12]
 8000cde:	f013 0f10 	tst.w	r3, #16
 8000ce2:	d10b      	bne.n	8000cfc <FLASH_WaitForLastOperation+0x5c>
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR) || 
 8000ce4:	4b07      	ldr	r3, [pc, #28]	@ (8000d04 <FLASH_WaitForLastOperation+0x64>)
 8000ce6:	69db      	ldr	r3, [r3, #28]
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR)  || 
 8000ce8:	f013 0f01 	tst.w	r3, #1
 8000cec:	d106      	bne.n	8000cfc <FLASH_WaitForLastOperation+0x5c>
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR))
 8000cee:	4b05      	ldr	r3, [pc, #20]	@ (8000d04 <FLASH_WaitForLastOperation+0x64>)
 8000cf0:	68db      	ldr	r3, [r3, #12]
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR) || 
 8000cf2:	f013 0f04 	tst.w	r3, #4
 8000cf6:	d101      	bne.n	8000cfc <FLASH_WaitForLastOperation+0x5c>
  return HAL_OK;
 8000cf8:	2000      	movs	r0, #0
 8000cfa:	e002      	b.n	8000d02 <FLASH_WaitForLastOperation+0x62>
    FLASH_SetErrorCode();
 8000cfc:	f7ff ff74 	bl	8000be8 <FLASH_SetErrorCode>
    return HAL_ERROR;
 8000d00:	2001      	movs	r0, #1
}
 8000d02:	bd38      	pop	{r3, r4, r5, pc}
 8000d04:	******** 	.word	0x********

08000d08 <HAL_FLASH_Program>:
{
 8000d08:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 8000d0c:	461e      	mov	r6, r3
  __HAL_LOCK(&pFlash);
 8000d0e:	4b24      	ldr	r3, [pc, #144]	@ (8000da0 <HAL_FLASH_Program+0x98>)
 8000d10:	7e1b      	ldrb	r3, [r3, #24]
 8000d12:	2b01      	cmp	r3, #1
 8000d14:	d041      	beq.n	8000d9a <HAL_FLASH_Program+0x92>
 8000d16:	4604      	mov	r4, r0
 8000d18:	460f      	mov	r7, r1
 8000d1a:	4690      	mov	r8, r2
 8000d1c:	4b20      	ldr	r3, [pc, #128]	@ (8000da0 <HAL_FLASH_Program+0x98>)
 8000d1e:	2201      	movs	r2, #1
 8000d20:	761a      	strb	r2, [r3, #24]
    status = FLASH_WaitForLastOperation(FLASH_TIMEOUT_VALUE);
 8000d22:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000d26:	f7ff ffbb 	bl	8000ca0 <FLASH_WaitForLastOperation>
  if(status == HAL_OK)
 8000d2a:	4603      	mov	r3, r0
 8000d2c:	bb78      	cbnz	r0, 8000d8e <HAL_FLASH_Program+0x86>
    if(TypeProgram == FLASH_TYPEPROGRAM_HALFWORD)
 8000d2e:	2c01      	cmp	r4, #1
 8000d30:	d008      	beq.n	8000d44 <HAL_FLASH_Program+0x3c>
    else if(TypeProgram == FLASH_TYPEPROGRAM_WORD)
 8000d32:	2c02      	cmp	r4, #2
 8000d34:	d003      	beq.n	8000d3e <HAL_FLASH_Program+0x36>
      nbiterations = 4U;
 8000d36:	f04f 0904 	mov.w	r9, #4
    for (index = 0U; index < nbiterations; index++)
 8000d3a:	461c      	mov	r4, r3
 8000d3c:	e007      	b.n	8000d4e <HAL_FLASH_Program+0x46>
      nbiterations = 2U;
 8000d3e:	f04f 0902 	mov.w	r9, #2
 8000d42:	e7fa      	b.n	8000d3a <HAL_FLASH_Program+0x32>
      nbiterations = 1U;
 8000d44:	f04f 0901 	mov.w	r9, #1
 8000d48:	e7f7      	b.n	8000d3a <HAL_FLASH_Program+0x32>
    for (index = 0U; index < nbiterations; index++)
 8000d4a:	3401      	adds	r4, #1
 8000d4c:	b2e4      	uxtb	r4, r4
 8000d4e:	454c      	cmp	r4, r9
 8000d50:	d21d      	bcs.n	8000d8e <HAL_FLASH_Program+0x86>
      FLASH_Program_HalfWord((Address + (2U*index)), (uint16_t)(Data >> (16U*index)));
 8000d52:	0121      	lsls	r1, r4, #4
 8000d54:	f1c1 0220 	rsb	r2, r1, #32
 8000d58:	f1a1 0320 	sub.w	r3, r1, #32
 8000d5c:	fa28 f101 	lsr.w	r1, r8, r1
 8000d60:	fa06 f202 	lsl.w	r2, r6, r2
 8000d64:	4311      	orrs	r1, r2
 8000d66:	fa26 f303 	lsr.w	r3, r6, r3
 8000d6a:	4319      	orrs	r1, r3
 8000d6c:	b289      	uxth	r1, r1
 8000d6e:	eb07 0044 	add.w	r0, r7, r4, lsl #1
 8000d72:	f7ff ff2b 	bl	8000bcc <FLASH_Program_HalfWord>
        status = FLASH_WaitForLastOperation(FLASH_TIMEOUT_VALUE);
 8000d76:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000d7a:	f7ff ff91 	bl	8000ca0 <FLASH_WaitForLastOperation>
        CLEAR_BIT(FLASH->CR, FLASH_CR_PG);
 8000d7e:	4b09      	ldr	r3, [pc, #36]	@ (8000da4 <HAL_FLASH_Program+0x9c>)
 8000d80:	691d      	ldr	r5, [r3, #16]
 8000d82:	f025 0501 	bic.w	r5, r5, #1
 8000d86:	611d      	str	r5, [r3, #16]
      if (status != HAL_OK)
 8000d88:	4603      	mov	r3, r0
 8000d8a:	2800      	cmp	r0, #0
 8000d8c:	d0dd      	beq.n	8000d4a <HAL_FLASH_Program+0x42>
  __HAL_UNLOCK(&pFlash);
 8000d8e:	4a04      	ldr	r2, [pc, #16]	@ (8000da0 <HAL_FLASH_Program+0x98>)
 8000d90:	2100      	movs	r1, #0
 8000d92:	7611      	strb	r1, [r2, #24]
}
 8000d94:	4618      	mov	r0, r3
 8000d96:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
  __HAL_LOCK(&pFlash);
 8000d9a:	2302      	movs	r3, #2
 8000d9c:	e7fa      	b.n	8000d94 <HAL_FLASH_Program+0x8c>
 8000d9e:	bf00      	nop
 8000da0:	******** 	.word	0x********
 8000da4:	******** 	.word	0x********

08000da8 <FLASH_MassErase>:
{
  /* Check the parameters */
  assert_param(IS_FLASH_BANK(Banks));

  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 8000da8:	4b06      	ldr	r3, [pc, #24]	@ (8000dc4 <FLASH_MassErase+0x1c>)
 8000daa:	2200      	movs	r2, #0
 8000dac:	61da      	str	r2, [r3, #28]
#if !defined(FLASH_BANK2_END)
  /* Prevent unused argument(s) compilation warning */
  UNUSED(Banks);
#endif /* FLASH_BANK2_END */  
    /* Only bank1 will be erased*/
    SET_BIT(FLASH->CR, FLASH_CR_MER);
 8000dae:	4b06      	ldr	r3, [pc, #24]	@ (8000dc8 <FLASH_MassErase+0x20>)
 8000db0:	691a      	ldr	r2, [r3, #16]
 8000db2:	f042 0204 	orr.w	r2, r2, #4
 8000db6:	611a      	str	r2, [r3, #16]
    SET_BIT(FLASH->CR, FLASH_CR_STRT);
 8000db8:	691a      	ldr	r2, [r3, #16]
 8000dba:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8000dbe:	611a      	str	r2, [r3, #16]
#if defined(FLASH_BANK2_END)
  }
#endif /* FLASH_BANK2_END */
}
 8000dc0:	4770      	bx	lr
 8000dc2:	bf00      	nop
 8000dc4:	******** 	.word	0x********
 8000dc8:	******** 	.word	0x********

08000dcc <FLASH_PageErase>:
  * @retval None
  */
void FLASH_PageErase(uint32_t PageAddress)
{
  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 8000dcc:	4b06      	ldr	r3, [pc, #24]	@ (8000de8 <FLASH_PageErase+0x1c>)
 8000dce:	2200      	movs	r2, #0
 8000dd0:	61da      	str	r2, [r3, #28]
  }
  else
  {
#endif /* FLASH_BANK2_END */
    /* Proceed to erase the page */
    SET_BIT(FLASH->CR, FLASH_CR_PER);
 8000dd2:	4b06      	ldr	r3, [pc, #24]	@ (8000dec <FLASH_PageErase+0x20>)
 8000dd4:	691a      	ldr	r2, [r3, #16]
 8000dd6:	f042 0202 	orr.w	r2, r2, #2
 8000dda:	611a      	str	r2, [r3, #16]
    WRITE_REG(FLASH->AR, PageAddress);
 8000ddc:	6158      	str	r0, [r3, #20]
    SET_BIT(FLASH->CR, FLASH_CR_STRT);
 8000dde:	691a      	ldr	r2, [r3, #16]
 8000de0:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8000de4:	611a      	str	r2, [r3, #16]
#if defined(FLASH_BANK2_END)
  }
#endif /* FLASH_BANK2_END */
}
 8000de6:	4770      	bx	lr
 8000de8:	******** 	.word	0x********
 8000dec:	******** 	.word	0x********

08000df0 <HAL_FLASHEx_Erase>:
  __HAL_LOCK(&pFlash);
 8000df0:	4b26      	ldr	r3, [pc, #152]	@ (8000e8c <HAL_FLASHEx_Erase+0x9c>)
 8000df2:	7e1b      	ldrb	r3, [r3, #24]
 8000df4:	2b01      	cmp	r3, #1
 8000df6:	d046      	beq.n	8000e86 <HAL_FLASHEx_Erase+0x96>
{
 8000df8:	b570      	push	{r4, r5, r6, lr}
 8000dfa:	4605      	mov	r5, r0
 8000dfc:	460e      	mov	r6, r1
  __HAL_LOCK(&pFlash);
 8000dfe:	4b23      	ldr	r3, [pc, #140]	@ (8000e8c <HAL_FLASHEx_Erase+0x9c>)
 8000e00:	2201      	movs	r2, #1
 8000e02:	761a      	strb	r2, [r3, #24]
  if (pEraseInit->TypeErase == FLASH_TYPEERASE_MASSERASE)
 8000e04:	6803      	ldr	r3, [r0, #0]
 8000e06:	2b02      	cmp	r3, #2
 8000e08:	d020      	beq.n	8000e4c <HAL_FLASHEx_Erase+0x5c>
      if (FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE) == HAL_OK)
 8000e0a:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000e0e:	f7ff ff47 	bl	8000ca0 <FLASH_WaitForLastOperation>
 8000e12:	bb90      	cbnz	r0, 8000e7a <HAL_FLASHEx_Erase+0x8a>
        *PageError = 0xFFFFFFFFU;
 8000e14:	f04f 33ff 	mov.w	r3, #4294967295
 8000e18:	6033      	str	r3, [r6, #0]
        for(address = pEraseInit->PageAddress;
 8000e1a:	68ac      	ldr	r4, [r5, #8]
  HAL_StatusTypeDef status = HAL_ERROR;
 8000e1c:	2101      	movs	r1, #1
            address < ((pEraseInit->NbPages * FLASH_PAGE_SIZE) + pEraseInit->PageAddress);
 8000e1e:	68ea      	ldr	r2, [r5, #12]
 8000e20:	68ab      	ldr	r3, [r5, #8]
 8000e22:	eb03 2382 	add.w	r3, r3, r2, lsl #10
 8000e26:	42a3      	cmp	r3, r4
 8000e28:	d928      	bls.n	8000e7c <HAL_FLASHEx_Erase+0x8c>
          FLASH_PageErase(address);
 8000e2a:	4620      	mov	r0, r4
 8000e2c:	f7ff ffce 	bl	8000dcc <FLASH_PageErase>
          status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE);
 8000e30:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000e34:	f7ff ff34 	bl	8000ca0 <FLASH_WaitForLastOperation>
          CLEAR_BIT(FLASH->CR, FLASH_CR_PER);
 8000e38:	4a15      	ldr	r2, [pc, #84]	@ (8000e90 <HAL_FLASHEx_Erase+0xa0>)
 8000e3a:	6913      	ldr	r3, [r2, #16]
 8000e3c:	f023 0302 	bic.w	r3, r3, #2
 8000e40:	6113      	str	r3, [r2, #16]
          if (status != HAL_OK)
 8000e42:	4601      	mov	r1, r0
 8000e44:	b9b8      	cbnz	r0, 8000e76 <HAL_FLASHEx_Erase+0x86>
            address += FLASH_PAGE_SIZE)
 8000e46:	f504 6480 	add.w	r4, r4, #1024	@ 0x400
 8000e4a:	e7e8      	b.n	8000e1e <HAL_FLASHEx_Erase+0x2e>
      if (FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE) == HAL_OK)
 8000e4c:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000e50:	f7ff ff26 	bl	8000ca0 <FLASH_WaitForLastOperation>
 8000e54:	b108      	cbz	r0, 8000e5a <HAL_FLASHEx_Erase+0x6a>
  HAL_StatusTypeDef status = HAL_ERROR;
 8000e56:	2101      	movs	r1, #1
 8000e58:	e010      	b.n	8000e7c <HAL_FLASHEx_Erase+0x8c>
        FLASH_MassErase(FLASH_BANK_1);
 8000e5a:	2001      	movs	r0, #1
 8000e5c:	f7ff ffa4 	bl	8000da8 <FLASH_MassErase>
        status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE);
 8000e60:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000e64:	f7ff ff1c 	bl	8000ca0 <FLASH_WaitForLastOperation>
 8000e68:	4601      	mov	r1, r0
        CLEAR_BIT(FLASH->CR, FLASH_CR_MER);
 8000e6a:	4a09      	ldr	r2, [pc, #36]	@ (8000e90 <HAL_FLASHEx_Erase+0xa0>)
 8000e6c:	6913      	ldr	r3, [r2, #16]
 8000e6e:	f023 0304 	bic.w	r3, r3, #4
 8000e72:	6113      	str	r3, [r2, #16]
 8000e74:	e002      	b.n	8000e7c <HAL_FLASHEx_Erase+0x8c>
            *PageError = address;
 8000e76:	6034      	str	r4, [r6, #0]
            break;
 8000e78:	e000      	b.n	8000e7c <HAL_FLASHEx_Erase+0x8c>
  HAL_StatusTypeDef status = HAL_ERROR;
 8000e7a:	2101      	movs	r1, #1
  __HAL_UNLOCK(&pFlash);
 8000e7c:	4b03      	ldr	r3, [pc, #12]	@ (8000e8c <HAL_FLASHEx_Erase+0x9c>)
 8000e7e:	2200      	movs	r2, #0
 8000e80:	761a      	strb	r2, [r3, #24]
}
 8000e82:	4608      	mov	r0, r1
 8000e84:	bd70      	pop	{r4, r5, r6, pc}
  __HAL_LOCK(&pFlash);
 8000e86:	2102      	movs	r1, #2
}
 8000e88:	4608      	mov	r0, r1
 8000e8a:	4770      	bx	lr
 8000e8c:	******** 	.word	0x********
 8000e90:	******** 	.word	0x********

08000e94 <HAL_GPIO_Init>:
  * @param  GPIO_Init: pointer to a GPIO_InitTypeDef structure that contains
  *         the configuration information for the specified GPIO peripheral.
  * @retval None
  */
void HAL_GPIO_Init(GPIO_TypeDef  *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
 8000e94:	b570      	push	{r4, r5, r6, lr}
 8000e96:	b082      	sub	sp, #8
  uint32_t position = 0x00u;
  uint32_t ioposition;
  uint32_t iocurrent;
  uint32_t temp;
  uint32_t config = 0x00u;
 8000e98:	2400      	movs	r4, #0
  uint32_t position = 0x00u;
 8000e9a:	46a4      	mov	ip, r4
  assert_param(IS_GPIO_ALL_INSTANCE(GPIOx));
  assert_param(IS_GPIO_PIN(GPIO_Init->Pin));
  assert_param(IS_GPIO_MODE(GPIO_Init->Mode));

  /* Configure the port pins */
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 8000e9c:	e0a2      	b.n	8000fe4 <HAL_GPIO_Init+0x150>
    {
      /* Check the Alternate function parameters */
      assert_param(IS_GPIO_AF_INSTANCE(GPIOx));

      /* Based on the required mode, filling config variable with MODEy[1:0] and CNFy[3:2] corresponding bits */
      switch (GPIO_Init->Mode)
 8000e9e:	4d7e      	ldr	r5, [pc, #504]	@ (8001098 <HAL_GPIO_Init+0x204>)
 8000ea0:	42ab      	cmp	r3, r5
 8000ea2:	d010      	beq.n	8000ec6 <HAL_GPIO_Init+0x32>
 8000ea4:	d907      	bls.n	8000eb6 <HAL_GPIO_Init+0x22>
 8000ea6:	4d7d      	ldr	r5, [pc, #500]	@ (800109c <HAL_GPIO_Init+0x208>)
 8000ea8:	42ab      	cmp	r3, r5
 8000eaa:	d00c      	beq.n	8000ec6 <HAL_GPIO_Init+0x32>
 8000eac:	f505 3580 	add.w	r5, r5, #65536	@ 0x10000
 8000eb0:	42ab      	cmp	r3, r5
 8000eb2:	d008      	beq.n	8000ec6 <HAL_GPIO_Init+0x32>
 8000eb4:	e013      	b.n	8000ede <HAL_GPIO_Init+0x4a>
 8000eb6:	f5a5 1580 	sub.w	r5, r5, #1048576	@ 0x100000
 8000eba:	42ab      	cmp	r3, r5
 8000ebc:	d003      	beq.n	8000ec6 <HAL_GPIO_Init+0x32>
 8000ebe:	f505 2570 	add.w	r5, r5, #983040	@ 0xf0000
 8000ec2:	42ab      	cmp	r3, r5
 8000ec4:	d107      	bne.n	8000ed6 <HAL_GPIO_Init+0x42>
        case GPIO_MODE_EVT_RISING:
        case GPIO_MODE_EVT_FALLING:
        case GPIO_MODE_EVT_RISING_FALLING:
          /* Check the GPIO pull parameter */
          assert_param(IS_GPIO_PULL(GPIO_Init->Pull));
          if (GPIO_Init->Pull == GPIO_NOPULL)
 8000ec6:	688b      	ldr	r3, [r1, #8]
 8000ec8:	2b00      	cmp	r3, #0
 8000eca:	d055      	beq.n	8000f78 <HAL_GPIO_Init+0xe4>
          {
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_FLOATING;
          }
          else if (GPIO_Init->Pull == GPIO_PULLUP)
 8000ecc:	2b01      	cmp	r3, #1
 8000ece:	d04e      	beq.n	8000f6e <HAL_GPIO_Init+0xda>
          else /* GPIO_PULLDOWN */
          {
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;

            /* Reset the corresponding ODR bit */
            GPIOx->BRR = ioposition;
 8000ed0:	6142      	str	r2, [r0, #20]
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;
 8000ed2:	2408      	movs	r4, #8
 8000ed4:	e003      	b.n	8000ede <HAL_GPIO_Init+0x4a>
      switch (GPIO_Init->Mode)
 8000ed6:	f5a5 1580 	sub.w	r5, r5, #1048576	@ 0x100000
 8000eda:	42ab      	cmp	r3, r5
 8000edc:	d0f3      	beq.n	8000ec6 <HAL_GPIO_Init+0x32>
          break;
      }

      /* Check if the current bit belongs to first half or last half of the pin count number
       in order to address CRH or CRL register*/
      configregister = (iocurrent < GPIO_PIN_8) ? &GPIOx->CRL     : &GPIOx->CRH;
 8000ede:	f1be 0fff 	cmp.w	lr, #255	@ 0xff
 8000ee2:	d84b      	bhi.n	8000f7c <HAL_GPIO_Init+0xe8>
 8000ee4:	4606      	mov	r6, r0
      registeroffset = (iocurrent < GPIO_PIN_8) ? (position << 2u) : ((position - 8u) << 2u);
 8000ee6:	ea4f 028c 	mov.w	r2, ip, lsl #2

      /* Apply the new configuration of the pin to the register */
      MODIFY_REG((*configregister), ((GPIO_CRL_MODE0 | GPIO_CRL_CNF0) << registeroffset), (config << registeroffset));
 8000eea:	6833      	ldr	r3, [r6, #0]
 8000eec:	250f      	movs	r5, #15
 8000eee:	4095      	lsls	r5, r2
 8000ef0:	ea23 0305 	bic.w	r3, r3, r5
 8000ef4:	fa04 f202 	lsl.w	r2, r4, r2
 8000ef8:	4313      	orrs	r3, r2
 8000efa:	6033      	str	r3, [r6, #0]

      /*--------------------- EXTI Mode Configuration ------------------------*/
      /* Configure the External Interrupt or event for the current IO */
      if ((GPIO_Init->Mode & EXTI_MODE) == EXTI_MODE)
 8000efc:	684b      	ldr	r3, [r1, #4]
 8000efe:	f013 5f80 	tst.w	r3, #268435456	@ 0x10000000
 8000f02:	d06d      	beq.n	8000fe0 <HAL_GPIO_Init+0x14c>
      {
        /* Enable AFIO Clock */
        __HAL_RCC_AFIO_CLK_ENABLE();
 8000f04:	4b66      	ldr	r3, [pc, #408]	@ (80010a0 <HAL_GPIO_Init+0x20c>)
 8000f06:	699a      	ldr	r2, [r3, #24]
 8000f08:	f042 0201 	orr.w	r2, r2, #1
 8000f0c:	619a      	str	r2, [r3, #24]
 8000f0e:	699b      	ldr	r3, [r3, #24]
 8000f10:	f003 0301 	and.w	r3, r3, #1
 8000f14:	9301      	str	r3, [sp, #4]
 8000f16:	9b01      	ldr	r3, [sp, #4]
        temp = AFIO->EXTICR[position >> 2u];
 8000f18:	ea4f 029c 	mov.w	r2, ip, lsr #2
 8000f1c:	1c95      	adds	r5, r2, #2
 8000f1e:	4b61      	ldr	r3, [pc, #388]	@ (80010a4 <HAL_GPIO_Init+0x210>)
 8000f20:	f853 6025 	ldr.w	r6, [r3, r5, lsl #2]
        CLEAR_BIT(temp, (0x0Fu) << (4u * (position & 0x03u)));
 8000f24:	f00c 0503 	and.w	r5, ip, #3
 8000f28:	00ad      	lsls	r5, r5, #2
 8000f2a:	230f      	movs	r3, #15
 8000f2c:	40ab      	lsls	r3, r5
 8000f2e:	ea26 0603 	bic.w	r6, r6, r3
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 8000f32:	4b5d      	ldr	r3, [pc, #372]	@ (80010a8 <HAL_GPIO_Init+0x214>)
 8000f34:	4298      	cmp	r0, r3
 8000f36:	d028      	beq.n	8000f8a <HAL_GPIO_Init+0xf6>
 8000f38:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 8000f3c:	4298      	cmp	r0, r3
 8000f3e:	f000 808d 	beq.w	800105c <HAL_GPIO_Init+0x1c8>
 8000f42:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 8000f46:	4298      	cmp	r0, r3
 8000f48:	f000 808a 	beq.w	8001060 <HAL_GPIO_Init+0x1cc>
 8000f4c:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 8000f50:	4298      	cmp	r0, r3
 8000f52:	d018      	beq.n	8000f86 <HAL_GPIO_Init+0xf2>
 8000f54:	2304      	movs	r3, #4
 8000f56:	e019      	b.n	8000f8c <HAL_GPIO_Init+0xf8>
          config = GPIO_Init->Speed + GPIO_CR_CNF_GP_OUTPUT_PP;
 8000f58:	68cc      	ldr	r4, [r1, #12]
          break;
 8000f5a:	e7c0      	b.n	8000ede <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_GP_OUTPUT_OD;
 8000f5c:	68cc      	ldr	r4, [r1, #12]
 8000f5e:	3404      	adds	r4, #4
          break;
 8000f60:	e7bd      	b.n	8000ede <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_AF_OUTPUT_PP;
 8000f62:	68cc      	ldr	r4, [r1, #12]
 8000f64:	3408      	adds	r4, #8
          break;
 8000f66:	e7ba      	b.n	8000ede <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_AF_OUTPUT_OD;
 8000f68:	68cc      	ldr	r4, [r1, #12]
 8000f6a:	340c      	adds	r4, #12
          break;
 8000f6c:	e7b7      	b.n	8000ede <HAL_GPIO_Init+0x4a>
            GPIOx->BSRR = ioposition;
 8000f6e:	6102      	str	r2, [r0, #16]
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;
 8000f70:	2408      	movs	r4, #8
 8000f72:	e7b4      	b.n	8000ede <HAL_GPIO_Init+0x4a>
          config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_ANALOG;
 8000f74:	2400      	movs	r4, #0
 8000f76:	e7b2      	b.n	8000ede <HAL_GPIO_Init+0x4a>
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_FLOATING;
 8000f78:	2404      	movs	r4, #4
 8000f7a:	e7b0      	b.n	8000ede <HAL_GPIO_Init+0x4a>
      configregister = (iocurrent < GPIO_PIN_8) ? &GPIOx->CRL     : &GPIOx->CRH;
 8000f7c:	1d06      	adds	r6, r0, #4
      registeroffset = (iocurrent < GPIO_PIN_8) ? (position << 2u) : ((position - 8u) << 2u);
 8000f7e:	f1ac 0208 	sub.w	r2, ip, #8
 8000f82:	0092      	lsls	r2, r2, #2
 8000f84:	e7b1      	b.n	8000eea <HAL_GPIO_Init+0x56>
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 8000f86:	2303      	movs	r3, #3
 8000f88:	e000      	b.n	8000f8c <HAL_GPIO_Init+0xf8>
 8000f8a:	2300      	movs	r3, #0
 8000f8c:	40ab      	lsls	r3, r5
 8000f8e:	4333      	orrs	r3, r6
        AFIO->EXTICR[position >> 2u] = temp;
 8000f90:	3202      	adds	r2, #2
 8000f92:	4d44      	ldr	r5, [pc, #272]	@ (80010a4 <HAL_GPIO_Init+0x210>)
 8000f94:	f845 3022 	str.w	r3, [r5, r2, lsl #2]


        /* Enable or disable the rising trigger */
        if ((GPIO_Init->Mode & RISING_EDGE) == RISING_EDGE)
 8000f98:	684b      	ldr	r3, [r1, #4]
 8000f9a:	f413 1f80 	tst.w	r3, #1048576	@ 0x100000
 8000f9e:	d061      	beq.n	8001064 <HAL_GPIO_Init+0x1d0>
        {
          SET_BIT(EXTI->RTSR, iocurrent);
 8000fa0:	4a42      	ldr	r2, [pc, #264]	@ (80010ac <HAL_GPIO_Init+0x218>)
 8000fa2:	6893      	ldr	r3, [r2, #8]
 8000fa4:	ea43 030e 	orr.w	r3, r3, lr
 8000fa8:	6093      	str	r3, [r2, #8]
        {
          CLEAR_BIT(EXTI->RTSR, iocurrent);
        }

        /* Enable or disable the falling trigger */
        if ((GPIO_Init->Mode & FALLING_EDGE) == FALLING_EDGE)
 8000faa:	684b      	ldr	r3, [r1, #4]
 8000fac:	f413 1f00 	tst.w	r3, #2097152	@ 0x200000
 8000fb0:	d05e      	beq.n	8001070 <HAL_GPIO_Init+0x1dc>
        {
          SET_BIT(EXTI->FTSR, iocurrent);
 8000fb2:	4a3e      	ldr	r2, [pc, #248]	@ (80010ac <HAL_GPIO_Init+0x218>)
 8000fb4:	68d3      	ldr	r3, [r2, #12]
 8000fb6:	ea43 030e 	orr.w	r3, r3, lr
 8000fba:	60d3      	str	r3, [r2, #12]
        {
          CLEAR_BIT(EXTI->FTSR, iocurrent);
        }

        /* Configure the event mask */
        if ((GPIO_Init->Mode & GPIO_MODE_EVT) == GPIO_MODE_EVT)
 8000fbc:	684b      	ldr	r3, [r1, #4]
 8000fbe:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8000fc2:	d05b      	beq.n	800107c <HAL_GPIO_Init+0x1e8>
        {
          SET_BIT(EXTI->EMR, iocurrent);
 8000fc4:	4a39      	ldr	r2, [pc, #228]	@ (80010ac <HAL_GPIO_Init+0x218>)
 8000fc6:	6853      	ldr	r3, [r2, #4]
 8000fc8:	ea43 030e 	orr.w	r3, r3, lr
 8000fcc:	6053      	str	r3, [r2, #4]
        {
          CLEAR_BIT(EXTI->EMR, iocurrent);
        }

        /* Configure the interrupt mask */
        if ((GPIO_Init->Mode & GPIO_MODE_IT) == GPIO_MODE_IT)
 8000fce:	684b      	ldr	r3, [r1, #4]
 8000fd0:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8000fd4:	d058      	beq.n	8001088 <HAL_GPIO_Init+0x1f4>
        {
          SET_BIT(EXTI->IMR, iocurrent);
 8000fd6:	4a35      	ldr	r2, [pc, #212]	@ (80010ac <HAL_GPIO_Init+0x218>)
 8000fd8:	6813      	ldr	r3, [r2, #0]
 8000fda:	ea43 030e 	orr.w	r3, r3, lr
 8000fde:	6013      	str	r3, [r2, #0]
          CLEAR_BIT(EXTI->IMR, iocurrent);
        }
      }
    }

	position++;
 8000fe0:	f10c 0c01 	add.w	ip, ip, #1
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 8000fe4:	680b      	ldr	r3, [r1, #0]
 8000fe6:	fa33 f20c 	lsrs.w	r2, r3, ip
 8000fea:	d053      	beq.n	8001094 <HAL_GPIO_Init+0x200>
    ioposition = (0x01uL << position);
 8000fec:	2201      	movs	r2, #1
 8000fee:	fa02 f20c 	lsl.w	r2, r2, ip
    iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;
 8000ff2:	ea03 0e02 	and.w	lr, r3, r2
    if (iocurrent == ioposition)
 8000ff6:	ea32 0303 	bics.w	r3, r2, r3
 8000ffa:	d1f1      	bne.n	8000fe0 <HAL_GPIO_Init+0x14c>
      switch (GPIO_Init->Mode)
 8000ffc:	684b      	ldr	r3, [r1, #4]
 8000ffe:	2b12      	cmp	r3, #18
 8001000:	f63f af4d 	bhi.w	8000e9e <HAL_GPIO_Init+0xa>
 8001004:	2b12      	cmp	r3, #18
 8001006:	f63f af6a 	bhi.w	8000ede <HAL_GPIO_Init+0x4a>
 800100a:	a501      	add	r5, pc, #4	@ (adr r5, 8001010 <HAL_GPIO_Init+0x17c>)
 800100c:	f855 f023 	ldr.w	pc, [r5, r3, lsl #2]
 8001010:	08000ec7 	.word	0x08000ec7
 8001014:	08000f59 	.word	0x08000f59
 8001018:	08000f63 	.word	0x08000f63
 800101c:	08000f75 	.word	0x08000f75
 8001020:	08000edf 	.word	0x08000edf
 8001024:	08000edf 	.word	0x08000edf
 8001028:	08000edf 	.word	0x08000edf
 800102c:	08000edf 	.word	0x08000edf
 8001030:	08000edf 	.word	0x08000edf
 8001034:	08000edf 	.word	0x08000edf
 8001038:	08000edf 	.word	0x08000edf
 800103c:	08000edf 	.word	0x08000edf
 8001040:	08000edf 	.word	0x08000edf
 8001044:	08000edf 	.word	0x08000edf
 8001048:	08000edf 	.word	0x08000edf
 800104c:	08000edf 	.word	0x08000edf
 8001050:	08000edf 	.word	0x08000edf
 8001054:	08000f5d 	.word	0x08000f5d
 8001058:	08000f69 	.word	0x08000f69
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 800105c:	2301      	movs	r3, #1
 800105e:	e795      	b.n	8000f8c <HAL_GPIO_Init+0xf8>
 8001060:	2302      	movs	r3, #2
 8001062:	e793      	b.n	8000f8c <HAL_GPIO_Init+0xf8>
          CLEAR_BIT(EXTI->RTSR, iocurrent);
 8001064:	4a11      	ldr	r2, [pc, #68]	@ (80010ac <HAL_GPIO_Init+0x218>)
 8001066:	6893      	ldr	r3, [r2, #8]
 8001068:	ea23 030e 	bic.w	r3, r3, lr
 800106c:	6093      	str	r3, [r2, #8]
 800106e:	e79c      	b.n	8000faa <HAL_GPIO_Init+0x116>
          CLEAR_BIT(EXTI->FTSR, iocurrent);
 8001070:	4a0e      	ldr	r2, [pc, #56]	@ (80010ac <HAL_GPIO_Init+0x218>)
 8001072:	68d3      	ldr	r3, [r2, #12]
 8001074:	ea23 030e 	bic.w	r3, r3, lr
 8001078:	60d3      	str	r3, [r2, #12]
 800107a:	e79f      	b.n	8000fbc <HAL_GPIO_Init+0x128>
          CLEAR_BIT(EXTI->EMR, iocurrent);
 800107c:	4a0b      	ldr	r2, [pc, #44]	@ (80010ac <HAL_GPIO_Init+0x218>)
 800107e:	6853      	ldr	r3, [r2, #4]
 8001080:	ea23 030e 	bic.w	r3, r3, lr
 8001084:	6053      	str	r3, [r2, #4]
 8001086:	e7a2      	b.n	8000fce <HAL_GPIO_Init+0x13a>
          CLEAR_BIT(EXTI->IMR, iocurrent);
 8001088:	4a08      	ldr	r2, [pc, #32]	@ (80010ac <HAL_GPIO_Init+0x218>)
 800108a:	6813      	ldr	r3, [r2, #0]
 800108c:	ea23 030e 	bic.w	r3, r3, lr
 8001090:	6013      	str	r3, [r2, #0]
 8001092:	e7a5      	b.n	8000fe0 <HAL_GPIO_Init+0x14c>
  }
}
 8001094:	b002      	add	sp, #8
 8001096:	bd70      	pop	{r4, r5, r6, pc}
 8001098:	10220000 	.word	0x10220000
 800109c:	10310000 	.word	0x10310000
 80010a0:	40021000 	.word	0x40021000
 80010a4:	40010000 	.word	0x40010000
 80010a8:	40010800 	.word	0x40010800
 80010ac:	40010400 	.word	0x40010400

080010b0 <HAL_GPIO_WritePin>:
{
  /* Check the parameters */
  assert_param(IS_GPIO_PIN(GPIO_Pin));
  assert_param(IS_GPIO_PIN_ACTION(PinState));

  if (PinState != GPIO_PIN_RESET)
 80010b0:	b10a      	cbz	r2, 80010b6 <HAL_GPIO_WritePin+0x6>
  {
    GPIOx->BSRR = GPIO_Pin;
 80010b2:	6101      	str	r1, [r0, #16]
 80010b4:	4770      	bx	lr
  }
  else
  {
    GPIOx->BSRR = (uint32_t)GPIO_Pin << 16u;
 80010b6:	0409      	lsls	r1, r1, #16
 80010b8:	6101      	str	r1, [r0, #16]
  }
}
 80010ba:	4770      	bx	lr

080010bc <HAL_GPIO_TogglePin>:

  /* Check the parameters */
  assert_param(IS_GPIO_PIN(GPIO_Pin));

  /* get current Output Data Register value */
  odr = GPIOx->ODR;
 80010bc:	68c3      	ldr	r3, [r0, #12]

  /* Set selected pins that were at low level, and reset ones that were high */
  GPIOx->BSRR = ((odr & GPIO_Pin) << GPIO_NUMBER) | (~odr & GPIO_Pin);
 80010be:	ea01 0203 	and.w	r2, r1, r3
 80010c2:	ea21 0103 	bic.w	r1, r1, r3
 80010c6:	ea41 4102 	orr.w	r1, r1, r2, lsl #16
 80010ca:	6101      	str	r1, [r0, #16]
}
 80010cc:	4770      	bx	lr
	...

080010d0 <RCC_Delay>:
  * @brief  This function provides delay (in milliseconds) based on CPU cycles method.
  * @param  mdelay: specifies the delay time length, in milliseconds.
  * @retval None
  */
static void RCC_Delay(uint32_t mdelay)
{
 80010d0:	b082      	sub	sp, #8
  __IO uint32_t Delay = mdelay * (SystemCoreClock / 8U / 1000U);
 80010d2:	4b08      	ldr	r3, [pc, #32]	@ (80010f4 <RCC_Delay+0x24>)
 80010d4:	681b      	ldr	r3, [r3, #0]
 80010d6:	4a08      	ldr	r2, [pc, #32]	@ (80010f8 <RCC_Delay+0x28>)
 80010d8:	fba2 2303 	umull	r2, r3, r2, r3
 80010dc:	0a5b      	lsrs	r3, r3, #9
 80010de:	fb00 f303 	mul.w	r3, r0, r3
 80010e2:	9301      	str	r3, [sp, #4]
  do
  {
    __NOP();
 80010e4:	bf00      	nop
  }
  while (Delay --);
 80010e6:	9b01      	ldr	r3, [sp, #4]
 80010e8:	1e5a      	subs	r2, r3, #1
 80010ea:	9201      	str	r2, [sp, #4]
 80010ec:	2b00      	cmp	r3, #0
 80010ee:	d1f9      	bne.n	80010e4 <RCC_Delay+0x14>
}
 80010f0:	b002      	add	sp, #8
 80010f2:	4770      	bx	lr
 80010f4:	20000004 	.word	0x20000004
 80010f8:	10624dd3 	.word	0x10624dd3

080010fc <HAL_RCC_OscConfig>:
  if (RCC_OscInitStruct == NULL)
 80010fc:	2800      	cmp	r0, #0
 80010fe:	f000 81f1 	beq.w	80014e4 <HAL_RCC_OscConfig+0x3e8>
{
 8001102:	b570      	push	{r4, r5, r6, lr}
 8001104:	b082      	sub	sp, #8
 8001106:	4604      	mov	r4, r0
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSE) == RCC_OSCILLATORTYPE_HSE)
 8001108:	6803      	ldr	r3, [r0, #0]
 800110a:	f013 0f01 	tst.w	r3, #1
 800110e:	d02c      	beq.n	800116a <HAL_RCC_OscConfig+0x6e>
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSE)
 8001110:	4b99      	ldr	r3, [pc, #612]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 8001112:	685b      	ldr	r3, [r3, #4]
 8001114:	f003 030c 	and.w	r3, r3, #12
 8001118:	2b04      	cmp	r3, #4
 800111a:	d01d      	beq.n	8001158 <HAL_RCC_OscConfig+0x5c>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSE)))
 800111c:	4b96      	ldr	r3, [pc, #600]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 800111e:	685b      	ldr	r3, [r3, #4]
 8001120:	f003 030c 	and.w	r3, r3, #12
 8001124:	2b08      	cmp	r3, #8
 8001126:	d012      	beq.n	800114e <HAL_RCC_OscConfig+0x52>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8001128:	6863      	ldr	r3, [r4, #4]
 800112a:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 800112e:	d041      	beq.n	80011b4 <HAL_RCC_OscConfig+0xb8>
 8001130:	2b00      	cmp	r3, #0
 8001132:	d155      	bne.n	80011e0 <HAL_RCC_OscConfig+0xe4>
 8001134:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 8001138:	f503 3304 	add.w	r3, r3, #135168	@ 0x21000
 800113c:	681a      	ldr	r2, [r3, #0]
 800113e:	f422 3280 	bic.w	r2, r2, #65536	@ 0x10000
 8001142:	601a      	str	r2, [r3, #0]
 8001144:	681a      	ldr	r2, [r3, #0]
 8001146:	f422 2280 	bic.w	r2, r2, #262144	@ 0x40000
 800114a:	601a      	str	r2, [r3, #0]
 800114c:	e037      	b.n	80011be <HAL_RCC_OscConfig+0xc2>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSE)))
 800114e:	4b8a      	ldr	r3, [pc, #552]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 8001150:	685b      	ldr	r3, [r3, #4]
 8001152:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8001156:	d0e7      	beq.n	8001128 <HAL_RCC_OscConfig+0x2c>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET) && (RCC_OscInitStruct->HSEState == RCC_HSE_OFF))
 8001158:	4b87      	ldr	r3, [pc, #540]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 800115a:	681b      	ldr	r3, [r3, #0]
 800115c:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8001160:	d003      	beq.n	800116a <HAL_RCC_OscConfig+0x6e>
 8001162:	6863      	ldr	r3, [r4, #4]
 8001164:	2b00      	cmp	r3, #0
 8001166:	f000 81bf 	beq.w	80014e8 <HAL_RCC_OscConfig+0x3ec>
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSI) == RCC_OSCILLATORTYPE_HSI)
 800116a:	6823      	ldr	r3, [r4, #0]
 800116c:	f013 0f02 	tst.w	r3, #2
 8001170:	d075      	beq.n	800125e <HAL_RCC_OscConfig+0x162>
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSI)
 8001172:	4b81      	ldr	r3, [pc, #516]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 8001174:	685b      	ldr	r3, [r3, #4]
 8001176:	f013 0f0c 	tst.w	r3, #12
 800117a:	d05f      	beq.n	800123c <HAL_RCC_OscConfig+0x140>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSI_DIV2)))
 800117c:	4b7e      	ldr	r3, [pc, #504]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 800117e:	685b      	ldr	r3, [r3, #4]
 8001180:	f003 030c 	and.w	r3, r3, #12
 8001184:	2b08      	cmp	r3, #8
 8001186:	d054      	beq.n	8001232 <HAL_RCC_OscConfig+0x136>
      if (RCC_OscInitStruct->HSIState != RCC_HSI_OFF)
 8001188:	6923      	ldr	r3, [r4, #16]
 800118a:	2b00      	cmp	r3, #0
 800118c:	f000 808a 	beq.w	80012a4 <HAL_RCC_OscConfig+0x1a8>
        __HAL_RCC_HSI_ENABLE();
 8001190:	4b7a      	ldr	r3, [pc, #488]	@ (800137c <HAL_RCC_OscConfig+0x280>)
 8001192:	2201      	movs	r2, #1
 8001194:	601a      	str	r2, [r3, #0]
        tickstart = HAL_GetTick();
 8001196:	f7ff fc95 	bl	8000ac4 <HAL_GetTick>
 800119a:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 800119c:	4b76      	ldr	r3, [pc, #472]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 800119e:	681b      	ldr	r3, [r3, #0]
 80011a0:	f013 0f02 	tst.w	r3, #2
 80011a4:	d175      	bne.n	8001292 <HAL_RCC_OscConfig+0x196>
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 80011a6:	f7ff fc8d 	bl	8000ac4 <HAL_GetTick>
 80011aa:	1b40      	subs	r0, r0, r5
 80011ac:	2802      	cmp	r0, #2
 80011ae:	d9f5      	bls.n	800119c <HAL_RCC_OscConfig+0xa0>
            return HAL_TIMEOUT;
 80011b0:	2003      	movs	r0, #3
 80011b2:	e19e      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 80011b4:	4a70      	ldr	r2, [pc, #448]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 80011b6:	6813      	ldr	r3, [r2, #0]
 80011b8:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 80011bc:	6013      	str	r3, [r2, #0]
      if (RCC_OscInitStruct->HSEState != RCC_HSE_OFF)
 80011be:	6863      	ldr	r3, [r4, #4]
 80011c0:	b343      	cbz	r3, 8001214 <HAL_RCC_OscConfig+0x118>
        tickstart = HAL_GetTick();
 80011c2:	f7ff fc7f 	bl	8000ac4 <HAL_GetTick>
 80011c6:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 80011c8:	4b6b      	ldr	r3, [pc, #428]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 80011ca:	681b      	ldr	r3, [r3, #0]
 80011cc:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 80011d0:	d1cb      	bne.n	800116a <HAL_RCC_OscConfig+0x6e>
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 80011d2:	f7ff fc77 	bl	8000ac4 <HAL_GetTick>
 80011d6:	1b40      	subs	r0, r0, r5
 80011d8:	2864      	cmp	r0, #100	@ 0x64
 80011da:	d9f5      	bls.n	80011c8 <HAL_RCC_OscConfig+0xcc>
            return HAL_TIMEOUT;
 80011dc:	2003      	movs	r0, #3
 80011de:	e188      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 80011e0:	f5b3 2fa0 	cmp.w	r3, #327680	@ 0x50000
 80011e4:	d009      	beq.n	80011fa <HAL_RCC_OscConfig+0xfe>
 80011e6:	4b64      	ldr	r3, [pc, #400]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 80011e8:	681a      	ldr	r2, [r3, #0]
 80011ea:	f422 3280 	bic.w	r2, r2, #65536	@ 0x10000
 80011ee:	601a      	str	r2, [r3, #0]
 80011f0:	681a      	ldr	r2, [r3, #0]
 80011f2:	f422 2280 	bic.w	r2, r2, #262144	@ 0x40000
 80011f6:	601a      	str	r2, [r3, #0]
 80011f8:	e7e1      	b.n	80011be <HAL_RCC_OscConfig+0xc2>
 80011fa:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 80011fe:	f5a3 333c 	sub.w	r3, r3, #192512	@ 0x2f000
 8001202:	681a      	ldr	r2, [r3, #0]
 8001204:	f442 2280 	orr.w	r2, r2, #262144	@ 0x40000
 8001208:	601a      	str	r2, [r3, #0]
 800120a:	681a      	ldr	r2, [r3, #0]
 800120c:	f442 3280 	orr.w	r2, r2, #65536	@ 0x10000
 8001210:	601a      	str	r2, [r3, #0]
 8001212:	e7d4      	b.n	80011be <HAL_RCC_OscConfig+0xc2>
        tickstart = HAL_GetTick();
 8001214:	f7ff fc56 	bl	8000ac4 <HAL_GetTick>
 8001218:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 800121a:	4b57      	ldr	r3, [pc, #348]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 800121c:	681b      	ldr	r3, [r3, #0]
 800121e:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8001222:	d0a2      	beq.n	800116a <HAL_RCC_OscConfig+0x6e>
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 8001224:	f7ff fc4e 	bl	8000ac4 <HAL_GetTick>
 8001228:	1b40      	subs	r0, r0, r5
 800122a:	2864      	cmp	r0, #100	@ 0x64
 800122c:	d9f5      	bls.n	800121a <HAL_RCC_OscConfig+0x11e>
            return HAL_TIMEOUT;
 800122e:	2003      	movs	r0, #3
 8001230:	e15f      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSI_DIV2)))
 8001232:	4b51      	ldr	r3, [pc, #324]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 8001234:	685b      	ldr	r3, [r3, #4]
 8001236:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 800123a:	d1a5      	bne.n	8001188 <HAL_RCC_OscConfig+0x8c>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET) && (RCC_OscInitStruct->HSIState != RCC_HSI_ON))
 800123c:	4b4e      	ldr	r3, [pc, #312]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 800123e:	681b      	ldr	r3, [r3, #0]
 8001240:	f013 0f02 	tst.w	r3, #2
 8001244:	d003      	beq.n	800124e <HAL_RCC_OscConfig+0x152>
 8001246:	6923      	ldr	r3, [r4, #16]
 8001248:	2b01      	cmp	r3, #1
 800124a:	f040 814f 	bne.w	80014ec <HAL_RCC_OscConfig+0x3f0>
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 800124e:	4a4a      	ldr	r2, [pc, #296]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 8001250:	6813      	ldr	r3, [r2, #0]
 8001252:	f023 03f8 	bic.w	r3, r3, #248	@ 0xf8
 8001256:	6961      	ldr	r1, [r4, #20]
 8001258:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 800125c:	6013      	str	r3, [r2, #0]
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 800125e:	6823      	ldr	r3, [r4, #0]
 8001260:	f013 0f08 	tst.w	r3, #8
 8001264:	d033      	beq.n	80012ce <HAL_RCC_OscConfig+0x1d2>
    if (RCC_OscInitStruct->LSIState != RCC_LSI_OFF)
 8001266:	69a3      	ldr	r3, [r4, #24]
 8001268:	2b00      	cmp	r3, #0
 800126a:	d05c      	beq.n	8001326 <HAL_RCC_OscConfig+0x22a>
      __HAL_RCC_LSI_ENABLE();
 800126c:	4b43      	ldr	r3, [pc, #268]	@ (800137c <HAL_RCC_OscConfig+0x280>)
 800126e:	2201      	movs	r2, #1
 8001270:	f8c3 2480 	str.w	r2, [r3, #1152]	@ 0x480
      tickstart = HAL_GetTick();
 8001274:	f7ff fc26 	bl	8000ac4 <HAL_GetTick>
 8001278:	4605      	mov	r5, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 800127a:	4b3f      	ldr	r3, [pc, #252]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 800127c:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 800127e:	f013 0f02 	tst.w	r3, #2
 8001282:	d121      	bne.n	80012c8 <HAL_RCC_OscConfig+0x1cc>
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 8001284:	f7ff fc1e 	bl	8000ac4 <HAL_GetTick>
 8001288:	1b40      	subs	r0, r0, r5
 800128a:	2802      	cmp	r0, #2
 800128c:	d9f5      	bls.n	800127a <HAL_RCC_OscConfig+0x17e>
          return HAL_TIMEOUT;
 800128e:	2003      	movs	r0, #3
 8001290:	e12f      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 8001292:	4a39      	ldr	r2, [pc, #228]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 8001294:	6813      	ldr	r3, [r2, #0]
 8001296:	f023 03f8 	bic.w	r3, r3, #248	@ 0xf8
 800129a:	6961      	ldr	r1, [r4, #20]
 800129c:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 80012a0:	6013      	str	r3, [r2, #0]
 80012a2:	e7dc      	b.n	800125e <HAL_RCC_OscConfig+0x162>
        __HAL_RCC_HSI_DISABLE();
 80012a4:	4b35      	ldr	r3, [pc, #212]	@ (800137c <HAL_RCC_OscConfig+0x280>)
 80012a6:	2200      	movs	r2, #0
 80012a8:	601a      	str	r2, [r3, #0]
        tickstart = HAL_GetTick();
 80012aa:	f7ff fc0b 	bl	8000ac4 <HAL_GetTick>
 80012ae:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 80012b0:	4b31      	ldr	r3, [pc, #196]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 80012b2:	681b      	ldr	r3, [r3, #0]
 80012b4:	f013 0f02 	tst.w	r3, #2
 80012b8:	d0d1      	beq.n	800125e <HAL_RCC_OscConfig+0x162>
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 80012ba:	f7ff fc03 	bl	8000ac4 <HAL_GetTick>
 80012be:	1b40      	subs	r0, r0, r5
 80012c0:	2802      	cmp	r0, #2
 80012c2:	d9f5      	bls.n	80012b0 <HAL_RCC_OscConfig+0x1b4>
            return HAL_TIMEOUT;
 80012c4:	2003      	movs	r0, #3
 80012c6:	e114      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
      RCC_Delay(1);
 80012c8:	2001      	movs	r0, #1
 80012ca:	f7ff ff01 	bl	80010d0 <RCC_Delay>
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSE) == RCC_OSCILLATORTYPE_LSE)
 80012ce:	6823      	ldr	r3, [r4, #0]
 80012d0:	f013 0f04 	tst.w	r3, #4
 80012d4:	f000 8096 	beq.w	8001404 <HAL_RCC_OscConfig+0x308>
    if (__HAL_RCC_PWR_IS_CLK_DISABLED())
 80012d8:	4b27      	ldr	r3, [pc, #156]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 80012da:	69db      	ldr	r3, [r3, #28]
 80012dc:	f013 5f80 	tst.w	r3, #268435456	@ 0x10000000
 80012e0:	d134      	bne.n	800134c <HAL_RCC_OscConfig+0x250>
      __HAL_RCC_PWR_CLK_ENABLE();
 80012e2:	4b25      	ldr	r3, [pc, #148]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 80012e4:	69da      	ldr	r2, [r3, #28]
 80012e6:	f042 5280 	orr.w	r2, r2, #268435456	@ 0x10000000
 80012ea:	61da      	str	r2, [r3, #28]
 80012ec:	69db      	ldr	r3, [r3, #28]
 80012ee:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 80012f2:	9301      	str	r3, [sp, #4]
 80012f4:	9b01      	ldr	r3, [sp, #4]
      pwrclkchanged = SET;
 80012f6:	2501      	movs	r5, #1
    if (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 80012f8:	4b21      	ldr	r3, [pc, #132]	@ (8001380 <HAL_RCC_OscConfig+0x284>)
 80012fa:	681b      	ldr	r3, [r3, #0]
 80012fc:	f413 7f80 	tst.w	r3, #256	@ 0x100
 8001300:	d026      	beq.n	8001350 <HAL_RCC_OscConfig+0x254>
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 8001302:	68e3      	ldr	r3, [r4, #12]
 8001304:	2b01      	cmp	r3, #1
 8001306:	d03d      	beq.n	8001384 <HAL_RCC_OscConfig+0x288>
 8001308:	2b00      	cmp	r3, #0
 800130a:	d153      	bne.n	80013b4 <HAL_RCC_OscConfig+0x2b8>
 800130c:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 8001310:	f503 3304 	add.w	r3, r3, #135168	@ 0x21000
 8001314:	6a1a      	ldr	r2, [r3, #32]
 8001316:	f022 0201 	bic.w	r2, r2, #1
 800131a:	621a      	str	r2, [r3, #32]
 800131c:	6a1a      	ldr	r2, [r3, #32]
 800131e:	f022 0204 	bic.w	r2, r2, #4
 8001322:	621a      	str	r2, [r3, #32]
 8001324:	e033      	b.n	800138e <HAL_RCC_OscConfig+0x292>
      __HAL_RCC_LSI_DISABLE();
 8001326:	4b15      	ldr	r3, [pc, #84]	@ (800137c <HAL_RCC_OscConfig+0x280>)
 8001328:	2200      	movs	r2, #0
 800132a:	f8c3 2480 	str.w	r2, [r3, #1152]	@ 0x480
      tickstart = HAL_GetTick();
 800132e:	f7ff fbc9 	bl	8000ac4 <HAL_GetTick>
 8001332:	4605      	mov	r5, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 8001334:	4b10      	ldr	r3, [pc, #64]	@ (8001378 <HAL_RCC_OscConfig+0x27c>)
 8001336:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8001338:	f013 0f02 	tst.w	r3, #2
 800133c:	d0c7      	beq.n	80012ce <HAL_RCC_OscConfig+0x1d2>
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 800133e:	f7ff fbc1 	bl	8000ac4 <HAL_GetTick>
 8001342:	1b40      	subs	r0, r0, r5
 8001344:	2802      	cmp	r0, #2
 8001346:	d9f5      	bls.n	8001334 <HAL_RCC_OscConfig+0x238>
          return HAL_TIMEOUT;
 8001348:	2003      	movs	r0, #3
 800134a:	e0d2      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
    FlagStatus       pwrclkchanged = RESET;
 800134c:	2500      	movs	r5, #0
 800134e:	e7d3      	b.n	80012f8 <HAL_RCC_OscConfig+0x1fc>
      SET_BIT(PWR->CR, PWR_CR_DBP);
 8001350:	4a0b      	ldr	r2, [pc, #44]	@ (8001380 <HAL_RCC_OscConfig+0x284>)
 8001352:	6813      	ldr	r3, [r2, #0]
 8001354:	f443 7380 	orr.w	r3, r3, #256	@ 0x100
 8001358:	6013      	str	r3, [r2, #0]
      tickstart = HAL_GetTick();
 800135a:	f7ff fbb3 	bl	8000ac4 <HAL_GetTick>
 800135e:	4606      	mov	r6, r0
      while (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8001360:	4b07      	ldr	r3, [pc, #28]	@ (8001380 <HAL_RCC_OscConfig+0x284>)
 8001362:	681b      	ldr	r3, [r3, #0]
 8001364:	f413 7f80 	tst.w	r3, #256	@ 0x100
 8001368:	d1cb      	bne.n	8001302 <HAL_RCC_OscConfig+0x206>
        if ((HAL_GetTick() - tickstart) > RCC_DBP_TIMEOUT_VALUE)
 800136a:	f7ff fbab 	bl	8000ac4 <HAL_GetTick>
 800136e:	1b80      	subs	r0, r0, r6
 8001370:	2864      	cmp	r0, #100	@ 0x64
 8001372:	d9f5      	bls.n	8001360 <HAL_RCC_OscConfig+0x264>
          return HAL_TIMEOUT;
 8001374:	2003      	movs	r0, #3
 8001376:	e0bc      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
 8001378:	40021000 	.word	0x40021000
 800137c:	42420000 	.word	0x42420000
 8001380:	40007000 	.word	0x40007000
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 8001384:	4a5f      	ldr	r2, [pc, #380]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 8001386:	6a13      	ldr	r3, [r2, #32]
 8001388:	f043 0301 	orr.w	r3, r3, #1
 800138c:	6213      	str	r3, [r2, #32]
    if (RCC_OscInitStruct->LSEState != RCC_LSE_OFF)
 800138e:	68e3      	ldr	r3, [r4, #12]
 8001390:	b333      	cbz	r3, 80013e0 <HAL_RCC_OscConfig+0x2e4>
      tickstart = HAL_GetTick();
 8001392:	f7ff fb97 	bl	8000ac4 <HAL_GetTick>
 8001396:	4606      	mov	r6, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 8001398:	4b5a      	ldr	r3, [pc, #360]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 800139a:	6a1b      	ldr	r3, [r3, #32]
 800139c:	f013 0f02 	tst.w	r3, #2
 80013a0:	d12f      	bne.n	8001402 <HAL_RCC_OscConfig+0x306>
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 80013a2:	f7ff fb8f 	bl	8000ac4 <HAL_GetTick>
 80013a6:	1b80      	subs	r0, r0, r6
 80013a8:	f241 3388 	movw	r3, #5000	@ 0x1388
 80013ac:	4298      	cmp	r0, r3
 80013ae:	d9f3      	bls.n	8001398 <HAL_RCC_OscConfig+0x29c>
          return HAL_TIMEOUT;
 80013b0:	2003      	movs	r0, #3
 80013b2:	e09e      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 80013b4:	2b05      	cmp	r3, #5
 80013b6:	d009      	beq.n	80013cc <HAL_RCC_OscConfig+0x2d0>
 80013b8:	4b52      	ldr	r3, [pc, #328]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 80013ba:	6a1a      	ldr	r2, [r3, #32]
 80013bc:	f022 0201 	bic.w	r2, r2, #1
 80013c0:	621a      	str	r2, [r3, #32]
 80013c2:	6a1a      	ldr	r2, [r3, #32]
 80013c4:	f022 0204 	bic.w	r2, r2, #4
 80013c8:	621a      	str	r2, [r3, #32]
 80013ca:	e7e0      	b.n	800138e <HAL_RCC_OscConfig+0x292>
 80013cc:	4b4d      	ldr	r3, [pc, #308]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 80013ce:	6a1a      	ldr	r2, [r3, #32]
 80013d0:	f042 0204 	orr.w	r2, r2, #4
 80013d4:	621a      	str	r2, [r3, #32]
 80013d6:	6a1a      	ldr	r2, [r3, #32]
 80013d8:	f042 0201 	orr.w	r2, r2, #1
 80013dc:	621a      	str	r2, [r3, #32]
 80013de:	e7d6      	b.n	800138e <HAL_RCC_OscConfig+0x292>
      tickstart = HAL_GetTick();
 80013e0:	f7ff fb70 	bl	8000ac4 <HAL_GetTick>
 80013e4:	4606      	mov	r6, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 80013e6:	4b47      	ldr	r3, [pc, #284]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 80013e8:	6a1b      	ldr	r3, [r3, #32]
 80013ea:	f013 0f02 	tst.w	r3, #2
 80013ee:	d008      	beq.n	8001402 <HAL_RCC_OscConfig+0x306>
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 80013f0:	f7ff fb68 	bl	8000ac4 <HAL_GetTick>
 80013f4:	1b80      	subs	r0, r0, r6
 80013f6:	f241 3388 	movw	r3, #5000	@ 0x1388
 80013fa:	4298      	cmp	r0, r3
 80013fc:	d9f3      	bls.n	80013e6 <HAL_RCC_OscConfig+0x2ea>
          return HAL_TIMEOUT;
 80013fe:	2003      	movs	r0, #3
 8001400:	e077      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
    if (pwrclkchanged == SET)
 8001402:	b9e5      	cbnz	r5, 800143e <HAL_RCC_OscConfig+0x342>
  if ((RCC_OscInitStruct->PLL.PLLState) != RCC_PLL_NONE)
 8001404:	69e3      	ldr	r3, [r4, #28]
 8001406:	2b00      	cmp	r3, #0
 8001408:	d072      	beq.n	80014f0 <HAL_RCC_OscConfig+0x3f4>
    if (__HAL_RCC_GET_SYSCLK_SOURCE() != RCC_SYSCLKSOURCE_STATUS_PLLCLK)
 800140a:	4a3e      	ldr	r2, [pc, #248]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 800140c:	6852      	ldr	r2, [r2, #4]
 800140e:	f002 020c 	and.w	r2, r2, #12
 8001412:	2a08      	cmp	r2, #8
 8001414:	d056      	beq.n	80014c4 <HAL_RCC_OscConfig+0x3c8>
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_ON)
 8001416:	2b02      	cmp	r3, #2
 8001418:	d017      	beq.n	800144a <HAL_RCC_OscConfig+0x34e>
        __HAL_RCC_PLL_DISABLE();
 800141a:	4b3b      	ldr	r3, [pc, #236]	@ (8001508 <HAL_RCC_OscConfig+0x40c>)
 800141c:	2200      	movs	r2, #0
 800141e:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 8001420:	f7ff fb50 	bl	8000ac4 <HAL_GetTick>
 8001424:	4604      	mov	r4, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8001426:	4b37      	ldr	r3, [pc, #220]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 8001428:	681b      	ldr	r3, [r3, #0]
 800142a:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 800142e:	d047      	beq.n	80014c0 <HAL_RCC_OscConfig+0x3c4>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8001430:	f7ff fb48 	bl	8000ac4 <HAL_GetTick>
 8001434:	1b00      	subs	r0, r0, r4
 8001436:	2802      	cmp	r0, #2
 8001438:	d9f5      	bls.n	8001426 <HAL_RCC_OscConfig+0x32a>
            return HAL_TIMEOUT;
 800143a:	2003      	movs	r0, #3
 800143c:	e059      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_PWR_CLK_DISABLE();
 800143e:	4a31      	ldr	r2, [pc, #196]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 8001440:	69d3      	ldr	r3, [r2, #28]
 8001442:	f023 5380 	bic.w	r3, r3, #268435456	@ 0x10000000
 8001446:	61d3      	str	r3, [r2, #28]
 8001448:	e7dc      	b.n	8001404 <HAL_RCC_OscConfig+0x308>
        __HAL_RCC_PLL_DISABLE();
 800144a:	4b2f      	ldr	r3, [pc, #188]	@ (8001508 <HAL_RCC_OscConfig+0x40c>)
 800144c:	2200      	movs	r2, #0
 800144e:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 8001450:	f7ff fb38 	bl	8000ac4 <HAL_GetTick>
 8001454:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8001456:	4b2b      	ldr	r3, [pc, #172]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 8001458:	681b      	ldr	r3, [r3, #0]
 800145a:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 800145e:	d006      	beq.n	800146e <HAL_RCC_OscConfig+0x372>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8001460:	f7ff fb30 	bl	8000ac4 <HAL_GetTick>
 8001464:	1b40      	subs	r0, r0, r5
 8001466:	2802      	cmp	r0, #2
 8001468:	d9f5      	bls.n	8001456 <HAL_RCC_OscConfig+0x35a>
            return HAL_TIMEOUT;
 800146a:	2003      	movs	r0, #3
 800146c:	e041      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
        if (RCC_OscInitStruct->PLL.PLLSource == RCC_PLLSOURCE_HSE)
 800146e:	6a23      	ldr	r3, [r4, #32]
 8001470:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 8001474:	d01a      	beq.n	80014ac <HAL_RCC_OscConfig+0x3b0>
        __HAL_RCC_PLL_CONFIG(RCC_OscInitStruct->PLL.PLLSource,
 8001476:	4923      	ldr	r1, [pc, #140]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 8001478:	684b      	ldr	r3, [r1, #4]
 800147a:	f423 1374 	bic.w	r3, r3, #3997696	@ 0x3d0000
 800147e:	6a22      	ldr	r2, [r4, #32]
 8001480:	6a60      	ldr	r0, [r4, #36]	@ 0x24
 8001482:	4302      	orrs	r2, r0
 8001484:	4313      	orrs	r3, r2
 8001486:	604b      	str	r3, [r1, #4]
        __HAL_RCC_PLL_ENABLE();
 8001488:	4b1f      	ldr	r3, [pc, #124]	@ (8001508 <HAL_RCC_OscConfig+0x40c>)
 800148a:	2201      	movs	r2, #1
 800148c:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 800148e:	f7ff fb19 	bl	8000ac4 <HAL_GetTick>
 8001492:	4604      	mov	r4, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  == RESET)
 8001494:	4b1b      	ldr	r3, [pc, #108]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 8001496:	681b      	ldr	r3, [r3, #0]
 8001498:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 800149c:	d10e      	bne.n	80014bc <HAL_RCC_OscConfig+0x3c0>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 800149e:	f7ff fb11 	bl	8000ac4 <HAL_GetTick>
 80014a2:	1b00      	subs	r0, r0, r4
 80014a4:	2802      	cmp	r0, #2
 80014a6:	d9f5      	bls.n	8001494 <HAL_RCC_OscConfig+0x398>
            return HAL_TIMEOUT;
 80014a8:	2003      	movs	r0, #3
 80014aa:	e022      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
          __HAL_RCC_HSE_PREDIV_CONFIG(RCC_OscInitStruct->HSEPredivValue);
 80014ac:	4a15      	ldr	r2, [pc, #84]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 80014ae:	6853      	ldr	r3, [r2, #4]
 80014b0:	f423 3300 	bic.w	r3, r3, #131072	@ 0x20000
 80014b4:	68a1      	ldr	r1, [r4, #8]
 80014b6:	430b      	orrs	r3, r1
 80014b8:	6053      	str	r3, [r2, #4]
 80014ba:	e7dc      	b.n	8001476 <HAL_RCC_OscConfig+0x37a>
  return HAL_OK;
 80014bc:	2000      	movs	r0, #0
 80014be:	e018      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
 80014c0:	2000      	movs	r0, #0
 80014c2:	e016      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF)
 80014c4:	2b01      	cmp	r3, #1
 80014c6:	d016      	beq.n	80014f6 <HAL_RCC_OscConfig+0x3fa>
        pll_config = RCC->CFGR;
 80014c8:	4b0e      	ldr	r3, [pc, #56]	@ (8001504 <HAL_RCC_OscConfig+0x408>)
 80014ca:	685b      	ldr	r3, [r3, #4]
        if ((READ_BIT(pll_config, RCC_CFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 80014cc:	f403 3180 	and.w	r1, r3, #65536	@ 0x10000
 80014d0:	6a22      	ldr	r2, [r4, #32]
 80014d2:	4291      	cmp	r1, r2
 80014d4:	d111      	bne.n	80014fa <HAL_RCC_OscConfig+0x3fe>
            (READ_BIT(pll_config, RCC_CFGR_PLLMULL) != RCC_OscInitStruct->PLL.PLLMUL))
 80014d6:	f403 1370 	and.w	r3, r3, #3932160	@ 0x3c0000
 80014da:	6a62      	ldr	r2, [r4, #36]	@ 0x24
        if ((READ_BIT(pll_config, RCC_CFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 80014dc:	4293      	cmp	r3, r2
 80014de:	d10e      	bne.n	80014fe <HAL_RCC_OscConfig+0x402>
  return HAL_OK;
 80014e0:	2000      	movs	r0, #0
 80014e2:	e006      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
    return HAL_ERROR;
 80014e4:	2001      	movs	r0, #1
}
 80014e6:	4770      	bx	lr
        return HAL_ERROR;
 80014e8:	2001      	movs	r0, #1
 80014ea:	e002      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
        return HAL_ERROR;
 80014ec:	2001      	movs	r0, #1
 80014ee:	e000      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
  return HAL_OK;
 80014f0:	2000      	movs	r0, #0
}
 80014f2:	b002      	add	sp, #8
 80014f4:	bd70      	pop	{r4, r5, r6, pc}
        return HAL_ERROR;
 80014f6:	2001      	movs	r0, #1
 80014f8:	e7fb      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
          return HAL_ERROR;
 80014fa:	2001      	movs	r0, #1
 80014fc:	e7f9      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
 80014fe:	2001      	movs	r0, #1
 8001500:	e7f7      	b.n	80014f2 <HAL_RCC_OscConfig+0x3f6>
 8001502:	bf00      	nop
 8001504:	40021000 	.word	0x40021000
 8001508:	42420000 	.word	0x42420000

0800150c <HAL_RCC_GetSysClockFreq>:
  tmpreg = RCC->CFGR;
 800150c:	4b0f      	ldr	r3, [pc, #60]	@ (800154c <HAL_RCC_GetSysClockFreq+0x40>)
 800150e:	685b      	ldr	r3, [r3, #4]
  switch (tmpreg & RCC_CFGR_SWS)
 8001510:	f003 020c 	and.w	r2, r3, #12
 8001514:	2a08      	cmp	r2, #8
 8001516:	d001      	beq.n	800151c <HAL_RCC_GetSysClockFreq+0x10>
      sysclockfreq = HSE_VALUE;
 8001518:	480d      	ldr	r0, [pc, #52]	@ (8001550 <HAL_RCC_GetSysClockFreq+0x44>)
}
 800151a:	4770      	bx	lr
      pllmul = aPLLMULFactorTable[(uint32_t)(tmpreg & RCC_CFGR_PLLMULL) >> RCC_CFGR_PLLMULL_Pos];
 800151c:	f3c3 4283 	ubfx	r2, r3, #18, #4
 8001520:	490c      	ldr	r1, [pc, #48]	@ (8001554 <HAL_RCC_GetSysClockFreq+0x48>)
 8001522:	5c88      	ldrb	r0, [r1, r2]
      if ((tmpreg & RCC_CFGR_PLLSRC) != RCC_PLLSOURCE_HSI_DIV2)
 8001524:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8001528:	d00b      	beq.n	8001542 <HAL_RCC_GetSysClockFreq+0x36>
        prediv = aPredivFactorTable[(uint32_t)(RCC->CFGR & RCC_CFGR_PLLXTPRE) >> RCC_CFGR_PLLXTPRE_Pos];
 800152a:	4b08      	ldr	r3, [pc, #32]	@ (800154c <HAL_RCC_GetSysClockFreq+0x40>)
 800152c:	685b      	ldr	r3, [r3, #4]
 800152e:	f3c3 4340 	ubfx	r3, r3, #17, #1
 8001532:	4a09      	ldr	r2, [pc, #36]	@ (8001558 <HAL_RCC_GetSysClockFreq+0x4c>)
 8001534:	5cd3      	ldrb	r3, [r2, r3]
        pllclk = (uint32_t)((HSE_VALUE  * pllmul) / prediv);
 8001536:	4a06      	ldr	r2, [pc, #24]	@ (8001550 <HAL_RCC_GetSysClockFreq+0x44>)
 8001538:	fb02 f000 	mul.w	r0, r2, r0
 800153c:	fbb0 f0f3 	udiv	r0, r0, r3
 8001540:	4770      	bx	lr
        pllclk = (uint32_t)((HSI_VALUE >> 1) * pllmul);
 8001542:	4b06      	ldr	r3, [pc, #24]	@ (800155c <HAL_RCC_GetSysClockFreq+0x50>)
 8001544:	fb03 f000 	mul.w	r0, r3, r0
 8001548:	4770      	bx	lr
 800154a:	bf00      	nop
 800154c:	40021000 	.word	0x40021000
 8001550:	007a1200 	.word	0x007a1200
 8001554:	080031a8 	.word	0x080031a8
 8001558:	080031a4 	.word	0x080031a4
 800155c:	003d0900 	.word	0x003d0900

08001560 <HAL_RCC_ClockConfig>:
  if (RCC_ClkInitStruct == NULL)
 8001560:	2800      	cmp	r0, #0
 8001562:	f000 80a0 	beq.w	80016a6 <HAL_RCC_ClockConfig+0x146>
{
 8001566:	b570      	push	{r4, r5, r6, lr}
 8001568:	460d      	mov	r5, r1
 800156a:	4604      	mov	r4, r0
  if (FLatency > __HAL_FLASH_GET_LATENCY())
 800156c:	4b52      	ldr	r3, [pc, #328]	@ (80016b8 <HAL_RCC_ClockConfig+0x158>)
 800156e:	681b      	ldr	r3, [r3, #0]
 8001570:	f003 0307 	and.w	r3, r3, #7
 8001574:	428b      	cmp	r3, r1
 8001576:	d20b      	bcs.n	8001590 <HAL_RCC_ClockConfig+0x30>
    __HAL_FLASH_SET_LATENCY(FLatency);
 8001578:	4a4f      	ldr	r2, [pc, #316]	@ (80016b8 <HAL_RCC_ClockConfig+0x158>)
 800157a:	6813      	ldr	r3, [r2, #0]
 800157c:	f023 0307 	bic.w	r3, r3, #7
 8001580:	430b      	orrs	r3, r1
 8001582:	6013      	str	r3, [r2, #0]
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 8001584:	6813      	ldr	r3, [r2, #0]
 8001586:	f003 0307 	and.w	r3, r3, #7
 800158a:	428b      	cmp	r3, r1
 800158c:	f040 808d 	bne.w	80016aa <HAL_RCC_ClockConfig+0x14a>
if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_HCLK) == RCC_CLOCKTYPE_HCLK)
 8001590:	6823      	ldr	r3, [r4, #0]
 8001592:	f013 0f02 	tst.w	r3, #2
 8001596:	d017      	beq.n	80015c8 <HAL_RCC_ClockConfig+0x68>
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 8001598:	f013 0f04 	tst.w	r3, #4
 800159c:	d004      	beq.n	80015a8 <HAL_RCC_ClockConfig+0x48>
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_HCLK_DIV16);
 800159e:	4a47      	ldr	r2, [pc, #284]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 80015a0:	6853      	ldr	r3, [r2, #4]
 80015a2:	f443 63e0 	orr.w	r3, r3, #1792	@ 0x700
 80015a6:	6053      	str	r3, [r2, #4]
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 80015a8:	6823      	ldr	r3, [r4, #0]
 80015aa:	f013 0f08 	tst.w	r3, #8
 80015ae:	d004      	beq.n	80015ba <HAL_RCC_ClockConfig+0x5a>
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, (RCC_HCLK_DIV16 << 3));
 80015b0:	4a42      	ldr	r2, [pc, #264]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 80015b2:	6853      	ldr	r3, [r2, #4]
 80015b4:	f443 5360 	orr.w	r3, r3, #14336	@ 0x3800
 80015b8:	6053      	str	r3, [r2, #4]
    MODIFY_REG(RCC->CFGR, RCC_CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
 80015ba:	4a40      	ldr	r2, [pc, #256]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 80015bc:	6853      	ldr	r3, [r2, #4]
 80015be:	f023 03f0 	bic.w	r3, r3, #240	@ 0xf0
 80015c2:	68a1      	ldr	r1, [r4, #8]
 80015c4:	430b      	orrs	r3, r1
 80015c6:	6053      	str	r3, [r2, #4]
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_SYSCLK) == RCC_CLOCKTYPE_SYSCLK)
 80015c8:	6823      	ldr	r3, [r4, #0]
 80015ca:	f013 0f01 	tst.w	r3, #1
 80015ce:	d031      	beq.n	8001634 <HAL_RCC_ClockConfig+0xd4>
    if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_HSE)
 80015d0:	6863      	ldr	r3, [r4, #4]
 80015d2:	2b01      	cmp	r3, #1
 80015d4:	d020      	beq.n	8001618 <HAL_RCC_ClockConfig+0xb8>
    else if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_PLLCLK)
 80015d6:	2b02      	cmp	r3, #2
 80015d8:	d025      	beq.n	8001626 <HAL_RCC_ClockConfig+0xc6>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 80015da:	4a38      	ldr	r2, [pc, #224]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 80015dc:	6812      	ldr	r2, [r2, #0]
 80015de:	f012 0f02 	tst.w	r2, #2
 80015e2:	d064      	beq.n	80016ae <HAL_RCC_ClockConfig+0x14e>
    __HAL_RCC_SYSCLK_CONFIG(RCC_ClkInitStruct->SYSCLKSource);
 80015e4:	4935      	ldr	r1, [pc, #212]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 80015e6:	684a      	ldr	r2, [r1, #4]
 80015e8:	f022 0203 	bic.w	r2, r2, #3
 80015ec:	4313      	orrs	r3, r2
 80015ee:	604b      	str	r3, [r1, #4]
    tickstart = HAL_GetTick();
 80015f0:	f7ff fa68 	bl	8000ac4 <HAL_GetTick>
 80015f4:	4606      	mov	r6, r0
    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 80015f6:	4b31      	ldr	r3, [pc, #196]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 80015f8:	685b      	ldr	r3, [r3, #4]
 80015fa:	f003 030c 	and.w	r3, r3, #12
 80015fe:	6862      	ldr	r2, [r4, #4]
 8001600:	ebb3 0f82 	cmp.w	r3, r2, lsl #2
 8001604:	d016      	beq.n	8001634 <HAL_RCC_ClockConfig+0xd4>
      if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
 8001606:	f7ff fa5d 	bl	8000ac4 <HAL_GetTick>
 800160a:	1b80      	subs	r0, r0, r6
 800160c:	f241 3388 	movw	r3, #5000	@ 0x1388
 8001610:	4298      	cmp	r0, r3
 8001612:	d9f0      	bls.n	80015f6 <HAL_RCC_ClockConfig+0x96>
        return HAL_TIMEOUT;
 8001614:	2003      	movs	r0, #3
 8001616:	e045      	b.n	80016a4 <HAL_RCC_ClockConfig+0x144>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8001618:	4a28      	ldr	r2, [pc, #160]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 800161a:	6812      	ldr	r2, [r2, #0]
 800161c:	f412 3f00 	tst.w	r2, #131072	@ 0x20000
 8001620:	d1e0      	bne.n	80015e4 <HAL_RCC_ClockConfig+0x84>
        return HAL_ERROR;
 8001622:	2001      	movs	r0, #1
 8001624:	e03e      	b.n	80016a4 <HAL_RCC_ClockConfig+0x144>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 8001626:	4a25      	ldr	r2, [pc, #148]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 8001628:	6812      	ldr	r2, [r2, #0]
 800162a:	f012 7f00 	tst.w	r2, #33554432	@ 0x2000000
 800162e:	d1d9      	bne.n	80015e4 <HAL_RCC_ClockConfig+0x84>
        return HAL_ERROR;
 8001630:	2001      	movs	r0, #1
 8001632:	e037      	b.n	80016a4 <HAL_RCC_ClockConfig+0x144>
  if (FLatency < __HAL_FLASH_GET_LATENCY())
 8001634:	4b20      	ldr	r3, [pc, #128]	@ (80016b8 <HAL_RCC_ClockConfig+0x158>)
 8001636:	681b      	ldr	r3, [r3, #0]
 8001638:	f003 0307 	and.w	r3, r3, #7
 800163c:	42ab      	cmp	r3, r5
 800163e:	d90a      	bls.n	8001656 <HAL_RCC_ClockConfig+0xf6>
    __HAL_FLASH_SET_LATENCY(FLatency);
 8001640:	4a1d      	ldr	r2, [pc, #116]	@ (80016b8 <HAL_RCC_ClockConfig+0x158>)
 8001642:	6813      	ldr	r3, [r2, #0]
 8001644:	f023 0307 	bic.w	r3, r3, #7
 8001648:	432b      	orrs	r3, r5
 800164a:	6013      	str	r3, [r2, #0]
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 800164c:	6813      	ldr	r3, [r2, #0]
 800164e:	f003 0307 	and.w	r3, r3, #7
 8001652:	42ab      	cmp	r3, r5
 8001654:	d12d      	bne.n	80016b2 <HAL_RCC_ClockConfig+0x152>
if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 8001656:	6823      	ldr	r3, [r4, #0]
 8001658:	f013 0f04 	tst.w	r3, #4
 800165c:	d006      	beq.n	800166c <HAL_RCC_ClockConfig+0x10c>
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_ClkInitStruct->APB1CLKDivider);
 800165e:	4a17      	ldr	r2, [pc, #92]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 8001660:	6853      	ldr	r3, [r2, #4]
 8001662:	f423 63e0 	bic.w	r3, r3, #1792	@ 0x700
 8001666:	68e1      	ldr	r1, [r4, #12]
 8001668:	430b      	orrs	r3, r1
 800166a:	6053      	str	r3, [r2, #4]
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 800166c:	6823      	ldr	r3, [r4, #0]
 800166e:	f013 0f08 	tst.w	r3, #8
 8001672:	d007      	beq.n	8001684 <HAL_RCC_ClockConfig+0x124>
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, ((RCC_ClkInitStruct->APB2CLKDivider) << 3));
 8001674:	4a11      	ldr	r2, [pc, #68]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 8001676:	6853      	ldr	r3, [r2, #4]
 8001678:	f423 5360 	bic.w	r3, r3, #14336	@ 0x3800
 800167c:	6921      	ldr	r1, [r4, #16]
 800167e:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 8001682:	6053      	str	r3, [r2, #4]
  SystemCoreClock = HAL_RCC_GetSysClockFreq() >> AHBPrescTable[(RCC->CFGR & RCC_CFGR_HPRE) >> RCC_CFGR_HPRE_Pos];
 8001684:	f7ff ff42 	bl	800150c <HAL_RCC_GetSysClockFreq>
 8001688:	4b0c      	ldr	r3, [pc, #48]	@ (80016bc <HAL_RCC_ClockConfig+0x15c>)
 800168a:	685b      	ldr	r3, [r3, #4]
 800168c:	f3c3 1303 	ubfx	r3, r3, #4, #4
 8001690:	4a0b      	ldr	r2, [pc, #44]	@ (80016c0 <HAL_RCC_ClockConfig+0x160>)
 8001692:	5cd3      	ldrb	r3, [r2, r3]
 8001694:	40d8      	lsrs	r0, r3
 8001696:	4b0b      	ldr	r3, [pc, #44]	@ (80016c4 <HAL_RCC_ClockConfig+0x164>)
 8001698:	6018      	str	r0, [r3, #0]
  HAL_InitTick(uwTickPrio);
 800169a:	4b0b      	ldr	r3, [pc, #44]	@ (80016c8 <HAL_RCC_ClockConfig+0x168>)
 800169c:	6818      	ldr	r0, [r3, #0]
 800169e:	f7ff f9cd 	bl	8000a3c <HAL_InitTick>
  return HAL_OK;
 80016a2:	2000      	movs	r0, #0
}
 80016a4:	bd70      	pop	{r4, r5, r6, pc}
    return HAL_ERROR;
 80016a6:	2001      	movs	r0, #1
}
 80016a8:	4770      	bx	lr
    return HAL_ERROR;
 80016aa:	2001      	movs	r0, #1
 80016ac:	e7fa      	b.n	80016a4 <HAL_RCC_ClockConfig+0x144>
        return HAL_ERROR;
 80016ae:	2001      	movs	r0, #1
 80016b0:	e7f8      	b.n	80016a4 <HAL_RCC_ClockConfig+0x144>
    return HAL_ERROR;
 80016b2:	2001      	movs	r0, #1
 80016b4:	e7f6      	b.n	80016a4 <HAL_RCC_ClockConfig+0x144>
 80016b6:	bf00      	nop
 80016b8:	******** 	.word	0x********
 80016bc:	40021000 	.word	0x40021000
 80016c0:	08003194 	.word	0x08003194
 80016c4:	20000004 	.word	0x20000004
 80016c8:	2000000c 	.word	0x2000000c

080016cc <HAL_RCC_GetHCLKFreq>:
}
 80016cc:	4b01      	ldr	r3, [pc, #4]	@ (80016d4 <HAL_RCC_GetHCLKFreq+0x8>)
 80016ce:	6818      	ldr	r0, [r3, #0]
 80016d0:	4770      	bx	lr
 80016d2:	bf00      	nop
 80016d4:	20000004 	.word	0x20000004

080016d8 <HAL_RCC_GetPCLK1Freq>:
{
 80016d8:	b508      	push	{r3, lr}
  return (HAL_RCC_GetHCLKFreq() >> APBPrescTable[(RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos]);
 80016da:	f7ff fff7 	bl	80016cc <HAL_RCC_GetHCLKFreq>
 80016de:	4b04      	ldr	r3, [pc, #16]	@ (80016f0 <HAL_RCC_GetPCLK1Freq+0x18>)
 80016e0:	685b      	ldr	r3, [r3, #4]
 80016e2:	f3c3 2302 	ubfx	r3, r3, #8, #3
 80016e6:	4a03      	ldr	r2, [pc, #12]	@ (80016f4 <HAL_RCC_GetPCLK1Freq+0x1c>)
 80016e8:	5cd3      	ldrb	r3, [r2, r3]
}
 80016ea:	40d8      	lsrs	r0, r3
 80016ec:	bd08      	pop	{r3, pc}
 80016ee:	bf00      	nop
 80016f0:	40021000 	.word	0x40021000
 80016f4:	0800318c 	.word	0x0800318c

080016f8 <HAL_RCC_GetPCLK2Freq>:
{
 80016f8:	b508      	push	{r3, lr}
  return (HAL_RCC_GetHCLKFreq() >> APBPrescTable[(RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos]);
 80016fa:	f7ff ffe7 	bl	80016cc <HAL_RCC_GetHCLKFreq>
 80016fe:	4b04      	ldr	r3, [pc, #16]	@ (8001710 <HAL_RCC_GetPCLK2Freq+0x18>)
 8001700:	685b      	ldr	r3, [r3, #4]
 8001702:	f3c3 23c2 	ubfx	r3, r3, #11, #3
 8001706:	4a03      	ldr	r2, [pc, #12]	@ (8001714 <HAL_RCC_GetPCLK2Freq+0x1c>)
 8001708:	5cd3      	ldrb	r3, [r2, r3]
}
 800170a:	40d8      	lsrs	r0, r3
 800170c:	bd08      	pop	{r3, pc}
 800170e:	bf00      	nop
 8001710:	40021000 	.word	0x40021000
 8001714:	0800318c 	.word	0x0800318c

08001718 <UART_EndRxTransfer>:
  * @retval None
  */
static void UART_EndRxTransfer(UART_HandleTypeDef *huart)
{
  /* Disable RXNE, PE and ERR (Frame error, noise error, overrun error) interrupts */
  ATOMIC_CLEAR_BIT(huart->Instance->CR1, (USART_CR1_RXNEIE | USART_CR1_PEIE));
 8001718:	6802      	ldr	r2, [r0, #0]
 */
__STATIC_FORCEINLINE uint32_t __LDREXW(volatile uint32_t *addr)
{
    uint32_t result;

   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 800171a:	f102 030c 	add.w	r3, r2, #12
 800171e:	e853 3f00 	ldrex	r3, [r3]
 8001722:	f423 7390 	bic.w	r3, r3, #288	@ 0x120
 */
__STATIC_FORCEINLINE uint32_t __STREXW(uint32_t value, volatile uint32_t *addr)
{
   uint32_t result;

   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 8001726:	320c      	adds	r2, #12
 8001728:	e842 3100 	strex	r1, r3, [r2]
 800172c:	2900      	cmp	r1, #0
 800172e:	d1f3      	bne.n	8001718 <UART_EndRxTransfer>
  ATOMIC_CLEAR_BIT(huart->Instance->CR3, USART_CR3_EIE);
 8001730:	6802      	ldr	r2, [r0, #0]
   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 8001732:	f102 0314 	add.w	r3, r2, #20
 8001736:	e853 3f00 	ldrex	r3, [r3]
 800173a:	f023 0301 	bic.w	r3, r3, #1
   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 800173e:	3214      	adds	r2, #20
 8001740:	e842 3100 	strex	r1, r3, [r2]
 8001744:	2900      	cmp	r1, #0
 8001746:	d1f3      	bne.n	8001730 <UART_EndRxTransfer+0x18>

  /* In case of reception waiting for IDLE event, disable also the IDLE IE interrupt source */
  if (huart->ReceptionType == HAL_UART_RECEPTION_TOIDLE)
 8001748:	6b03      	ldr	r3, [r0, #48]	@ 0x30
 800174a:	2b01      	cmp	r3, #1
 800174c:	d005      	beq.n	800175a <UART_EndRxTransfer+0x42>
  {
    ATOMIC_CLEAR_BIT(huart->Instance->CR1, USART_CR1_IDLEIE);
  }

  /* At end of Rx process, restore huart->RxState to Ready */
  huart->RxState = HAL_UART_STATE_READY;
 800174e:	2320      	movs	r3, #32
 8001750:	f880 3042 	strb.w	r3, [r0, #66]	@ 0x42
  huart->ReceptionType = HAL_UART_RECEPTION_STANDARD;
 8001754:	2300      	movs	r3, #0
 8001756:	6303      	str	r3, [r0, #48]	@ 0x30
}
 8001758:	4770      	bx	lr
    ATOMIC_CLEAR_BIT(huart->Instance->CR1, USART_CR1_IDLEIE);
 800175a:	6802      	ldr	r2, [r0, #0]
   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 800175c:	f102 030c 	add.w	r3, r2, #12
 8001760:	e853 3f00 	ldrex	r3, [r3]
 8001764:	f023 0310 	bic.w	r3, r3, #16
   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 8001768:	320c      	adds	r2, #12
 800176a:	e842 3100 	strex	r1, r3, [r2]
 800176e:	2900      	cmp	r1, #0
 8001770:	d1f3      	bne.n	800175a <UART_EndRxTransfer+0x42>
 8001772:	e7ec      	b.n	800174e <UART_EndRxTransfer+0x36>

08001774 <UART_SetConfig>:
  * @param  huart  Pointer to a UART_HandleTypeDef structure that contains
  *                the configuration information for the specified UART module.
  * @retval None
  */
static void UART_SetConfig(UART_HandleTypeDef *huart)
{
 8001774:	b510      	push	{r4, lr}
 8001776:	4604      	mov	r4, r0
  assert_param(IS_UART_MODE(huart->Init.Mode));

  /*-------------------------- USART CR2 Configuration -----------------------*/
  /* Configure the UART Stop Bits: Set STOP[13:12] bits
     according to huart->Init.StopBits value */
  MODIFY_REG(huart->Instance->CR2, USART_CR2_STOP, huart->Init.StopBits);
 8001778:	6802      	ldr	r2, [r0, #0]
 800177a:	6913      	ldr	r3, [r2, #16]
 800177c:	f423 5340 	bic.w	r3, r3, #12288	@ 0x3000
 8001780:	68c1      	ldr	r1, [r0, #12]
 8001782:	430b      	orrs	r3, r1
 8001784:	6113      	str	r3, [r2, #16]
  tmpreg = (uint32_t)huart->Init.WordLength | huart->Init.Parity | huart->Init.Mode | huart->Init.OverSampling;
  MODIFY_REG(huart->Instance->CR1,
             (uint32_t)(USART_CR1_M | USART_CR1_PCE | USART_CR1_PS | USART_CR1_TE | USART_CR1_RE | USART_CR1_OVER8),
             tmpreg);
#else
  tmpreg = (uint32_t)huart->Init.WordLength | huart->Init.Parity | huart->Init.Mode;
 8001786:	6883      	ldr	r3, [r0, #8]
 8001788:	6902      	ldr	r2, [r0, #16]
 800178a:	4313      	orrs	r3, r2
 800178c:	6942      	ldr	r2, [r0, #20]
 800178e:	431a      	orrs	r2, r3
  MODIFY_REG(huart->Instance->CR1,
 8001790:	6801      	ldr	r1, [r0, #0]
 8001792:	68cb      	ldr	r3, [r1, #12]
 8001794:	f423 53b0 	bic.w	r3, r3, #5632	@ 0x1600
 8001798:	f023 030c 	bic.w	r3, r3, #12
 800179c:	4313      	orrs	r3, r2
 800179e:	60cb      	str	r3, [r1, #12]
             tmpreg);
#endif /* USART_CR1_OVER8 */

  /*-------------------------- USART CR3 Configuration -----------------------*/
  /* Configure the UART HFC: Set CTSE and RTSE bits according to huart->Init.HwFlowCtl value */
  MODIFY_REG(huart->Instance->CR3, (USART_CR3_RTSE | USART_CR3_CTSE), huart->Init.HwFlowCtl);
 80017a0:	6802      	ldr	r2, [r0, #0]
 80017a2:	6953      	ldr	r3, [r2, #20]
 80017a4:	f423 7340 	bic.w	r3, r3, #768	@ 0x300
 80017a8:	6981      	ldr	r1, [r0, #24]
 80017aa:	430b      	orrs	r3, r1
 80017ac:	6153      	str	r3, [r2, #20]


  if(huart->Instance == USART1)
 80017ae:	6802      	ldr	r2, [r0, #0]
 80017b0:	4b13      	ldr	r3, [pc, #76]	@ (8001800 <UART_SetConfig+0x8c>)
 80017b2:	429a      	cmp	r2, r3
 80017b4:	d020      	beq.n	80017f8 <UART_SetConfig+0x84>
  {
    pclk = HAL_RCC_GetPCLK2Freq();
  }
  else
  {
    pclk = HAL_RCC_GetPCLK1Freq();
 80017b6:	f7ff ff8f 	bl	80016d8 <HAL_RCC_GetPCLK1Freq>
 80017ba:	4602      	mov	r2, r0
  else
  {
    huart->Instance->BRR = UART_BRR_SAMPLING16(pclk, huart->Init.BaudRate);
  }
#else
  huart->Instance->BRR = UART_BRR_SAMPLING16(pclk, huart->Init.BaudRate);
 80017bc:	eb02 0282 	add.w	r2, r2, r2, lsl #2
 80017c0:	eb02 0282 	add.w	r2, r2, r2, lsl #2
 80017c4:	6863      	ldr	r3, [r4, #4]
 80017c6:	009b      	lsls	r3, r3, #2
 80017c8:	fbb2 f2f3 	udiv	r2, r2, r3
 80017cc:	480d      	ldr	r0, [pc, #52]	@ (8001804 <UART_SetConfig+0x90>)
 80017ce:	fba0 3102 	umull	r3, r1, r0, r2
 80017d2:	0949      	lsrs	r1, r1, #5
 80017d4:	2364      	movs	r3, #100	@ 0x64
 80017d6:	fb03 2311 	mls	r3, r3, r1, r2
 80017da:	011b      	lsls	r3, r3, #4
 80017dc:	3332      	adds	r3, #50	@ 0x32
 80017de:	fba0 0303 	umull	r0, r3, r0, r3
 80017e2:	095b      	lsrs	r3, r3, #5
 80017e4:	f003 02f0 	and.w	r2, r3, #240	@ 0xf0
 80017e8:	eb02 1201 	add.w	r2, r2, r1, lsl #4
 80017ec:	f003 030f 	and.w	r3, r3, #15
 80017f0:	6821      	ldr	r1, [r4, #0]
 80017f2:	4413      	add	r3, r2
 80017f4:	608b      	str	r3, [r1, #8]
#endif /* USART_CR1_OVER8 */
}
 80017f6:	bd10      	pop	{r4, pc}
    pclk = HAL_RCC_GetPCLK2Freq();
 80017f8:	f7ff ff7e 	bl	80016f8 <HAL_RCC_GetPCLK2Freq>
 80017fc:	4602      	mov	r2, r0
 80017fe:	e7dd      	b.n	80017bc <UART_SetConfig+0x48>
 8001800:	40013800 	.word	0x40013800
 8001804:	51eb851f 	.word	0x51eb851f

08001808 <UART_WaitOnFlagUntilTimeout>:
{
 8001808:	e92d 43f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, lr}
 800180c:	b083      	sub	sp, #12
 800180e:	4605      	mov	r5, r0
 8001810:	460e      	mov	r6, r1
 8001812:	4617      	mov	r7, r2
 8001814:	4699      	mov	r9, r3
 8001816:	f8dd 8028 	ldr.w	r8, [sp, #40]	@ 0x28
  while ((__HAL_UART_GET_FLAG(huart, Flag) ? SET : RESET) == Status)
 800181a:	682b      	ldr	r3, [r5, #0]
 800181c:	681c      	ldr	r4, [r3, #0]
 800181e:	ea36 0404 	bics.w	r4, r6, r4
 8001822:	bf0c      	ite	eq
 8001824:	2401      	moveq	r4, #1
 8001826:	2400      	movne	r4, #0
 8001828:	42bc      	cmp	r4, r7
 800182a:	d128      	bne.n	800187e <UART_WaitOnFlagUntilTimeout+0x76>
    if (Timeout != HAL_MAX_DELAY)
 800182c:	f1b8 3fff 	cmp.w	r8, #4294967295
 8001830:	d0f3      	beq.n	800181a <UART_WaitOnFlagUntilTimeout+0x12>
      if (((HAL_GetTick() - Tickstart) > Timeout) || (Timeout == 0U))
 8001832:	f7ff f947 	bl	8000ac4 <HAL_GetTick>
 8001836:	eba0 0009 	sub.w	r0, r0, r9
 800183a:	4540      	cmp	r0, r8
 800183c:	d823      	bhi.n	8001886 <UART_WaitOnFlagUntilTimeout+0x7e>
 800183e:	f1b8 0f00 	cmp.w	r8, #0
 8001842:	d022      	beq.n	800188a <UART_WaitOnFlagUntilTimeout+0x82>
      if ((READ_BIT(huart->Instance->CR1, USART_CR1_RE) != 0U) && (Flag != UART_FLAG_TXE) && (Flag != UART_FLAG_TC))
 8001844:	682b      	ldr	r3, [r5, #0]
 8001846:	68da      	ldr	r2, [r3, #12]
 8001848:	f012 0f04 	tst.w	r2, #4
 800184c:	d0e5      	beq.n	800181a <UART_WaitOnFlagUntilTimeout+0x12>
 800184e:	2e80      	cmp	r6, #128	@ 0x80
 8001850:	d0e3      	beq.n	800181a <UART_WaitOnFlagUntilTimeout+0x12>
 8001852:	2e40      	cmp	r6, #64	@ 0x40
 8001854:	d0e1      	beq.n	800181a <UART_WaitOnFlagUntilTimeout+0x12>
        if (__HAL_UART_GET_FLAG(huart, UART_FLAG_ORE) == SET)
 8001856:	681a      	ldr	r2, [r3, #0]
 8001858:	f012 0f08 	tst.w	r2, #8
 800185c:	d0dd      	beq.n	800181a <UART_WaitOnFlagUntilTimeout+0x12>
          __HAL_UART_CLEAR_OREFLAG(huart);
 800185e:	2400      	movs	r4, #0
 8001860:	9401      	str	r4, [sp, #4]
 8001862:	681a      	ldr	r2, [r3, #0]
 8001864:	9201      	str	r2, [sp, #4]
 8001866:	685b      	ldr	r3, [r3, #4]
 8001868:	9301      	str	r3, [sp, #4]
 800186a:	9b01      	ldr	r3, [sp, #4]
          UART_EndRxTransfer(huart);
 800186c:	4628      	mov	r0, r5
 800186e:	f7ff ff53 	bl	8001718 <UART_EndRxTransfer>
          huart->ErrorCode = HAL_UART_ERROR_ORE;
 8001872:	2308      	movs	r3, #8
 8001874:	646b      	str	r3, [r5, #68]	@ 0x44
          __HAL_UNLOCK(huart);
 8001876:	f885 4040 	strb.w	r4, [r5, #64]	@ 0x40
          return HAL_ERROR;
 800187a:	2001      	movs	r0, #1
 800187c:	e000      	b.n	8001880 <UART_WaitOnFlagUntilTimeout+0x78>
  return HAL_OK;
 800187e:	2000      	movs	r0, #0
}
 8001880:	b003      	add	sp, #12
 8001882:	e8bd 83f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, pc}
        return HAL_TIMEOUT;
 8001886:	2003      	movs	r0, #3
 8001888:	e7fa      	b.n	8001880 <UART_WaitOnFlagUntilTimeout+0x78>
 800188a:	2003      	movs	r0, #3
 800188c:	e7f8      	b.n	8001880 <UART_WaitOnFlagUntilTimeout+0x78>

0800188e <HAL_UART_Init>:
  if (huart == NULL)
 800188e:	b360      	cbz	r0, 80018ea <HAL_UART_Init+0x5c>
{
 8001890:	b510      	push	{r4, lr}
 8001892:	4604      	mov	r4, r0
  if (huart->gState == HAL_UART_STATE_RESET)
 8001894:	f890 3041 	ldrb.w	r3, [r0, #65]	@ 0x41
 8001898:	b313      	cbz	r3, 80018e0 <HAL_UART_Init+0x52>
  huart->gState = HAL_UART_STATE_BUSY;
 800189a:	2324      	movs	r3, #36	@ 0x24
 800189c:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
  __HAL_UART_DISABLE(huart);
 80018a0:	6822      	ldr	r2, [r4, #0]
 80018a2:	68d3      	ldr	r3, [r2, #12]
 80018a4:	f423 5300 	bic.w	r3, r3, #8192	@ 0x2000
 80018a8:	60d3      	str	r3, [r2, #12]
  UART_SetConfig(huart);
 80018aa:	4620      	mov	r0, r4
 80018ac:	f7ff ff62 	bl	8001774 <UART_SetConfig>
  CLEAR_BIT(huart->Instance->CR2, (USART_CR2_LINEN | USART_CR2_CLKEN));
 80018b0:	6822      	ldr	r2, [r4, #0]
 80018b2:	6913      	ldr	r3, [r2, #16]
 80018b4:	f423 4390 	bic.w	r3, r3, #18432	@ 0x4800
 80018b8:	6113      	str	r3, [r2, #16]
  CLEAR_BIT(huart->Instance->CR3, (USART_CR3_SCEN | USART_CR3_HDSEL | USART_CR3_IREN));
 80018ba:	6822      	ldr	r2, [r4, #0]
 80018bc:	6953      	ldr	r3, [r2, #20]
 80018be:	f023 032a 	bic.w	r3, r3, #42	@ 0x2a
 80018c2:	6153      	str	r3, [r2, #20]
  __HAL_UART_ENABLE(huart);
 80018c4:	6822      	ldr	r2, [r4, #0]
 80018c6:	68d3      	ldr	r3, [r2, #12]
 80018c8:	f443 5300 	orr.w	r3, r3, #8192	@ 0x2000
 80018cc:	60d3      	str	r3, [r2, #12]
  huart->ErrorCode = HAL_UART_ERROR_NONE;
 80018ce:	2000      	movs	r0, #0
 80018d0:	6460      	str	r0, [r4, #68]	@ 0x44
  huart->gState = HAL_UART_STATE_READY;
 80018d2:	2320      	movs	r3, #32
 80018d4:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
  huart->RxState = HAL_UART_STATE_READY;
 80018d8:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
  huart->RxEventType = HAL_UART_RXEVENT_TC;
 80018dc:	6360      	str	r0, [r4, #52]	@ 0x34
}
 80018de:	bd10      	pop	{r4, pc}
    huart->Lock = HAL_UNLOCKED;
 80018e0:	f880 3040 	strb.w	r3, [r0, #64]	@ 0x40
    HAL_UART_MspInit(huart);
 80018e4:	f7fe ffba 	bl	800085c <HAL_UART_MspInit>
 80018e8:	e7d7      	b.n	800189a <HAL_UART_Init+0xc>
    return HAL_ERROR;
 80018ea:	2001      	movs	r0, #1
}
 80018ec:	4770      	bx	lr

080018ee <HAL_UART_Transmit>:
{
 80018ee:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 80018f2:	b082      	sub	sp, #8
 80018f4:	461e      	mov	r6, r3
  if (huart->gState == HAL_UART_STATE_READY)
 80018f6:	f890 3041 	ldrb.w	r3, [r0, #65]	@ 0x41
 80018fa:	b2db      	uxtb	r3, r3
 80018fc:	2b20      	cmp	r3, #32
 80018fe:	d156      	bne.n	80019ae <HAL_UART_Transmit+0xc0>
 8001900:	4604      	mov	r4, r0
 8001902:	460d      	mov	r5, r1
 8001904:	4690      	mov	r8, r2
    if ((pData == NULL) || (Size == 0U))
 8001906:	2900      	cmp	r1, #0
 8001908:	d055      	beq.n	80019b6 <HAL_UART_Transmit+0xc8>
 800190a:	b90a      	cbnz	r2, 8001910 <HAL_UART_Transmit+0x22>
      return  HAL_ERROR;
 800190c:	2001      	movs	r0, #1
 800190e:	e04f      	b.n	80019b0 <HAL_UART_Transmit+0xc2>
    huart->ErrorCode = HAL_UART_ERROR_NONE;
 8001910:	2300      	movs	r3, #0
 8001912:	6443      	str	r3, [r0, #68]	@ 0x44
    huart->gState = HAL_UART_STATE_BUSY_TX;
 8001914:	2321      	movs	r3, #33	@ 0x21
 8001916:	f880 3041 	strb.w	r3, [r0, #65]	@ 0x41
    tickstart = HAL_GetTick();
 800191a:	f7ff f8d3 	bl	8000ac4 <HAL_GetTick>
 800191e:	4607      	mov	r7, r0
    huart->TxXferSize = Size;
 8001920:	f8a4 8024 	strh.w	r8, [r4, #36]	@ 0x24
    huart->TxXferCount = Size;
 8001924:	f8a4 8026 	strh.w	r8, [r4, #38]	@ 0x26
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 8001928:	68a3      	ldr	r3, [r4, #8]
 800192a:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 800192e:	d002      	beq.n	8001936 <HAL_UART_Transmit+0x48>
      pdata16bits = NULL;
 8001930:	f04f 0800 	mov.w	r8, #0
 8001934:	e014      	b.n	8001960 <HAL_UART_Transmit+0x72>
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 8001936:	6923      	ldr	r3, [r4, #16]
 8001938:	b32b      	cbz	r3, 8001986 <HAL_UART_Transmit+0x98>
      pdata16bits = NULL;
 800193a:	f04f 0800 	mov.w	r8, #0
 800193e:	e00f      	b.n	8001960 <HAL_UART_Transmit+0x72>
        huart->gState = HAL_UART_STATE_READY;
 8001940:	2320      	movs	r3, #32
 8001942:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
        return HAL_TIMEOUT;
 8001946:	2003      	movs	r0, #3
 8001948:	e032      	b.n	80019b0 <HAL_UART_Transmit+0xc2>
        huart->Instance->DR = (uint16_t)(*pdata16bits & 0x01FFU);
 800194a:	f838 3b02 	ldrh.w	r3, [r8], #2
 800194e:	6822      	ldr	r2, [r4, #0]
 8001950:	f3c3 0308 	ubfx	r3, r3, #0, #9
 8001954:	6053      	str	r3, [r2, #4]
      huart->TxXferCount--;
 8001956:	8ce2      	ldrh	r2, [r4, #38]	@ 0x26
 8001958:	b292      	uxth	r2, r2
 800195a:	3a01      	subs	r2, #1
 800195c:	b292      	uxth	r2, r2
 800195e:	84e2      	strh	r2, [r4, #38]	@ 0x26
    while (huart->TxXferCount > 0U)
 8001960:	8ce3      	ldrh	r3, [r4, #38]	@ 0x26
 8001962:	b29b      	uxth	r3, r3
 8001964:	b193      	cbz	r3, 800198c <HAL_UART_Transmit+0x9e>
      if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_TXE, RESET, tickstart, Timeout) != HAL_OK)
 8001966:	9600      	str	r6, [sp, #0]
 8001968:	463b      	mov	r3, r7
 800196a:	2200      	movs	r2, #0
 800196c:	2180      	movs	r1, #128	@ 0x80
 800196e:	4620      	mov	r0, r4
 8001970:	f7ff ff4a 	bl	8001808 <UART_WaitOnFlagUntilTimeout>
 8001974:	2800      	cmp	r0, #0
 8001976:	d1e3      	bne.n	8001940 <HAL_UART_Transmit+0x52>
      if (pdata8bits == NULL)
 8001978:	2d00      	cmp	r5, #0
 800197a:	d0e6      	beq.n	800194a <HAL_UART_Transmit+0x5c>
        huart->Instance->DR = (uint8_t)(*pdata8bits & 0xFFU);
 800197c:	f815 2b01 	ldrb.w	r2, [r5], #1
 8001980:	6823      	ldr	r3, [r4, #0]
 8001982:	605a      	str	r2, [r3, #4]
        pdata8bits++;
 8001984:	e7e7      	b.n	8001956 <HAL_UART_Transmit+0x68>
      pdata16bits = (const uint16_t *) pData;
 8001986:	46a8      	mov	r8, r5
      pdata8bits  = NULL;
 8001988:	2500      	movs	r5, #0
 800198a:	e7e9      	b.n	8001960 <HAL_UART_Transmit+0x72>
    if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_TC, RESET, tickstart, Timeout) != HAL_OK)
 800198c:	9600      	str	r6, [sp, #0]
 800198e:	463b      	mov	r3, r7
 8001990:	2200      	movs	r2, #0
 8001992:	2140      	movs	r1, #64	@ 0x40
 8001994:	4620      	mov	r0, r4
 8001996:	f7ff ff37 	bl	8001808 <UART_WaitOnFlagUntilTimeout>
 800199a:	b918      	cbnz	r0, 80019a4 <HAL_UART_Transmit+0xb6>
    huart->gState = HAL_UART_STATE_READY;
 800199c:	2320      	movs	r3, #32
 800199e:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
    return HAL_OK;
 80019a2:	e005      	b.n	80019b0 <HAL_UART_Transmit+0xc2>
      huart->gState = HAL_UART_STATE_READY;
 80019a4:	2320      	movs	r3, #32
 80019a6:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
      return HAL_TIMEOUT;
 80019aa:	2003      	movs	r0, #3
 80019ac:	e000      	b.n	80019b0 <HAL_UART_Transmit+0xc2>
    return HAL_BUSY;
 80019ae:	2002      	movs	r0, #2
}
 80019b0:	b002      	add	sp, #8
 80019b2:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
      return  HAL_ERROR;
 80019b6:	2001      	movs	r0, #1
 80019b8:	e7fa      	b.n	80019b0 <HAL_UART_Transmit+0xc2>

080019ba <HAL_UART_Receive>:
{
 80019ba:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 80019be:	b082      	sub	sp, #8
 80019c0:	461e      	mov	r6, r3
  if (huart->RxState == HAL_UART_STATE_READY)
 80019c2:	f890 3042 	ldrb.w	r3, [r0, #66]	@ 0x42
 80019c6:	b2db      	uxtb	r3, r3
 80019c8:	2b20      	cmp	r3, #32
 80019ca:	d159      	bne.n	8001a80 <HAL_UART_Receive+0xc6>
 80019cc:	4604      	mov	r4, r0
 80019ce:	460d      	mov	r5, r1
 80019d0:	4690      	mov	r8, r2
    if ((pData == NULL) || (Size == 0U))
 80019d2:	2900      	cmp	r1, #0
 80019d4:	d058      	beq.n	8001a88 <HAL_UART_Receive+0xce>
 80019d6:	b90a      	cbnz	r2, 80019dc <HAL_UART_Receive+0x22>
      return  HAL_ERROR;
 80019d8:	2001      	movs	r0, #1
 80019da:	e052      	b.n	8001a82 <HAL_UART_Receive+0xc8>
    huart->ErrorCode = HAL_UART_ERROR_NONE;
 80019dc:	2300      	movs	r3, #0
 80019de:	6443      	str	r3, [r0, #68]	@ 0x44
    huart->RxState = HAL_UART_STATE_BUSY_RX;
 80019e0:	2222      	movs	r2, #34	@ 0x22
 80019e2:	f880 2042 	strb.w	r2, [r0, #66]	@ 0x42
    huart->ReceptionType = HAL_UART_RECEPTION_STANDARD;
 80019e6:	6303      	str	r3, [r0, #48]	@ 0x30
    tickstart = HAL_GetTick();
 80019e8:	f7ff f86c 	bl	8000ac4 <HAL_GetTick>
 80019ec:	4607      	mov	r7, r0
    huart->RxXferSize = Size;
 80019ee:	f8a4 802c 	strh.w	r8, [r4, #44]	@ 0x2c
    huart->RxXferCount = Size;
 80019f2:	f8a4 802e 	strh.w	r8, [r4, #46]	@ 0x2e
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 80019f6:	68a3      	ldr	r3, [r4, #8]
 80019f8:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 80019fc:	d002      	beq.n	8001a04 <HAL_UART_Receive+0x4a>
      pdata16bits = NULL;
 80019fe:	f04f 0800 	mov.w	r8, #0
 8001a02:	e01c      	b.n	8001a3e <HAL_UART_Receive+0x84>
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 8001a04:	6923      	ldr	r3, [r4, #16]
 8001a06:	b113      	cbz	r3, 8001a0e <HAL_UART_Receive+0x54>
      pdata16bits = NULL;
 8001a08:	f04f 0800 	mov.w	r8, #0
 8001a0c:	e017      	b.n	8001a3e <HAL_UART_Receive+0x84>
      pdata16bits = (uint16_t *) pData;
 8001a0e:	46a8      	mov	r8, r5
      pdata8bits  = NULL;
 8001a10:	2500      	movs	r5, #0
 8001a12:	e014      	b.n	8001a3e <HAL_UART_Receive+0x84>
        huart->RxState = HAL_UART_STATE_READY;
 8001a14:	2320      	movs	r3, #32
 8001a16:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
        return HAL_TIMEOUT;
 8001a1a:	2003      	movs	r0, #3
 8001a1c:	e031      	b.n	8001a82 <HAL_UART_Receive+0xc8>
        *pdata16bits = (uint16_t)(huart->Instance->DR & 0x01FF);
 8001a1e:	6823      	ldr	r3, [r4, #0]
 8001a20:	685b      	ldr	r3, [r3, #4]
 8001a22:	f3c3 0308 	ubfx	r3, r3, #0, #9
 8001a26:	f828 3b02 	strh.w	r3, [r8], #2
        pdata16bits++;
 8001a2a:	e003      	b.n	8001a34 <HAL_UART_Receive+0x7a>
          *pdata8bits = (uint8_t)(huart->Instance->DR & (uint8_t)0x00FF);
 8001a2c:	6823      	ldr	r3, [r4, #0]
 8001a2e:	685b      	ldr	r3, [r3, #4]
 8001a30:	702b      	strb	r3, [r5, #0]
        pdata8bits++;
 8001a32:	3501      	adds	r5, #1
      huart->RxXferCount--;
 8001a34:	8de2      	ldrh	r2, [r4, #46]	@ 0x2e
 8001a36:	b292      	uxth	r2, r2
 8001a38:	3a01      	subs	r2, #1
 8001a3a:	b292      	uxth	r2, r2
 8001a3c:	85e2      	strh	r2, [r4, #46]	@ 0x2e
    while (huart->RxXferCount > 0U)
 8001a3e:	8de3      	ldrh	r3, [r4, #46]	@ 0x2e
 8001a40:	b29b      	uxth	r3, r3
 8001a42:	b1c3      	cbz	r3, 8001a76 <HAL_UART_Receive+0xbc>
      if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_RXNE, RESET, tickstart, Timeout) != HAL_OK)
 8001a44:	9600      	str	r6, [sp, #0]
 8001a46:	463b      	mov	r3, r7
 8001a48:	2200      	movs	r2, #0
 8001a4a:	2120      	movs	r1, #32
 8001a4c:	4620      	mov	r0, r4
 8001a4e:	f7ff fedb 	bl	8001808 <UART_WaitOnFlagUntilTimeout>
 8001a52:	2800      	cmp	r0, #0
 8001a54:	d1de      	bne.n	8001a14 <HAL_UART_Receive+0x5a>
      if (pdata8bits == NULL)
 8001a56:	2d00      	cmp	r5, #0
 8001a58:	d0e1      	beq.n	8001a1e <HAL_UART_Receive+0x64>
        if ((huart->Init.WordLength == UART_WORDLENGTH_9B) || ((huart->Init.WordLength == UART_WORDLENGTH_8B) && (huart->Init.Parity == UART_PARITY_NONE)))
 8001a5a:	68a3      	ldr	r3, [r4, #8]
 8001a5c:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 8001a60:	d0e4      	beq.n	8001a2c <HAL_UART_Receive+0x72>
 8001a62:	b913      	cbnz	r3, 8001a6a <HAL_UART_Receive+0xb0>
 8001a64:	6923      	ldr	r3, [r4, #16]
 8001a66:	2b00      	cmp	r3, #0
 8001a68:	d0e0      	beq.n	8001a2c <HAL_UART_Receive+0x72>
          *pdata8bits = (uint8_t)(huart->Instance->DR & (uint8_t)0x007F);
 8001a6a:	6823      	ldr	r3, [r4, #0]
 8001a6c:	685b      	ldr	r3, [r3, #4]
 8001a6e:	f003 037f 	and.w	r3, r3, #127	@ 0x7f
 8001a72:	702b      	strb	r3, [r5, #0]
 8001a74:	e7dd      	b.n	8001a32 <HAL_UART_Receive+0x78>
    huart->RxState = HAL_UART_STATE_READY;
 8001a76:	2320      	movs	r3, #32
 8001a78:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
    return HAL_OK;
 8001a7c:	2000      	movs	r0, #0
 8001a7e:	e000      	b.n	8001a82 <HAL_UART_Receive+0xc8>
    return HAL_BUSY;
 8001a80:	2002      	movs	r0, #2
}
 8001a82:	b002      	add	sp, #8
 8001a84:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
      return  HAL_ERROR;
 8001a88:	2001      	movs	r0, #1
 8001a8a:	e7fa      	b.n	8001a82 <HAL_UART_Receive+0xc8>

08001a8c <atoi>:
 8001a8c:	220a      	movs	r2, #10
 8001a8e:	2100      	movs	r1, #0
 8001a90:	f000 b87a 	b.w	8001b88 <strtol>

08001a94 <_strtol_l.constprop.0>:
 8001a94:	2b24      	cmp	r3, #36	@ 0x24
 8001a96:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
 8001a9a:	4686      	mov	lr, r0
 8001a9c:	4690      	mov	r8, r2
 8001a9e:	d801      	bhi.n	8001aa4 <_strtol_l.constprop.0+0x10>
 8001aa0:	2b01      	cmp	r3, #1
 8001aa2:	d106      	bne.n	8001ab2 <_strtol_l.constprop.0+0x1e>
 8001aa4:	f000 fad2 	bl	800204c <__errno>
 8001aa8:	2316      	movs	r3, #22
 8001aaa:	6003      	str	r3, [r0, #0]
 8001aac:	2000      	movs	r0, #0
 8001aae:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 8001ab2:	460d      	mov	r5, r1
 8001ab4:	4833      	ldr	r0, [pc, #204]	@ (8001b84 <_strtol_l.constprop.0+0xf0>)
 8001ab6:	462a      	mov	r2, r5
 8001ab8:	f815 4b01 	ldrb.w	r4, [r5], #1
 8001abc:	5d06      	ldrb	r6, [r0, r4]
 8001abe:	f016 0608 	ands.w	r6, r6, #8
 8001ac2:	d1f8      	bne.n	8001ab6 <_strtol_l.constprop.0+0x22>
 8001ac4:	2c2d      	cmp	r4, #45	@ 0x2d
 8001ac6:	d12d      	bne.n	8001b24 <_strtol_l.constprop.0+0x90>
 8001ac8:	2601      	movs	r6, #1
 8001aca:	782c      	ldrb	r4, [r5, #0]
 8001acc:	1c95      	adds	r5, r2, #2
 8001ace:	f033 0210 	bics.w	r2, r3, #16
 8001ad2:	d109      	bne.n	8001ae8 <_strtol_l.constprop.0+0x54>
 8001ad4:	2c30      	cmp	r4, #48	@ 0x30
 8001ad6:	d12a      	bne.n	8001b2e <_strtol_l.constprop.0+0x9a>
 8001ad8:	782a      	ldrb	r2, [r5, #0]
 8001ada:	f002 02df 	and.w	r2, r2, #223	@ 0xdf
 8001ade:	2a58      	cmp	r2, #88	@ 0x58
 8001ae0:	d125      	bne.n	8001b2e <_strtol_l.constprop.0+0x9a>
 8001ae2:	2310      	movs	r3, #16
 8001ae4:	786c      	ldrb	r4, [r5, #1]
 8001ae6:	3502      	adds	r5, #2
 8001ae8:	2200      	movs	r2, #0
 8001aea:	f106 4c00 	add.w	ip, r6, #2147483648	@ 0x80000000
 8001aee:	f10c 3cff 	add.w	ip, ip, #4294967295
 8001af2:	fbbc f9f3 	udiv	r9, ip, r3
 8001af6:	4610      	mov	r0, r2
 8001af8:	fb03 ca19 	mls	sl, r3, r9, ip
 8001afc:	f1a4 0730 	sub.w	r7, r4, #48	@ 0x30
 8001b00:	2f09      	cmp	r7, #9
 8001b02:	d81b      	bhi.n	8001b3c <_strtol_l.constprop.0+0xa8>
 8001b04:	463c      	mov	r4, r7
 8001b06:	42a3      	cmp	r3, r4
 8001b08:	dd27      	ble.n	8001b5a <_strtol_l.constprop.0+0xc6>
 8001b0a:	1c57      	adds	r7, r2, #1
 8001b0c:	d007      	beq.n	8001b1e <_strtol_l.constprop.0+0x8a>
 8001b0e:	4581      	cmp	r9, r0
 8001b10:	d320      	bcc.n	8001b54 <_strtol_l.constprop.0+0xc0>
 8001b12:	d101      	bne.n	8001b18 <_strtol_l.constprop.0+0x84>
 8001b14:	45a2      	cmp	sl, r4
 8001b16:	db1d      	blt.n	8001b54 <_strtol_l.constprop.0+0xc0>
 8001b18:	2201      	movs	r2, #1
 8001b1a:	fb00 4003 	mla	r0, r0, r3, r4
 8001b1e:	f815 4b01 	ldrb.w	r4, [r5], #1
 8001b22:	e7eb      	b.n	8001afc <_strtol_l.constprop.0+0x68>
 8001b24:	2c2b      	cmp	r4, #43	@ 0x2b
 8001b26:	bf04      	itt	eq
 8001b28:	782c      	ldrbeq	r4, [r5, #0]
 8001b2a:	1c95      	addeq	r5, r2, #2
 8001b2c:	e7cf      	b.n	8001ace <_strtol_l.constprop.0+0x3a>
 8001b2e:	2b00      	cmp	r3, #0
 8001b30:	d1da      	bne.n	8001ae8 <_strtol_l.constprop.0+0x54>
 8001b32:	2c30      	cmp	r4, #48	@ 0x30
 8001b34:	bf0c      	ite	eq
 8001b36:	2308      	moveq	r3, #8
 8001b38:	230a      	movne	r3, #10
 8001b3a:	e7d5      	b.n	8001ae8 <_strtol_l.constprop.0+0x54>
 8001b3c:	f1a4 0741 	sub.w	r7, r4, #65	@ 0x41
 8001b40:	2f19      	cmp	r7, #25
 8001b42:	d801      	bhi.n	8001b48 <_strtol_l.constprop.0+0xb4>
 8001b44:	3c37      	subs	r4, #55	@ 0x37
 8001b46:	e7de      	b.n	8001b06 <_strtol_l.constprop.0+0x72>
 8001b48:	f1a4 0761 	sub.w	r7, r4, #97	@ 0x61
 8001b4c:	2f19      	cmp	r7, #25
 8001b4e:	d804      	bhi.n	8001b5a <_strtol_l.constprop.0+0xc6>
 8001b50:	3c57      	subs	r4, #87	@ 0x57
 8001b52:	e7d8      	b.n	8001b06 <_strtol_l.constprop.0+0x72>
 8001b54:	f04f 32ff 	mov.w	r2, #4294967295
 8001b58:	e7e1      	b.n	8001b1e <_strtol_l.constprop.0+0x8a>
 8001b5a:	1c53      	adds	r3, r2, #1
 8001b5c:	d108      	bne.n	8001b70 <_strtol_l.constprop.0+0xdc>
 8001b5e:	2322      	movs	r3, #34	@ 0x22
 8001b60:	4660      	mov	r0, ip
 8001b62:	f8ce 3000 	str.w	r3, [lr]
 8001b66:	f1b8 0f00 	cmp.w	r8, #0
 8001b6a:	d0a0      	beq.n	8001aae <_strtol_l.constprop.0+0x1a>
 8001b6c:	1e69      	subs	r1, r5, #1
 8001b6e:	e006      	b.n	8001b7e <_strtol_l.constprop.0+0xea>
 8001b70:	b106      	cbz	r6, 8001b74 <_strtol_l.constprop.0+0xe0>
 8001b72:	4240      	negs	r0, r0
 8001b74:	f1b8 0f00 	cmp.w	r8, #0
 8001b78:	d099      	beq.n	8001aae <_strtol_l.constprop.0+0x1a>
 8001b7a:	2a00      	cmp	r2, #0
 8001b7c:	d1f6      	bne.n	8001b6c <_strtol_l.constprop.0+0xd8>
 8001b7e:	f8c8 1000 	str.w	r1, [r8]
 8001b82:	e794      	b.n	8001aae <_strtol_l.constprop.0+0x1a>
 8001b84:	080031b9 	.word	0x080031b9

08001b88 <strtol>:
 8001b88:	4613      	mov	r3, r2
 8001b8a:	460a      	mov	r2, r1
 8001b8c:	4601      	mov	r1, r0
 8001b8e:	4802      	ldr	r0, [pc, #8]	@ (8001b98 <strtol+0x10>)
 8001b90:	6800      	ldr	r0, [r0, #0]
 8001b92:	f7ff bf7f 	b.w	8001a94 <_strtol_l.constprop.0>
 8001b96:	bf00      	nop
 8001b98:	2000001c 	.word	0x2000001c

08001b9c <std>:
 8001b9c:	2300      	movs	r3, #0
 8001b9e:	b510      	push	{r4, lr}
 8001ba0:	4604      	mov	r4, r0
 8001ba2:	e9c0 3300 	strd	r3, r3, [r0]
 8001ba6:	e9c0 3304 	strd	r3, r3, [r0, #16]
 8001baa:	6083      	str	r3, [r0, #8]
 8001bac:	8181      	strh	r1, [r0, #12]
 8001bae:	6643      	str	r3, [r0, #100]	@ 0x64
 8001bb0:	81c2      	strh	r2, [r0, #14]
 8001bb2:	6183      	str	r3, [r0, #24]
 8001bb4:	4619      	mov	r1, r3
 8001bb6:	2208      	movs	r2, #8
 8001bb8:	305c      	adds	r0, #92	@ 0x5c
 8001bba:	f000 f9f9 	bl	8001fb0 <memset>
 8001bbe:	4b0d      	ldr	r3, [pc, #52]	@ (8001bf4 <std+0x58>)
 8001bc0:	6224      	str	r4, [r4, #32]
 8001bc2:	6263      	str	r3, [r4, #36]	@ 0x24
 8001bc4:	4b0c      	ldr	r3, [pc, #48]	@ (8001bf8 <std+0x5c>)
 8001bc6:	62a3      	str	r3, [r4, #40]	@ 0x28
 8001bc8:	4b0c      	ldr	r3, [pc, #48]	@ (8001bfc <std+0x60>)
 8001bca:	62e3      	str	r3, [r4, #44]	@ 0x2c
 8001bcc:	4b0c      	ldr	r3, [pc, #48]	@ (8001c00 <std+0x64>)
 8001bce:	6323      	str	r3, [r4, #48]	@ 0x30
 8001bd0:	4b0c      	ldr	r3, [pc, #48]	@ (8001c04 <std+0x68>)
 8001bd2:	429c      	cmp	r4, r3
 8001bd4:	d006      	beq.n	8001be4 <std+0x48>
 8001bd6:	f103 0268 	add.w	r2, r3, #104	@ 0x68
 8001bda:	4294      	cmp	r4, r2
 8001bdc:	d002      	beq.n	8001be4 <std+0x48>
 8001bde:	33d0      	adds	r3, #208	@ 0xd0
 8001be0:	429c      	cmp	r4, r3
 8001be2:	d105      	bne.n	8001bf0 <std+0x54>
 8001be4:	f104 0058 	add.w	r0, r4, #88	@ 0x58
 8001be8:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 8001bec:	f000 ba58 	b.w	80020a0 <__retarget_lock_init_recursive>
 8001bf0:	bd10      	pop	{r4, pc}
 8001bf2:	bf00      	nop
 8001bf4:	08001e01 	.word	0x08001e01
 8001bf8:	08001e23 	.word	0x08001e23
 8001bfc:	08001e5b 	.word	0x08001e5b
 8001c00:	08001e7f 	.word	0x08001e7f
 8001c04:	20000150 	.word	0x20000150

08001c08 <stdio_exit_handler>:
 8001c08:	4a02      	ldr	r2, [pc, #8]	@ (8001c14 <stdio_exit_handler+0xc>)
 8001c0a:	4903      	ldr	r1, [pc, #12]	@ (8001c18 <stdio_exit_handler+0x10>)
 8001c0c:	4803      	ldr	r0, [pc, #12]	@ (8001c1c <stdio_exit_handler+0x14>)
 8001c0e:	f000 b869 	b.w	8001ce4 <_fwalk_sglue>
 8001c12:	bf00      	nop
 8001c14:	20000010 	.word	0x20000010
 8001c18:	08002955 	.word	0x08002955
 8001c1c:	20000020 	.word	0x20000020

08001c20 <cleanup_stdio>:
 8001c20:	6841      	ldr	r1, [r0, #4]
 8001c22:	4b0c      	ldr	r3, [pc, #48]	@ (8001c54 <cleanup_stdio+0x34>)
 8001c24:	b510      	push	{r4, lr}
 8001c26:	4299      	cmp	r1, r3
 8001c28:	4604      	mov	r4, r0
 8001c2a:	d001      	beq.n	8001c30 <cleanup_stdio+0x10>
 8001c2c:	f000 fe92 	bl	8002954 <_fflush_r>
 8001c30:	68a1      	ldr	r1, [r4, #8]
 8001c32:	4b09      	ldr	r3, [pc, #36]	@ (8001c58 <cleanup_stdio+0x38>)
 8001c34:	4299      	cmp	r1, r3
 8001c36:	d002      	beq.n	8001c3e <cleanup_stdio+0x1e>
 8001c38:	4620      	mov	r0, r4
 8001c3a:	f000 fe8b 	bl	8002954 <_fflush_r>
 8001c3e:	68e1      	ldr	r1, [r4, #12]
 8001c40:	4b06      	ldr	r3, [pc, #24]	@ (8001c5c <cleanup_stdio+0x3c>)
 8001c42:	4299      	cmp	r1, r3
 8001c44:	d004      	beq.n	8001c50 <cleanup_stdio+0x30>
 8001c46:	4620      	mov	r0, r4
 8001c48:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 8001c4c:	f000 be82 	b.w	8002954 <_fflush_r>
 8001c50:	bd10      	pop	{r4, pc}
 8001c52:	bf00      	nop
 8001c54:	20000150 	.word	0x20000150
 8001c58:	200001b8 	.word	0x200001b8
 8001c5c:	20000220 	.word	0x20000220

08001c60 <global_stdio_init.part.0>:
 8001c60:	b510      	push	{r4, lr}
 8001c62:	4b0b      	ldr	r3, [pc, #44]	@ (8001c90 <global_stdio_init.part.0+0x30>)
 8001c64:	4c0b      	ldr	r4, [pc, #44]	@ (8001c94 <global_stdio_init.part.0+0x34>)
 8001c66:	4a0c      	ldr	r2, [pc, #48]	@ (8001c98 <global_stdio_init.part.0+0x38>)
 8001c68:	4620      	mov	r0, r4
 8001c6a:	601a      	str	r2, [r3, #0]
 8001c6c:	2104      	movs	r1, #4
 8001c6e:	2200      	movs	r2, #0
 8001c70:	f7ff ff94 	bl	8001b9c <std>
 8001c74:	f104 0068 	add.w	r0, r4, #104	@ 0x68
 8001c78:	2201      	movs	r2, #1
 8001c7a:	2109      	movs	r1, #9
 8001c7c:	f7ff ff8e 	bl	8001b9c <std>
 8001c80:	f104 00d0 	add.w	r0, r4, #208	@ 0xd0
 8001c84:	2202      	movs	r2, #2
 8001c86:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 8001c8a:	2112      	movs	r1, #18
 8001c8c:	f7ff bf86 	b.w	8001b9c <std>
 8001c90:	20000288 	.word	0x20000288
 8001c94:	20000150 	.word	0x20000150
 8001c98:	08001c09 	.word	0x08001c09

08001c9c <__sfp_lock_acquire>:
 8001c9c:	4801      	ldr	r0, [pc, #4]	@ (8001ca4 <__sfp_lock_acquire+0x8>)
 8001c9e:	f000 ba00 	b.w	80020a2 <__retarget_lock_acquire_recursive>
 8001ca2:	bf00      	nop
 8001ca4:	20000291 	.word	0x20000291

08001ca8 <__sfp_lock_release>:
 8001ca8:	4801      	ldr	r0, [pc, #4]	@ (8001cb0 <__sfp_lock_release+0x8>)
 8001caa:	f000 b9fb 	b.w	80020a4 <__retarget_lock_release_recursive>
 8001cae:	bf00      	nop
 8001cb0:	20000291 	.word	0x20000291

08001cb4 <__sinit>:
 8001cb4:	b510      	push	{r4, lr}
 8001cb6:	4604      	mov	r4, r0
 8001cb8:	f7ff fff0 	bl	8001c9c <__sfp_lock_acquire>
 8001cbc:	6a23      	ldr	r3, [r4, #32]
 8001cbe:	b11b      	cbz	r3, 8001cc8 <__sinit+0x14>
 8001cc0:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 8001cc4:	f7ff bff0 	b.w	8001ca8 <__sfp_lock_release>
 8001cc8:	4b04      	ldr	r3, [pc, #16]	@ (8001cdc <__sinit+0x28>)
 8001cca:	6223      	str	r3, [r4, #32]
 8001ccc:	4b04      	ldr	r3, [pc, #16]	@ (8001ce0 <__sinit+0x2c>)
 8001cce:	681b      	ldr	r3, [r3, #0]
 8001cd0:	2b00      	cmp	r3, #0
 8001cd2:	d1f5      	bne.n	8001cc0 <__sinit+0xc>
 8001cd4:	f7ff ffc4 	bl	8001c60 <global_stdio_init.part.0>
 8001cd8:	e7f2      	b.n	8001cc0 <__sinit+0xc>
 8001cda:	bf00      	nop
 8001cdc:	08001c21 	.word	0x08001c21
 8001ce0:	20000288 	.word	0x20000288

08001ce4 <_fwalk_sglue>:
 8001ce4:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 8001ce8:	4607      	mov	r7, r0
 8001cea:	4688      	mov	r8, r1
 8001cec:	4614      	mov	r4, r2
 8001cee:	2600      	movs	r6, #0
 8001cf0:	e9d4 9501 	ldrd	r9, r5, [r4, #4]
 8001cf4:	f1b9 0901 	subs.w	r9, r9, #1
 8001cf8:	d505      	bpl.n	8001d06 <_fwalk_sglue+0x22>
 8001cfa:	6824      	ldr	r4, [r4, #0]
 8001cfc:	2c00      	cmp	r4, #0
 8001cfe:	d1f7      	bne.n	8001cf0 <_fwalk_sglue+0xc>
 8001d00:	4630      	mov	r0, r6
 8001d02:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
 8001d06:	89ab      	ldrh	r3, [r5, #12]
 8001d08:	2b01      	cmp	r3, #1
 8001d0a:	d907      	bls.n	8001d1c <_fwalk_sglue+0x38>
 8001d0c:	f9b5 300e 	ldrsh.w	r3, [r5, #14]
 8001d10:	3301      	adds	r3, #1
 8001d12:	d003      	beq.n	8001d1c <_fwalk_sglue+0x38>
 8001d14:	4629      	mov	r1, r5
 8001d16:	4638      	mov	r0, r7
 8001d18:	47c0      	blx	r8
 8001d1a:	4306      	orrs	r6, r0
 8001d1c:	3568      	adds	r5, #104	@ 0x68
 8001d1e:	e7e9      	b.n	8001cf4 <_fwalk_sglue+0x10>

08001d20 <iprintf>:
 8001d20:	b40f      	push	{r0, r1, r2, r3}
 8001d22:	b507      	push	{r0, r1, r2, lr}
 8001d24:	4906      	ldr	r1, [pc, #24]	@ (8001d40 <iprintf+0x20>)
 8001d26:	ab04      	add	r3, sp, #16
 8001d28:	6808      	ldr	r0, [r1, #0]
 8001d2a:	f853 2b04 	ldr.w	r2, [r3], #4
 8001d2e:	6881      	ldr	r1, [r0, #8]
 8001d30:	9301      	str	r3, [sp, #4]
 8001d32:	f000 fae5 	bl	8002300 <_vfiprintf_r>
 8001d36:	b003      	add	sp, #12
 8001d38:	f85d eb04 	ldr.w	lr, [sp], #4
 8001d3c:	b004      	add	sp, #16
 8001d3e:	4770      	bx	lr
 8001d40:	2000001c 	.word	0x2000001c

08001d44 <_puts_r>:
 8001d44:	6a03      	ldr	r3, [r0, #32]
 8001d46:	b570      	push	{r4, r5, r6, lr}
 8001d48:	4605      	mov	r5, r0
 8001d4a:	460e      	mov	r6, r1
 8001d4c:	6884      	ldr	r4, [r0, #8]
 8001d4e:	b90b      	cbnz	r3, 8001d54 <_puts_r+0x10>
 8001d50:	f7ff ffb0 	bl	8001cb4 <__sinit>
 8001d54:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 8001d56:	07db      	lsls	r3, r3, #31
 8001d58:	d405      	bmi.n	8001d66 <_puts_r+0x22>
 8001d5a:	89a3      	ldrh	r3, [r4, #12]
 8001d5c:	0598      	lsls	r0, r3, #22
 8001d5e:	d402      	bmi.n	8001d66 <_puts_r+0x22>
 8001d60:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 8001d62:	f000 f99e 	bl	80020a2 <__retarget_lock_acquire_recursive>
 8001d66:	89a3      	ldrh	r3, [r4, #12]
 8001d68:	0719      	lsls	r1, r3, #28
 8001d6a:	d502      	bpl.n	8001d72 <_puts_r+0x2e>
 8001d6c:	6923      	ldr	r3, [r4, #16]
 8001d6e:	2b00      	cmp	r3, #0
 8001d70:	d135      	bne.n	8001dde <_puts_r+0x9a>
 8001d72:	4621      	mov	r1, r4
 8001d74:	4628      	mov	r0, r5
 8001d76:	f000 f8c5 	bl	8001f04 <__swsetup_r>
 8001d7a:	b380      	cbz	r0, 8001dde <_puts_r+0x9a>
 8001d7c:	f04f 35ff 	mov.w	r5, #4294967295
 8001d80:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 8001d82:	07da      	lsls	r2, r3, #31
 8001d84:	d405      	bmi.n	8001d92 <_puts_r+0x4e>
 8001d86:	89a3      	ldrh	r3, [r4, #12]
 8001d88:	059b      	lsls	r3, r3, #22
 8001d8a:	d402      	bmi.n	8001d92 <_puts_r+0x4e>
 8001d8c:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 8001d8e:	f000 f989 	bl	80020a4 <__retarget_lock_release_recursive>
 8001d92:	4628      	mov	r0, r5
 8001d94:	bd70      	pop	{r4, r5, r6, pc}
 8001d96:	2b00      	cmp	r3, #0
 8001d98:	da04      	bge.n	8001da4 <_puts_r+0x60>
 8001d9a:	69a2      	ldr	r2, [r4, #24]
 8001d9c:	429a      	cmp	r2, r3
 8001d9e:	dc17      	bgt.n	8001dd0 <_puts_r+0x8c>
 8001da0:	290a      	cmp	r1, #10
 8001da2:	d015      	beq.n	8001dd0 <_puts_r+0x8c>
 8001da4:	6823      	ldr	r3, [r4, #0]
 8001da6:	1c5a      	adds	r2, r3, #1
 8001da8:	6022      	str	r2, [r4, #0]
 8001daa:	7019      	strb	r1, [r3, #0]
 8001dac:	68a3      	ldr	r3, [r4, #8]
 8001dae:	f816 1f01 	ldrb.w	r1, [r6, #1]!
 8001db2:	3b01      	subs	r3, #1
 8001db4:	60a3      	str	r3, [r4, #8]
 8001db6:	2900      	cmp	r1, #0
 8001db8:	d1ed      	bne.n	8001d96 <_puts_r+0x52>
 8001dba:	2b00      	cmp	r3, #0
 8001dbc:	da11      	bge.n	8001de2 <_puts_r+0x9e>
 8001dbe:	4622      	mov	r2, r4
 8001dc0:	210a      	movs	r1, #10
 8001dc2:	4628      	mov	r0, r5
 8001dc4:	f000 f85f 	bl	8001e86 <__swbuf_r>
 8001dc8:	3001      	adds	r0, #1
 8001dca:	d0d7      	beq.n	8001d7c <_puts_r+0x38>
 8001dcc:	250a      	movs	r5, #10
 8001dce:	e7d7      	b.n	8001d80 <_puts_r+0x3c>
 8001dd0:	4622      	mov	r2, r4
 8001dd2:	4628      	mov	r0, r5
 8001dd4:	f000 f857 	bl	8001e86 <__swbuf_r>
 8001dd8:	3001      	adds	r0, #1
 8001dda:	d1e7      	bne.n	8001dac <_puts_r+0x68>
 8001ddc:	e7ce      	b.n	8001d7c <_puts_r+0x38>
 8001dde:	3e01      	subs	r6, #1
 8001de0:	e7e4      	b.n	8001dac <_puts_r+0x68>
 8001de2:	6823      	ldr	r3, [r4, #0]
 8001de4:	1c5a      	adds	r2, r3, #1
 8001de6:	6022      	str	r2, [r4, #0]
 8001de8:	220a      	movs	r2, #10
 8001dea:	701a      	strb	r2, [r3, #0]
 8001dec:	e7ee      	b.n	8001dcc <_puts_r+0x88>
	...

08001df0 <puts>:
 8001df0:	4b02      	ldr	r3, [pc, #8]	@ (8001dfc <puts+0xc>)
 8001df2:	4601      	mov	r1, r0
 8001df4:	6818      	ldr	r0, [r3, #0]
 8001df6:	f7ff bfa5 	b.w	8001d44 <_puts_r>
 8001dfa:	bf00      	nop
 8001dfc:	2000001c 	.word	0x2000001c

08001e00 <__sread>:
 8001e00:	b510      	push	{r4, lr}
 8001e02:	460c      	mov	r4, r1
 8001e04:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8001e08:	f000 f8fc 	bl	8002004 <_read_r>
 8001e0c:	2800      	cmp	r0, #0
 8001e0e:	bfab      	itete	ge
 8001e10:	6d63      	ldrge	r3, [r4, #84]	@ 0x54
 8001e12:	89a3      	ldrhlt	r3, [r4, #12]
 8001e14:	181b      	addge	r3, r3, r0
 8001e16:	f423 5380 	biclt.w	r3, r3, #4096	@ 0x1000
 8001e1a:	bfac      	ite	ge
 8001e1c:	6563      	strge	r3, [r4, #84]	@ 0x54
 8001e1e:	81a3      	strhlt	r3, [r4, #12]
 8001e20:	bd10      	pop	{r4, pc}

08001e22 <__swrite>:
 8001e22:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 8001e26:	461f      	mov	r7, r3
 8001e28:	898b      	ldrh	r3, [r1, #12]
 8001e2a:	4605      	mov	r5, r0
 8001e2c:	05db      	lsls	r3, r3, #23
 8001e2e:	460c      	mov	r4, r1
 8001e30:	4616      	mov	r6, r2
 8001e32:	d505      	bpl.n	8001e40 <__swrite+0x1e>
 8001e34:	2302      	movs	r3, #2
 8001e36:	2200      	movs	r2, #0
 8001e38:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8001e3c:	f000 f8d0 	bl	8001fe0 <_lseek_r>
 8001e40:	89a3      	ldrh	r3, [r4, #12]
 8001e42:	4632      	mov	r2, r6
 8001e44:	f423 5380 	bic.w	r3, r3, #4096	@ 0x1000
 8001e48:	81a3      	strh	r3, [r4, #12]
 8001e4a:	4628      	mov	r0, r5
 8001e4c:	463b      	mov	r3, r7
 8001e4e:	f9b4 100e 	ldrsh.w	r1, [r4, #14]
 8001e52:	e8bd 41f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, lr}
 8001e56:	f000 b8e7 	b.w	8002028 <_write_r>

08001e5a <__sseek>:
 8001e5a:	b510      	push	{r4, lr}
 8001e5c:	460c      	mov	r4, r1
 8001e5e:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8001e62:	f000 f8bd 	bl	8001fe0 <_lseek_r>
 8001e66:	1c43      	adds	r3, r0, #1
 8001e68:	89a3      	ldrh	r3, [r4, #12]
 8001e6a:	bf15      	itete	ne
 8001e6c:	6560      	strne	r0, [r4, #84]	@ 0x54
 8001e6e:	f423 5380 	biceq.w	r3, r3, #4096	@ 0x1000
 8001e72:	f443 5380 	orrne.w	r3, r3, #4096	@ 0x1000
 8001e76:	81a3      	strheq	r3, [r4, #12]
 8001e78:	bf18      	it	ne
 8001e7a:	81a3      	strhne	r3, [r4, #12]
 8001e7c:	bd10      	pop	{r4, pc}

08001e7e <__sclose>:
 8001e7e:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8001e82:	f000 b89d 	b.w	8001fc0 <_close_r>

08001e86 <__swbuf_r>:
 8001e86:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8001e88:	460e      	mov	r6, r1
 8001e8a:	4614      	mov	r4, r2
 8001e8c:	4605      	mov	r5, r0
 8001e8e:	b118      	cbz	r0, 8001e98 <__swbuf_r+0x12>
 8001e90:	6a03      	ldr	r3, [r0, #32]
 8001e92:	b90b      	cbnz	r3, 8001e98 <__swbuf_r+0x12>
 8001e94:	f7ff ff0e 	bl	8001cb4 <__sinit>
 8001e98:	69a3      	ldr	r3, [r4, #24]
 8001e9a:	60a3      	str	r3, [r4, #8]
 8001e9c:	89a3      	ldrh	r3, [r4, #12]
 8001e9e:	071a      	lsls	r2, r3, #28
 8001ea0:	d501      	bpl.n	8001ea6 <__swbuf_r+0x20>
 8001ea2:	6923      	ldr	r3, [r4, #16]
 8001ea4:	b943      	cbnz	r3, 8001eb8 <__swbuf_r+0x32>
 8001ea6:	4621      	mov	r1, r4
 8001ea8:	4628      	mov	r0, r5
 8001eaa:	f000 f82b 	bl	8001f04 <__swsetup_r>
 8001eae:	b118      	cbz	r0, 8001eb8 <__swbuf_r+0x32>
 8001eb0:	f04f 37ff 	mov.w	r7, #4294967295
 8001eb4:	4638      	mov	r0, r7
 8001eb6:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
 8001eb8:	6823      	ldr	r3, [r4, #0]
 8001eba:	6922      	ldr	r2, [r4, #16]
 8001ebc:	b2f6      	uxtb	r6, r6
 8001ebe:	1a98      	subs	r0, r3, r2
 8001ec0:	6963      	ldr	r3, [r4, #20]
 8001ec2:	4637      	mov	r7, r6
 8001ec4:	4283      	cmp	r3, r0
 8001ec6:	dc05      	bgt.n	8001ed4 <__swbuf_r+0x4e>
 8001ec8:	4621      	mov	r1, r4
 8001eca:	4628      	mov	r0, r5
 8001ecc:	f000 fd42 	bl	8002954 <_fflush_r>
 8001ed0:	2800      	cmp	r0, #0
 8001ed2:	d1ed      	bne.n	8001eb0 <__swbuf_r+0x2a>
 8001ed4:	68a3      	ldr	r3, [r4, #8]
 8001ed6:	3b01      	subs	r3, #1
 8001ed8:	60a3      	str	r3, [r4, #8]
 8001eda:	6823      	ldr	r3, [r4, #0]
 8001edc:	1c5a      	adds	r2, r3, #1
 8001ede:	6022      	str	r2, [r4, #0]
 8001ee0:	701e      	strb	r6, [r3, #0]
 8001ee2:	6962      	ldr	r2, [r4, #20]
 8001ee4:	1c43      	adds	r3, r0, #1
 8001ee6:	429a      	cmp	r2, r3
 8001ee8:	d004      	beq.n	8001ef4 <__swbuf_r+0x6e>
 8001eea:	89a3      	ldrh	r3, [r4, #12]
 8001eec:	07db      	lsls	r3, r3, #31
 8001eee:	d5e1      	bpl.n	8001eb4 <__swbuf_r+0x2e>
 8001ef0:	2e0a      	cmp	r6, #10
 8001ef2:	d1df      	bne.n	8001eb4 <__swbuf_r+0x2e>
 8001ef4:	4621      	mov	r1, r4
 8001ef6:	4628      	mov	r0, r5
 8001ef8:	f000 fd2c 	bl	8002954 <_fflush_r>
 8001efc:	2800      	cmp	r0, #0
 8001efe:	d0d9      	beq.n	8001eb4 <__swbuf_r+0x2e>
 8001f00:	e7d6      	b.n	8001eb0 <__swbuf_r+0x2a>
	...

08001f04 <__swsetup_r>:
 8001f04:	b538      	push	{r3, r4, r5, lr}
 8001f06:	4b29      	ldr	r3, [pc, #164]	@ (8001fac <__swsetup_r+0xa8>)
 8001f08:	4605      	mov	r5, r0
 8001f0a:	6818      	ldr	r0, [r3, #0]
 8001f0c:	460c      	mov	r4, r1
 8001f0e:	b118      	cbz	r0, 8001f18 <__swsetup_r+0x14>
 8001f10:	6a03      	ldr	r3, [r0, #32]
 8001f12:	b90b      	cbnz	r3, 8001f18 <__swsetup_r+0x14>
 8001f14:	f7ff fece 	bl	8001cb4 <__sinit>
 8001f18:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8001f1c:	0719      	lsls	r1, r3, #28
 8001f1e:	d422      	bmi.n	8001f66 <__swsetup_r+0x62>
 8001f20:	06da      	lsls	r2, r3, #27
 8001f22:	d407      	bmi.n	8001f34 <__swsetup_r+0x30>
 8001f24:	2209      	movs	r2, #9
 8001f26:	602a      	str	r2, [r5, #0]
 8001f28:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 8001f2c:	f04f 30ff 	mov.w	r0, #4294967295
 8001f30:	81a3      	strh	r3, [r4, #12]
 8001f32:	e033      	b.n	8001f9c <__swsetup_r+0x98>
 8001f34:	0758      	lsls	r0, r3, #29
 8001f36:	d512      	bpl.n	8001f5e <__swsetup_r+0x5a>
 8001f38:	6b61      	ldr	r1, [r4, #52]	@ 0x34
 8001f3a:	b141      	cbz	r1, 8001f4e <__swsetup_r+0x4a>
 8001f3c:	f104 0344 	add.w	r3, r4, #68	@ 0x44
 8001f40:	4299      	cmp	r1, r3
 8001f42:	d002      	beq.n	8001f4a <__swsetup_r+0x46>
 8001f44:	4628      	mov	r0, r5
 8001f46:	f000 f8bd 	bl	80020c4 <_free_r>
 8001f4a:	2300      	movs	r3, #0
 8001f4c:	6363      	str	r3, [r4, #52]	@ 0x34
 8001f4e:	89a3      	ldrh	r3, [r4, #12]
 8001f50:	f023 0324 	bic.w	r3, r3, #36	@ 0x24
 8001f54:	81a3      	strh	r3, [r4, #12]
 8001f56:	2300      	movs	r3, #0
 8001f58:	6063      	str	r3, [r4, #4]
 8001f5a:	6923      	ldr	r3, [r4, #16]
 8001f5c:	6023      	str	r3, [r4, #0]
 8001f5e:	89a3      	ldrh	r3, [r4, #12]
 8001f60:	f043 0308 	orr.w	r3, r3, #8
 8001f64:	81a3      	strh	r3, [r4, #12]
 8001f66:	6923      	ldr	r3, [r4, #16]
 8001f68:	b94b      	cbnz	r3, 8001f7e <__swsetup_r+0x7a>
 8001f6a:	89a3      	ldrh	r3, [r4, #12]
 8001f6c:	f403 7320 	and.w	r3, r3, #640	@ 0x280
 8001f70:	f5b3 7f00 	cmp.w	r3, #512	@ 0x200
 8001f74:	d003      	beq.n	8001f7e <__swsetup_r+0x7a>
 8001f76:	4621      	mov	r1, r4
 8001f78:	4628      	mov	r0, r5
 8001f7a:	f000 fd38 	bl	80029ee <__smakebuf_r>
 8001f7e:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8001f82:	f013 0201 	ands.w	r2, r3, #1
 8001f86:	d00a      	beq.n	8001f9e <__swsetup_r+0x9a>
 8001f88:	2200      	movs	r2, #0
 8001f8a:	60a2      	str	r2, [r4, #8]
 8001f8c:	6962      	ldr	r2, [r4, #20]
 8001f8e:	4252      	negs	r2, r2
 8001f90:	61a2      	str	r2, [r4, #24]
 8001f92:	6922      	ldr	r2, [r4, #16]
 8001f94:	b942      	cbnz	r2, 8001fa8 <__swsetup_r+0xa4>
 8001f96:	f013 0080 	ands.w	r0, r3, #128	@ 0x80
 8001f9a:	d1c5      	bne.n	8001f28 <__swsetup_r+0x24>
 8001f9c:	bd38      	pop	{r3, r4, r5, pc}
 8001f9e:	0799      	lsls	r1, r3, #30
 8001fa0:	bf58      	it	pl
 8001fa2:	6962      	ldrpl	r2, [r4, #20]
 8001fa4:	60a2      	str	r2, [r4, #8]
 8001fa6:	e7f4      	b.n	8001f92 <__swsetup_r+0x8e>
 8001fa8:	2000      	movs	r0, #0
 8001faa:	e7f7      	b.n	8001f9c <__swsetup_r+0x98>
 8001fac:	2000001c 	.word	0x2000001c

08001fb0 <memset>:
 8001fb0:	4603      	mov	r3, r0
 8001fb2:	4402      	add	r2, r0
 8001fb4:	4293      	cmp	r3, r2
 8001fb6:	d100      	bne.n	8001fba <memset+0xa>
 8001fb8:	4770      	bx	lr
 8001fba:	f803 1b01 	strb.w	r1, [r3], #1
 8001fbe:	e7f9      	b.n	8001fb4 <memset+0x4>

08001fc0 <_close_r>:
 8001fc0:	b538      	push	{r3, r4, r5, lr}
 8001fc2:	2300      	movs	r3, #0
 8001fc4:	4d05      	ldr	r5, [pc, #20]	@ (8001fdc <_close_r+0x1c>)
 8001fc6:	4604      	mov	r4, r0
 8001fc8:	4608      	mov	r0, r1
 8001fca:	602b      	str	r3, [r5, #0]
 8001fcc:	f7fe fcde 	bl	800098c <_close>
 8001fd0:	1c43      	adds	r3, r0, #1
 8001fd2:	d102      	bne.n	8001fda <_close_r+0x1a>
 8001fd4:	682b      	ldr	r3, [r5, #0]
 8001fd6:	b103      	cbz	r3, 8001fda <_close_r+0x1a>
 8001fd8:	6023      	str	r3, [r4, #0]
 8001fda:	bd38      	pop	{r3, r4, r5, pc}
 8001fdc:	2000028c 	.word	0x2000028c

08001fe0 <_lseek_r>:
 8001fe0:	b538      	push	{r3, r4, r5, lr}
 8001fe2:	4604      	mov	r4, r0
 8001fe4:	4608      	mov	r0, r1
 8001fe6:	4611      	mov	r1, r2
 8001fe8:	2200      	movs	r2, #0
 8001fea:	4d05      	ldr	r5, [pc, #20]	@ (8002000 <_lseek_r+0x20>)
 8001fec:	602a      	str	r2, [r5, #0]
 8001fee:	461a      	mov	r2, r3
 8001ff0:	f7fe fcd6 	bl	80009a0 <_lseek>
 8001ff4:	1c43      	adds	r3, r0, #1
 8001ff6:	d102      	bne.n	8001ffe <_lseek_r+0x1e>
 8001ff8:	682b      	ldr	r3, [r5, #0]
 8001ffa:	b103      	cbz	r3, 8001ffe <_lseek_r+0x1e>
 8001ffc:	6023      	str	r3, [r4, #0]
 8001ffe:	bd38      	pop	{r3, r4, r5, pc}
 8002000:	2000028c 	.word	0x2000028c

08002004 <_read_r>:
 8002004:	b538      	push	{r3, r4, r5, lr}
 8002006:	4604      	mov	r4, r0
 8002008:	4608      	mov	r0, r1
 800200a:	4611      	mov	r1, r2
 800200c:	2200      	movs	r2, #0
 800200e:	4d05      	ldr	r5, [pc, #20]	@ (8002024 <_read_r+0x20>)
 8002010:	602a      	str	r2, [r5, #0]
 8002012:	461a      	mov	r2, r3
 8002014:	f7fe fc9c 	bl	8000950 <_read>
 8002018:	1c43      	adds	r3, r0, #1
 800201a:	d102      	bne.n	8002022 <_read_r+0x1e>
 800201c:	682b      	ldr	r3, [r5, #0]
 800201e:	b103      	cbz	r3, 8002022 <_read_r+0x1e>
 8002020:	6023      	str	r3, [r4, #0]
 8002022:	bd38      	pop	{r3, r4, r5, pc}
 8002024:	2000028c 	.word	0x2000028c

08002028 <_write_r>:
 8002028:	b538      	push	{r3, r4, r5, lr}
 800202a:	4604      	mov	r4, r0
 800202c:	4608      	mov	r0, r1
 800202e:	4611      	mov	r1, r2
 8002030:	2200      	movs	r2, #0
 8002032:	4d05      	ldr	r5, [pc, #20]	@ (8002048 <_write_r+0x20>)
 8002034:	602a      	str	r2, [r5, #0]
 8002036:	461a      	mov	r2, r3
 8002038:	f7fe fc9a 	bl	8000970 <_write>
 800203c:	1c43      	adds	r3, r0, #1
 800203e:	d102      	bne.n	8002046 <_write_r+0x1e>
 8002040:	682b      	ldr	r3, [r5, #0]
 8002042:	b103      	cbz	r3, 8002046 <_write_r+0x1e>
 8002044:	6023      	str	r3, [r4, #0]
 8002046:	bd38      	pop	{r3, r4, r5, pc}
 8002048:	2000028c 	.word	0x2000028c

0800204c <__errno>:
 800204c:	4b01      	ldr	r3, [pc, #4]	@ (8002054 <__errno+0x8>)
 800204e:	6818      	ldr	r0, [r3, #0]
 8002050:	4770      	bx	lr
 8002052:	bf00      	nop
 8002054:	2000001c 	.word	0x2000001c

08002058 <__libc_init_array>:
 8002058:	b570      	push	{r4, r5, r6, lr}
 800205a:	2600      	movs	r6, #0
 800205c:	4d0c      	ldr	r5, [pc, #48]	@ (8002090 <__libc_init_array+0x38>)
 800205e:	4c0d      	ldr	r4, [pc, #52]	@ (8002094 <__libc_init_array+0x3c>)
 8002060:	1b64      	subs	r4, r4, r5
 8002062:	10a4      	asrs	r4, r4, #2
 8002064:	42a6      	cmp	r6, r4
 8002066:	d109      	bne.n	800207c <__libc_init_array+0x24>
 8002068:	f000 fd3e 	bl	8002ae8 <_init>
 800206c:	2600      	movs	r6, #0
 800206e:	4d0a      	ldr	r5, [pc, #40]	@ (8002098 <__libc_init_array+0x40>)
 8002070:	4c0a      	ldr	r4, [pc, #40]	@ (800209c <__libc_init_array+0x44>)
 8002072:	1b64      	subs	r4, r4, r5
 8002074:	10a4      	asrs	r4, r4, #2
 8002076:	42a6      	cmp	r6, r4
 8002078:	d105      	bne.n	8002086 <__libc_init_array+0x2e>
 800207a:	bd70      	pop	{r4, r5, r6, pc}
 800207c:	f855 3b04 	ldr.w	r3, [r5], #4
 8002080:	4798      	blx	r3
 8002082:	3601      	adds	r6, #1
 8002084:	e7ee      	b.n	8002064 <__libc_init_array+0xc>
 8002086:	f855 3b04 	ldr.w	r3, [r5], #4
 800208a:	4798      	blx	r3
 800208c:	3601      	adds	r6, #1
 800208e:	e7f2      	b.n	8002076 <__libc_init_array+0x1e>
 8002090:	080032ec 	.word	0x080032ec
 8002094:	080032ec 	.word	0x080032ec
 8002098:	080032ec 	.word	0x080032ec
 800209c:	080032f0 	.word	0x080032f0

080020a0 <__retarget_lock_init_recursive>:
 80020a0:	4770      	bx	lr

080020a2 <__retarget_lock_acquire_recursive>:
 80020a2:	4770      	bx	lr

080020a4 <__retarget_lock_release_recursive>:
 80020a4:	4770      	bx	lr

080020a6 <memcpy>:
 80020a6:	440a      	add	r2, r1
 80020a8:	4291      	cmp	r1, r2
 80020aa:	f100 33ff 	add.w	r3, r0, #4294967295
 80020ae:	d100      	bne.n	80020b2 <memcpy+0xc>
 80020b0:	4770      	bx	lr
 80020b2:	b510      	push	{r4, lr}
 80020b4:	f811 4b01 	ldrb.w	r4, [r1], #1
 80020b8:	4291      	cmp	r1, r2
 80020ba:	f803 4f01 	strb.w	r4, [r3, #1]!
 80020be:	d1f9      	bne.n	80020b4 <memcpy+0xe>
 80020c0:	bd10      	pop	{r4, pc}
	...

080020c4 <_free_r>:
 80020c4:	b538      	push	{r3, r4, r5, lr}
 80020c6:	4605      	mov	r5, r0
 80020c8:	2900      	cmp	r1, #0
 80020ca:	d040      	beq.n	800214e <_free_r+0x8a>
 80020cc:	f851 3c04 	ldr.w	r3, [r1, #-4]
 80020d0:	1f0c      	subs	r4, r1, #4
 80020d2:	2b00      	cmp	r3, #0
 80020d4:	bfb8      	it	lt
 80020d6:	18e4      	addlt	r4, r4, r3
 80020d8:	f000 f8de 	bl	8002298 <__malloc_lock>
 80020dc:	4a1c      	ldr	r2, [pc, #112]	@ (8002150 <_free_r+0x8c>)
 80020de:	6813      	ldr	r3, [r2, #0]
 80020e0:	b933      	cbnz	r3, 80020f0 <_free_r+0x2c>
 80020e2:	6063      	str	r3, [r4, #4]
 80020e4:	6014      	str	r4, [r2, #0]
 80020e6:	4628      	mov	r0, r5
 80020e8:	e8bd 4038 	ldmia.w	sp!, {r3, r4, r5, lr}
 80020ec:	f000 b8da 	b.w	80022a4 <__malloc_unlock>
 80020f0:	42a3      	cmp	r3, r4
 80020f2:	d908      	bls.n	8002106 <_free_r+0x42>
 80020f4:	6820      	ldr	r0, [r4, #0]
 80020f6:	1821      	adds	r1, r4, r0
 80020f8:	428b      	cmp	r3, r1
 80020fa:	bf01      	itttt	eq
 80020fc:	6819      	ldreq	r1, [r3, #0]
 80020fe:	685b      	ldreq	r3, [r3, #4]
 8002100:	1809      	addeq	r1, r1, r0
 8002102:	6021      	streq	r1, [r4, #0]
 8002104:	e7ed      	b.n	80020e2 <_free_r+0x1e>
 8002106:	461a      	mov	r2, r3
 8002108:	685b      	ldr	r3, [r3, #4]
 800210a:	b10b      	cbz	r3, 8002110 <_free_r+0x4c>
 800210c:	42a3      	cmp	r3, r4
 800210e:	d9fa      	bls.n	8002106 <_free_r+0x42>
 8002110:	6811      	ldr	r1, [r2, #0]
 8002112:	1850      	adds	r0, r2, r1
 8002114:	42a0      	cmp	r0, r4
 8002116:	d10b      	bne.n	8002130 <_free_r+0x6c>
 8002118:	6820      	ldr	r0, [r4, #0]
 800211a:	4401      	add	r1, r0
 800211c:	1850      	adds	r0, r2, r1
 800211e:	4283      	cmp	r3, r0
 8002120:	6011      	str	r1, [r2, #0]
 8002122:	d1e0      	bne.n	80020e6 <_free_r+0x22>
 8002124:	6818      	ldr	r0, [r3, #0]
 8002126:	685b      	ldr	r3, [r3, #4]
 8002128:	4408      	add	r0, r1
 800212a:	6010      	str	r0, [r2, #0]
 800212c:	6053      	str	r3, [r2, #4]
 800212e:	e7da      	b.n	80020e6 <_free_r+0x22>
 8002130:	d902      	bls.n	8002138 <_free_r+0x74>
 8002132:	230c      	movs	r3, #12
 8002134:	602b      	str	r3, [r5, #0]
 8002136:	e7d6      	b.n	80020e6 <_free_r+0x22>
 8002138:	6820      	ldr	r0, [r4, #0]
 800213a:	1821      	adds	r1, r4, r0
 800213c:	428b      	cmp	r3, r1
 800213e:	bf01      	itttt	eq
 8002140:	6819      	ldreq	r1, [r3, #0]
 8002142:	685b      	ldreq	r3, [r3, #4]
 8002144:	1809      	addeq	r1, r1, r0
 8002146:	6021      	streq	r1, [r4, #0]
 8002148:	6063      	str	r3, [r4, #4]
 800214a:	6054      	str	r4, [r2, #4]
 800214c:	e7cb      	b.n	80020e6 <_free_r+0x22>
 800214e:	bd38      	pop	{r3, r4, r5, pc}
 8002150:	20000298 	.word	0x20000298

08002154 <sbrk_aligned>:
 8002154:	b570      	push	{r4, r5, r6, lr}
 8002156:	4e0f      	ldr	r6, [pc, #60]	@ (8002194 <sbrk_aligned+0x40>)
 8002158:	460c      	mov	r4, r1
 800215a:	6831      	ldr	r1, [r6, #0]
 800215c:	4605      	mov	r5, r0
 800215e:	b911      	cbnz	r1, 8002166 <sbrk_aligned+0x12>
 8002160:	f000 fca4 	bl	8002aac <_sbrk_r>
 8002164:	6030      	str	r0, [r6, #0]
 8002166:	4621      	mov	r1, r4
 8002168:	4628      	mov	r0, r5
 800216a:	f000 fc9f 	bl	8002aac <_sbrk_r>
 800216e:	1c43      	adds	r3, r0, #1
 8002170:	d103      	bne.n	800217a <sbrk_aligned+0x26>
 8002172:	f04f 34ff 	mov.w	r4, #4294967295
 8002176:	4620      	mov	r0, r4
 8002178:	bd70      	pop	{r4, r5, r6, pc}
 800217a:	1cc4      	adds	r4, r0, #3
 800217c:	f024 0403 	bic.w	r4, r4, #3
 8002180:	42a0      	cmp	r0, r4
 8002182:	d0f8      	beq.n	8002176 <sbrk_aligned+0x22>
 8002184:	1a21      	subs	r1, r4, r0
 8002186:	4628      	mov	r0, r5
 8002188:	f000 fc90 	bl	8002aac <_sbrk_r>
 800218c:	3001      	adds	r0, #1
 800218e:	d1f2      	bne.n	8002176 <sbrk_aligned+0x22>
 8002190:	e7ef      	b.n	8002172 <sbrk_aligned+0x1e>
 8002192:	bf00      	nop
 8002194:	20000294 	.word	0x20000294

08002198 <_malloc_r>:
 8002198:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 800219c:	1ccd      	adds	r5, r1, #3
 800219e:	f025 0503 	bic.w	r5, r5, #3
 80021a2:	3508      	adds	r5, #8
 80021a4:	2d0c      	cmp	r5, #12
 80021a6:	bf38      	it	cc
 80021a8:	250c      	movcc	r5, #12
 80021aa:	2d00      	cmp	r5, #0
 80021ac:	4606      	mov	r6, r0
 80021ae:	db01      	blt.n	80021b4 <_malloc_r+0x1c>
 80021b0:	42a9      	cmp	r1, r5
 80021b2:	d904      	bls.n	80021be <_malloc_r+0x26>
 80021b4:	230c      	movs	r3, #12
 80021b6:	6033      	str	r3, [r6, #0]
 80021b8:	2000      	movs	r0, #0
 80021ba:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
 80021be:	f8df 80d4 	ldr.w	r8, [pc, #212]	@ 8002294 <_malloc_r+0xfc>
 80021c2:	f000 f869 	bl	8002298 <__malloc_lock>
 80021c6:	f8d8 3000 	ldr.w	r3, [r8]
 80021ca:	461c      	mov	r4, r3
 80021cc:	bb44      	cbnz	r4, 8002220 <_malloc_r+0x88>
 80021ce:	4629      	mov	r1, r5
 80021d0:	4630      	mov	r0, r6
 80021d2:	f7ff ffbf 	bl	8002154 <sbrk_aligned>
 80021d6:	1c43      	adds	r3, r0, #1
 80021d8:	4604      	mov	r4, r0
 80021da:	d158      	bne.n	800228e <_malloc_r+0xf6>
 80021dc:	f8d8 4000 	ldr.w	r4, [r8]
 80021e0:	4627      	mov	r7, r4
 80021e2:	2f00      	cmp	r7, #0
 80021e4:	d143      	bne.n	800226e <_malloc_r+0xd6>
 80021e6:	2c00      	cmp	r4, #0
 80021e8:	d04b      	beq.n	8002282 <_malloc_r+0xea>
 80021ea:	6823      	ldr	r3, [r4, #0]
 80021ec:	4639      	mov	r1, r7
 80021ee:	4630      	mov	r0, r6
 80021f0:	eb04 0903 	add.w	r9, r4, r3
 80021f4:	f000 fc5a 	bl	8002aac <_sbrk_r>
 80021f8:	4581      	cmp	r9, r0
 80021fa:	d142      	bne.n	8002282 <_malloc_r+0xea>
 80021fc:	6821      	ldr	r1, [r4, #0]
 80021fe:	4630      	mov	r0, r6
 8002200:	1a6d      	subs	r5, r5, r1
 8002202:	4629      	mov	r1, r5
 8002204:	f7ff ffa6 	bl	8002154 <sbrk_aligned>
 8002208:	3001      	adds	r0, #1
 800220a:	d03a      	beq.n	8002282 <_malloc_r+0xea>
 800220c:	6823      	ldr	r3, [r4, #0]
 800220e:	442b      	add	r3, r5
 8002210:	6023      	str	r3, [r4, #0]
 8002212:	f8d8 3000 	ldr.w	r3, [r8]
 8002216:	685a      	ldr	r2, [r3, #4]
 8002218:	bb62      	cbnz	r2, 8002274 <_malloc_r+0xdc>
 800221a:	f8c8 7000 	str.w	r7, [r8]
 800221e:	e00f      	b.n	8002240 <_malloc_r+0xa8>
 8002220:	6822      	ldr	r2, [r4, #0]
 8002222:	1b52      	subs	r2, r2, r5
 8002224:	d420      	bmi.n	8002268 <_malloc_r+0xd0>
 8002226:	2a0b      	cmp	r2, #11
 8002228:	d917      	bls.n	800225a <_malloc_r+0xc2>
 800222a:	1961      	adds	r1, r4, r5
 800222c:	42a3      	cmp	r3, r4
 800222e:	6025      	str	r5, [r4, #0]
 8002230:	bf18      	it	ne
 8002232:	6059      	strne	r1, [r3, #4]
 8002234:	6863      	ldr	r3, [r4, #4]
 8002236:	bf08      	it	eq
 8002238:	f8c8 1000 	streq.w	r1, [r8]
 800223c:	5162      	str	r2, [r4, r5]
 800223e:	604b      	str	r3, [r1, #4]
 8002240:	4630      	mov	r0, r6
 8002242:	f000 f82f 	bl	80022a4 <__malloc_unlock>
 8002246:	f104 000b 	add.w	r0, r4, #11
 800224a:	1d23      	adds	r3, r4, #4
 800224c:	f020 0007 	bic.w	r0, r0, #7
 8002250:	1ac2      	subs	r2, r0, r3
 8002252:	bf1c      	itt	ne
 8002254:	1a1b      	subne	r3, r3, r0
 8002256:	50a3      	strne	r3, [r4, r2]
 8002258:	e7af      	b.n	80021ba <_malloc_r+0x22>
 800225a:	6862      	ldr	r2, [r4, #4]
 800225c:	42a3      	cmp	r3, r4
 800225e:	bf0c      	ite	eq
 8002260:	f8c8 2000 	streq.w	r2, [r8]
 8002264:	605a      	strne	r2, [r3, #4]
 8002266:	e7eb      	b.n	8002240 <_malloc_r+0xa8>
 8002268:	4623      	mov	r3, r4
 800226a:	6864      	ldr	r4, [r4, #4]
 800226c:	e7ae      	b.n	80021cc <_malloc_r+0x34>
 800226e:	463c      	mov	r4, r7
 8002270:	687f      	ldr	r7, [r7, #4]
 8002272:	e7b6      	b.n	80021e2 <_malloc_r+0x4a>
 8002274:	461a      	mov	r2, r3
 8002276:	685b      	ldr	r3, [r3, #4]
 8002278:	42a3      	cmp	r3, r4
 800227a:	d1fb      	bne.n	8002274 <_malloc_r+0xdc>
 800227c:	2300      	movs	r3, #0
 800227e:	6053      	str	r3, [r2, #4]
 8002280:	e7de      	b.n	8002240 <_malloc_r+0xa8>
 8002282:	230c      	movs	r3, #12
 8002284:	4630      	mov	r0, r6
 8002286:	6033      	str	r3, [r6, #0]
 8002288:	f000 f80c 	bl	80022a4 <__malloc_unlock>
 800228c:	e794      	b.n	80021b8 <_malloc_r+0x20>
 800228e:	6005      	str	r5, [r0, #0]
 8002290:	e7d6      	b.n	8002240 <_malloc_r+0xa8>
 8002292:	bf00      	nop
 8002294:	20000298 	.word	0x20000298

08002298 <__malloc_lock>:
 8002298:	4801      	ldr	r0, [pc, #4]	@ (80022a0 <__malloc_lock+0x8>)
 800229a:	f7ff bf02 	b.w	80020a2 <__retarget_lock_acquire_recursive>
 800229e:	bf00      	nop
 80022a0:	20000290 	.word	0x20000290

080022a4 <__malloc_unlock>:
 80022a4:	4801      	ldr	r0, [pc, #4]	@ (80022ac <__malloc_unlock+0x8>)
 80022a6:	f7ff befd 	b.w	80020a4 <__retarget_lock_release_recursive>
 80022aa:	bf00      	nop
 80022ac:	20000290 	.word	0x20000290

080022b0 <__sfputc_r>:
 80022b0:	6893      	ldr	r3, [r2, #8]
 80022b2:	b410      	push	{r4}
 80022b4:	3b01      	subs	r3, #1
 80022b6:	2b00      	cmp	r3, #0
 80022b8:	6093      	str	r3, [r2, #8]
 80022ba:	da07      	bge.n	80022cc <__sfputc_r+0x1c>
 80022bc:	6994      	ldr	r4, [r2, #24]
 80022be:	42a3      	cmp	r3, r4
 80022c0:	db01      	blt.n	80022c6 <__sfputc_r+0x16>
 80022c2:	290a      	cmp	r1, #10
 80022c4:	d102      	bne.n	80022cc <__sfputc_r+0x1c>
 80022c6:	bc10      	pop	{r4}
 80022c8:	f7ff bddd 	b.w	8001e86 <__swbuf_r>
 80022cc:	6813      	ldr	r3, [r2, #0]
 80022ce:	1c58      	adds	r0, r3, #1
 80022d0:	6010      	str	r0, [r2, #0]
 80022d2:	7019      	strb	r1, [r3, #0]
 80022d4:	4608      	mov	r0, r1
 80022d6:	bc10      	pop	{r4}
 80022d8:	4770      	bx	lr

080022da <__sfputs_r>:
 80022da:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 80022dc:	4606      	mov	r6, r0
 80022de:	460f      	mov	r7, r1
 80022e0:	4614      	mov	r4, r2
 80022e2:	18d5      	adds	r5, r2, r3
 80022e4:	42ac      	cmp	r4, r5
 80022e6:	d101      	bne.n	80022ec <__sfputs_r+0x12>
 80022e8:	2000      	movs	r0, #0
 80022ea:	e007      	b.n	80022fc <__sfputs_r+0x22>
 80022ec:	463a      	mov	r2, r7
 80022ee:	4630      	mov	r0, r6
 80022f0:	f814 1b01 	ldrb.w	r1, [r4], #1
 80022f4:	f7ff ffdc 	bl	80022b0 <__sfputc_r>
 80022f8:	1c43      	adds	r3, r0, #1
 80022fa:	d1f3      	bne.n	80022e4 <__sfputs_r+0xa>
 80022fc:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
	...

08002300 <_vfiprintf_r>:
 8002300:	e92d 4ff0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
 8002304:	460d      	mov	r5, r1
 8002306:	4614      	mov	r4, r2
 8002308:	4698      	mov	r8, r3
 800230a:	4606      	mov	r6, r0
 800230c:	b09d      	sub	sp, #116	@ 0x74
 800230e:	b118      	cbz	r0, 8002318 <_vfiprintf_r+0x18>
 8002310:	6a03      	ldr	r3, [r0, #32]
 8002312:	b90b      	cbnz	r3, 8002318 <_vfiprintf_r+0x18>
 8002314:	f7ff fcce 	bl	8001cb4 <__sinit>
 8002318:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 800231a:	07d9      	lsls	r1, r3, #31
 800231c:	d405      	bmi.n	800232a <_vfiprintf_r+0x2a>
 800231e:	89ab      	ldrh	r3, [r5, #12]
 8002320:	059a      	lsls	r2, r3, #22
 8002322:	d402      	bmi.n	800232a <_vfiprintf_r+0x2a>
 8002324:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 8002326:	f7ff febc 	bl	80020a2 <__retarget_lock_acquire_recursive>
 800232a:	89ab      	ldrh	r3, [r5, #12]
 800232c:	071b      	lsls	r3, r3, #28
 800232e:	d501      	bpl.n	8002334 <_vfiprintf_r+0x34>
 8002330:	692b      	ldr	r3, [r5, #16]
 8002332:	b99b      	cbnz	r3, 800235c <_vfiprintf_r+0x5c>
 8002334:	4629      	mov	r1, r5
 8002336:	4630      	mov	r0, r6
 8002338:	f7ff fde4 	bl	8001f04 <__swsetup_r>
 800233c:	b170      	cbz	r0, 800235c <_vfiprintf_r+0x5c>
 800233e:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 8002340:	07dc      	lsls	r4, r3, #31
 8002342:	d504      	bpl.n	800234e <_vfiprintf_r+0x4e>
 8002344:	f04f 30ff 	mov.w	r0, #4294967295
 8002348:	b01d      	add	sp, #116	@ 0x74
 800234a:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
 800234e:	89ab      	ldrh	r3, [r5, #12]
 8002350:	0598      	lsls	r0, r3, #22
 8002352:	d4f7      	bmi.n	8002344 <_vfiprintf_r+0x44>
 8002354:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 8002356:	f7ff fea5 	bl	80020a4 <__retarget_lock_release_recursive>
 800235a:	e7f3      	b.n	8002344 <_vfiprintf_r+0x44>
 800235c:	2300      	movs	r3, #0
 800235e:	9309      	str	r3, [sp, #36]	@ 0x24
 8002360:	2320      	movs	r3, #32
 8002362:	f88d 3029 	strb.w	r3, [sp, #41]	@ 0x29
 8002366:	2330      	movs	r3, #48	@ 0x30
 8002368:	f04f 0901 	mov.w	r9, #1
 800236c:	f8cd 800c 	str.w	r8, [sp, #12]
 8002370:	f8df 81a8 	ldr.w	r8, [pc, #424]	@ 800251c <_vfiprintf_r+0x21c>
 8002374:	f88d 302a 	strb.w	r3, [sp, #42]	@ 0x2a
 8002378:	4623      	mov	r3, r4
 800237a:	469a      	mov	sl, r3
 800237c:	f813 2b01 	ldrb.w	r2, [r3], #1
 8002380:	b10a      	cbz	r2, 8002386 <_vfiprintf_r+0x86>
 8002382:	2a25      	cmp	r2, #37	@ 0x25
 8002384:	d1f9      	bne.n	800237a <_vfiprintf_r+0x7a>
 8002386:	ebba 0b04 	subs.w	fp, sl, r4
 800238a:	d00b      	beq.n	80023a4 <_vfiprintf_r+0xa4>
 800238c:	465b      	mov	r3, fp
 800238e:	4622      	mov	r2, r4
 8002390:	4629      	mov	r1, r5
 8002392:	4630      	mov	r0, r6
 8002394:	f7ff ffa1 	bl	80022da <__sfputs_r>
 8002398:	3001      	adds	r0, #1
 800239a:	f000 80a7 	beq.w	80024ec <_vfiprintf_r+0x1ec>
 800239e:	9a09      	ldr	r2, [sp, #36]	@ 0x24
 80023a0:	445a      	add	r2, fp
 80023a2:	9209      	str	r2, [sp, #36]	@ 0x24
 80023a4:	f89a 3000 	ldrb.w	r3, [sl]
 80023a8:	2b00      	cmp	r3, #0
 80023aa:	f000 809f 	beq.w	80024ec <_vfiprintf_r+0x1ec>
 80023ae:	2300      	movs	r3, #0
 80023b0:	f04f 32ff 	mov.w	r2, #4294967295
 80023b4:	e9cd 2305 	strd	r2, r3, [sp, #20]
 80023b8:	f10a 0a01 	add.w	sl, sl, #1
 80023bc:	9304      	str	r3, [sp, #16]
 80023be:	9307      	str	r3, [sp, #28]
 80023c0:	f88d 3053 	strb.w	r3, [sp, #83]	@ 0x53
 80023c4:	931a      	str	r3, [sp, #104]	@ 0x68
 80023c6:	4654      	mov	r4, sl
 80023c8:	2205      	movs	r2, #5
 80023ca:	f814 1b01 	ldrb.w	r1, [r4], #1
 80023ce:	4853      	ldr	r0, [pc, #332]	@ (800251c <_vfiprintf_r+0x21c>)
 80023d0:	f000 fb7c 	bl	8002acc <memchr>
 80023d4:	9a04      	ldr	r2, [sp, #16]
 80023d6:	b9d8      	cbnz	r0, 8002410 <_vfiprintf_r+0x110>
 80023d8:	06d1      	lsls	r1, r2, #27
 80023da:	bf44      	itt	mi
 80023dc:	2320      	movmi	r3, #32
 80023de:	f88d 3053 	strbmi.w	r3, [sp, #83]	@ 0x53
 80023e2:	0713      	lsls	r3, r2, #28
 80023e4:	bf44      	itt	mi
 80023e6:	232b      	movmi	r3, #43	@ 0x2b
 80023e8:	f88d 3053 	strbmi.w	r3, [sp, #83]	@ 0x53
 80023ec:	f89a 3000 	ldrb.w	r3, [sl]
 80023f0:	2b2a      	cmp	r3, #42	@ 0x2a
 80023f2:	d015      	beq.n	8002420 <_vfiprintf_r+0x120>
 80023f4:	4654      	mov	r4, sl
 80023f6:	2000      	movs	r0, #0
 80023f8:	f04f 0c0a 	mov.w	ip, #10
 80023fc:	9a07      	ldr	r2, [sp, #28]
 80023fe:	4621      	mov	r1, r4
 8002400:	f811 3b01 	ldrb.w	r3, [r1], #1
 8002404:	3b30      	subs	r3, #48	@ 0x30
 8002406:	2b09      	cmp	r3, #9
 8002408:	d94b      	bls.n	80024a2 <_vfiprintf_r+0x1a2>
 800240a:	b1b0      	cbz	r0, 800243a <_vfiprintf_r+0x13a>
 800240c:	9207      	str	r2, [sp, #28]
 800240e:	e014      	b.n	800243a <_vfiprintf_r+0x13a>
 8002410:	eba0 0308 	sub.w	r3, r0, r8
 8002414:	fa09 f303 	lsl.w	r3, r9, r3
 8002418:	4313      	orrs	r3, r2
 800241a:	46a2      	mov	sl, r4
 800241c:	9304      	str	r3, [sp, #16]
 800241e:	e7d2      	b.n	80023c6 <_vfiprintf_r+0xc6>
 8002420:	9b03      	ldr	r3, [sp, #12]
 8002422:	1d19      	adds	r1, r3, #4
 8002424:	681b      	ldr	r3, [r3, #0]
 8002426:	9103      	str	r1, [sp, #12]
 8002428:	2b00      	cmp	r3, #0
 800242a:	bfbb      	ittet	lt
 800242c:	425b      	neglt	r3, r3
 800242e:	f042 0202 	orrlt.w	r2, r2, #2
 8002432:	9307      	strge	r3, [sp, #28]
 8002434:	9307      	strlt	r3, [sp, #28]
 8002436:	bfb8      	it	lt
 8002438:	9204      	strlt	r2, [sp, #16]
 800243a:	7823      	ldrb	r3, [r4, #0]
 800243c:	2b2e      	cmp	r3, #46	@ 0x2e
 800243e:	d10a      	bne.n	8002456 <_vfiprintf_r+0x156>
 8002440:	7863      	ldrb	r3, [r4, #1]
 8002442:	2b2a      	cmp	r3, #42	@ 0x2a
 8002444:	d132      	bne.n	80024ac <_vfiprintf_r+0x1ac>
 8002446:	9b03      	ldr	r3, [sp, #12]
 8002448:	3402      	adds	r4, #2
 800244a:	1d1a      	adds	r2, r3, #4
 800244c:	681b      	ldr	r3, [r3, #0]
 800244e:	9203      	str	r2, [sp, #12]
 8002450:	ea43 73e3 	orr.w	r3, r3, r3, asr #31
 8002454:	9305      	str	r3, [sp, #20]
 8002456:	f8df a0c8 	ldr.w	sl, [pc, #200]	@ 8002520 <_vfiprintf_r+0x220>
 800245a:	2203      	movs	r2, #3
 800245c:	4650      	mov	r0, sl
 800245e:	7821      	ldrb	r1, [r4, #0]
 8002460:	f000 fb34 	bl	8002acc <memchr>
 8002464:	b138      	cbz	r0, 8002476 <_vfiprintf_r+0x176>
 8002466:	2240      	movs	r2, #64	@ 0x40
 8002468:	9b04      	ldr	r3, [sp, #16]
 800246a:	eba0 000a 	sub.w	r0, r0, sl
 800246e:	4082      	lsls	r2, r0
 8002470:	4313      	orrs	r3, r2
 8002472:	3401      	adds	r4, #1
 8002474:	9304      	str	r3, [sp, #16]
 8002476:	f814 1b01 	ldrb.w	r1, [r4], #1
 800247a:	2206      	movs	r2, #6
 800247c:	4829      	ldr	r0, [pc, #164]	@ (8002524 <_vfiprintf_r+0x224>)
 800247e:	f88d 1028 	strb.w	r1, [sp, #40]	@ 0x28
 8002482:	f000 fb23 	bl	8002acc <memchr>
 8002486:	2800      	cmp	r0, #0
 8002488:	d03f      	beq.n	800250a <_vfiprintf_r+0x20a>
 800248a:	4b27      	ldr	r3, [pc, #156]	@ (8002528 <_vfiprintf_r+0x228>)
 800248c:	bb1b      	cbnz	r3, 80024d6 <_vfiprintf_r+0x1d6>
 800248e:	9b03      	ldr	r3, [sp, #12]
 8002490:	3307      	adds	r3, #7
 8002492:	f023 0307 	bic.w	r3, r3, #7
 8002496:	3308      	adds	r3, #8
 8002498:	9303      	str	r3, [sp, #12]
 800249a:	9b09      	ldr	r3, [sp, #36]	@ 0x24
 800249c:	443b      	add	r3, r7
 800249e:	9309      	str	r3, [sp, #36]	@ 0x24
 80024a0:	e76a      	b.n	8002378 <_vfiprintf_r+0x78>
 80024a2:	460c      	mov	r4, r1
 80024a4:	2001      	movs	r0, #1
 80024a6:	fb0c 3202 	mla	r2, ip, r2, r3
 80024aa:	e7a8      	b.n	80023fe <_vfiprintf_r+0xfe>
 80024ac:	2300      	movs	r3, #0
 80024ae:	f04f 0c0a 	mov.w	ip, #10
 80024b2:	4619      	mov	r1, r3
 80024b4:	3401      	adds	r4, #1
 80024b6:	9305      	str	r3, [sp, #20]
 80024b8:	4620      	mov	r0, r4
 80024ba:	f810 2b01 	ldrb.w	r2, [r0], #1
 80024be:	3a30      	subs	r2, #48	@ 0x30
 80024c0:	2a09      	cmp	r2, #9
 80024c2:	d903      	bls.n	80024cc <_vfiprintf_r+0x1cc>
 80024c4:	2b00      	cmp	r3, #0
 80024c6:	d0c6      	beq.n	8002456 <_vfiprintf_r+0x156>
 80024c8:	9105      	str	r1, [sp, #20]
 80024ca:	e7c4      	b.n	8002456 <_vfiprintf_r+0x156>
 80024cc:	4604      	mov	r4, r0
 80024ce:	2301      	movs	r3, #1
 80024d0:	fb0c 2101 	mla	r1, ip, r1, r2
 80024d4:	e7f0      	b.n	80024b8 <_vfiprintf_r+0x1b8>
 80024d6:	ab03      	add	r3, sp, #12
 80024d8:	9300      	str	r3, [sp, #0]
 80024da:	462a      	mov	r2, r5
 80024dc:	4630      	mov	r0, r6
 80024de:	4b13      	ldr	r3, [pc, #76]	@ (800252c <_vfiprintf_r+0x22c>)
 80024e0:	a904      	add	r1, sp, #16
 80024e2:	f3af 8000 	nop.w
 80024e6:	4607      	mov	r7, r0
 80024e8:	1c78      	adds	r0, r7, #1
 80024ea:	d1d6      	bne.n	800249a <_vfiprintf_r+0x19a>
 80024ec:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 80024ee:	07d9      	lsls	r1, r3, #31
 80024f0:	d405      	bmi.n	80024fe <_vfiprintf_r+0x1fe>
 80024f2:	89ab      	ldrh	r3, [r5, #12]
 80024f4:	059a      	lsls	r2, r3, #22
 80024f6:	d402      	bmi.n	80024fe <_vfiprintf_r+0x1fe>
 80024f8:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 80024fa:	f7ff fdd3 	bl	80020a4 <__retarget_lock_release_recursive>
 80024fe:	89ab      	ldrh	r3, [r5, #12]
 8002500:	065b      	lsls	r3, r3, #25
 8002502:	f53f af1f 	bmi.w	8002344 <_vfiprintf_r+0x44>
 8002506:	9809      	ldr	r0, [sp, #36]	@ 0x24
 8002508:	e71e      	b.n	8002348 <_vfiprintf_r+0x48>
 800250a:	ab03      	add	r3, sp, #12
 800250c:	9300      	str	r3, [sp, #0]
 800250e:	462a      	mov	r2, r5
 8002510:	4630      	mov	r0, r6
 8002512:	4b06      	ldr	r3, [pc, #24]	@ (800252c <_vfiprintf_r+0x22c>)
 8002514:	a904      	add	r1, sp, #16
 8002516:	f000 f87d 	bl	8002614 <_printf_i>
 800251a:	e7e4      	b.n	80024e6 <_vfiprintf_r+0x1e6>
 800251c:	080032b9 	.word	0x080032b9
 8002520:	080032bf 	.word	0x080032bf
 8002524:	080032c3 	.word	0x080032c3
 8002528:	00000000 	.word	0x00000000
 800252c:	080022db 	.word	0x080022db

08002530 <_printf_common>:
 8002530:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
 8002534:	4616      	mov	r6, r2
 8002536:	4698      	mov	r8, r3
 8002538:	688a      	ldr	r2, [r1, #8]
 800253a:	690b      	ldr	r3, [r1, #16]
 800253c:	4607      	mov	r7, r0
 800253e:	4293      	cmp	r3, r2
 8002540:	bfb8      	it	lt
 8002542:	4613      	movlt	r3, r2
 8002544:	6033      	str	r3, [r6, #0]
 8002546:	f891 2043 	ldrb.w	r2, [r1, #67]	@ 0x43
 800254a:	460c      	mov	r4, r1
 800254c:	f8dd 9020 	ldr.w	r9, [sp, #32]
 8002550:	b10a      	cbz	r2, 8002556 <_printf_common+0x26>
 8002552:	3301      	adds	r3, #1
 8002554:	6033      	str	r3, [r6, #0]
 8002556:	6823      	ldr	r3, [r4, #0]
 8002558:	0699      	lsls	r1, r3, #26
 800255a:	bf42      	ittt	mi
 800255c:	6833      	ldrmi	r3, [r6, #0]
 800255e:	3302      	addmi	r3, #2
 8002560:	6033      	strmi	r3, [r6, #0]
 8002562:	6825      	ldr	r5, [r4, #0]
 8002564:	f015 0506 	ands.w	r5, r5, #6
 8002568:	d106      	bne.n	8002578 <_printf_common+0x48>
 800256a:	f104 0a19 	add.w	sl, r4, #25
 800256e:	68e3      	ldr	r3, [r4, #12]
 8002570:	6832      	ldr	r2, [r6, #0]
 8002572:	1a9b      	subs	r3, r3, r2
 8002574:	42ab      	cmp	r3, r5
 8002576:	dc2b      	bgt.n	80025d0 <_printf_common+0xa0>
 8002578:	f894 3043 	ldrb.w	r3, [r4, #67]	@ 0x43
 800257c:	6822      	ldr	r2, [r4, #0]
 800257e:	3b00      	subs	r3, #0
 8002580:	bf18      	it	ne
 8002582:	2301      	movne	r3, #1
 8002584:	0692      	lsls	r2, r2, #26
 8002586:	d430      	bmi.n	80025ea <_printf_common+0xba>
 8002588:	4641      	mov	r1, r8
 800258a:	4638      	mov	r0, r7
 800258c:	f104 0243 	add.w	r2, r4, #67	@ 0x43
 8002590:	47c8      	blx	r9
 8002592:	3001      	adds	r0, #1
 8002594:	d023      	beq.n	80025de <_printf_common+0xae>
 8002596:	6823      	ldr	r3, [r4, #0]
 8002598:	6922      	ldr	r2, [r4, #16]
 800259a:	f003 0306 	and.w	r3, r3, #6
 800259e:	2b04      	cmp	r3, #4
 80025a0:	bf14      	ite	ne
 80025a2:	2500      	movne	r5, #0
 80025a4:	6833      	ldreq	r3, [r6, #0]
 80025a6:	f04f 0600 	mov.w	r6, #0
 80025aa:	bf08      	it	eq
 80025ac:	68e5      	ldreq	r5, [r4, #12]
 80025ae:	f104 041a 	add.w	r4, r4, #26
 80025b2:	bf08      	it	eq
 80025b4:	1aed      	subeq	r5, r5, r3
 80025b6:	f854 3c12 	ldr.w	r3, [r4, #-18]
 80025ba:	bf08      	it	eq
 80025bc:	ea25 75e5 	biceq.w	r5, r5, r5, asr #31
 80025c0:	4293      	cmp	r3, r2
 80025c2:	bfc4      	itt	gt
 80025c4:	1a9b      	subgt	r3, r3, r2
 80025c6:	18ed      	addgt	r5, r5, r3
 80025c8:	42b5      	cmp	r5, r6
 80025ca:	d11a      	bne.n	8002602 <_printf_common+0xd2>
 80025cc:	2000      	movs	r0, #0
 80025ce:	e008      	b.n	80025e2 <_printf_common+0xb2>
 80025d0:	2301      	movs	r3, #1
 80025d2:	4652      	mov	r2, sl
 80025d4:	4641      	mov	r1, r8
 80025d6:	4638      	mov	r0, r7
 80025d8:	47c8      	blx	r9
 80025da:	3001      	adds	r0, #1
 80025dc:	d103      	bne.n	80025e6 <_printf_common+0xb6>
 80025de:	f04f 30ff 	mov.w	r0, #4294967295
 80025e2:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 80025e6:	3501      	adds	r5, #1
 80025e8:	e7c1      	b.n	800256e <_printf_common+0x3e>
 80025ea:	2030      	movs	r0, #48	@ 0x30
 80025ec:	18e1      	adds	r1, r4, r3
 80025ee:	f881 0043 	strb.w	r0, [r1, #67]	@ 0x43
 80025f2:	1c5a      	adds	r2, r3, #1
 80025f4:	f894 1045 	ldrb.w	r1, [r4, #69]	@ 0x45
 80025f8:	4422      	add	r2, r4
 80025fa:	3302      	adds	r3, #2
 80025fc:	f882 1043 	strb.w	r1, [r2, #67]	@ 0x43
 8002600:	e7c2      	b.n	8002588 <_printf_common+0x58>
 8002602:	2301      	movs	r3, #1
 8002604:	4622      	mov	r2, r4
 8002606:	4641      	mov	r1, r8
 8002608:	4638      	mov	r0, r7
 800260a:	47c8      	blx	r9
 800260c:	3001      	adds	r0, #1
 800260e:	d0e6      	beq.n	80025de <_printf_common+0xae>
 8002610:	3601      	adds	r6, #1
 8002612:	e7d9      	b.n	80025c8 <_printf_common+0x98>

08002614 <_printf_i>:
 8002614:	e92d 47ff 	stmdb	sp!, {r0, r1, r2, r3, r4, r5, r6, r7, r8, r9, sl, lr}
 8002618:	7e0f      	ldrb	r7, [r1, #24]
 800261a:	4691      	mov	r9, r2
 800261c:	2f78      	cmp	r7, #120	@ 0x78
 800261e:	4680      	mov	r8, r0
 8002620:	460c      	mov	r4, r1
 8002622:	469a      	mov	sl, r3
 8002624:	9e0c      	ldr	r6, [sp, #48]	@ 0x30
 8002626:	f101 0243 	add.w	r2, r1, #67	@ 0x43
 800262a:	d807      	bhi.n	800263c <_printf_i+0x28>
 800262c:	2f62      	cmp	r7, #98	@ 0x62
 800262e:	d80a      	bhi.n	8002646 <_printf_i+0x32>
 8002630:	2f00      	cmp	r7, #0
 8002632:	f000 80d3 	beq.w	80027dc <_printf_i+0x1c8>
 8002636:	2f58      	cmp	r7, #88	@ 0x58
 8002638:	f000 80ba 	beq.w	80027b0 <_printf_i+0x19c>
 800263c:	f104 0642 	add.w	r6, r4, #66	@ 0x42
 8002640:	f884 7042 	strb.w	r7, [r4, #66]	@ 0x42
 8002644:	e03a      	b.n	80026bc <_printf_i+0xa8>
 8002646:	f1a7 0363 	sub.w	r3, r7, #99	@ 0x63
 800264a:	2b15      	cmp	r3, #21
 800264c:	d8f6      	bhi.n	800263c <_printf_i+0x28>
 800264e:	a101      	add	r1, pc, #4	@ (adr r1, 8002654 <_printf_i+0x40>)
 8002650:	f851 f023 	ldr.w	pc, [r1, r3, lsl #2]
 8002654:	080026ad 	.word	0x080026ad
 8002658:	080026c1 	.word	0x080026c1
 800265c:	0800263d 	.word	0x0800263d
 8002660:	0800263d 	.word	0x0800263d
 8002664:	0800263d 	.word	0x0800263d
 8002668:	0800263d 	.word	0x0800263d
 800266c:	080026c1 	.word	0x080026c1
 8002670:	0800263d 	.word	0x0800263d
 8002674:	0800263d 	.word	0x0800263d
 8002678:	0800263d 	.word	0x0800263d
 800267c:	0800263d 	.word	0x0800263d
 8002680:	080027c3 	.word	0x080027c3
 8002684:	080026eb 	.word	0x080026eb
 8002688:	0800277d 	.word	0x0800277d
 800268c:	0800263d 	.word	0x0800263d
 8002690:	0800263d 	.word	0x0800263d
 8002694:	080027e5 	.word	0x080027e5
 8002698:	0800263d 	.word	0x0800263d
 800269c:	080026eb 	.word	0x080026eb
 80026a0:	0800263d 	.word	0x0800263d
 80026a4:	0800263d 	.word	0x0800263d
 80026a8:	08002785 	.word	0x08002785
 80026ac:	6833      	ldr	r3, [r6, #0]
 80026ae:	1d1a      	adds	r2, r3, #4
 80026b0:	681b      	ldr	r3, [r3, #0]
 80026b2:	6032      	str	r2, [r6, #0]
 80026b4:	f104 0642 	add.w	r6, r4, #66	@ 0x42
 80026b8:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
 80026bc:	2301      	movs	r3, #1
 80026be:	e09e      	b.n	80027fe <_printf_i+0x1ea>
 80026c0:	6833      	ldr	r3, [r6, #0]
 80026c2:	6820      	ldr	r0, [r4, #0]
 80026c4:	1d19      	adds	r1, r3, #4
 80026c6:	6031      	str	r1, [r6, #0]
 80026c8:	0606      	lsls	r6, r0, #24
 80026ca:	d501      	bpl.n	80026d0 <_printf_i+0xbc>
 80026cc:	681d      	ldr	r5, [r3, #0]
 80026ce:	e003      	b.n	80026d8 <_printf_i+0xc4>
 80026d0:	0645      	lsls	r5, r0, #25
 80026d2:	d5fb      	bpl.n	80026cc <_printf_i+0xb8>
 80026d4:	f9b3 5000 	ldrsh.w	r5, [r3]
 80026d8:	2d00      	cmp	r5, #0
 80026da:	da03      	bge.n	80026e4 <_printf_i+0xd0>
 80026dc:	232d      	movs	r3, #45	@ 0x2d
 80026de:	426d      	negs	r5, r5
 80026e0:	f884 3043 	strb.w	r3, [r4, #67]	@ 0x43
 80026e4:	230a      	movs	r3, #10
 80026e6:	4859      	ldr	r0, [pc, #356]	@ (800284c <_printf_i+0x238>)
 80026e8:	e011      	b.n	800270e <_printf_i+0xfa>
 80026ea:	6821      	ldr	r1, [r4, #0]
 80026ec:	6833      	ldr	r3, [r6, #0]
 80026ee:	0608      	lsls	r0, r1, #24
 80026f0:	f853 5b04 	ldr.w	r5, [r3], #4
 80026f4:	d402      	bmi.n	80026fc <_printf_i+0xe8>
 80026f6:	0649      	lsls	r1, r1, #25
 80026f8:	bf48      	it	mi
 80026fa:	b2ad      	uxthmi	r5, r5
 80026fc:	2f6f      	cmp	r7, #111	@ 0x6f
 80026fe:	6033      	str	r3, [r6, #0]
 8002700:	bf14      	ite	ne
 8002702:	230a      	movne	r3, #10
 8002704:	2308      	moveq	r3, #8
 8002706:	4851      	ldr	r0, [pc, #324]	@ (800284c <_printf_i+0x238>)
 8002708:	2100      	movs	r1, #0
 800270a:	f884 1043 	strb.w	r1, [r4, #67]	@ 0x43
 800270e:	6866      	ldr	r6, [r4, #4]
 8002710:	2e00      	cmp	r6, #0
 8002712:	bfa8      	it	ge
 8002714:	6821      	ldrge	r1, [r4, #0]
 8002716:	60a6      	str	r6, [r4, #8]
 8002718:	bfa4      	itt	ge
 800271a:	f021 0104 	bicge.w	r1, r1, #4
 800271e:	6021      	strge	r1, [r4, #0]
 8002720:	b90d      	cbnz	r5, 8002726 <_printf_i+0x112>
 8002722:	2e00      	cmp	r6, #0
 8002724:	d04b      	beq.n	80027be <_printf_i+0x1aa>
 8002726:	4616      	mov	r6, r2
 8002728:	fbb5 f1f3 	udiv	r1, r5, r3
 800272c:	fb03 5711 	mls	r7, r3, r1, r5
 8002730:	5dc7      	ldrb	r7, [r0, r7]
 8002732:	f806 7d01 	strb.w	r7, [r6, #-1]!
 8002736:	462f      	mov	r7, r5
 8002738:	42bb      	cmp	r3, r7
 800273a:	460d      	mov	r5, r1
 800273c:	d9f4      	bls.n	8002728 <_printf_i+0x114>
 800273e:	2b08      	cmp	r3, #8
 8002740:	d10b      	bne.n	800275a <_printf_i+0x146>
 8002742:	6823      	ldr	r3, [r4, #0]
 8002744:	07df      	lsls	r7, r3, #31
 8002746:	d508      	bpl.n	800275a <_printf_i+0x146>
 8002748:	6923      	ldr	r3, [r4, #16]
 800274a:	6861      	ldr	r1, [r4, #4]
 800274c:	4299      	cmp	r1, r3
 800274e:	bfde      	ittt	le
 8002750:	2330      	movle	r3, #48	@ 0x30
 8002752:	f806 3c01 	strble.w	r3, [r6, #-1]
 8002756:	f106 36ff 	addle.w	r6, r6, #4294967295
 800275a:	1b92      	subs	r2, r2, r6
 800275c:	6122      	str	r2, [r4, #16]
 800275e:	464b      	mov	r3, r9
 8002760:	4621      	mov	r1, r4
 8002762:	4640      	mov	r0, r8
 8002764:	f8cd a000 	str.w	sl, [sp]
 8002768:	aa03      	add	r2, sp, #12
 800276a:	f7ff fee1 	bl	8002530 <_printf_common>
 800276e:	3001      	adds	r0, #1
 8002770:	d14a      	bne.n	8002808 <_printf_i+0x1f4>
 8002772:	f04f 30ff 	mov.w	r0, #4294967295
 8002776:	b004      	add	sp, #16
 8002778:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 800277c:	6823      	ldr	r3, [r4, #0]
 800277e:	f043 0320 	orr.w	r3, r3, #32
 8002782:	6023      	str	r3, [r4, #0]
 8002784:	2778      	movs	r7, #120	@ 0x78
 8002786:	4832      	ldr	r0, [pc, #200]	@ (8002850 <_printf_i+0x23c>)
 8002788:	f884 7045 	strb.w	r7, [r4, #69]	@ 0x45
 800278c:	6823      	ldr	r3, [r4, #0]
 800278e:	6831      	ldr	r1, [r6, #0]
 8002790:	061f      	lsls	r7, r3, #24
 8002792:	f851 5b04 	ldr.w	r5, [r1], #4
 8002796:	d402      	bmi.n	800279e <_printf_i+0x18a>
 8002798:	065f      	lsls	r7, r3, #25
 800279a:	bf48      	it	mi
 800279c:	b2ad      	uxthmi	r5, r5
 800279e:	6031      	str	r1, [r6, #0]
 80027a0:	07d9      	lsls	r1, r3, #31
 80027a2:	bf44      	itt	mi
 80027a4:	f043 0320 	orrmi.w	r3, r3, #32
 80027a8:	6023      	strmi	r3, [r4, #0]
 80027aa:	b11d      	cbz	r5, 80027b4 <_printf_i+0x1a0>
 80027ac:	2310      	movs	r3, #16
 80027ae:	e7ab      	b.n	8002708 <_printf_i+0xf4>
 80027b0:	4826      	ldr	r0, [pc, #152]	@ (800284c <_printf_i+0x238>)
 80027b2:	e7e9      	b.n	8002788 <_printf_i+0x174>
 80027b4:	6823      	ldr	r3, [r4, #0]
 80027b6:	f023 0320 	bic.w	r3, r3, #32
 80027ba:	6023      	str	r3, [r4, #0]
 80027bc:	e7f6      	b.n	80027ac <_printf_i+0x198>
 80027be:	4616      	mov	r6, r2
 80027c0:	e7bd      	b.n	800273e <_printf_i+0x12a>
 80027c2:	6833      	ldr	r3, [r6, #0]
 80027c4:	6825      	ldr	r5, [r4, #0]
 80027c6:	1d18      	adds	r0, r3, #4
 80027c8:	6961      	ldr	r1, [r4, #20]
 80027ca:	6030      	str	r0, [r6, #0]
 80027cc:	062e      	lsls	r6, r5, #24
 80027ce:	681b      	ldr	r3, [r3, #0]
 80027d0:	d501      	bpl.n	80027d6 <_printf_i+0x1c2>
 80027d2:	6019      	str	r1, [r3, #0]
 80027d4:	e002      	b.n	80027dc <_printf_i+0x1c8>
 80027d6:	0668      	lsls	r0, r5, #25
 80027d8:	d5fb      	bpl.n	80027d2 <_printf_i+0x1be>
 80027da:	8019      	strh	r1, [r3, #0]
 80027dc:	2300      	movs	r3, #0
 80027de:	4616      	mov	r6, r2
 80027e0:	6123      	str	r3, [r4, #16]
 80027e2:	e7bc      	b.n	800275e <_printf_i+0x14a>
 80027e4:	6833      	ldr	r3, [r6, #0]
 80027e6:	2100      	movs	r1, #0
 80027e8:	1d1a      	adds	r2, r3, #4
 80027ea:	6032      	str	r2, [r6, #0]
 80027ec:	681e      	ldr	r6, [r3, #0]
 80027ee:	6862      	ldr	r2, [r4, #4]
 80027f0:	4630      	mov	r0, r6
 80027f2:	f000 f96b 	bl	8002acc <memchr>
 80027f6:	b108      	cbz	r0, 80027fc <_printf_i+0x1e8>
 80027f8:	1b80      	subs	r0, r0, r6
 80027fa:	6060      	str	r0, [r4, #4]
 80027fc:	6863      	ldr	r3, [r4, #4]
 80027fe:	6123      	str	r3, [r4, #16]
 8002800:	2300      	movs	r3, #0
 8002802:	f884 3043 	strb.w	r3, [r4, #67]	@ 0x43
 8002806:	e7aa      	b.n	800275e <_printf_i+0x14a>
 8002808:	4632      	mov	r2, r6
 800280a:	4649      	mov	r1, r9
 800280c:	4640      	mov	r0, r8
 800280e:	6923      	ldr	r3, [r4, #16]
 8002810:	47d0      	blx	sl
 8002812:	3001      	adds	r0, #1
 8002814:	d0ad      	beq.n	8002772 <_printf_i+0x15e>
 8002816:	6823      	ldr	r3, [r4, #0]
 8002818:	079b      	lsls	r3, r3, #30
 800281a:	d413      	bmi.n	8002844 <_printf_i+0x230>
 800281c:	68e0      	ldr	r0, [r4, #12]
 800281e:	9b03      	ldr	r3, [sp, #12]
 8002820:	4298      	cmp	r0, r3
 8002822:	bfb8      	it	lt
 8002824:	4618      	movlt	r0, r3
 8002826:	e7a6      	b.n	8002776 <_printf_i+0x162>
 8002828:	2301      	movs	r3, #1
 800282a:	4632      	mov	r2, r6
 800282c:	4649      	mov	r1, r9
 800282e:	4640      	mov	r0, r8
 8002830:	47d0      	blx	sl
 8002832:	3001      	adds	r0, #1
 8002834:	d09d      	beq.n	8002772 <_printf_i+0x15e>
 8002836:	3501      	adds	r5, #1
 8002838:	68e3      	ldr	r3, [r4, #12]
 800283a:	9903      	ldr	r1, [sp, #12]
 800283c:	1a5b      	subs	r3, r3, r1
 800283e:	42ab      	cmp	r3, r5
 8002840:	dcf2      	bgt.n	8002828 <_printf_i+0x214>
 8002842:	e7eb      	b.n	800281c <_printf_i+0x208>
 8002844:	2500      	movs	r5, #0
 8002846:	f104 0619 	add.w	r6, r4, #25
 800284a:	e7f5      	b.n	8002838 <_printf_i+0x224>
 800284c:	080032ca 	.word	0x080032ca
 8002850:	080032db 	.word	0x080032db

08002854 <__sflush_r>:
 8002854:	f9b1 200c 	ldrsh.w	r2, [r1, #12]
 8002858:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 800285a:	0716      	lsls	r6, r2, #28
 800285c:	4605      	mov	r5, r0
 800285e:	460c      	mov	r4, r1
 8002860:	d454      	bmi.n	800290c <__sflush_r+0xb8>
 8002862:	684b      	ldr	r3, [r1, #4]
 8002864:	2b00      	cmp	r3, #0
 8002866:	dc02      	bgt.n	800286e <__sflush_r+0x1a>
 8002868:	6c0b      	ldr	r3, [r1, #64]	@ 0x40
 800286a:	2b00      	cmp	r3, #0
 800286c:	dd48      	ble.n	8002900 <__sflush_r+0xac>
 800286e:	6ae6      	ldr	r6, [r4, #44]	@ 0x2c
 8002870:	2e00      	cmp	r6, #0
 8002872:	d045      	beq.n	8002900 <__sflush_r+0xac>
 8002874:	2300      	movs	r3, #0
 8002876:	f412 5280 	ands.w	r2, r2, #4096	@ 0x1000
 800287a:	682f      	ldr	r7, [r5, #0]
 800287c:	6a21      	ldr	r1, [r4, #32]
 800287e:	602b      	str	r3, [r5, #0]
 8002880:	d030      	beq.n	80028e4 <__sflush_r+0x90>
 8002882:	6d62      	ldr	r2, [r4, #84]	@ 0x54
 8002884:	89a3      	ldrh	r3, [r4, #12]
 8002886:	0759      	lsls	r1, r3, #29
 8002888:	d505      	bpl.n	8002896 <__sflush_r+0x42>
 800288a:	6863      	ldr	r3, [r4, #4]
 800288c:	1ad2      	subs	r2, r2, r3
 800288e:	6b63      	ldr	r3, [r4, #52]	@ 0x34
 8002890:	b10b      	cbz	r3, 8002896 <__sflush_r+0x42>
 8002892:	6c23      	ldr	r3, [r4, #64]	@ 0x40
 8002894:	1ad2      	subs	r2, r2, r3
 8002896:	2300      	movs	r3, #0
 8002898:	4628      	mov	r0, r5
 800289a:	6ae6      	ldr	r6, [r4, #44]	@ 0x2c
 800289c:	6a21      	ldr	r1, [r4, #32]
 800289e:	47b0      	blx	r6
 80028a0:	1c43      	adds	r3, r0, #1
 80028a2:	89a3      	ldrh	r3, [r4, #12]
 80028a4:	d106      	bne.n	80028b4 <__sflush_r+0x60>
 80028a6:	6829      	ldr	r1, [r5, #0]
 80028a8:	291d      	cmp	r1, #29
 80028aa:	d82b      	bhi.n	8002904 <__sflush_r+0xb0>
 80028ac:	4a28      	ldr	r2, [pc, #160]	@ (8002950 <__sflush_r+0xfc>)
 80028ae:	410a      	asrs	r2, r1
 80028b0:	07d6      	lsls	r6, r2, #31
 80028b2:	d427      	bmi.n	8002904 <__sflush_r+0xb0>
 80028b4:	2200      	movs	r2, #0
 80028b6:	6062      	str	r2, [r4, #4]
 80028b8:	6922      	ldr	r2, [r4, #16]
 80028ba:	04d9      	lsls	r1, r3, #19
 80028bc:	6022      	str	r2, [r4, #0]
 80028be:	d504      	bpl.n	80028ca <__sflush_r+0x76>
 80028c0:	1c42      	adds	r2, r0, #1
 80028c2:	d101      	bne.n	80028c8 <__sflush_r+0x74>
 80028c4:	682b      	ldr	r3, [r5, #0]
 80028c6:	b903      	cbnz	r3, 80028ca <__sflush_r+0x76>
 80028c8:	6560      	str	r0, [r4, #84]	@ 0x54
 80028ca:	6b61      	ldr	r1, [r4, #52]	@ 0x34
 80028cc:	602f      	str	r7, [r5, #0]
 80028ce:	b1b9      	cbz	r1, 8002900 <__sflush_r+0xac>
 80028d0:	f104 0344 	add.w	r3, r4, #68	@ 0x44
 80028d4:	4299      	cmp	r1, r3
 80028d6:	d002      	beq.n	80028de <__sflush_r+0x8a>
 80028d8:	4628      	mov	r0, r5
 80028da:	f7ff fbf3 	bl	80020c4 <_free_r>
 80028de:	2300      	movs	r3, #0
 80028e0:	6363      	str	r3, [r4, #52]	@ 0x34
 80028e2:	e00d      	b.n	8002900 <__sflush_r+0xac>
 80028e4:	2301      	movs	r3, #1
 80028e6:	4628      	mov	r0, r5
 80028e8:	47b0      	blx	r6
 80028ea:	4602      	mov	r2, r0
 80028ec:	1c50      	adds	r0, r2, #1
 80028ee:	d1c9      	bne.n	8002884 <__sflush_r+0x30>
 80028f0:	682b      	ldr	r3, [r5, #0]
 80028f2:	2b00      	cmp	r3, #0
 80028f4:	d0c6      	beq.n	8002884 <__sflush_r+0x30>
 80028f6:	2b1d      	cmp	r3, #29
 80028f8:	d001      	beq.n	80028fe <__sflush_r+0xaa>
 80028fa:	2b16      	cmp	r3, #22
 80028fc:	d11d      	bne.n	800293a <__sflush_r+0xe6>
 80028fe:	602f      	str	r7, [r5, #0]
 8002900:	2000      	movs	r0, #0
 8002902:	e021      	b.n	8002948 <__sflush_r+0xf4>
 8002904:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 8002908:	b21b      	sxth	r3, r3
 800290a:	e01a      	b.n	8002942 <__sflush_r+0xee>
 800290c:	690f      	ldr	r7, [r1, #16]
 800290e:	2f00      	cmp	r7, #0
 8002910:	d0f6      	beq.n	8002900 <__sflush_r+0xac>
 8002912:	0793      	lsls	r3, r2, #30
 8002914:	bf18      	it	ne
 8002916:	2300      	movne	r3, #0
 8002918:	680e      	ldr	r6, [r1, #0]
 800291a:	bf08      	it	eq
 800291c:	694b      	ldreq	r3, [r1, #20]
 800291e:	1bf6      	subs	r6, r6, r7
 8002920:	600f      	str	r7, [r1, #0]
 8002922:	608b      	str	r3, [r1, #8]
 8002924:	2e00      	cmp	r6, #0
 8002926:	ddeb      	ble.n	8002900 <__sflush_r+0xac>
 8002928:	4633      	mov	r3, r6
 800292a:	463a      	mov	r2, r7
 800292c:	4628      	mov	r0, r5
 800292e:	6a21      	ldr	r1, [r4, #32]
 8002930:	f8d4 c028 	ldr.w	ip, [r4, #40]	@ 0x28
 8002934:	47e0      	blx	ip
 8002936:	2800      	cmp	r0, #0
 8002938:	dc07      	bgt.n	800294a <__sflush_r+0xf6>
 800293a:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 800293e:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 8002942:	f04f 30ff 	mov.w	r0, #4294967295
 8002946:	81a3      	strh	r3, [r4, #12]
 8002948:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
 800294a:	4407      	add	r7, r0
 800294c:	1a36      	subs	r6, r6, r0
 800294e:	e7e9      	b.n	8002924 <__sflush_r+0xd0>
 8002950:	dfbffffe 	.word	0xdfbffffe

08002954 <_fflush_r>:
 8002954:	b538      	push	{r3, r4, r5, lr}
 8002956:	690b      	ldr	r3, [r1, #16]
 8002958:	4605      	mov	r5, r0
 800295a:	460c      	mov	r4, r1
 800295c:	b913      	cbnz	r3, 8002964 <_fflush_r+0x10>
 800295e:	2500      	movs	r5, #0
 8002960:	4628      	mov	r0, r5
 8002962:	bd38      	pop	{r3, r4, r5, pc}
 8002964:	b118      	cbz	r0, 800296e <_fflush_r+0x1a>
 8002966:	6a03      	ldr	r3, [r0, #32]
 8002968:	b90b      	cbnz	r3, 800296e <_fflush_r+0x1a>
 800296a:	f7ff f9a3 	bl	8001cb4 <__sinit>
 800296e:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8002972:	2b00      	cmp	r3, #0
 8002974:	d0f3      	beq.n	800295e <_fflush_r+0xa>
 8002976:	6e62      	ldr	r2, [r4, #100]	@ 0x64
 8002978:	07d0      	lsls	r0, r2, #31
 800297a:	d404      	bmi.n	8002986 <_fflush_r+0x32>
 800297c:	0599      	lsls	r1, r3, #22
 800297e:	d402      	bmi.n	8002986 <_fflush_r+0x32>
 8002980:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 8002982:	f7ff fb8e 	bl	80020a2 <__retarget_lock_acquire_recursive>
 8002986:	4628      	mov	r0, r5
 8002988:	4621      	mov	r1, r4
 800298a:	f7ff ff63 	bl	8002854 <__sflush_r>
 800298e:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 8002990:	4605      	mov	r5, r0
 8002992:	07da      	lsls	r2, r3, #31
 8002994:	d4e4      	bmi.n	8002960 <_fflush_r+0xc>
 8002996:	89a3      	ldrh	r3, [r4, #12]
 8002998:	059b      	lsls	r3, r3, #22
 800299a:	d4e1      	bmi.n	8002960 <_fflush_r+0xc>
 800299c:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 800299e:	f7ff fb81 	bl	80020a4 <__retarget_lock_release_recursive>
 80029a2:	e7dd      	b.n	8002960 <_fflush_r+0xc>

080029a4 <__swhatbuf_r>:
 80029a4:	b570      	push	{r4, r5, r6, lr}
 80029a6:	460c      	mov	r4, r1
 80029a8:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 80029ac:	4615      	mov	r5, r2
 80029ae:	2900      	cmp	r1, #0
 80029b0:	461e      	mov	r6, r3
 80029b2:	b096      	sub	sp, #88	@ 0x58
 80029b4:	da0c      	bge.n	80029d0 <__swhatbuf_r+0x2c>
 80029b6:	89a3      	ldrh	r3, [r4, #12]
 80029b8:	2100      	movs	r1, #0
 80029ba:	f013 0f80 	tst.w	r3, #128	@ 0x80
 80029be:	bf14      	ite	ne
 80029c0:	2340      	movne	r3, #64	@ 0x40
 80029c2:	f44f 6380 	moveq.w	r3, #1024	@ 0x400
 80029c6:	2000      	movs	r0, #0
 80029c8:	6031      	str	r1, [r6, #0]
 80029ca:	602b      	str	r3, [r5, #0]
 80029cc:	b016      	add	sp, #88	@ 0x58
 80029ce:	bd70      	pop	{r4, r5, r6, pc}
 80029d0:	466a      	mov	r2, sp
 80029d2:	f000 f849 	bl	8002a68 <_fstat_r>
 80029d6:	2800      	cmp	r0, #0
 80029d8:	dbed      	blt.n	80029b6 <__swhatbuf_r+0x12>
 80029da:	9901      	ldr	r1, [sp, #4]
 80029dc:	f401 4170 	and.w	r1, r1, #61440	@ 0xf000
 80029e0:	f5a1 5300 	sub.w	r3, r1, #8192	@ 0x2000
 80029e4:	4259      	negs	r1, r3
 80029e6:	4159      	adcs	r1, r3
 80029e8:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 80029ec:	e7eb      	b.n	80029c6 <__swhatbuf_r+0x22>

080029ee <__smakebuf_r>:
 80029ee:	898b      	ldrh	r3, [r1, #12]
 80029f0:	b5f7      	push	{r0, r1, r2, r4, r5, r6, r7, lr}
 80029f2:	079d      	lsls	r5, r3, #30
 80029f4:	4606      	mov	r6, r0
 80029f6:	460c      	mov	r4, r1
 80029f8:	d507      	bpl.n	8002a0a <__smakebuf_r+0x1c>
 80029fa:	f104 0347 	add.w	r3, r4, #71	@ 0x47
 80029fe:	6023      	str	r3, [r4, #0]
 8002a00:	6123      	str	r3, [r4, #16]
 8002a02:	2301      	movs	r3, #1
 8002a04:	6163      	str	r3, [r4, #20]
 8002a06:	b003      	add	sp, #12
 8002a08:	bdf0      	pop	{r4, r5, r6, r7, pc}
 8002a0a:	466a      	mov	r2, sp
 8002a0c:	ab01      	add	r3, sp, #4
 8002a0e:	f7ff ffc9 	bl	80029a4 <__swhatbuf_r>
 8002a12:	9f00      	ldr	r7, [sp, #0]
 8002a14:	4605      	mov	r5, r0
 8002a16:	4639      	mov	r1, r7
 8002a18:	4630      	mov	r0, r6
 8002a1a:	f7ff fbbd 	bl	8002198 <_malloc_r>
 8002a1e:	b948      	cbnz	r0, 8002a34 <__smakebuf_r+0x46>
 8002a20:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8002a24:	059a      	lsls	r2, r3, #22
 8002a26:	d4ee      	bmi.n	8002a06 <__smakebuf_r+0x18>
 8002a28:	f023 0303 	bic.w	r3, r3, #3
 8002a2c:	f043 0302 	orr.w	r3, r3, #2
 8002a30:	81a3      	strh	r3, [r4, #12]
 8002a32:	e7e2      	b.n	80029fa <__smakebuf_r+0xc>
 8002a34:	89a3      	ldrh	r3, [r4, #12]
 8002a36:	e9c4 0704 	strd	r0, r7, [r4, #16]
 8002a3a:	f043 0380 	orr.w	r3, r3, #128	@ 0x80
 8002a3e:	81a3      	strh	r3, [r4, #12]
 8002a40:	9b01      	ldr	r3, [sp, #4]
 8002a42:	6020      	str	r0, [r4, #0]
 8002a44:	b15b      	cbz	r3, 8002a5e <__smakebuf_r+0x70>
 8002a46:	4630      	mov	r0, r6
 8002a48:	f9b4 100e 	ldrsh.w	r1, [r4, #14]
 8002a4c:	f000 f81e 	bl	8002a8c <_isatty_r>
 8002a50:	b128      	cbz	r0, 8002a5e <__smakebuf_r+0x70>
 8002a52:	89a3      	ldrh	r3, [r4, #12]
 8002a54:	f023 0303 	bic.w	r3, r3, #3
 8002a58:	f043 0301 	orr.w	r3, r3, #1
 8002a5c:	81a3      	strh	r3, [r4, #12]
 8002a5e:	89a3      	ldrh	r3, [r4, #12]
 8002a60:	431d      	orrs	r5, r3
 8002a62:	81a5      	strh	r5, [r4, #12]
 8002a64:	e7cf      	b.n	8002a06 <__smakebuf_r+0x18>
	...

08002a68 <_fstat_r>:
 8002a68:	b538      	push	{r3, r4, r5, lr}
 8002a6a:	2300      	movs	r3, #0
 8002a6c:	4d06      	ldr	r5, [pc, #24]	@ (8002a88 <_fstat_r+0x20>)
 8002a6e:	4604      	mov	r4, r0
 8002a70:	4608      	mov	r0, r1
 8002a72:	4611      	mov	r1, r2
 8002a74:	602b      	str	r3, [r5, #0]
 8002a76:	f7fd ff8c 	bl	8000992 <_fstat>
 8002a7a:	1c43      	adds	r3, r0, #1
 8002a7c:	d102      	bne.n	8002a84 <_fstat_r+0x1c>
 8002a7e:	682b      	ldr	r3, [r5, #0]
 8002a80:	b103      	cbz	r3, 8002a84 <_fstat_r+0x1c>
 8002a82:	6023      	str	r3, [r4, #0]
 8002a84:	bd38      	pop	{r3, r4, r5, pc}
 8002a86:	bf00      	nop
 8002a88:	2000028c 	.word	0x2000028c

08002a8c <_isatty_r>:
 8002a8c:	b538      	push	{r3, r4, r5, lr}
 8002a8e:	2300      	movs	r3, #0
 8002a90:	4d05      	ldr	r5, [pc, #20]	@ (8002aa8 <_isatty_r+0x1c>)
 8002a92:	4604      	mov	r4, r0
 8002a94:	4608      	mov	r0, r1
 8002a96:	602b      	str	r3, [r5, #0]
 8002a98:	f7fd ff80 	bl	800099c <_isatty>
 8002a9c:	1c43      	adds	r3, r0, #1
 8002a9e:	d102      	bne.n	8002aa6 <_isatty_r+0x1a>
 8002aa0:	682b      	ldr	r3, [r5, #0]
 8002aa2:	b103      	cbz	r3, 8002aa6 <_isatty_r+0x1a>
 8002aa4:	6023      	str	r3, [r4, #0]
 8002aa6:	bd38      	pop	{r3, r4, r5, pc}
 8002aa8:	2000028c 	.word	0x2000028c

08002aac <_sbrk_r>:
 8002aac:	b538      	push	{r3, r4, r5, lr}
 8002aae:	2300      	movs	r3, #0
 8002ab0:	4d05      	ldr	r5, [pc, #20]	@ (8002ac8 <_sbrk_r+0x1c>)
 8002ab2:	4604      	mov	r4, r0
 8002ab4:	4608      	mov	r0, r1
 8002ab6:	602b      	str	r3, [r5, #0]
 8002ab8:	f7fd ff74 	bl	80009a4 <_sbrk>
 8002abc:	1c43      	adds	r3, r0, #1
 8002abe:	d102      	bne.n	8002ac6 <_sbrk_r+0x1a>
 8002ac0:	682b      	ldr	r3, [r5, #0]
 8002ac2:	b103      	cbz	r3, 8002ac6 <_sbrk_r+0x1a>
 8002ac4:	6023      	str	r3, [r4, #0]
 8002ac6:	bd38      	pop	{r3, r4, r5, pc}
 8002ac8:	2000028c 	.word	0x2000028c

08002acc <memchr>:
 8002acc:	4603      	mov	r3, r0
 8002ace:	b510      	push	{r4, lr}
 8002ad0:	b2c9      	uxtb	r1, r1
 8002ad2:	4402      	add	r2, r0
 8002ad4:	4293      	cmp	r3, r2
 8002ad6:	4618      	mov	r0, r3
 8002ad8:	d101      	bne.n	8002ade <memchr+0x12>
 8002ada:	2000      	movs	r0, #0
 8002adc:	e003      	b.n	8002ae6 <memchr+0x1a>
 8002ade:	7804      	ldrb	r4, [r0, #0]
 8002ae0:	3301      	adds	r3, #1
 8002ae2:	428c      	cmp	r4, r1
 8002ae4:	d1f6      	bne.n	8002ad4 <memchr+0x8>
 8002ae6:	bd10      	pop	{r4, pc}

08002ae8 <_init>:
 8002ae8:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8002aea:	bf00      	nop
 8002aec:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8002aee:	bc08      	pop	{r3}
 8002af0:	469e      	mov	lr, r3
 8002af2:	4770      	bx	lr

08002af4 <_fini>:
 8002af4:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8002af6:	bf00      	nop
 8002af8:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8002afa:	bc08      	pop	{r3}
 8002afc:	469e      	mov	lr, r3
 8002afe:	4770      	bx	lr
