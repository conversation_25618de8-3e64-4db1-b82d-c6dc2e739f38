
Bootloader.elf:     file format elf32-littlearm

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .isr_vector   0000010c  08000000  08000000  00001000  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  1 .text         00002660  0800010c  0800010c  0000110c  2**2
                  CONTENTS, ALL<PERSON>, LOAD, READ<PERSON>L<PERSON>, CODE
  2 .rodata       00000270  0800276c  0800276c  0000376c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  3 .ARM.extab    00000000  080029dc  080029dc  0000406c  2**0
                  CONTENTS
  4 .ARM          00000000  080029dc  080029dc  0000406c  2**0
                  CONTENTS
  5 .preinit_array 00000000  080029dc  080029dc  0000406c  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  6 .init_array   00000004  080029dc  080029dc  000039dc  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  7 .fini_array   00000004  080029e0  080029e0  000039e0  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .data         0000006c  20000000  080029e4  00004000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  9 .bss          00000224  20000070  08002a50  00004070  2**3
                  ALLOC
 10 ._user_heap_stack 00000604  20000294  08002a50  00004294  2**0
                  ALLOC
 11 .ARM.attributes 00000029  00000000  00000000  0000406c  2**0
                  CONTENTS, READONLY
 12 .debug_info   00009c1d  00000000  00000000  00004095  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 13 .debug_abbrev 00001c18  00000000  00000000  0000dcb2  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 14 .debug_loclists 000042cc  00000000  00000000  0000f8ca  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 15 .debug_aranges 000007c8  00000000  00000000  00013b98  2**3
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 16 .debug_rnglists 000005eb  00000000  00000000  00014360  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 17 .debug_macro  00017b88  00000000  00000000  0001494b  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 18 .debug_line   0000c61f  00000000  00000000  0002c4d3  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 19 .debug_str    000842e7  00000000  00000000  00038af2  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 20 .comment      00000043  00000000  00000000  000bcdd9  2**0
                  CONTENTS, READONLY
 21 .debug_frame  00001aec  00000000  00000000  000bce1c  2**2
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 22 .debug_line_str 00000096  00000000  00000000  000be908  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS

Disassembly of section .text:

0800010c <__do_global_dtors_aux>:
 800010c:	b510      	push	{r4, lr}
 800010e:	4c05      	ldr	r4, [pc, #20]	@ (8000124 <__do_global_dtors_aux+0x18>)
 8000110:	7823      	ldrb	r3, [r4, #0]
 8000112:	b933      	cbnz	r3, 8000122 <__do_global_dtors_aux+0x16>
 8000114:	4b04      	ldr	r3, [pc, #16]	@ (8000128 <__do_global_dtors_aux+0x1c>)
 8000116:	b113      	cbz	r3, 800011e <__do_global_dtors_aux+0x12>
 8000118:	4804      	ldr	r0, [pc, #16]	@ (800012c <__do_global_dtors_aux+0x20>)
 800011a:	f3af 8000 	nop.w
 800011e:	2301      	movs	r3, #1
 8000120:	7023      	strb	r3, [r4, #0]
 8000122:	bd10      	pop	{r4, pc}
 8000124:	20000070 	.word	0x20000070
 8000128:	00000000 	.word	0x00000000
 800012c:	08002754 	.word	0x08002754

08000130 <frame_dummy>:
 8000130:	b508      	push	{r3, lr}
 8000132:	4b03      	ldr	r3, [pc, #12]	@ (8000140 <frame_dummy+0x10>)
 8000134:	b11b      	cbz	r3, 800013e <frame_dummy+0xe>
 8000136:	4903      	ldr	r1, [pc, #12]	@ (8000144 <frame_dummy+0x14>)
 8000138:	4803      	ldr	r0, [pc, #12]	@ (8000148 <frame_dummy+0x18>)
 800013a:	f3af 8000 	nop.w
 800013e:	bd08      	pop	{r3, pc}
 8000140:	00000000 	.word	0x00000000
 8000144:	20000074 	.word	0x20000074
 8000148:	08002754 	.word	0x08002754

0800014c <MX_GPIO_Init>:
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
 800014c:	b530      	push	{r4, r5, lr}
 800014e:	b087      	sub	sp, #28
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 8000150:	2400      	movs	r4, #0
 8000152:	9402      	str	r4, [sp, #8]
 8000154:	9403      	str	r4, [sp, #12]
 8000156:	9404      	str	r4, [sp, #16]
 8000158:	9405      	str	r4, [sp, #20]
/* USER CODE BEGIN MX_GPIO_Init_1 */
/* USER CODE END MX_GPIO_Init_1 */

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOB_CLK_ENABLE();
 800015a:	4b14      	ldr	r3, [pc, #80]	@ (80001ac <MX_GPIO_Init+0x60>)
 800015c:	699a      	ldr	r2, [r3, #24]
 800015e:	f042 0208 	orr.w	r2, r2, #8
 8000162:	619a      	str	r2, [r3, #24]
 8000164:	699a      	ldr	r2, [r3, #24]
 8000166:	f002 0208 	and.w	r2, r2, #8
 800016a:	9200      	str	r2, [sp, #0]
 800016c:	9a00      	ldr	r2, [sp, #0]
  __HAL_RCC_GPIOA_CLK_ENABLE();
 800016e:	699a      	ldr	r2, [r3, #24]
 8000170:	f042 0204 	orr.w	r2, r2, #4
 8000174:	619a      	str	r2, [r3, #24]
 8000176:	699b      	ldr	r3, [r3, #24]
 8000178:	f003 0304 	and.w	r3, r3, #4
 800017c:	9301      	str	r3, [sp, #4]
 800017e:	9b01      	ldr	r3, [sp, #4]

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET);
 8000180:	4d0b      	ldr	r5, [pc, #44]	@ (80001b0 <MX_GPIO_Init+0x64>)
 8000182:	4622      	mov	r2, r4
 8000184:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000188:	4628      	mov	r0, r5
 800018a:	f000 fe5d 	bl	8000e48 <HAL_GPIO_WritePin>

  /*Configure GPIO pin : PB13 */
  GPIO_InitStruct.Pin = GPIO_PIN_13;
 800018e:	f44f 5300 	mov.w	r3, #8192	@ 0x2000
 8000192:	9302      	str	r3, [sp, #8]
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 8000194:	2301      	movs	r3, #1
 8000196:	9303      	str	r3, [sp, #12]
  GPIO_InitStruct.Pull = GPIO_NOPULL;
 8000198:	9404      	str	r4, [sp, #16]
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 800019a:	2302      	movs	r3, #2
 800019c:	9305      	str	r3, [sp, #20]
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 800019e:	a902      	add	r1, sp, #8
 80001a0:	4628      	mov	r0, r5
 80001a2:	f000 fd43 	bl	8000c2c <HAL_GPIO_Init>

/* USER CODE BEGIN MX_GPIO_Init_2 */
/* USER CODE END MX_GPIO_Init_2 */
}
 80001a6:	b007      	add	sp, #28
 80001a8:	bd30      	pop	{r4, r5, pc}
 80001aa:	bf00      	nop
 80001ac:	40021000 	.word	0x40021000
 80001b0:	40010c00 	.word	0x40010c00

080001b4 <goto_application>:
    while(1);
  }
}

static void goto_application( void )
{
 80001b4:	b510      	push	{r4, lr}
	printf("Gonna Jump to Application...\n");
 80001b6:	480d      	ldr	r0, [pc, #52]	@ (80001ec <goto_application+0x38>)
 80001b8:	f001 fc5e 	bl	8001a78 <puts>
	void (*app_reset_handler)(void) = (void*)(*((volatile uint32_t*)(ETX_APP_START_ADDRESS + 4U)));
 80001bc:	4b0c      	ldr	r3, [pc, #48]	@ (80001f0 <goto_application+0x3c>)
 80001be:	f8d3 4404 	ldr.w	r4, [r3, #1028]	@ 0x404

	if( app_reset_handler == (void*)0xFFFFFFFF )
 80001c2:	f1b4 3fff 	cmp.w	r4, #4294967295
 80001c6:	d00c      	beq.n	80001e2 <goto_application+0x2e>
	{
	  printf("Invalid Application... HALT!!!\r\n");
	  while(1);
	}

	__set_MSP(*(volatile uint32_t*) ETX_APP_START_ADDRESS);
 80001c8:	4b09      	ldr	r3, [pc, #36]	@ (80001f0 <goto_application+0x3c>)
 80001ca:	f8d3 3400 	ldr.w	r3, [r3, #1024]	@ 0x400
  \details Assigns the given value to the Main Stack Pointer (MSP).
  \param [in]    topOfMainStack  Main Stack Pointer value to set
 */
__STATIC_FORCEINLINE void __set_MSP(uint32_t topOfMainStack)
{
  __ASM volatile ("MSR msp, %0" : : "r" (topOfMainStack) : );
 80001ce:	f383 8808 	msr	MSP, r3

	// Turn OFF the Led to tell the user that Bootloader is not running
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET );
 80001d2:	2200      	movs	r2, #0
 80001d4:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 80001d8:	4806      	ldr	r0, [pc, #24]	@ (80001f4 <goto_application+0x40>)
 80001da:	f000 fe35 	bl	8000e48 <HAL_GPIO_WritePin>

	app_reset_handler();    //call the app reset handler
 80001de:	47a0      	blx	r4
}
 80001e0:	bd10      	pop	{r4, pc}
	  printf("Invalid Application... HALT!!!\r\n");
 80001e2:	4805      	ldr	r0, [pc, #20]	@ (80001f8 <goto_application+0x44>)
 80001e4:	f001 fc48 	bl	8001a78 <puts>
	  while(1);
 80001e8:	e7fe      	b.n	80001e8 <goto_application+0x34>
 80001ea:	bf00      	nop
 80001ec:	0800276c 	.word	0x0800276c
 80001f0:	08004000 	.word	0x08004000
 80001f4:	40010c00 	.word	0x40010c00
 80001f8:	0800278c 	.word	0x0800278c

080001fc <UART_Write_Loop>:
{
 80001fc:	b530      	push	{r4, r5, lr}
 80001fe:	b083      	sub	sp, #12
  printf("\n Press 'o' to start Firmware Update...\r\n");
 8000200:	481b      	ldr	r0, [pc, #108]	@ (8000270 <UART_Write_Loop+0x74>)
 8000202:	f001 fc39 	bl	8001a78 <puts>
  char tx = '.';
 8000206:	232e      	movs	r3, #46	@ 0x2e
 8000208:	f88d 3007 	strb.w	r3, [sp, #7]
  char rx = '0';
 800020c:	2330      	movs	r3, #48	@ 0x30
 800020e:	f88d 3006 	strb.w	r3, [sp, #6]
  int count = 0;
 8000212:	2500      	movs	r5, #0
 8000214:	e005      	b.n	8000222 <UART_Write_Loop+0x26>
    if( count == 250 )  // Changed from 100 to 250 to make it 5 seconds (250 * 20ms = 5000ms = 5 seconds)
 8000216:	2dfa      	cmp	r5, #250	@ 0xfa
 8000218:	d024      	beq.n	8000264 <UART_Write_Loop+0x68>
    count++;
 800021a:	3501      	adds	r5, #1
    HAL_Delay(20);              //20ms delay
 800021c:	2014      	movs	r0, #20
 800021e:	f000 fb23 	bl	8000868 <HAL_Delay>
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_13);
 8000222:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000226:	4813      	ldr	r0, [pc, #76]	@ (8000274 <UART_Write_Loop+0x78>)
 8000228:	f000 fe14 	bl	8000e54 <HAL_GPIO_TogglePin>
    HAL_UART_Transmit(&huart3, (uint8_t *)&tx, 1, HAL_MAX_DELAY);
 800022c:	4c12      	ldr	r4, [pc, #72]	@ (8000278 <UART_Write_Loop+0x7c>)
 800022e:	f04f 33ff 	mov.w	r3, #4294967295
 8000232:	2201      	movs	r2, #1
 8000234:	f10d 0107 	add.w	r1, sp, #7
 8000238:	4620      	mov	r0, r4
 800023a:	f001 fa24 	bl	8001686 <HAL_UART_Transmit>
    ex = HAL_UART_Receive(&huart3, (uint8_t *)&rx, 1, 20);  // Changed from 10ms to 20ms to give Arduino more time to respond
 800023e:	2314      	movs	r3, #20
 8000240:	2201      	movs	r2, #1
 8000242:	f10d 0106 	add.w	r1, sp, #6
 8000246:	4620      	mov	r0, r4
 8000248:	f001 fa83 	bl	8001752 <HAL_UART_Receive>
    if( ( ex == HAL_OK ) && ( rx == 'o' ) )
 800024c:	2800      	cmp	r0, #0
 800024e:	d1e2      	bne.n	8000216 <UART_Write_Loop+0x1a>
 8000250:	f89d 3006 	ldrb.w	r3, [sp, #6]
 8000254:	2b6f      	cmp	r3, #111	@ 0x6f
 8000256:	d1de      	bne.n	8000216 <UART_Write_Loop+0x1a>
      printf("Firmware Update Started\r\n");
 8000258:	4808      	ldr	r0, [pc, #32]	@ (800027c <UART_Write_Loop+0x80>)
 800025a:	f001 fc0d 	bl	8001a78 <puts>
      ret = 1;
 800025e:	2001      	movs	r0, #1
}
 8000260:	b003      	add	sp, #12
 8000262:	bd30      	pop	{r4, r5, pc}
      printf("No Data Received for Firmware Update\r\n");
 8000264:	4806      	ldr	r0, [pc, #24]	@ (8000280 <UART_Write_Loop+0x84>)
 8000266:	f001 fc07 	bl	8001a78 <puts>
  int ret = 0;
 800026a:	2000      	movs	r0, #0
      break;
 800026c:	e7f8      	b.n	8000260 <UART_Write_Loop+0x64>
 800026e:	bf00      	nop
 8000270:	080027ac 	.word	0x080027ac
 8000274:	40010c00 	.word	0x40010c00
 8000278:	20000090 	.word	0x20000090
 800027c:	080027d8 	.word	0x080027d8
 8000280:	080027f4 	.word	0x080027f4

08000284 <write_data_to_flash_app>:
{
 8000284:	b5f0      	push	{r4, r5, r6, r7, lr}
 8000286:	b087      	sub	sp, #28
 8000288:	4605      	mov	r5, r0
 800028a:	460e      	mov	r6, r1
 800028c:	4614      	mov	r4, r2
    ret = HAL_FLASH_Unlock();
 800028e:	f000 fbaf 	bl	80009f0 <HAL_FLASH_Unlock>
    if( ret != HAL_OK )
 8000292:	4607      	mov	r7, r0
 8000294:	2800      	cmp	r0, #0
 8000296:	d134      	bne.n	8000302 <write_data_to_flash_app+0x7e>
    if( is_first_block )
 8000298:	b9e4      	cbnz	r4, 80002d4 <write_data_to_flash_app+0x50>
{
 800029a:	2400      	movs	r4, #0
    for(int i = 0; i < data_len/2; i++)
 800029c:	ebb4 0f56 	cmp.w	r4, r6, lsr #1
 80002a0:	da2e      	bge.n	8000300 <write_data_to_flash_app+0x7c>
      uint16_t halfword_data = data[i * 2] | (data[i * 2 + 1] << 8);
 80002a2:	f815 3014 	ldrb.w	r3, [r5, r4, lsl #1]
 80002a6:	eb05 0244 	add.w	r2, r5, r4, lsl #1
 80002aa:	7852      	ldrb	r2, [r2, #1]
                               (ETX_APP_START_ADDRESS + application_write_idx ),
 80002ac:	4918      	ldr	r1, [pc, #96]	@ (8000310 <write_data_to_flash_app+0x8c>)
 80002ae:	8809      	ldrh	r1, [r1, #0]
      ret = HAL_FLASH_Program( FLASH_TYPEPROGRAM_HALFWORD,
 80002b0:	ea43 2202 	orr.w	r2, r3, r2, lsl #8
 80002b4:	2300      	movs	r3, #0
 80002b6:	f101 6100 	add.w	r1, r1, #134217728	@ 0x8000000
 80002ba:	f501 4188 	add.w	r1, r1, #17408	@ 0x4400
 80002be:	2001      	movs	r0, #1
 80002c0:	f000 fbee 	bl	8000aa0 <HAL_FLASH_Program>
      if( ret == HAL_OK )
 80002c4:	4607      	mov	r7, r0
 80002c6:	b9c0      	cbnz	r0, 80002fa <write_data_to_flash_app+0x76>
        application_write_idx += 2;
 80002c8:	4a11      	ldr	r2, [pc, #68]	@ (8000310 <write_data_to_flash_app+0x8c>)
 80002ca:	8813      	ldrh	r3, [r2, #0]
 80002cc:	3302      	adds	r3, #2
 80002ce:	8013      	strh	r3, [r2, #0]
    for(int i = 0; i < data_len/2; i++)
 80002d0:	3401      	adds	r4, #1
 80002d2:	e7e3      	b.n	800029c <write_data_to_flash_app+0x18>
      printf("Erasing the Flash memory...\r\n");
 80002d4:	480f      	ldr	r0, [pc, #60]	@ (8000314 <write_data_to_flash_app+0x90>)
 80002d6:	f001 fbcf 	bl	8001a78 <puts>
      EraseInitStruct.TypeErase     = FLASH_TYPEERASE_PAGES;
 80002da:	2300      	movs	r3, #0
 80002dc:	9302      	str	r3, [sp, #8]
      EraseInitStruct.PageAddress   = ETX_APP_START_ADDRESS;
 80002de:	4b0e      	ldr	r3, [pc, #56]	@ (8000318 <write_data_to_flash_app+0x94>)
 80002e0:	9304      	str	r3, [sp, #16]
      EraseInitStruct.NbPages       = 47;                     //47 Pages
 80002e2:	232f      	movs	r3, #47	@ 0x2f
 80002e4:	9305      	str	r3, [sp, #20]
      ret = HAL_FLASHEx_Erase( &EraseInitStruct, &SectorError );
 80002e6:	a901      	add	r1, sp, #4
 80002e8:	a802      	add	r0, sp, #8
 80002ea:	f000 fc4d 	bl	8000b88 <HAL_FLASHEx_Erase>
      if( ret != HAL_OK )
 80002ee:	4607      	mov	r7, r0
 80002f0:	b938      	cbnz	r0, 8000302 <write_data_to_flash_app+0x7e>
      application_write_idx = 0;
 80002f2:	4b07      	ldr	r3, [pc, #28]	@ (8000310 <write_data_to_flash_app+0x8c>)
 80002f4:	2200      	movs	r2, #0
 80002f6:	801a      	strh	r2, [r3, #0]
 80002f8:	e7cf      	b.n	800029a <write_data_to_flash_app+0x16>
        printf("Flash Write Error...HALT!!!\r\n");
 80002fa:	4808      	ldr	r0, [pc, #32]	@ (800031c <write_data_to_flash_app+0x98>)
 80002fc:	f001 fbbc 	bl	8001a78 <puts>
    if( ret != HAL_OK )
 8000300:	b117      	cbz	r7, 8000308 <write_data_to_flash_app+0x84>
}
 8000302:	4638      	mov	r0, r7
 8000304:	b007      	add	sp, #28
 8000306:	bdf0      	pop	{r4, r5, r6, r7, pc}
    ret = HAL_FLASH_Lock();
 8000308:	f000 fb8c 	bl	8000a24 <HAL_FLASH_Lock>
 800030c:	4607      	mov	r7, r0
    if( ret != HAL_OK )
 800030e:	e7f8      	b.n	8000302 <write_data_to_flash_app+0x7e>
 8000310:	2000008c 	.word	0x2000008c
 8000314:	0800281c 	.word	0x0800281c
 8000318:	08004400 	.word	0x08004400
 800031c:	0800283c 	.word	0x0800283c

08000320 <Firmware_Update>:
{
 8000320:	b5f0      	push	{r4, r5, r6, r7, lr}
 8000322:	f2ad 4d0c 	subw	sp, sp, #1036	@ 0x40c
  uint8_t x = 'x';
 8000326:	2378      	movs	r3, #120	@ 0x78
 8000328:	f88d 3405 	strb.w	r3, [sp, #1029]	@ 0x405
  uint8_t y = 'y';
 800032c:	2379      	movs	r3, #121	@ 0x79
 800032e:	f88d 3404 	strb.w	r3, [sp, #1028]	@ 0x404
  uint8_t block[MAX_BLOCK_SIZE] = { 0 };
 8000332:	2100      	movs	r1, #0
 8000334:	9101      	str	r1, [sp, #4]
 8000336:	f44f 727f 	mov.w	r2, #1020	@ 0x3fc
 800033a:	a802      	add	r0, sp, #8
 800033c:	f001 fc7c 	bl	8001c38 <memset>
    if( UART_Write_Loop() != 0 )
 8000340:	f7ff ff5c 	bl	80001fc <UART_Write_Loop>
 8000344:	b910      	cbnz	r0, 800034c <Firmware_Update+0x2c>
}
 8000346:	f20d 4d0c 	addw	sp, sp, #1036	@ 0x40c
 800034a:	bdf0      	pop	{r4, r5, r6, r7, pc}
      HAL_UART_Transmit(&huart3, &y, 1, HAL_MAX_DELAY);
 800034c:	4c50      	ldr	r4, [pc, #320]	@ (8000490 <Firmware_Update+0x170>)
 800034e:	f04f 33ff 	mov.w	r3, #4294967295
 8000352:	2201      	movs	r2, #1
 8000354:	f20d 4104 	addw	r1, sp, #1028	@ 0x404
 8000358:	4620      	mov	r0, r4
 800035a:	f001 f994 	bl	8001686 <HAL_UART_Transmit>
      ex = HAL_UART_Receive(&huart3, &yy, 1, 5000);
 800035e:	f241 3388 	movw	r3, #5000	@ 0x1388
 8000362:	2201      	movs	r2, #1
 8000364:	f20d 4106 	addw	r1, sp, #1030	@ 0x406
 8000368:	4620      	mov	r0, r4
 800036a:	f001 f9f2 	bl	8001752 <HAL_UART_Receive>
      if( ex != HAL_OK )
 800036e:	4607      	mov	r7, r0
 8000370:	b128      	cbz	r0, 800037e <Firmware_Update+0x5e>
        printf("Get application Size error (yy)...HALT!!!\r\n");
 8000372:	4848      	ldr	r0, [pc, #288]	@ (8000494 <Firmware_Update+0x174>)
 8000374:	f001 fb80 	bl	8001a78 <puts>
  if( ex != HAL_OK )
 8000378:	2f00      	cmp	r7, #0
 800037a:	d0e4      	beq.n	8000346 <Firmware_Update+0x26>
    while(1);
 800037c:	e7fe      	b.n	800037c <Firmware_Update+0x5c>
      HAL_UART_Transmit(&huart3, &x, 1, HAL_MAX_DELAY);
 800037e:	4c44      	ldr	r4, [pc, #272]	@ (8000490 <Firmware_Update+0x170>)
 8000380:	f04f 33ff 	mov.w	r3, #4294967295
 8000384:	2201      	movs	r2, #1
 8000386:	f20d 4105 	addw	r1, sp, #1029	@ 0x405
 800038a:	4620      	mov	r0, r4
 800038c:	f001 f97b 	bl	8001686 <HAL_UART_Transmit>
      ex = HAL_UART_Receive(&huart3, &xx, 1, 5000);
 8000390:	f241 3388 	movw	r3, #5000	@ 0x1388
 8000394:	2201      	movs	r2, #1
 8000396:	f20d 4107 	addw	r1, sp, #1031	@ 0x407
 800039a:	4620      	mov	r0, r4
 800039c:	f001 f9d9 	bl	8001752 <HAL_UART_Receive>
      if( ex != HAL_OK )
 80003a0:	4607      	mov	r7, r0
 80003a2:	b118      	cbz	r0, 80003ac <Firmware_Update+0x8c>
        printf("Get application Size error(XX)...HALT!!!\r\n");
 80003a4:	483c      	ldr	r0, [pc, #240]	@ (8000498 <Firmware_Update+0x178>)
 80003a6:	f001 fb67 	bl	8001a78 <puts>
        break;
 80003aa:	e7e5      	b.n	8000378 <Firmware_Update+0x58>
      application_size = yy | (xx << 8);
 80003ac:	f89d 3407 	ldrb.w	r3, [sp, #1031]	@ 0x407
 80003b0:	f89d 1406 	ldrb.w	r1, [sp, #1030]	@ 0x406
 80003b4:	ea41 2103 	orr.w	r1, r1, r3, lsl #8
 80003b8:	4b38      	ldr	r3, [pc, #224]	@ (800049c <Firmware_Update+0x17c>)
 80003ba:	8019      	strh	r1, [r3, #0]
      printf("Application Size = %d bytes\r\n", application_size);
 80003bc:	4838      	ldr	r0, [pc, #224]	@ (80004a0 <Firmware_Update+0x180>)
 80003be:	f001 faf3 	bl	80019a8 <iprintf>
  uint16_t i = 0;
 80003c2:	2400      	movs	r4, #0
  uint16_t current_app_size = 0;
 80003c4:	4625      	mov	r5, r4
 80003c6:	e04d      	b.n	8000464 <Firmware_Update+0x144>
          printf("Received Block[%d]\r\n", current_app_size/MAX_BLOCK_SIZE);
 80003c8:	0aa9      	lsrs	r1, r5, #10
 80003ca:	4836      	ldr	r0, [pc, #216]	@ (80004a4 <Firmware_Update+0x184>)
 80003cc:	f001 faec 	bl	80019a8 <iprintf>
          ex = write_data_to_flash_app(block, MAX_BLOCK_SIZE, (current_app_size <= MAX_BLOCK_SIZE) );
 80003d0:	f5b5 6f80 	cmp.w	r5, #1024	@ 0x400
 80003d4:	bf8c      	ite	hi
 80003d6:	2200      	movhi	r2, #0
 80003d8:	2201      	movls	r2, #1
 80003da:	f44f 6180 	mov.w	r1, #1024	@ 0x400
 80003de:	a801      	add	r0, sp, #4
 80003e0:	f7ff ff50 	bl	8000284 <write_data_to_flash_app>
          if( ex != HAL_OK )
 80003e4:	2800      	cmp	r0, #0
 80003e6:	d151      	bne.n	800048c <Firmware_Update+0x16c>
          memset(block, 0,MAX_BLOCK_SIZE);
 80003e8:	f44f 6280 	mov.w	r2, #1024	@ 0x400
 80003ec:	2100      	movs	r1, #0
 80003ee:	a801      	add	r0, sp, #4
 80003f0:	f001 fc22 	bl	8001c38 <memset>
          i = 0;
 80003f4:	2400      	movs	r4, #0
        if( current_app_size >= application_size)
 80003f6:	4b29      	ldr	r3, [pc, #164]	@ (800049c <Firmware_Update+0x17c>)
 80003f8:	881b      	ldrh	r3, [r3, #0]
 80003fa:	42ab      	cmp	r3, r5
 80003fc:	d9bc      	bls.n	8000378 <Firmware_Update+0x58>
        HAL_UART_Transmit(&huart3, &y, 1, HAL_MAX_DELAY);
 80003fe:	4e24      	ldr	r6, [pc, #144]	@ (8000490 <Firmware_Update+0x170>)
 8000400:	f04f 33ff 	mov.w	r3, #4294967295
 8000404:	2201      	movs	r2, #1
 8000406:	f20d 4104 	addw	r1, sp, #1028	@ 0x404
 800040a:	4630      	mov	r0, r6
 800040c:	f001 f93b 	bl	8001686 <HAL_UART_Transmit>
        ex = HAL_UART_Receive(&huart3, &yy, 1, 5000);
 8000410:	f241 3388 	movw	r3, #5000	@ 0x1388
 8000414:	2201      	movs	r2, #1
 8000416:	f20d 4106 	addw	r1, sp, #1030	@ 0x406
 800041a:	4630      	mov	r0, r6
 800041c:	f001 f999 	bl	8001752 <HAL_UART_Receive>
        if( ex != HAL_OK )
 8000420:	4606      	mov	r6, r0
 8000422:	bb38      	cbnz	r0, 8000474 <Firmware_Update+0x154>
        HAL_UART_Transmit(&huart3, &x, 1, HAL_MAX_DELAY);
 8000424:	4e1a      	ldr	r6, [pc, #104]	@ (8000490 <Firmware_Update+0x170>)
 8000426:	f04f 33ff 	mov.w	r3, #4294967295
 800042a:	2201      	movs	r2, #1
 800042c:	f20d 4105 	addw	r1, sp, #1029	@ 0x405
 8000430:	4630      	mov	r0, r6
 8000432:	f001 f928 	bl	8001686 <HAL_UART_Transmit>
        ex = HAL_UART_Receive(&huart3, &xx, 1, 5000);
 8000436:	f241 3388 	movw	r3, #5000	@ 0x1388
 800043a:	2201      	movs	r2, #1
 800043c:	f20d 4107 	addw	r1, sp, #1031	@ 0x407
 8000440:	4630      	mov	r0, r6
 8000442:	f001 f986 	bl	8001752 <HAL_UART_Receive>
        if( ex != HAL_OK )
 8000446:	4606      	mov	r6, r0
 8000448:	b9d0      	cbnz	r0, 8000480 <Firmware_Update+0x160>
        block[i++] = yy;
 800044a:	1c63      	adds	r3, r4, #1
 800044c:	b29b      	uxth	r3, r3
 800044e:	aa01      	add	r2, sp, #4
 8000450:	f89d 1406 	ldrb.w	r1, [sp, #1030]	@ 0x406
 8000454:	5511      	strb	r1, [r2, r4]
        block[i++] = xx;
 8000456:	3402      	adds	r4, #2
 8000458:	b2a4      	uxth	r4, r4
 800045a:	f89d 1407 	ldrb.w	r1, [sp, #1031]	@ 0x407
 800045e:	54d1      	strb	r1, [r2, r3]
        current_app_size += 2;
 8000460:	3502      	adds	r5, #2
 8000462:	b2ad      	uxth	r5, r5
        if( ( i == MAX_BLOCK_SIZE ) || ( current_app_size >= application_size) )
 8000464:	f5b4 6f80 	cmp.w	r4, #1024	@ 0x400
 8000468:	d0ae      	beq.n	80003c8 <Firmware_Update+0xa8>
 800046a:	4b0c      	ldr	r3, [pc, #48]	@ (800049c <Firmware_Update+0x17c>)
 800046c:	881b      	ldrh	r3, [r3, #0]
 800046e:	42ab      	cmp	r3, r5
 8000470:	d8c1      	bhi.n	80003f6 <Firmware_Update+0xd6>
 8000472:	e7a9      	b.n	80003c8 <Firmware_Update+0xa8>
          printf("Get application data[index:%d] error (yy)...HALT!!!\r\n", i);
 8000474:	4621      	mov	r1, r4
 8000476:	480c      	ldr	r0, [pc, #48]	@ (80004a8 <Firmware_Update+0x188>)
 8000478:	f001 fa96 	bl	80019a8 <iprintf>
        ex = HAL_UART_Receive(&huart3, &yy, 1, 5000);
 800047c:	4637      	mov	r7, r6
          break;
 800047e:	e77b      	b.n	8000378 <Firmware_Update+0x58>
          printf("Get application data[index:%d] error(XX)...HALT!!!\r\n", i);
 8000480:	4621      	mov	r1, r4
 8000482:	480a      	ldr	r0, [pc, #40]	@ (80004ac <Firmware_Update+0x18c>)
 8000484:	f001 fa90 	bl	80019a8 <iprintf>
        ex = HAL_UART_Receive(&huart3, &xx, 1, 5000);
 8000488:	4637      	mov	r7, r6
          break;
 800048a:	e775      	b.n	8000378 <Firmware_Update+0x58>
          ex = write_data_to_flash_app(block, MAX_BLOCK_SIZE, (current_app_size <= MAX_BLOCK_SIZE) );
 800048c:	4607      	mov	r7, r0
 800048e:	e773      	b.n	8000378 <Firmware_Update+0x58>
 8000490:	20000090 	.word	0x20000090
 8000494:	0800285c 	.word	0x0800285c
 8000498:	08002888 	.word	0x08002888
 800049c:	2000008e 	.word	0x2000008e
 80004a0:	080028b4 	.word	0x080028b4
 80004a4:	080028d4 	.word	0x080028d4
 80004a8:	080028ec 	.word	0x080028ec
 80004ac:	08002924 	.word	0x08002924

080004b0 <__io_putchar>:
{
 80004b0:	b500      	push	{lr}
 80004b2:	b083      	sub	sp, #12
 80004b4:	9001      	str	r0, [sp, #4]
  HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
 80004b6:	f04f 33ff 	mov.w	r3, #4294967295
 80004ba:	2201      	movs	r2, #1
 80004bc:	a901      	add	r1, sp, #4
 80004be:	4803      	ldr	r0, [pc, #12]	@ (80004cc <__io_putchar+0x1c>)
 80004c0:	f001 f8e1 	bl	8001686 <HAL_UART_Transmit>
}
 80004c4:	9801      	ldr	r0, [sp, #4]
 80004c6:	b003      	add	sp, #12
 80004c8:	f85d fb04 	ldr.w	pc, [sp], #4
 80004cc:	200000d8 	.word	0x200000d8

080004d0 <Error_Handler>:
  __ASM volatile ("cpsid i" : : : "memory");
 80004d0:	b672      	cpsid	i
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
 80004d2:	e7fe      	b.n	80004d2 <Error_Handler+0x2>

080004d4 <MX_USART1_UART_Init>:
{
 80004d4:	b508      	push	{r3, lr}
  huart1.Instance = USART1;
 80004d6:	480a      	ldr	r0, [pc, #40]	@ (8000500 <MX_USART1_UART_Init+0x2c>)
 80004d8:	4b0a      	ldr	r3, [pc, #40]	@ (8000504 <MX_USART1_UART_Init+0x30>)
 80004da:	6003      	str	r3, [r0, #0]
  huart1.Init.BaudRate = 115200;
 80004dc:	f44f 33e1 	mov.w	r3, #115200	@ 0x1c200
 80004e0:	6043      	str	r3, [r0, #4]
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
 80004e2:	2300      	movs	r3, #0
 80004e4:	6083      	str	r3, [r0, #8]
  huart1.Init.StopBits = UART_STOPBITS_1;
 80004e6:	60c3      	str	r3, [r0, #12]
  huart1.Init.Parity = UART_PARITY_NONE;
 80004e8:	6103      	str	r3, [r0, #16]
  huart1.Init.Mode = UART_MODE_TX_RX;
 80004ea:	220c      	movs	r2, #12
 80004ec:	6142      	str	r2, [r0, #20]
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
 80004ee:	6183      	str	r3, [r0, #24]
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;
 80004f0:	61c3      	str	r3, [r0, #28]
  if (HAL_UART_Init(&huart1) != HAL_OK)
 80004f2:	f001 f898 	bl	8001626 <HAL_UART_Init>
 80004f6:	b900      	cbnz	r0, 80004fa <MX_USART1_UART_Init+0x26>
}
 80004f8:	bd08      	pop	{r3, pc}
    Error_Handler();
 80004fa:	f7ff ffe9 	bl	80004d0 <Error_Handler>
 80004fe:	bf00      	nop
 8000500:	200000d8 	.word	0x200000d8
 8000504:	40013800 	.word	0x40013800

08000508 <MX_USART3_UART_Init>:
{
 8000508:	b508      	push	{r3, lr}
  huart3.Instance = USART3;
 800050a:	480a      	ldr	r0, [pc, #40]	@ (8000534 <MX_USART3_UART_Init+0x2c>)
 800050c:	4b0a      	ldr	r3, [pc, #40]	@ (8000538 <MX_USART3_UART_Init+0x30>)
 800050e:	6003      	str	r3, [r0, #0]
  huart3.Init.BaudRate = 115200;
 8000510:	f44f 33e1 	mov.w	r3, #115200	@ 0x1c200
 8000514:	6043      	str	r3, [r0, #4]
  huart3.Init.WordLength = UART_WORDLENGTH_8B;
 8000516:	2300      	movs	r3, #0
 8000518:	6083      	str	r3, [r0, #8]
  huart3.Init.StopBits = UART_STOPBITS_1;
 800051a:	60c3      	str	r3, [r0, #12]
  huart3.Init.Parity = UART_PARITY_NONE;
 800051c:	6103      	str	r3, [r0, #16]
  huart3.Init.Mode = UART_MODE_TX_RX;
 800051e:	220c      	movs	r2, #12
 8000520:	6142      	str	r2, [r0, #20]
  huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
 8000522:	6183      	str	r3, [r0, #24]
  huart3.Init.OverSampling = UART_OVERSAMPLING_16;
 8000524:	61c3      	str	r3, [r0, #28]
  if (HAL_UART_Init(&huart3) != HAL_OK)
 8000526:	f001 f87e 	bl	8001626 <HAL_UART_Init>
 800052a:	b900      	cbnz	r0, 800052e <MX_USART3_UART_Init+0x26>
}
 800052c:	bd08      	pop	{r3, pc}
    Error_Handler();
 800052e:	f7ff ffcf 	bl	80004d0 <Error_Handler>
 8000532:	bf00      	nop
 8000534:	20000090 	.word	0x20000090
 8000538:	40004800 	.word	0x40004800

0800053c <SystemClock_Config>:
{
 800053c:	b500      	push	{lr}
 800053e:	b091      	sub	sp, #68	@ 0x44
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 8000540:	2228      	movs	r2, #40	@ 0x28
 8000542:	2100      	movs	r1, #0
 8000544:	a806      	add	r0, sp, #24
 8000546:	f001 fb77 	bl	8001c38 <memset>
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 800054a:	2300      	movs	r3, #0
 800054c:	9301      	str	r3, [sp, #4]
 800054e:	9302      	str	r3, [sp, #8]
 8000550:	9303      	str	r3, [sp, #12]
 8000552:	9304      	str	r3, [sp, #16]
 8000554:	9305      	str	r3, [sp, #20]
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
 8000556:	2302      	movs	r3, #2
 8000558:	9306      	str	r3, [sp, #24]
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
 800055a:	2301      	movs	r3, #1
 800055c:	930a      	str	r3, [sp, #40]	@ 0x28
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
 800055e:	2310      	movs	r3, #16
 8000560:	930b      	str	r3, [sp, #44]	@ 0x2c
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 8000562:	a806      	add	r0, sp, #24
 8000564:	f000 fc96 	bl	8000e94 <HAL_RCC_OscConfig>
 8000568:	b968      	cbnz	r0, 8000586 <SystemClock_Config+0x4a>
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
 800056a:	230f      	movs	r3, #15
 800056c:	9301      	str	r3, [sp, #4]
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
 800056e:	2100      	movs	r1, #0
 8000570:	9102      	str	r1, [sp, #8]
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
 8000572:	9103      	str	r1, [sp, #12]
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
 8000574:	9104      	str	r1, [sp, #16]
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
 8000576:	9105      	str	r1, [sp, #20]
  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
 8000578:	a801      	add	r0, sp, #4
 800057a:	f000 febd 	bl	80012f8 <HAL_RCC_ClockConfig>
 800057e:	b920      	cbnz	r0, 800058a <SystemClock_Config+0x4e>
}
 8000580:	b011      	add	sp, #68	@ 0x44
 8000582:	f85d fb04 	ldr.w	pc, [sp], #4
    Error_Handler();
 8000586:	f7ff ffa3 	bl	80004d0 <Error_Handler>
    Error_Handler();
 800058a:	f7ff ffa1 	bl	80004d0 <Error_Handler>
	...

08000590 <main>:
{
 8000590:	b508      	push	{r3, lr}
  HAL_Init();
 8000592:	f000 f945 	bl	8000820 <HAL_Init>
  SystemClock_Config();
 8000596:	f7ff ffd1 	bl	800053c <SystemClock_Config>
  MX_GPIO_Init();
 800059a:	f7ff fdd7 	bl	800014c <MX_GPIO_Init>
  MX_USART1_UART_Init();
 800059e:	f7ff ff99 	bl	80004d4 <MX_USART1_UART_Init>
  MX_USART3_UART_Init();
 80005a2:	f7ff ffb1 	bl	8000508 <MX_USART3_UART_Init>
  printf("Bootloader v%d:%d Started!!!\n", BL_Version[0], BL_Version[1]);
 80005a6:	4b05      	ldr	r3, [pc, #20]	@ (80005bc <main+0x2c>)
 80005a8:	785a      	ldrb	r2, [r3, #1]
 80005aa:	7819      	ldrb	r1, [r3, #0]
 80005ac:	4804      	ldr	r0, [pc, #16]	@ (80005c0 <main+0x30>)
 80005ae:	f001 f9fb 	bl	80019a8 <iprintf>
  Firmware_Update();
 80005b2:	f7ff feb5 	bl	8000320 <Firmware_Update>
  goto_application();
 80005b6:	f7ff fdfd 	bl	80001b4 <goto_application>
  while (1)
 80005ba:	e7fe      	b.n	80005ba <main+0x2a>
 80005bc:	20000000 	.word	0x20000000
 80005c0:	0800295c 	.word	0x0800295c

080005c4 <HAL_MspInit>:
/* USER CODE END 0 */
/**
  * Initializes the Global MSP.
  */
void HAL_MspInit(void)
{
 80005c4:	b082      	sub	sp, #8

  /* USER CODE BEGIN MspInit 0 */

  /* USER CODE END MspInit 0 */

  __HAL_RCC_AFIO_CLK_ENABLE();
 80005c6:	4b0a      	ldr	r3, [pc, #40]	@ (80005f0 <HAL_MspInit+0x2c>)
 80005c8:	699a      	ldr	r2, [r3, #24]
 80005ca:	f042 0201 	orr.w	r2, r2, #1
 80005ce:	619a      	str	r2, [r3, #24]
 80005d0:	699a      	ldr	r2, [r3, #24]
 80005d2:	f002 0201 	and.w	r2, r2, #1
 80005d6:	9200      	str	r2, [sp, #0]
 80005d8:	9a00      	ldr	r2, [sp, #0]
  __HAL_RCC_PWR_CLK_ENABLE();
 80005da:	69da      	ldr	r2, [r3, #28]
 80005dc:	f042 5280 	orr.w	r2, r2, #268435456	@ 0x10000000
 80005e0:	61da      	str	r2, [r3, #28]
 80005e2:	69db      	ldr	r3, [r3, #28]
 80005e4:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 80005e8:	9301      	str	r3, [sp, #4]
 80005ea:	9b01      	ldr	r3, [sp, #4]
  /* System interrupt init*/

  /* USER CODE BEGIN MspInit 1 */

  /* USER CODE END MspInit 1 */
}
 80005ec:	b002      	add	sp, #8
 80005ee:	4770      	bx	lr
 80005f0:	40021000 	.word	0x40021000

080005f4 <HAL_UART_MspInit>:
* This function configures the hardware resources used in this example
* @param huart: UART handle pointer
* @retval None
*/
void HAL_UART_MspInit(UART_HandleTypeDef* huart)
{
 80005f4:	b510      	push	{r4, lr}
 80005f6:	b088      	sub	sp, #32
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 80005f8:	2300      	movs	r3, #0
 80005fa:	9304      	str	r3, [sp, #16]
 80005fc:	9305      	str	r3, [sp, #20]
 80005fe:	9306      	str	r3, [sp, #24]
 8000600:	9307      	str	r3, [sp, #28]
  if(huart->Instance==USART1)
 8000602:	6803      	ldr	r3, [r0, #0]
 8000604:	4a2d      	ldr	r2, [pc, #180]	@ (80006bc <HAL_UART_MspInit+0xc8>)
 8000606:	4293      	cmp	r3, r2
 8000608:	d004      	beq.n	8000614 <HAL_UART_MspInit+0x20>

  /* USER CODE BEGIN USART1_MspInit 1 */

  /* USER CODE END USART1_MspInit 1 */
  }
  else if(huart->Instance==USART3)
 800060a:	4a2d      	ldr	r2, [pc, #180]	@ (80006c0 <HAL_UART_MspInit+0xcc>)
 800060c:	4293      	cmp	r3, r2
 800060e:	d02b      	beq.n	8000668 <HAL_UART_MspInit+0x74>
  /* USER CODE BEGIN USART3_MspInit 1 */

  /* USER CODE END USART3_MspInit 1 */
  }

}
 8000610:	b008      	add	sp, #32
 8000612:	bd10      	pop	{r4, pc}
    __HAL_RCC_USART1_CLK_ENABLE();
 8000614:	4b2b      	ldr	r3, [pc, #172]	@ (80006c4 <HAL_UART_MspInit+0xd0>)
 8000616:	699a      	ldr	r2, [r3, #24]
 8000618:	f442 4280 	orr.w	r2, r2, #16384	@ 0x4000
 800061c:	619a      	str	r2, [r3, #24]
 800061e:	699a      	ldr	r2, [r3, #24]
 8000620:	f402 4280 	and.w	r2, r2, #16384	@ 0x4000
 8000624:	9200      	str	r2, [sp, #0]
 8000626:	9a00      	ldr	r2, [sp, #0]
    __HAL_RCC_GPIOA_CLK_ENABLE();
 8000628:	699a      	ldr	r2, [r3, #24]
 800062a:	f042 0204 	orr.w	r2, r2, #4
 800062e:	619a      	str	r2, [r3, #24]
 8000630:	699b      	ldr	r3, [r3, #24]
 8000632:	f003 0304 	and.w	r3, r3, #4
 8000636:	9301      	str	r3, [sp, #4]
 8000638:	9b01      	ldr	r3, [sp, #4]
    GPIO_InitStruct.Pin = GPIO_PIN_9;
 800063a:	f44f 7300 	mov.w	r3, #512	@ 0x200
 800063e:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 8000640:	2302      	movs	r3, #2
 8000642:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
 8000644:	2303      	movs	r3, #3
 8000646:	9307      	str	r3, [sp, #28]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 8000648:	4c1f      	ldr	r4, [pc, #124]	@ (80006c8 <HAL_UART_MspInit+0xd4>)
 800064a:	a904      	add	r1, sp, #16
 800064c:	4620      	mov	r0, r4
 800064e:	f000 faed 	bl	8000c2c <HAL_GPIO_Init>
    GPIO_InitStruct.Pin = GPIO_PIN_10;
 8000652:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 8000656:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 8000658:	2300      	movs	r3, #0
 800065a:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 800065c:	9306      	str	r3, [sp, #24]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 800065e:	a904      	add	r1, sp, #16
 8000660:	4620      	mov	r0, r4
 8000662:	f000 fae3 	bl	8000c2c <HAL_GPIO_Init>
 8000666:	e7d3      	b.n	8000610 <HAL_UART_MspInit+0x1c>
    __HAL_RCC_USART3_CLK_ENABLE();
 8000668:	4b16      	ldr	r3, [pc, #88]	@ (80006c4 <HAL_UART_MspInit+0xd0>)
 800066a:	69da      	ldr	r2, [r3, #28]
 800066c:	f442 2280 	orr.w	r2, r2, #262144	@ 0x40000
 8000670:	61da      	str	r2, [r3, #28]
 8000672:	69da      	ldr	r2, [r3, #28]
 8000674:	f402 2280 	and.w	r2, r2, #262144	@ 0x40000
 8000678:	9202      	str	r2, [sp, #8]
 800067a:	9a02      	ldr	r2, [sp, #8]
    __HAL_RCC_GPIOB_CLK_ENABLE();
 800067c:	699a      	ldr	r2, [r3, #24]
 800067e:	f042 0208 	orr.w	r2, r2, #8
 8000682:	619a      	str	r2, [r3, #24]
 8000684:	699b      	ldr	r3, [r3, #24]
 8000686:	f003 0308 	and.w	r3, r3, #8
 800068a:	9303      	str	r3, [sp, #12]
 800068c:	9b03      	ldr	r3, [sp, #12]
    GPIO_InitStruct.Pin = GPIO_PIN_10;
 800068e:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 8000692:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 8000694:	2302      	movs	r3, #2
 8000696:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
 8000698:	2303      	movs	r3, #3
 800069a:	9307      	str	r3, [sp, #28]
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 800069c:	4c0b      	ldr	r4, [pc, #44]	@ (80006cc <HAL_UART_MspInit+0xd8>)
 800069e:	a904      	add	r1, sp, #16
 80006a0:	4620      	mov	r0, r4
 80006a2:	f000 fac3 	bl	8000c2c <HAL_GPIO_Init>
    GPIO_InitStruct.Pin = GPIO_PIN_11;
 80006a6:	f44f 6300 	mov.w	r3, #2048	@ 0x800
 80006aa:	9304      	str	r3, [sp, #16]
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 80006ac:	2300      	movs	r3, #0
 80006ae:	9305      	str	r3, [sp, #20]
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 80006b0:	9306      	str	r3, [sp, #24]
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 80006b2:	a904      	add	r1, sp, #16
 80006b4:	4620      	mov	r0, r4
 80006b6:	f000 fab9 	bl	8000c2c <HAL_GPIO_Init>
}
 80006ba:	e7a9      	b.n	8000610 <HAL_UART_MspInit+0x1c>
 80006bc:	40013800 	.word	0x40013800
 80006c0:	40004800 	.word	0x40004800
 80006c4:	40021000 	.word	0x40021000
 80006c8:	40010800 	.word	0x40010800
 80006cc:	40010c00 	.word	0x40010c00

080006d0 <NMI_Handler>:
{
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */

  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
  while (1)
 80006d0:	e7fe      	b.n	80006d0 <NMI_Handler>

080006d2 <HardFault_Handler>:
void HardFault_Handler(void)
{
  /* USER CODE BEGIN HardFault_IRQn 0 */

  /* USER CODE END HardFault_IRQn 0 */
  while (1)
 80006d2:	e7fe      	b.n	80006d2 <HardFault_Handler>

080006d4 <MemManage_Handler>:
void MemManage_Handler(void)
{
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */

  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
 80006d4:	e7fe      	b.n	80006d4 <MemManage_Handler>

080006d6 <BusFault_Handler>:
void BusFault_Handler(void)
{
  /* USER CODE BEGIN BusFault_IRQn 0 */

  /* USER CODE END BusFault_IRQn 0 */
  while (1)
 80006d6:	e7fe      	b.n	80006d6 <BusFault_Handler>

080006d8 <UsageFault_Handler>:
void UsageFault_Handler(void)
{
  /* USER CODE BEGIN UsageFault_IRQn 0 */

  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
 80006d8:	e7fe      	b.n	80006d8 <UsageFault_Handler>

080006da <SVC_Handler>:

  /* USER CODE END SVCall_IRQn 0 */
  /* USER CODE BEGIN SVCall_IRQn 1 */

  /* USER CODE END SVCall_IRQn 1 */
}
 80006da:	4770      	bx	lr

080006dc <DebugMon_Handler>:

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}
 80006dc:	4770      	bx	lr

080006de <PendSV_Handler>:

  /* USER CODE END PendSV_IRQn 0 */
  /* USER CODE BEGIN PendSV_IRQn 1 */

  /* USER CODE END PendSV_IRQn 1 */
}
 80006de:	4770      	bx	lr

080006e0 <SysTick_Handler>:

/**
  * @brief This function handles System tick timer.
  */
void SysTick_Handler(void)
{
 80006e0:	b508      	push	{r3, lr}
  /* USER CODE BEGIN SysTick_IRQn 0 */

  /* USER CODE END SysTick_IRQn 0 */
  HAL_IncTick();
 80006e2:	f000 f8af 	bl	8000844 <HAL_IncTick>
  /* USER CODE BEGIN SysTick_IRQn 1 */

  /* USER CODE END SysTick_IRQn 1 */
}
 80006e6:	bd08      	pop	{r3, pc}

080006e8 <_read>:
	_kill(status, -1);
	while (1) {}		/* Make sure we hang here */
}

__attribute__((weak)) int _read(int file, char *ptr, int len)
{
 80006e8:	b570      	push	{r4, r5, r6, lr}
 80006ea:	460c      	mov	r4, r1
 80006ec:	4616      	mov	r6, r2
	int DataIdx;

	for (DataIdx = 0; DataIdx < len; DataIdx++)
 80006ee:	2500      	movs	r5, #0
 80006f0:	e006      	b.n	8000700 <_read+0x18>
	{
		*ptr++ = __io_getchar();
 80006f2:	f3af 8000 	nop.w
 80006f6:	4621      	mov	r1, r4
 80006f8:	f801 0b01 	strb.w	r0, [r1], #1
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 80006fc:	3501      	adds	r5, #1
		*ptr++ = __io_getchar();
 80006fe:	460c      	mov	r4, r1
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 8000700:	42b5      	cmp	r5, r6
 8000702:	dbf6      	blt.n	80006f2 <_read+0xa>
	}

return len;
}
 8000704:	4630      	mov	r0, r6
 8000706:	bd70      	pop	{r4, r5, r6, pc}

08000708 <_write>:

__attribute__((weak)) int _write(int file, char *ptr, int len)
{
 8000708:	b570      	push	{r4, r5, r6, lr}
 800070a:	460c      	mov	r4, r1
 800070c:	4616      	mov	r6, r2
	int DataIdx;

	for (DataIdx = 0; DataIdx < len; DataIdx++)
 800070e:	2500      	movs	r5, #0
 8000710:	e004      	b.n	800071c <_write+0x14>
	{
		__io_putchar(*ptr++);
 8000712:	f814 0b01 	ldrb.w	r0, [r4], #1
 8000716:	f7ff fecb 	bl	80004b0 <__io_putchar>
	for (DataIdx = 0; DataIdx < len; DataIdx++)
 800071a:	3501      	adds	r5, #1
 800071c:	42b5      	cmp	r5, r6
 800071e:	dbf8      	blt.n	8000712 <_write+0xa>
	}
	return len;
}
 8000720:	4630      	mov	r0, r6
 8000722:	bd70      	pop	{r4, r5, r6, pc}

08000724 <_close>:

int _close(int file)
{
	return -1;
}
 8000724:	f04f 30ff 	mov.w	r0, #4294967295
 8000728:	4770      	bx	lr

0800072a <_fstat>:


int _fstat(int file, struct stat *st)
{
	st->st_mode = S_IFCHR;
 800072a:	f44f 5300 	mov.w	r3, #8192	@ 0x2000
 800072e:	604b      	str	r3, [r1, #4]
	return 0;
}
 8000730:	2000      	movs	r0, #0
 8000732:	4770      	bx	lr

08000734 <_isatty>:

int _isatty(int file)
{
	return 1;
}
 8000734:	2001      	movs	r0, #1
 8000736:	4770      	bx	lr

08000738 <_lseek>:

int _lseek(int file, int ptr, int dir)
{
	return 0;
}
 8000738:	2000      	movs	r0, #0
 800073a:	4770      	bx	lr

0800073c <_sbrk>:
 *
 * @param incr Memory size
 * @return Pointer to allocated memory
 */
void *_sbrk(ptrdiff_t incr)
{
 800073c:	b510      	push	{r4, lr}
 800073e:	4603      	mov	r3, r0
  extern uint8_t _end; /* Symbol defined in the linker script */
  extern uint8_t _estack; /* Symbol defined in the linker script */
  extern uint32_t _Min_Stack_Size; /* Symbol defined in the linker script */
  const uint32_t stack_limit = (uint32_t)&_estack - (uint32_t)&_Min_Stack_Size;
 8000740:	4a0c      	ldr	r2, [pc, #48]	@ (8000774 <_sbrk+0x38>)
 8000742:	490d      	ldr	r1, [pc, #52]	@ (8000778 <_sbrk+0x3c>)
  const uint8_t *max_heap = (uint8_t *)stack_limit;
  uint8_t *prev_heap_end;

  /* Initialize heap end at first call */
  if (NULL == __sbrk_heap_end)
 8000744:	480d      	ldr	r0, [pc, #52]	@ (800077c <_sbrk+0x40>)
 8000746:	6800      	ldr	r0, [r0, #0]
 8000748:	b140      	cbz	r0, 800075c <_sbrk+0x20>
  {
    __sbrk_heap_end = &_end;
  }

  /* Protect heap from growing into the reserved MSP stack */
  if (__sbrk_heap_end + incr > max_heap)
 800074a:	480c      	ldr	r0, [pc, #48]	@ (800077c <_sbrk+0x40>)
 800074c:	6800      	ldr	r0, [r0, #0]
 800074e:	4403      	add	r3, r0
 8000750:	1a52      	subs	r2, r2, r1
 8000752:	4293      	cmp	r3, r2
 8000754:	d806      	bhi.n	8000764 <_sbrk+0x28>
    errno = ENOMEM;
    return (void *)-1;
  }

  prev_heap_end = __sbrk_heap_end;
  __sbrk_heap_end += incr;
 8000756:	4a09      	ldr	r2, [pc, #36]	@ (800077c <_sbrk+0x40>)
 8000758:	6013      	str	r3, [r2, #0]

  return (void *)prev_heap_end;
}
 800075a:	bd10      	pop	{r4, pc}
    __sbrk_heap_end = &_end;
 800075c:	4807      	ldr	r0, [pc, #28]	@ (800077c <_sbrk+0x40>)
 800075e:	4c08      	ldr	r4, [pc, #32]	@ (8000780 <_sbrk+0x44>)
 8000760:	6004      	str	r4, [r0, #0]
 8000762:	e7f2      	b.n	800074a <_sbrk+0xe>
    errno = ENOMEM;
 8000764:	f001 fab6 	bl	8001cd4 <__errno>
 8000768:	230c      	movs	r3, #12
 800076a:	6003      	str	r3, [r0, #0]
    return (void *)-1;
 800076c:	f04f 30ff 	mov.w	r0, #4294967295
 8000770:	e7f3      	b.n	800075a <_sbrk+0x1e>
 8000772:	bf00      	nop
 8000774:	20005000 	.word	0x20005000
 8000778:	00000400 	.word	0x00000400
 800077c:	20000120 	.word	0x20000120
 8000780:	20000298 	.word	0x20000298

08000784 <SystemInit>:

  /* Configure the Vector Table location -------------------------------------*/
#if defined(USER_VECT_TAB_ADDRESS)
  SCB->VTOR = VECT_TAB_BASE_ADDRESS | VECT_TAB_OFFSET; /* Vector Table Relocation in Internal SRAM. */
#endif /* USER_VECT_TAB_ADDRESS */
}
 8000784:	4770      	bx	lr
	...

08000788 <Reset_Handler>:
  .weak Reset_Handler
  .type Reset_Handler, %function
Reset_Handler:

/* Copy the data segment initializers from flash to SRAM */
  ldr r0, =_sdata
 8000788:	480c      	ldr	r0, [pc, #48]	@ (80007bc <LoopFillZerobss+0x12>)
  ldr r1, =_edata
 800078a:	490d      	ldr	r1, [pc, #52]	@ (80007c0 <LoopFillZerobss+0x16>)
  ldr r2, =_sidata
 800078c:	4a0d      	ldr	r2, [pc, #52]	@ (80007c4 <LoopFillZerobss+0x1a>)
  movs r3, #0
 800078e:	2300      	movs	r3, #0
  b LoopCopyDataInit
 8000790:	e002      	b.n	8000798 <LoopCopyDataInit>

08000792 <CopyDataInit>:

CopyDataInit:
  ldr r4, [r2, r3]
 8000792:	58d4      	ldr	r4, [r2, r3]
  str r4, [r0, r3]
 8000794:	50c4      	str	r4, [r0, r3]
  adds r3, r3, #4
 8000796:	3304      	adds	r3, #4

08000798 <LoopCopyDataInit>:

LoopCopyDataInit:
  adds r4, r0, r3
 8000798:	18c4      	adds	r4, r0, r3
  cmp r4, r1
 800079a:	428c      	cmp	r4, r1
  bcc CopyDataInit
 800079c:	d3f9      	bcc.n	8000792 <CopyDataInit>
  
/* Zero fill the bss segment. */
  ldr r2, =_sbss
 800079e:	4a0a      	ldr	r2, [pc, #40]	@ (80007c8 <LoopFillZerobss+0x1e>)
  ldr r4, =_ebss
 80007a0:	4c0a      	ldr	r4, [pc, #40]	@ (80007cc <LoopFillZerobss+0x22>)
  movs r3, #0
 80007a2:	2300      	movs	r3, #0
  b LoopFillZerobss
 80007a4:	e001      	b.n	80007aa <LoopFillZerobss>

080007a6 <FillZerobss>:

FillZerobss:
  str  r3, [r2]
 80007a6:	6013      	str	r3, [r2, #0]
  adds r2, r2, #4
 80007a8:	3204      	adds	r2, #4

080007aa <LoopFillZerobss>:

LoopFillZerobss:
  cmp r2, r4
 80007aa:	42a2      	cmp	r2, r4
  bcc FillZerobss
 80007ac:	d3fb      	bcc.n	80007a6 <FillZerobss>

/* Call the clock system intitialization function.*/
    bl  SystemInit
 80007ae:	f7ff ffe9 	bl	8000784 <SystemInit>
/* Call static constructors */
    bl __libc_init_array
 80007b2:	f001 fa95 	bl	8001ce0 <__libc_init_array>
/* Call the application's entry point.*/
  bl main
 80007b6:	f7ff feeb 	bl	8000590 <main>
  bx lr
 80007ba:	4770      	bx	lr
  ldr r0, =_sdata
 80007bc:	20000000 	.word	0x20000000
  ldr r1, =_edata
 80007c0:	2000006c 	.word	0x2000006c
  ldr r2, =_sidata
 80007c4:	080029e4 	.word	0x080029e4
  ldr r2, =_sbss
 80007c8:	20000070 	.word	0x20000070
  ldr r4, =_ebss
 80007cc:	20000294 	.word	0x20000294

080007d0 <ADC1_2_IRQHandler>:
 * @retval : None
*/
    .section .text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
  b Infinite_Loop
 80007d0:	e7fe      	b.n	80007d0 <ADC1_2_IRQHandler>
	...

080007d4 <HAL_InitTick>:
  *       implementation  in user file.
  * @param TickPriority Tick interrupt priority.
  * @retval HAL status
  */
__weak HAL_StatusTypeDef HAL_InitTick(uint32_t TickPriority)
{
 80007d4:	b510      	push	{r4, lr}
 80007d6:	4604      	mov	r4, r0
  /* Configure the SysTick to have interrupt in 1ms time basis*/
  if (HAL_SYSTICK_Config(SystemCoreClock / (1000U / uwTickFreq)) > 0U)
 80007d8:	4b0e      	ldr	r3, [pc, #56]	@ (8000814 <HAL_InitTick+0x40>)
 80007da:	781a      	ldrb	r2, [r3, #0]
 80007dc:	f44f 737a 	mov.w	r3, #1000	@ 0x3e8
 80007e0:	fbb3 f3f2 	udiv	r3, r3, r2
 80007e4:	4a0c      	ldr	r2, [pc, #48]	@ (8000818 <HAL_InitTick+0x44>)
 80007e6:	6810      	ldr	r0, [r2, #0]
 80007e8:	fbb0 f0f3 	udiv	r0, r0, r3
 80007ec:	f000 f8a6 	bl	800093c <HAL_SYSTICK_Config>
 80007f0:	b968      	cbnz	r0, 800080e <HAL_InitTick+0x3a>
  {
    return HAL_ERROR;
  }

  /* Configure the SysTick IRQ priority */
  if (TickPriority < (1UL << __NVIC_PRIO_BITS))
 80007f2:	2c0f      	cmp	r4, #15
 80007f4:	d901      	bls.n	80007fa <HAL_InitTick+0x26>
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
    uwTickPrio = TickPriority;
  }
  else
  {
    return HAL_ERROR;
 80007f6:	2001      	movs	r0, #1
 80007f8:	e00a      	b.n	8000810 <HAL_InitTick+0x3c>
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
 80007fa:	2200      	movs	r2, #0
 80007fc:	4621      	mov	r1, r4
 80007fe:	f04f 30ff 	mov.w	r0, #4294967295
 8000802:	f000 f88b 	bl	800091c <HAL_NVIC_SetPriority>
    uwTickPrio = TickPriority;
 8000806:	4b05      	ldr	r3, [pc, #20]	@ (800081c <HAL_InitTick+0x48>)
 8000808:	601c      	str	r4, [r3, #0]
  }

  /* Return function status */
  return HAL_OK;
 800080a:	2000      	movs	r0, #0
 800080c:	e000      	b.n	8000810 <HAL_InitTick+0x3c>
    return HAL_ERROR;
 800080e:	2001      	movs	r0, #1
}
 8000810:	bd10      	pop	{r4, pc}
 8000812:	bf00      	nop
 8000814:	20000008 	.word	0x20000008
 8000818:	20000004 	.word	0x20000004
 800081c:	2000000c 	.word	0x2000000c

08000820 <HAL_Init>:
{
 8000820:	b508      	push	{r3, lr}
  __HAL_FLASH_PREFETCH_BUFFER_ENABLE();
 8000822:	4a07      	ldr	r2, [pc, #28]	@ (8000840 <HAL_Init+0x20>)
 8000824:	6813      	ldr	r3, [r2, #0]
 8000826:	f043 0310 	orr.w	r3, r3, #16
 800082a:	6013      	str	r3, [r2, #0]
  HAL_NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);
 800082c:	2003      	movs	r0, #3
 800082e:	f000 f863 	bl	80008f8 <HAL_NVIC_SetPriorityGrouping>
  HAL_InitTick(TICK_INT_PRIORITY);
 8000832:	200f      	movs	r0, #15
 8000834:	f7ff ffce 	bl	80007d4 <HAL_InitTick>
  HAL_MspInit();
 8000838:	f7ff fec4 	bl	80005c4 <HAL_MspInit>
}
 800083c:	2000      	movs	r0, #0
 800083e:	bd08      	pop	{r3, pc}
 8000840:	******** 	.word	0x********

08000844 <HAL_IncTick>:
  *      implementations in user file.
  * @retval None
  */
__weak void HAL_IncTick(void)
{
  uwTick += uwTickFreq;
 8000844:	4a03      	ldr	r2, [pc, #12]	@ (8000854 <HAL_IncTick+0x10>)
 8000846:	6811      	ldr	r1, [r2, #0]
 8000848:	4b03      	ldr	r3, [pc, #12]	@ (8000858 <HAL_IncTick+0x14>)
 800084a:	781b      	ldrb	r3, [r3, #0]
 800084c:	440b      	add	r3, r1
 800084e:	6013      	str	r3, [r2, #0]
}
 8000850:	4770      	bx	lr
 8000852:	bf00      	nop
 8000854:	20000124 	.word	0x20000124
 8000858:	20000008 	.word	0x20000008

0800085c <HAL_GetTick>:
  *       implementations in user file.
  * @retval tick value
  */
__weak uint32_t HAL_GetTick(void)
{
  return uwTick;
 800085c:	4b01      	ldr	r3, [pc, #4]	@ (8000864 <HAL_GetTick+0x8>)
 800085e:	6818      	ldr	r0, [r3, #0]
}
 8000860:	4770      	bx	lr
 8000862:	bf00      	nop
 8000864:	20000124 	.word	0x20000124

08000868 <HAL_Delay>:
  *       implementations in user file.
  * @param Delay specifies the delay time length, in milliseconds.
  * @retval None
  */
__weak void HAL_Delay(uint32_t Delay)
{
 8000868:	b538      	push	{r3, r4, r5, lr}
 800086a:	4604      	mov	r4, r0
  uint32_t tickstart = HAL_GetTick();
 800086c:	f7ff fff6 	bl	800085c <HAL_GetTick>
 8000870:	4605      	mov	r5, r0
  uint32_t wait = Delay;

  /* Add a freq to guarantee minimum wait */
  if (wait < HAL_MAX_DELAY)
 8000872:	f1b4 3fff 	cmp.w	r4, #4294967295
 8000876:	d002      	beq.n	800087e <HAL_Delay+0x16>
  {
    wait += (uint32_t)(uwTickFreq);
 8000878:	4b04      	ldr	r3, [pc, #16]	@ (800088c <HAL_Delay+0x24>)
 800087a:	781b      	ldrb	r3, [r3, #0]
 800087c:	441c      	add	r4, r3
  }

  while ((HAL_GetTick() - tickstart) < wait)
 800087e:	f7ff ffed 	bl	800085c <HAL_GetTick>
 8000882:	1b40      	subs	r0, r0, r5
 8000884:	42a0      	cmp	r0, r4
 8000886:	d3fa      	bcc.n	800087e <HAL_Delay+0x16>
  {
  }
}
 8000888:	bd38      	pop	{r3, r4, r5, pc}
 800088a:	bf00      	nop
 800088c:	20000008 	.word	0x20000008

08000890 <__NVIC_SetPriority>:
  \param [in]  priority  Priority to set.
  \note    The priority cannot be set for every processor exception.
 */
__STATIC_INLINE void __NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority)
{
  if ((int32_t)(IRQn) >= 0)
 8000890:	2800      	cmp	r0, #0
 8000892:	db08      	blt.n	80008a6 <__NVIC_SetPriority+0x16>
  {
    NVIC->IP[((uint32_t)IRQn)]               = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000894:	0109      	lsls	r1, r1, #4
 8000896:	b2c9      	uxtb	r1, r1
 8000898:	f100 4060 	add.w	r0, r0, #3758096384	@ 0xe0000000
 800089c:	f500 4061 	add.w	r0, r0, #57600	@ 0xe100
 80008a0:	f880 1300 	strb.w	r1, [r0, #768]	@ 0x300
 80008a4:	4770      	bx	lr
  }
  else
  {
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 80008a6:	f000 000f 	and.w	r0, r0, #15
 80008aa:	0109      	lsls	r1, r1, #4
 80008ac:	b2c9      	uxtb	r1, r1
 80008ae:	4b01      	ldr	r3, [pc, #4]	@ (80008b4 <__NVIC_SetPriority+0x24>)
 80008b0:	5419      	strb	r1, [r3, r0]
  }
}
 80008b2:	4770      	bx	lr
 80008b4:	e000ed14 	.word	0xe000ed14

080008b8 <NVIC_EncodePriority>:
  \param [in]   PreemptPriority  Preemptive priority value (starting from 0).
  \param [in]       SubPriority  Subpriority value (starting from 0).
  \return                        Encoded priority. Value can be used in the function \ref NVIC_SetPriority().
 */
__STATIC_INLINE uint32_t NVIC_EncodePriority (uint32_t PriorityGroup, uint32_t PreemptPriority, uint32_t SubPriority)
{
 80008b8:	b500      	push	{lr}
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used          */
 80008ba:	f000 0007 	and.w	r0, r0, #7
  uint32_t PreemptPriorityBits;
  uint32_t SubPriorityBits;

  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NVIC_PRIO_BITS) : (uint32_t)(7UL - PriorityGroupTmp);
 80008be:	f1c0 0c07 	rsb	ip, r0, #7
 80008c2:	f1bc 0f04 	cmp.w	ip, #4
 80008c6:	bf28      	it	cs
 80008c8:	f04f 0c04 	movcs.w	ip, #4
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 80008cc:	1d03      	adds	r3, r0, #4
 80008ce:	2b06      	cmp	r3, #6
 80008d0:	d90f      	bls.n	80008f2 <NVIC_EncodePriority+0x3a>
 80008d2:	1ec3      	subs	r3, r0, #3

  return (
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
 80008d4:	f04f 3eff 	mov.w	lr, #4294967295
 80008d8:	fa0e f00c 	lsl.w	r0, lr, ip
 80008dc:	ea21 0100 	bic.w	r1, r1, r0
 80008e0:	4099      	lsls	r1, r3
           ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
 80008e2:	fa0e fe03 	lsl.w	lr, lr, r3
 80008e6:	ea22 020e 	bic.w	r2, r2, lr
         );
}
 80008ea:	ea41 0002 	orr.w	r0, r1, r2
 80008ee:	f85d fb04 	ldr.w	pc, [sp], #4
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 80008f2:	2300      	movs	r3, #0
 80008f4:	e7ee      	b.n	80008d4 <NVIC_EncodePriority+0x1c>
	...

080008f8 <HAL_NVIC_SetPriorityGrouping>:
  reg_value  =  SCB->AIRCR;                                                   /* read old register configuration    */
 80008f8:	4a07      	ldr	r2, [pc, #28]	@ (8000918 <HAL_NVIC_SetPriorityGrouping+0x20>)
 80008fa:	68d3      	ldr	r3, [r2, #12]
  reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to change               */
 80008fc:	f423 63e0 	bic.w	r3, r3, #1792	@ 0x700
 8000900:	041b      	lsls	r3, r3, #16
 8000902:	0c1b      	lsrs	r3, r3, #16
                (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos) );               /* Insert write key and priority group */
 8000904:	0200      	lsls	r0, r0, #8
 8000906:	f400 60e0 	and.w	r0, r0, #1792	@ 0x700
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
 800090a:	4303      	orrs	r3, r0
  reg_value  =  (reg_value                                   |
 800090c:	f043 63bf 	orr.w	r3, r3, #100139008	@ 0x5f80000
 8000910:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
  SCB->AIRCR =  reg_value;
 8000914:	60d3      	str	r3, [r2, #12]
  /* Check the parameters */
  assert_param(IS_NVIC_PRIORITY_GROUP(PriorityGroup));
  
  /* Set the PRIGROUP[10:8] bits according to the PriorityGroup parameter value */
  NVIC_SetPriorityGrouping(PriorityGroup);
}
 8000916:	4770      	bx	lr
 8000918:	e000ed00 	.word	0xe000ed00

0800091c <HAL_NVIC_SetPriority>:
  *         This parameter can be a value between 0 and 15
  *         A lower priority value indicates a higher priority.          
  * @retval None
  */
void HAL_NVIC_SetPriority(IRQn_Type IRQn, uint32_t PreemptPriority, uint32_t SubPriority)
{ 
 800091c:	b510      	push	{r4, lr}
 800091e:	4604      	mov	r4, r0
  return ((uint32_t)((SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) >> SCB_AIRCR_PRIGROUP_Pos));
 8000920:	4b05      	ldr	r3, [pc, #20]	@ (8000938 <HAL_NVIC_SetPriority+0x1c>)
 8000922:	68d8      	ldr	r0, [r3, #12]
  assert_param(IS_NVIC_SUB_PRIORITY(SubPriority));
  assert_param(IS_NVIC_PREEMPTION_PRIORITY(PreemptPriority));
  
  prioritygroup = NVIC_GetPriorityGrouping();
  
  NVIC_SetPriority(IRQn, NVIC_EncodePriority(prioritygroup, PreemptPriority, SubPriority));
 8000924:	f3c0 2002 	ubfx	r0, r0, #8, #3
 8000928:	f7ff ffc6 	bl	80008b8 <NVIC_EncodePriority>
 800092c:	4601      	mov	r1, r0
 800092e:	4620      	mov	r0, r4
 8000930:	f7ff ffae 	bl	8000890 <__NVIC_SetPriority>
}
 8000934:	bd10      	pop	{r4, pc}
 8000936:	bf00      	nop
 8000938:	e000ed00 	.word	0xe000ed00

0800093c <HAL_SYSTICK_Config>:
           function <b>SysTick_Config</b> is not included. In this case, the file <b><i>device</i>.h</b>
           must contain a vendor-specific implementation of this function.
 */
__STATIC_INLINE uint32_t SysTick_Config(uint32_t ticks)
{
  if ((ticks - 1UL) > SysTick_LOAD_RELOAD_Msk)
 800093c:	3801      	subs	r0, #1
 800093e:	f1b0 7f80 	cmp.w	r0, #16777216	@ 0x1000000
 8000942:	d20b      	bcs.n	800095c <HAL_SYSTICK_Config+0x20>
  {
    return (1UL);                                                   /* Reload value impossible */
  }

  SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
 8000944:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8000948:	6158      	str	r0, [r3, #20]
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 800094a:	4a05      	ldr	r2, [pc, #20]	@ (8000960 <HAL_SYSTICK_Config+0x24>)
 800094c:	21f0      	movs	r1, #240	@ 0xf0
 800094e:	f882 1023 	strb.w	r1, [r2, #35]	@ 0x23
  NVIC_SetPriority (SysTick_IRQn, (1UL << __NVIC_PRIO_BITS) - 1UL); /* set Priority for Systick Interrupt */
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
 8000952:	2000      	movs	r0, #0
 8000954:	6198      	str	r0, [r3, #24]
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
 8000956:	2207      	movs	r2, #7
 8000958:	611a      	str	r2, [r3, #16]
                   SysTick_CTRL_TICKINT_Msk   |
                   SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTick Timer */
  return (0UL);                                                     /* Function successful */
 800095a:	4770      	bx	lr
    return (1UL);                                                   /* Reload value impossible */
 800095c:	2001      	movs	r0, #1
  *                  - 1  Function failed.
  */
uint32_t HAL_SYSTICK_Config(uint32_t TicksNumb)
{
   return SysTick_Config(TicksNumb);
}
 800095e:	4770      	bx	lr
 8000960:	e000ed00 	.word	0xe000ed00

******** <FLASH_Program_HalfWord>:
  * @retval None
  */
static void FLASH_Program_HalfWord(uint32_t Address, uint16_t Data)
{
  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 8000964:	4b04      	ldr	r3, [pc, #16]	@ (8000978 <FLASH_Program_HalfWord+0x14>)
 8000966:	2200      	movs	r2, #0
 8000968:	61da      	str	r2, [r3, #28]
#if defined(FLASH_BANK2_END)
  if(Address <= FLASH_BANK1_END)
  {
#endif /* FLASH_BANK2_END */
    /* Proceed to program the new data */
    SET_BIT(FLASH->CR, FLASH_CR_PG);
 800096a:	4a04      	ldr	r2, [pc, #16]	@ (800097c <FLASH_Program_HalfWord+0x18>)
 800096c:	6913      	ldr	r3, [r2, #16]
 800096e:	f043 0301 	orr.w	r3, r3, #1
 8000972:	6113      	str	r3, [r2, #16]
    SET_BIT(FLASH->CR2, FLASH_CR2_PG);
  }
#endif /* FLASH_BANK2_END */

  /* Write data in the address */
  *(__IO uint16_t*)Address = Data;
 8000974:	8001      	strh	r1, [r0, #0]
}
 8000976:	4770      	bx	lr
 8000978:	******** 	.word	0x********
 800097c:	******** 	.word	0x********

******** <FLASH_SetErrorCode>:
  uint32_t flags = 0U;
  
#if defined(FLASH_BANK2_END)
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR) || __HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR_BANK2))
#else
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR))
 8000980:	4b19      	ldr	r3, [pc, #100]	@ (80009e8 <FLASH_SetErrorCode+0x68>)
 8000982:	68db      	ldr	r3, [r3, #12]
 8000984:	f013 0310 	ands.w	r3, r3, #16
 8000988:	d005      	beq.n	8000996 <FLASH_SetErrorCode+0x16>
#endif /* FLASH_BANK2_END */
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_WRP;
 800098a:	4a18      	ldr	r2, [pc, #96]	@ (80009ec <FLASH_SetErrorCode+0x6c>)
 800098c:	69d3      	ldr	r3, [r2, #28]
 800098e:	f043 0302 	orr.w	r3, r3, #2
 8000992:	61d3      	str	r3, [r2, #28]
#if defined(FLASH_BANK2_END)
    flags |= FLASH_FLAG_WRPERR | FLASH_FLAG_WRPERR_BANK2;
#else
    flags |= FLASH_FLAG_WRPERR;
 8000994:	2310      	movs	r3, #16
#endif /* FLASH_BANK2_END */
  }
#if defined(FLASH_BANK2_END)
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR) || __HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR_BANK2))
#else
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR))
 8000996:	4a14      	ldr	r2, [pc, #80]	@ (80009e8 <FLASH_SetErrorCode+0x68>)
 8000998:	68d2      	ldr	r2, [r2, #12]
 800099a:	f012 0f04 	tst.w	r2, #4
 800099e:	d006      	beq.n	80009ae <FLASH_SetErrorCode+0x2e>
#endif /* FLASH_BANK2_END */
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_PROG;
 80009a0:	4912      	ldr	r1, [pc, #72]	@ (80009ec <FLASH_SetErrorCode+0x6c>)
 80009a2:	69ca      	ldr	r2, [r1, #28]
 80009a4:	f042 0201 	orr.w	r2, r2, #1
 80009a8:	61ca      	str	r2, [r1, #28]
#if defined(FLASH_BANK2_END)
    flags |= FLASH_FLAG_PGERR | FLASH_FLAG_PGERR_BANK2;
#else
    flags |= FLASH_FLAG_PGERR;
 80009aa:	f043 0304 	orr.w	r3, r3, #4
#endif /* FLASH_BANK2_END */
  }
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR))
 80009ae:	4a0e      	ldr	r2, [pc, #56]	@ (80009e8 <FLASH_SetErrorCode+0x68>)
 80009b0:	69d2      	ldr	r2, [r2, #28]
 80009b2:	f012 0f01 	tst.w	r2, #1
 80009b6:	d009      	beq.n	80009cc <FLASH_SetErrorCode+0x4c>
  {
    pFlash.ErrorCode |= HAL_FLASH_ERROR_OPTV;
 80009b8:	490c      	ldr	r1, [pc, #48]	@ (80009ec <FLASH_SetErrorCode+0x6c>)
 80009ba:	69ca      	ldr	r2, [r1, #28]
 80009bc:	f042 0204 	orr.w	r2, r2, #4
 80009c0:	61ca      	str	r2, [r1, #28]
  __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_OPTVERR);
 80009c2:	4909      	ldr	r1, [pc, #36]	@ (80009e8 <FLASH_SetErrorCode+0x68>)
 80009c4:	69ca      	ldr	r2, [r1, #28]
 80009c6:	f022 0201 	bic.w	r2, r2, #1
 80009ca:	61ca      	str	r2, [r1, #28]
  }

  /* Clear FLASH error pending bits */
  __HAL_FLASH_CLEAR_FLAG(flags);
 80009cc:	f240 1201 	movw	r2, #257	@ 0x101
 80009d0:	4293      	cmp	r3, r2
 80009d2:	d002      	beq.n	80009da <FLASH_SetErrorCode+0x5a>
 80009d4:	4a04      	ldr	r2, [pc, #16]	@ (80009e8 <FLASH_SetErrorCode+0x68>)
 80009d6:	60d3      	str	r3, [r2, #12]
}  
 80009d8:	4770      	bx	lr
  __HAL_FLASH_CLEAR_FLAG(flags);
 80009da:	4a03      	ldr	r2, [pc, #12]	@ (80009e8 <FLASH_SetErrorCode+0x68>)
 80009dc:	69d3      	ldr	r3, [r2, #28]
 80009de:	f023 0301 	bic.w	r3, r3, #1
 80009e2:	61d3      	str	r3, [r2, #28]
 80009e4:	4770      	bx	lr
 80009e6:	bf00      	nop
 80009e8:	******** 	.word	0x********
 80009ec:	******** 	.word	0x********

080009f0 <HAL_FLASH_Unlock>:
  if(READ_BIT(FLASH->CR, FLASH_CR_LOCK) != RESET)
 80009f0:	4b0a      	ldr	r3, [pc, #40]	@ (8000a1c <HAL_FLASH_Unlock+0x2c>)
 80009f2:	691b      	ldr	r3, [r3, #16]
 80009f4:	f013 0f80 	tst.w	r3, #128	@ 0x80
 80009f8:	d00b      	beq.n	8000a12 <HAL_FLASH_Unlock+0x22>
    WRITE_REG(FLASH->KEYR, FLASH_KEY1);
 80009fa:	4b08      	ldr	r3, [pc, #32]	@ (8000a1c <HAL_FLASH_Unlock+0x2c>)
 80009fc:	4a08      	ldr	r2, [pc, #32]	@ (8000a20 <HAL_FLASH_Unlock+0x30>)
 80009fe:	605a      	str	r2, [r3, #4]
    WRITE_REG(FLASH->KEYR, FLASH_KEY2);
 8000a00:	f102 3288 	add.w	r2, r2, #2290649224	@ 0x88888888
 8000a04:	605a      	str	r2, [r3, #4]
    if(READ_BIT(FLASH->CR, FLASH_CR_LOCK) != RESET)
 8000a06:	691b      	ldr	r3, [r3, #16]
 8000a08:	f013 0f80 	tst.w	r3, #128	@ 0x80
 8000a0c:	d103      	bne.n	8000a16 <HAL_FLASH_Unlock+0x26>
  HAL_StatusTypeDef status = HAL_OK;
 8000a0e:	2000      	movs	r0, #0
 8000a10:	4770      	bx	lr
 8000a12:	2000      	movs	r0, #0
 8000a14:	4770      	bx	lr
      status = HAL_ERROR;
 8000a16:	2001      	movs	r0, #1
}
 8000a18:	4770      	bx	lr
 8000a1a:	bf00      	nop
 8000a1c:	******** 	.word	0x********
 8000a20:	45670123 	.word	0x45670123

08000a24 <HAL_FLASH_Lock>:
  SET_BIT(FLASH->CR, FLASH_CR_LOCK);
 8000a24:	4a03      	ldr	r2, [pc, #12]	@ (8000a34 <HAL_FLASH_Lock+0x10>)
 8000a26:	6913      	ldr	r3, [r2, #16]
 8000a28:	f043 0380 	orr.w	r3, r3, #128	@ 0x80
 8000a2c:	6113      	str	r3, [r2, #16]
}
 8000a2e:	2000      	movs	r0, #0
 8000a30:	4770      	bx	lr
 8000a32:	bf00      	nop
 8000a34:	******** 	.word	0x********

08000a38 <FLASH_WaitForLastOperation>:
{
 8000a38:	b538      	push	{r3, r4, r5, lr}
 8000a3a:	4604      	mov	r4, r0
  uint32_t tickstart = HAL_GetTick();
 8000a3c:	f7ff ff0e 	bl	800085c <HAL_GetTick>
 8000a40:	4605      	mov	r5, r0
  while(__HAL_FLASH_GET_FLAG(FLASH_FLAG_BSY)) 
 8000a42:	4b16      	ldr	r3, [pc, #88]	@ (8000a9c <FLASH_WaitForLastOperation+0x64>)
 8000a44:	68db      	ldr	r3, [r3, #12]
 8000a46:	f013 0f01 	tst.w	r3, #1
 8000a4a:	d00a      	beq.n	8000a62 <FLASH_WaitForLastOperation+0x2a>
    if (Timeout != HAL_MAX_DELAY)
 8000a4c:	f1b4 3fff 	cmp.w	r4, #4294967295
 8000a50:	d0f7      	beq.n	8000a42 <FLASH_WaitForLastOperation+0xa>
      if((Timeout == 0U) || ((HAL_GetTick()-tickstart) > Timeout))
 8000a52:	b124      	cbz	r4, 8000a5e <FLASH_WaitForLastOperation+0x26>
 8000a54:	f7ff ff02 	bl	800085c <HAL_GetTick>
 8000a58:	1b40      	subs	r0, r0, r5
 8000a5a:	42a0      	cmp	r0, r4
 8000a5c:	d9f1      	bls.n	8000a42 <FLASH_WaitForLastOperation+0xa>
        return HAL_TIMEOUT;
 8000a5e:	2003      	movs	r0, #3
 8000a60:	e01b      	b.n	8000a9a <FLASH_WaitForLastOperation+0x62>
  if (__HAL_FLASH_GET_FLAG(FLASH_FLAG_EOP))
 8000a62:	4b0e      	ldr	r3, [pc, #56]	@ (8000a9c <FLASH_WaitForLastOperation+0x64>)
 8000a64:	68db      	ldr	r3, [r3, #12]
 8000a66:	f013 0f20 	tst.w	r3, #32
 8000a6a:	d002      	beq.n	8000a72 <FLASH_WaitForLastOperation+0x3a>
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP);
 8000a6c:	4b0b      	ldr	r3, [pc, #44]	@ (8000a9c <FLASH_WaitForLastOperation+0x64>)
 8000a6e:	2220      	movs	r2, #32
 8000a70:	60da      	str	r2, [r3, #12]
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR)  || 
 8000a72:	4b0a      	ldr	r3, [pc, #40]	@ (8000a9c <FLASH_WaitForLastOperation+0x64>)
 8000a74:	68db      	ldr	r3, [r3, #12]
 8000a76:	f013 0f10 	tst.w	r3, #16
 8000a7a:	d10b      	bne.n	8000a94 <FLASH_WaitForLastOperation+0x5c>
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR) || 
 8000a7c:	4b07      	ldr	r3, [pc, #28]	@ (8000a9c <FLASH_WaitForLastOperation+0x64>)
 8000a7e:	69db      	ldr	r3, [r3, #28]
  if(__HAL_FLASH_GET_FLAG(FLASH_FLAG_WRPERR)  || 
 8000a80:	f013 0f01 	tst.w	r3, #1
 8000a84:	d106      	bne.n	8000a94 <FLASH_WaitForLastOperation+0x5c>
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_PGERR))
 8000a86:	4b05      	ldr	r3, [pc, #20]	@ (8000a9c <FLASH_WaitForLastOperation+0x64>)
 8000a88:	68db      	ldr	r3, [r3, #12]
     __HAL_FLASH_GET_FLAG(FLASH_FLAG_OPTVERR) || 
 8000a8a:	f013 0f04 	tst.w	r3, #4
 8000a8e:	d101      	bne.n	8000a94 <FLASH_WaitForLastOperation+0x5c>
  return HAL_OK;
 8000a90:	2000      	movs	r0, #0
 8000a92:	e002      	b.n	8000a9a <FLASH_WaitForLastOperation+0x62>
    FLASH_SetErrorCode();
 8000a94:	f7ff ff74 	bl	8000980 <FLASH_SetErrorCode>
    return HAL_ERROR;
 8000a98:	2001      	movs	r0, #1
}
 8000a9a:	bd38      	pop	{r3, r4, r5, pc}
 8000a9c:	******** 	.word	0x********

08000aa0 <HAL_FLASH_Program>:
{
 8000aa0:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 8000aa4:	461e      	mov	r6, r3
  __HAL_LOCK(&pFlash);
 8000aa6:	4b24      	ldr	r3, [pc, #144]	@ (8000b38 <HAL_FLASH_Program+0x98>)
 8000aa8:	7e1b      	ldrb	r3, [r3, #24]
 8000aaa:	2b01      	cmp	r3, #1
 8000aac:	d041      	beq.n	8000b32 <HAL_FLASH_Program+0x92>
 8000aae:	4604      	mov	r4, r0
 8000ab0:	460f      	mov	r7, r1
 8000ab2:	4690      	mov	r8, r2
 8000ab4:	4b20      	ldr	r3, [pc, #128]	@ (8000b38 <HAL_FLASH_Program+0x98>)
 8000ab6:	2201      	movs	r2, #1
 8000ab8:	761a      	strb	r2, [r3, #24]
    status = FLASH_WaitForLastOperation(FLASH_TIMEOUT_VALUE);
 8000aba:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000abe:	f7ff ffbb 	bl	8000a38 <FLASH_WaitForLastOperation>
  if(status == HAL_OK)
 8000ac2:	4603      	mov	r3, r0
 8000ac4:	bb78      	cbnz	r0, 8000b26 <HAL_FLASH_Program+0x86>
    if(TypeProgram == FLASH_TYPEPROGRAM_HALFWORD)
 8000ac6:	2c01      	cmp	r4, #1
 8000ac8:	d008      	beq.n	8000adc <HAL_FLASH_Program+0x3c>
    else if(TypeProgram == FLASH_TYPEPROGRAM_WORD)
 8000aca:	2c02      	cmp	r4, #2
 8000acc:	d003      	beq.n	8000ad6 <HAL_FLASH_Program+0x36>
      nbiterations = 4U;
 8000ace:	f04f 0904 	mov.w	r9, #4
    for (index = 0U; index < nbiterations; index++)
 8000ad2:	461c      	mov	r4, r3
 8000ad4:	e007      	b.n	8000ae6 <HAL_FLASH_Program+0x46>
      nbiterations = 2U;
 8000ad6:	f04f 0902 	mov.w	r9, #2
 8000ada:	e7fa      	b.n	8000ad2 <HAL_FLASH_Program+0x32>
      nbiterations = 1U;
 8000adc:	f04f 0901 	mov.w	r9, #1
 8000ae0:	e7f7      	b.n	8000ad2 <HAL_FLASH_Program+0x32>
    for (index = 0U; index < nbiterations; index++)
 8000ae2:	3401      	adds	r4, #1
 8000ae4:	b2e4      	uxtb	r4, r4
 8000ae6:	454c      	cmp	r4, r9
 8000ae8:	d21d      	bcs.n	8000b26 <HAL_FLASH_Program+0x86>
      FLASH_Program_HalfWord((Address + (2U*index)), (uint16_t)(Data >> (16U*index)));
 8000aea:	0121      	lsls	r1, r4, #4
 8000aec:	f1c1 0220 	rsb	r2, r1, #32
 8000af0:	f1a1 0320 	sub.w	r3, r1, #32
 8000af4:	fa28 f101 	lsr.w	r1, r8, r1
 8000af8:	fa06 f202 	lsl.w	r2, r6, r2
 8000afc:	4311      	orrs	r1, r2
 8000afe:	fa26 f303 	lsr.w	r3, r6, r3
 8000b02:	4319      	orrs	r1, r3
 8000b04:	b289      	uxth	r1, r1
 8000b06:	eb07 0044 	add.w	r0, r7, r4, lsl #1
 8000b0a:	f7ff ff2b 	bl	8000964 <FLASH_Program_HalfWord>
        status = FLASH_WaitForLastOperation(FLASH_TIMEOUT_VALUE);
 8000b0e:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000b12:	f7ff ff91 	bl	8000a38 <FLASH_WaitForLastOperation>
        CLEAR_BIT(FLASH->CR, FLASH_CR_PG);
 8000b16:	4b09      	ldr	r3, [pc, #36]	@ (8000b3c <HAL_FLASH_Program+0x9c>)
 8000b18:	691d      	ldr	r5, [r3, #16]
 8000b1a:	f025 0501 	bic.w	r5, r5, #1
 8000b1e:	611d      	str	r5, [r3, #16]
      if (status != HAL_OK)
 8000b20:	4603      	mov	r3, r0
 8000b22:	2800      	cmp	r0, #0
 8000b24:	d0dd      	beq.n	8000ae2 <HAL_FLASH_Program+0x42>
  __HAL_UNLOCK(&pFlash);
 8000b26:	4a04      	ldr	r2, [pc, #16]	@ (8000b38 <HAL_FLASH_Program+0x98>)
 8000b28:	2100      	movs	r1, #0
 8000b2a:	7611      	strb	r1, [r2, #24]
}
 8000b2c:	4618      	mov	r0, r3
 8000b2e:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
  __HAL_LOCK(&pFlash);
 8000b32:	2302      	movs	r3, #2
 8000b34:	e7fa      	b.n	8000b2c <HAL_FLASH_Program+0x8c>
 8000b36:	bf00      	nop
 8000b38:	******** 	.word	0x********
 8000b3c:	******** 	.word	0x********

08000b40 <FLASH_MassErase>:
{
  /* Check the parameters */
  assert_param(IS_FLASH_BANK(Banks));

  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 8000b40:	4b06      	ldr	r3, [pc, #24]	@ (8000b5c <FLASH_MassErase+0x1c>)
 8000b42:	2200      	movs	r2, #0
 8000b44:	61da      	str	r2, [r3, #28]
#if !defined(FLASH_BANK2_END)
  /* Prevent unused argument(s) compilation warning */
  UNUSED(Banks);
#endif /* FLASH_BANK2_END */  
    /* Only bank1 will be erased*/
    SET_BIT(FLASH->CR, FLASH_CR_MER);
 8000b46:	4b06      	ldr	r3, [pc, #24]	@ (8000b60 <FLASH_MassErase+0x20>)
 8000b48:	691a      	ldr	r2, [r3, #16]
 8000b4a:	f042 0204 	orr.w	r2, r2, #4
 8000b4e:	611a      	str	r2, [r3, #16]
    SET_BIT(FLASH->CR, FLASH_CR_STRT);
 8000b50:	691a      	ldr	r2, [r3, #16]
 8000b52:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8000b56:	611a      	str	r2, [r3, #16]
#if defined(FLASH_BANK2_END)
  }
#endif /* FLASH_BANK2_END */
}
 8000b58:	4770      	bx	lr
 8000b5a:	bf00      	nop
 8000b5c:	******** 	.word	0x********
 8000b60:	******** 	.word	0x********

08000b64 <FLASH_PageErase>:
  * @retval None
  */
void FLASH_PageErase(uint32_t PageAddress)
{
  /* Clean the error context */
  pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 8000b64:	4b06      	ldr	r3, [pc, #24]	@ (8000b80 <FLASH_PageErase+0x1c>)
 8000b66:	2200      	movs	r2, #0
 8000b68:	61da      	str	r2, [r3, #28]
  }
  else
  {
#endif /* FLASH_BANK2_END */
    /* Proceed to erase the page */
    SET_BIT(FLASH->CR, FLASH_CR_PER);
 8000b6a:	4b06      	ldr	r3, [pc, #24]	@ (8000b84 <FLASH_PageErase+0x20>)
 8000b6c:	691a      	ldr	r2, [r3, #16]
 8000b6e:	f042 0202 	orr.w	r2, r2, #2
 8000b72:	611a      	str	r2, [r3, #16]
    WRITE_REG(FLASH->AR, PageAddress);
 8000b74:	6158      	str	r0, [r3, #20]
    SET_BIT(FLASH->CR, FLASH_CR_STRT);
 8000b76:	691a      	ldr	r2, [r3, #16]
 8000b78:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8000b7c:	611a      	str	r2, [r3, #16]
#if defined(FLASH_BANK2_END)
  }
#endif /* FLASH_BANK2_END */
}
 8000b7e:	4770      	bx	lr
 8000b80:	******** 	.word	0x********
 8000b84:	******** 	.word	0x********

08000b88 <HAL_FLASHEx_Erase>:
  __HAL_LOCK(&pFlash);
 8000b88:	4b26      	ldr	r3, [pc, #152]	@ (8000c24 <HAL_FLASHEx_Erase+0x9c>)
 8000b8a:	7e1b      	ldrb	r3, [r3, #24]
 8000b8c:	2b01      	cmp	r3, #1
 8000b8e:	d046      	beq.n	8000c1e <HAL_FLASHEx_Erase+0x96>
{
 8000b90:	b570      	push	{r4, r5, r6, lr}
 8000b92:	4605      	mov	r5, r0
 8000b94:	460e      	mov	r6, r1
  __HAL_LOCK(&pFlash);
 8000b96:	4b23      	ldr	r3, [pc, #140]	@ (8000c24 <HAL_FLASHEx_Erase+0x9c>)
 8000b98:	2201      	movs	r2, #1
 8000b9a:	761a      	strb	r2, [r3, #24]
  if (pEraseInit->TypeErase == FLASH_TYPEERASE_MASSERASE)
 8000b9c:	6803      	ldr	r3, [r0, #0]
 8000b9e:	2b02      	cmp	r3, #2
 8000ba0:	d020      	beq.n	8000be4 <HAL_FLASHEx_Erase+0x5c>
      if (FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE) == HAL_OK)
 8000ba2:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000ba6:	f7ff ff47 	bl	8000a38 <FLASH_WaitForLastOperation>
 8000baa:	bb90      	cbnz	r0, 8000c12 <HAL_FLASHEx_Erase+0x8a>
        *PageError = 0xFFFFFFFFU;
 8000bac:	f04f 33ff 	mov.w	r3, #4294967295
 8000bb0:	6033      	str	r3, [r6, #0]
        for(address = pEraseInit->PageAddress;
 8000bb2:	68ac      	ldr	r4, [r5, #8]
  HAL_StatusTypeDef status = HAL_ERROR;
 8000bb4:	2101      	movs	r1, #1
            address < ((pEraseInit->NbPages * FLASH_PAGE_SIZE) + pEraseInit->PageAddress);
 8000bb6:	68ea      	ldr	r2, [r5, #12]
 8000bb8:	68ab      	ldr	r3, [r5, #8]
 8000bba:	eb03 2382 	add.w	r3, r3, r2, lsl #10
 8000bbe:	42a3      	cmp	r3, r4
 8000bc0:	d928      	bls.n	8000c14 <HAL_FLASHEx_Erase+0x8c>
          FLASH_PageErase(address);
 8000bc2:	4620      	mov	r0, r4
 8000bc4:	f7ff ffce 	bl	8000b64 <FLASH_PageErase>
          status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE);
 8000bc8:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000bcc:	f7ff ff34 	bl	8000a38 <FLASH_WaitForLastOperation>
          CLEAR_BIT(FLASH->CR, FLASH_CR_PER);
 8000bd0:	4a15      	ldr	r2, [pc, #84]	@ (8000c28 <HAL_FLASHEx_Erase+0xa0>)
 8000bd2:	6913      	ldr	r3, [r2, #16]
 8000bd4:	f023 0302 	bic.w	r3, r3, #2
 8000bd8:	6113      	str	r3, [r2, #16]
          if (status != HAL_OK)
 8000bda:	4601      	mov	r1, r0
 8000bdc:	b9b8      	cbnz	r0, 8000c0e <HAL_FLASHEx_Erase+0x86>
            address += FLASH_PAGE_SIZE)
 8000bde:	f504 6480 	add.w	r4, r4, #1024	@ 0x400
 8000be2:	e7e8      	b.n	8000bb6 <HAL_FLASHEx_Erase+0x2e>
      if (FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE) == HAL_OK)
 8000be4:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000be8:	f7ff ff26 	bl	8000a38 <FLASH_WaitForLastOperation>
 8000bec:	b108      	cbz	r0, 8000bf2 <HAL_FLASHEx_Erase+0x6a>
  HAL_StatusTypeDef status = HAL_ERROR;
 8000bee:	2101      	movs	r1, #1
 8000bf0:	e010      	b.n	8000c14 <HAL_FLASHEx_Erase+0x8c>
        FLASH_MassErase(FLASH_BANK_1);
 8000bf2:	2001      	movs	r0, #1
 8000bf4:	f7ff ffa4 	bl	8000b40 <FLASH_MassErase>
        status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE);
 8000bf8:	f24c 3050 	movw	r0, #50000	@ 0xc350
 8000bfc:	f7ff ff1c 	bl	8000a38 <FLASH_WaitForLastOperation>
 8000c00:	4601      	mov	r1, r0
        CLEAR_BIT(FLASH->CR, FLASH_CR_MER);
 8000c02:	4a09      	ldr	r2, [pc, #36]	@ (8000c28 <HAL_FLASHEx_Erase+0xa0>)
 8000c04:	6913      	ldr	r3, [r2, #16]
 8000c06:	f023 0304 	bic.w	r3, r3, #4
 8000c0a:	6113      	str	r3, [r2, #16]
 8000c0c:	e002      	b.n	8000c14 <HAL_FLASHEx_Erase+0x8c>
            *PageError = address;
 8000c0e:	6034      	str	r4, [r6, #0]
            break;
 8000c10:	e000      	b.n	8000c14 <HAL_FLASHEx_Erase+0x8c>
  HAL_StatusTypeDef status = HAL_ERROR;
 8000c12:	2101      	movs	r1, #1
  __HAL_UNLOCK(&pFlash);
 8000c14:	4b03      	ldr	r3, [pc, #12]	@ (8000c24 <HAL_FLASHEx_Erase+0x9c>)
 8000c16:	2200      	movs	r2, #0
 8000c18:	761a      	strb	r2, [r3, #24]
}
 8000c1a:	4608      	mov	r0, r1
 8000c1c:	bd70      	pop	{r4, r5, r6, pc}
  __HAL_LOCK(&pFlash);
 8000c1e:	2102      	movs	r1, #2
}
 8000c20:	4608      	mov	r0, r1
 8000c22:	4770      	bx	lr
 8000c24:	******** 	.word	0x********
 8000c28:	******** 	.word	0x********

08000c2c <HAL_GPIO_Init>:
  * @param  GPIO_Init: pointer to a GPIO_InitTypeDef structure that contains
  *         the configuration information for the specified GPIO peripheral.
  * @retval None
  */
void HAL_GPIO_Init(GPIO_TypeDef  *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
 8000c2c:	b570      	push	{r4, r5, r6, lr}
 8000c2e:	b082      	sub	sp, #8
  uint32_t position = 0x00u;
  uint32_t ioposition;
  uint32_t iocurrent;
  uint32_t temp;
  uint32_t config = 0x00u;
 8000c30:	2400      	movs	r4, #0
  uint32_t position = 0x00u;
 8000c32:	46a4      	mov	ip, r4
  assert_param(IS_GPIO_ALL_INSTANCE(GPIOx));
  assert_param(IS_GPIO_PIN(GPIO_Init->Pin));
  assert_param(IS_GPIO_MODE(GPIO_Init->Mode));

  /* Configure the port pins */
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 8000c34:	e0a2      	b.n	8000d7c <HAL_GPIO_Init+0x150>
    {
      /* Check the Alternate function parameters */
      assert_param(IS_GPIO_AF_INSTANCE(GPIOx));

      /* Based on the required mode, filling config variable with MODEy[1:0] and CNFy[3:2] corresponding bits */
      switch (GPIO_Init->Mode)
 8000c36:	4d7e      	ldr	r5, [pc, #504]	@ (8000e30 <HAL_GPIO_Init+0x204>)
 8000c38:	42ab      	cmp	r3, r5
 8000c3a:	d010      	beq.n	8000c5e <HAL_GPIO_Init+0x32>
 8000c3c:	d907      	bls.n	8000c4e <HAL_GPIO_Init+0x22>
 8000c3e:	4d7d      	ldr	r5, [pc, #500]	@ (8000e34 <HAL_GPIO_Init+0x208>)
 8000c40:	42ab      	cmp	r3, r5
 8000c42:	d00c      	beq.n	8000c5e <HAL_GPIO_Init+0x32>
 8000c44:	f505 3580 	add.w	r5, r5, #65536	@ 0x10000
 8000c48:	42ab      	cmp	r3, r5
 8000c4a:	d008      	beq.n	8000c5e <HAL_GPIO_Init+0x32>
 8000c4c:	e013      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
 8000c4e:	f5a5 1580 	sub.w	r5, r5, #1048576	@ 0x100000
 8000c52:	42ab      	cmp	r3, r5
 8000c54:	d003      	beq.n	8000c5e <HAL_GPIO_Init+0x32>
 8000c56:	f505 2570 	add.w	r5, r5, #983040	@ 0xf0000
 8000c5a:	42ab      	cmp	r3, r5
 8000c5c:	d107      	bne.n	8000c6e <HAL_GPIO_Init+0x42>
        case GPIO_MODE_EVT_RISING:
        case GPIO_MODE_EVT_FALLING:
        case GPIO_MODE_EVT_RISING_FALLING:
          /* Check the GPIO pull parameter */
          assert_param(IS_GPIO_PULL(GPIO_Init->Pull));
          if (GPIO_Init->Pull == GPIO_NOPULL)
 8000c5e:	688b      	ldr	r3, [r1, #8]
 8000c60:	2b00      	cmp	r3, #0
 8000c62:	d055      	beq.n	8000d10 <HAL_GPIO_Init+0xe4>
          {
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_FLOATING;
          }
          else if (GPIO_Init->Pull == GPIO_PULLUP)
 8000c64:	2b01      	cmp	r3, #1
 8000c66:	d04e      	beq.n	8000d06 <HAL_GPIO_Init+0xda>
          else /* GPIO_PULLDOWN */
          {
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;

            /* Reset the corresponding ODR bit */
            GPIOx->BRR = ioposition;
 8000c68:	6142      	str	r2, [r0, #20]
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;
 8000c6a:	2408      	movs	r4, #8
 8000c6c:	e003      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
      switch (GPIO_Init->Mode)
 8000c6e:	f5a5 1580 	sub.w	r5, r5, #1048576	@ 0x100000
 8000c72:	42ab      	cmp	r3, r5
 8000c74:	d0f3      	beq.n	8000c5e <HAL_GPIO_Init+0x32>
          break;
      }

      /* Check if the current bit belongs to first half or last half of the pin count number
       in order to address CRH or CRL register*/
      configregister = (iocurrent < GPIO_PIN_8) ? &GPIOx->CRL     : &GPIOx->CRH;
 8000c76:	f1be 0fff 	cmp.w	lr, #255	@ 0xff
 8000c7a:	d84b      	bhi.n	8000d14 <HAL_GPIO_Init+0xe8>
 8000c7c:	4606      	mov	r6, r0
      registeroffset = (iocurrent < GPIO_PIN_8) ? (position << 2u) : ((position - 8u) << 2u);
 8000c7e:	ea4f 028c 	mov.w	r2, ip, lsl #2

      /* Apply the new configuration of the pin to the register */
      MODIFY_REG((*configregister), ((GPIO_CRL_MODE0 | GPIO_CRL_CNF0) << registeroffset), (config << registeroffset));
 8000c82:	6833      	ldr	r3, [r6, #0]
 8000c84:	250f      	movs	r5, #15
 8000c86:	4095      	lsls	r5, r2
 8000c88:	ea23 0305 	bic.w	r3, r3, r5
 8000c8c:	fa04 f202 	lsl.w	r2, r4, r2
 8000c90:	4313      	orrs	r3, r2
 8000c92:	6033      	str	r3, [r6, #0]

      /*--------------------- EXTI Mode Configuration ------------------------*/
      /* Configure the External Interrupt or event for the current IO */
      if ((GPIO_Init->Mode & EXTI_MODE) == EXTI_MODE)
 8000c94:	684b      	ldr	r3, [r1, #4]
 8000c96:	f013 5f80 	tst.w	r3, #268435456	@ 0x10000000
 8000c9a:	d06d      	beq.n	8000d78 <HAL_GPIO_Init+0x14c>
      {
        /* Enable AFIO Clock */
        __HAL_RCC_AFIO_CLK_ENABLE();
 8000c9c:	4b66      	ldr	r3, [pc, #408]	@ (8000e38 <HAL_GPIO_Init+0x20c>)
 8000c9e:	699a      	ldr	r2, [r3, #24]
 8000ca0:	f042 0201 	orr.w	r2, r2, #1
 8000ca4:	619a      	str	r2, [r3, #24]
 8000ca6:	699b      	ldr	r3, [r3, #24]
 8000ca8:	f003 0301 	and.w	r3, r3, #1
 8000cac:	9301      	str	r3, [sp, #4]
 8000cae:	9b01      	ldr	r3, [sp, #4]
        temp = AFIO->EXTICR[position >> 2u];
 8000cb0:	ea4f 029c 	mov.w	r2, ip, lsr #2
 8000cb4:	1c95      	adds	r5, r2, #2
 8000cb6:	4b61      	ldr	r3, [pc, #388]	@ (8000e3c <HAL_GPIO_Init+0x210>)
 8000cb8:	f853 6025 	ldr.w	r6, [r3, r5, lsl #2]
        CLEAR_BIT(temp, (0x0Fu) << (4u * (position & 0x03u)));
 8000cbc:	f00c 0503 	and.w	r5, ip, #3
 8000cc0:	00ad      	lsls	r5, r5, #2
 8000cc2:	230f      	movs	r3, #15
 8000cc4:	40ab      	lsls	r3, r5
 8000cc6:	ea26 0603 	bic.w	r6, r6, r3
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 8000cca:	4b5d      	ldr	r3, [pc, #372]	@ (8000e40 <HAL_GPIO_Init+0x214>)
 8000ccc:	4298      	cmp	r0, r3
 8000cce:	d028      	beq.n	8000d22 <HAL_GPIO_Init+0xf6>
 8000cd0:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 8000cd4:	4298      	cmp	r0, r3
 8000cd6:	f000 808d 	beq.w	8000df4 <HAL_GPIO_Init+0x1c8>
 8000cda:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 8000cde:	4298      	cmp	r0, r3
 8000ce0:	f000 808a 	beq.w	8000df8 <HAL_GPIO_Init+0x1cc>
 8000ce4:	f503 6380 	add.w	r3, r3, #1024	@ 0x400
 8000ce8:	4298      	cmp	r0, r3
 8000cea:	d018      	beq.n	8000d1e <HAL_GPIO_Init+0xf2>
 8000cec:	2304      	movs	r3, #4
 8000cee:	e019      	b.n	8000d24 <HAL_GPIO_Init+0xf8>
          config = GPIO_Init->Speed + GPIO_CR_CNF_GP_OUTPUT_PP;
 8000cf0:	68cc      	ldr	r4, [r1, #12]
          break;
 8000cf2:	e7c0      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_GP_OUTPUT_OD;
 8000cf4:	68cc      	ldr	r4, [r1, #12]
 8000cf6:	3404      	adds	r4, #4
          break;
 8000cf8:	e7bd      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_AF_OUTPUT_PP;
 8000cfa:	68cc      	ldr	r4, [r1, #12]
 8000cfc:	3408      	adds	r4, #8
          break;
 8000cfe:	e7ba      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
          config = GPIO_Init->Speed + GPIO_CR_CNF_AF_OUTPUT_OD;
 8000d00:	68cc      	ldr	r4, [r1, #12]
 8000d02:	340c      	adds	r4, #12
          break;
 8000d04:	e7b7      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
            GPIOx->BSRR = ioposition;
 8000d06:	6102      	str	r2, [r0, #16]
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;
 8000d08:	2408      	movs	r4, #8
 8000d0a:	e7b4      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
          config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_ANALOG;
 8000d0c:	2400      	movs	r4, #0
 8000d0e:	e7b2      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_FLOATING;
 8000d10:	2404      	movs	r4, #4
 8000d12:	e7b0      	b.n	8000c76 <HAL_GPIO_Init+0x4a>
      configregister = (iocurrent < GPIO_PIN_8) ? &GPIOx->CRL     : &GPIOx->CRH;
 8000d14:	1d06      	adds	r6, r0, #4
      registeroffset = (iocurrent < GPIO_PIN_8) ? (position << 2u) : ((position - 8u) << 2u);
 8000d16:	f1ac 0208 	sub.w	r2, ip, #8
 8000d1a:	0092      	lsls	r2, r2, #2
 8000d1c:	e7b1      	b.n	8000c82 <HAL_GPIO_Init+0x56>
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 8000d1e:	2303      	movs	r3, #3
 8000d20:	e000      	b.n	8000d24 <HAL_GPIO_Init+0xf8>
 8000d22:	2300      	movs	r3, #0
 8000d24:	40ab      	lsls	r3, r5
 8000d26:	4333      	orrs	r3, r6
        AFIO->EXTICR[position >> 2u] = temp;
 8000d28:	3202      	adds	r2, #2
 8000d2a:	4d44      	ldr	r5, [pc, #272]	@ (8000e3c <HAL_GPIO_Init+0x210>)
 8000d2c:	f845 3022 	str.w	r3, [r5, r2, lsl #2]


        /* Enable or disable the rising trigger */
        if ((GPIO_Init->Mode & RISING_EDGE) == RISING_EDGE)
 8000d30:	684b      	ldr	r3, [r1, #4]
 8000d32:	f413 1f80 	tst.w	r3, #1048576	@ 0x100000
 8000d36:	d061      	beq.n	8000dfc <HAL_GPIO_Init+0x1d0>
        {
          SET_BIT(EXTI->RTSR, iocurrent);
 8000d38:	4a42      	ldr	r2, [pc, #264]	@ (8000e44 <HAL_GPIO_Init+0x218>)
 8000d3a:	6893      	ldr	r3, [r2, #8]
 8000d3c:	ea43 030e 	orr.w	r3, r3, lr
 8000d40:	6093      	str	r3, [r2, #8]
        {
          CLEAR_BIT(EXTI->RTSR, iocurrent);
        }

        /* Enable or disable the falling trigger */
        if ((GPIO_Init->Mode & FALLING_EDGE) == FALLING_EDGE)
 8000d42:	684b      	ldr	r3, [r1, #4]
 8000d44:	f413 1f00 	tst.w	r3, #2097152	@ 0x200000
 8000d48:	d05e      	beq.n	8000e08 <HAL_GPIO_Init+0x1dc>
        {
          SET_BIT(EXTI->FTSR, iocurrent);
 8000d4a:	4a3e      	ldr	r2, [pc, #248]	@ (8000e44 <HAL_GPIO_Init+0x218>)
 8000d4c:	68d3      	ldr	r3, [r2, #12]
 8000d4e:	ea43 030e 	orr.w	r3, r3, lr
 8000d52:	60d3      	str	r3, [r2, #12]
        {
          CLEAR_BIT(EXTI->FTSR, iocurrent);
        }

        /* Configure the event mask */
        if ((GPIO_Init->Mode & GPIO_MODE_EVT) == GPIO_MODE_EVT)
 8000d54:	684b      	ldr	r3, [r1, #4]
 8000d56:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8000d5a:	d05b      	beq.n	8000e14 <HAL_GPIO_Init+0x1e8>
        {
          SET_BIT(EXTI->EMR, iocurrent);
 8000d5c:	4a39      	ldr	r2, [pc, #228]	@ (8000e44 <HAL_GPIO_Init+0x218>)
 8000d5e:	6853      	ldr	r3, [r2, #4]
 8000d60:	ea43 030e 	orr.w	r3, r3, lr
 8000d64:	6053      	str	r3, [r2, #4]
        {
          CLEAR_BIT(EXTI->EMR, iocurrent);
        }

        /* Configure the interrupt mask */
        if ((GPIO_Init->Mode & GPIO_MODE_IT) == GPIO_MODE_IT)
 8000d66:	684b      	ldr	r3, [r1, #4]
 8000d68:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8000d6c:	d058      	beq.n	8000e20 <HAL_GPIO_Init+0x1f4>
        {
          SET_BIT(EXTI->IMR, iocurrent);
 8000d6e:	4a35      	ldr	r2, [pc, #212]	@ (8000e44 <HAL_GPIO_Init+0x218>)
 8000d70:	6813      	ldr	r3, [r2, #0]
 8000d72:	ea43 030e 	orr.w	r3, r3, lr
 8000d76:	6013      	str	r3, [r2, #0]
          CLEAR_BIT(EXTI->IMR, iocurrent);
        }
      }
    }

	position++;
 8000d78:	f10c 0c01 	add.w	ip, ip, #1
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 8000d7c:	680b      	ldr	r3, [r1, #0]
 8000d7e:	fa33 f20c 	lsrs.w	r2, r3, ip
 8000d82:	d053      	beq.n	8000e2c <HAL_GPIO_Init+0x200>
    ioposition = (0x01uL << position);
 8000d84:	2201      	movs	r2, #1
 8000d86:	fa02 f20c 	lsl.w	r2, r2, ip
    iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;
 8000d8a:	ea03 0e02 	and.w	lr, r3, r2
    if (iocurrent == ioposition)
 8000d8e:	ea32 0303 	bics.w	r3, r2, r3
 8000d92:	d1f1      	bne.n	8000d78 <HAL_GPIO_Init+0x14c>
      switch (GPIO_Init->Mode)
 8000d94:	684b      	ldr	r3, [r1, #4]
 8000d96:	2b12      	cmp	r3, #18
 8000d98:	f63f af4d 	bhi.w	8000c36 <HAL_GPIO_Init+0xa>
 8000d9c:	2b12      	cmp	r3, #18
 8000d9e:	f63f af6a 	bhi.w	8000c76 <HAL_GPIO_Init+0x4a>
 8000da2:	a501      	add	r5, pc, #4	@ (adr r5, 8000da8 <HAL_GPIO_Init+0x17c>)
 8000da4:	f855 f023 	ldr.w	pc, [r5, r3, lsl #2]
 8000da8:	08000c5f 	.word	0x08000c5f
 8000dac:	08000cf1 	.word	0x08000cf1
 8000db0:	08000cfb 	.word	0x08000cfb
 8000db4:	08000d0d 	.word	0x08000d0d
 8000db8:	08000c77 	.word	0x08000c77
 8000dbc:	08000c77 	.word	0x08000c77
 8000dc0:	08000c77 	.word	0x08000c77
 8000dc4:	08000c77 	.word	0x08000c77
 8000dc8:	08000c77 	.word	0x08000c77
 8000dcc:	08000c77 	.word	0x08000c77
 8000dd0:	08000c77 	.word	0x08000c77
 8000dd4:	08000c77 	.word	0x08000c77
 8000dd8:	08000c77 	.word	0x08000c77
 8000ddc:	08000c77 	.word	0x08000c77
 8000de0:	08000c77 	.word	0x08000c77
 8000de4:	08000c77 	.word	0x08000c77
 8000de8:	08000c77 	.word	0x08000c77
 8000dec:	08000cf5 	.word	0x08000cf5
 8000df0:	08000d01 	.word	0x08000d01
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 8000df4:	2301      	movs	r3, #1
 8000df6:	e795      	b.n	8000d24 <HAL_GPIO_Init+0xf8>
 8000df8:	2302      	movs	r3, #2
 8000dfa:	e793      	b.n	8000d24 <HAL_GPIO_Init+0xf8>
          CLEAR_BIT(EXTI->RTSR, iocurrent);
 8000dfc:	4a11      	ldr	r2, [pc, #68]	@ (8000e44 <HAL_GPIO_Init+0x218>)
 8000dfe:	6893      	ldr	r3, [r2, #8]
 8000e00:	ea23 030e 	bic.w	r3, r3, lr
 8000e04:	6093      	str	r3, [r2, #8]
 8000e06:	e79c      	b.n	8000d42 <HAL_GPIO_Init+0x116>
          CLEAR_BIT(EXTI->FTSR, iocurrent);
 8000e08:	4a0e      	ldr	r2, [pc, #56]	@ (8000e44 <HAL_GPIO_Init+0x218>)
 8000e0a:	68d3      	ldr	r3, [r2, #12]
 8000e0c:	ea23 030e 	bic.w	r3, r3, lr
 8000e10:	60d3      	str	r3, [r2, #12]
 8000e12:	e79f      	b.n	8000d54 <HAL_GPIO_Init+0x128>
          CLEAR_BIT(EXTI->EMR, iocurrent);
 8000e14:	4a0b      	ldr	r2, [pc, #44]	@ (8000e44 <HAL_GPIO_Init+0x218>)
 8000e16:	6853      	ldr	r3, [r2, #4]
 8000e18:	ea23 030e 	bic.w	r3, r3, lr
 8000e1c:	6053      	str	r3, [r2, #4]
 8000e1e:	e7a2      	b.n	8000d66 <HAL_GPIO_Init+0x13a>
          CLEAR_BIT(EXTI->IMR, iocurrent);
 8000e20:	4a08      	ldr	r2, [pc, #32]	@ (8000e44 <HAL_GPIO_Init+0x218>)
 8000e22:	6813      	ldr	r3, [r2, #0]
 8000e24:	ea23 030e 	bic.w	r3, r3, lr
 8000e28:	6013      	str	r3, [r2, #0]
 8000e2a:	e7a5      	b.n	8000d78 <HAL_GPIO_Init+0x14c>
  }
}
 8000e2c:	b002      	add	sp, #8
 8000e2e:	bd70      	pop	{r4, r5, r6, pc}
 8000e30:	10220000 	.word	0x10220000
 8000e34:	10310000 	.word	0x10310000
 8000e38:	40021000 	.word	0x40021000
 8000e3c:	40010000 	.word	0x40010000
 8000e40:	40010800 	.word	0x40010800
 8000e44:	40010400 	.word	0x40010400

08000e48 <HAL_GPIO_WritePin>:
{
  /* Check the parameters */
  assert_param(IS_GPIO_PIN(GPIO_Pin));
  assert_param(IS_GPIO_PIN_ACTION(PinState));

  if (PinState != GPIO_PIN_RESET)
 8000e48:	b10a      	cbz	r2, 8000e4e <HAL_GPIO_WritePin+0x6>
  {
    GPIOx->BSRR = GPIO_Pin;
 8000e4a:	6101      	str	r1, [r0, #16]
 8000e4c:	4770      	bx	lr
  }
  else
  {
    GPIOx->BSRR = (uint32_t)GPIO_Pin << 16u;
 8000e4e:	0409      	lsls	r1, r1, #16
 8000e50:	6101      	str	r1, [r0, #16]
  }
}
 8000e52:	4770      	bx	lr

08000e54 <HAL_GPIO_TogglePin>:

  /* Check the parameters */
  assert_param(IS_GPIO_PIN(GPIO_Pin));

  /* get current Output Data Register value */
  odr = GPIOx->ODR;
 8000e54:	68c3      	ldr	r3, [r0, #12]

  /* Set selected pins that were at low level, and reset ones that were high */
  GPIOx->BSRR = ((odr & GPIO_Pin) << GPIO_NUMBER) | (~odr & GPIO_Pin);
 8000e56:	ea01 0203 	and.w	r2, r1, r3
 8000e5a:	ea21 0103 	bic.w	r1, r1, r3
 8000e5e:	ea41 4102 	orr.w	r1, r1, r2, lsl #16
 8000e62:	6101      	str	r1, [r0, #16]
}
 8000e64:	4770      	bx	lr
	...

08000e68 <RCC_Delay>:
  * @brief  This function provides delay (in milliseconds) based on CPU cycles method.
  * @param  mdelay: specifies the delay time length, in milliseconds.
  * @retval None
  */
static void RCC_Delay(uint32_t mdelay)
{
 8000e68:	b082      	sub	sp, #8
  __IO uint32_t Delay = mdelay * (SystemCoreClock / 8U / 1000U);
 8000e6a:	4b08      	ldr	r3, [pc, #32]	@ (8000e8c <RCC_Delay+0x24>)
 8000e6c:	681b      	ldr	r3, [r3, #0]
 8000e6e:	4a08      	ldr	r2, [pc, #32]	@ (8000e90 <RCC_Delay+0x28>)
 8000e70:	fba2 2303 	umull	r2, r3, r2, r3
 8000e74:	0a5b      	lsrs	r3, r3, #9
 8000e76:	fb00 f303 	mul.w	r3, r0, r3
 8000e7a:	9301      	str	r3, [sp, #4]
  do
  {
    __NOP();
 8000e7c:	bf00      	nop
  }
  while (Delay --);
 8000e7e:	9b01      	ldr	r3, [sp, #4]
 8000e80:	1e5a      	subs	r2, r3, #1
 8000e82:	9201      	str	r2, [sp, #4]
 8000e84:	2b00      	cmp	r3, #0
 8000e86:	d1f9      	bne.n	8000e7c <RCC_Delay+0x14>
}
 8000e88:	b002      	add	sp, #8
 8000e8a:	4770      	bx	lr
 8000e8c:	20000004 	.word	0x20000004
 8000e90:	10624dd3 	.word	0x10624dd3

08000e94 <HAL_RCC_OscConfig>:
  if (RCC_OscInitStruct == NULL)
 8000e94:	2800      	cmp	r0, #0
 8000e96:	f000 81f1 	beq.w	800127c <HAL_RCC_OscConfig+0x3e8>
{
 8000e9a:	b570      	push	{r4, r5, r6, lr}
 8000e9c:	b082      	sub	sp, #8
 8000e9e:	4604      	mov	r4, r0
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSE) == RCC_OSCILLATORTYPE_HSE)
 8000ea0:	6803      	ldr	r3, [r0, #0]
 8000ea2:	f013 0f01 	tst.w	r3, #1
 8000ea6:	d02c      	beq.n	8000f02 <HAL_RCC_OscConfig+0x6e>
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSE)
 8000ea8:	4b99      	ldr	r3, [pc, #612]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000eaa:	685b      	ldr	r3, [r3, #4]
 8000eac:	f003 030c 	and.w	r3, r3, #12
 8000eb0:	2b04      	cmp	r3, #4
 8000eb2:	d01d      	beq.n	8000ef0 <HAL_RCC_OscConfig+0x5c>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSE)))
 8000eb4:	4b96      	ldr	r3, [pc, #600]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000eb6:	685b      	ldr	r3, [r3, #4]
 8000eb8:	f003 030c 	and.w	r3, r3, #12
 8000ebc:	2b08      	cmp	r3, #8
 8000ebe:	d012      	beq.n	8000ee6 <HAL_RCC_OscConfig+0x52>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8000ec0:	6863      	ldr	r3, [r4, #4]
 8000ec2:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 8000ec6:	d041      	beq.n	8000f4c <HAL_RCC_OscConfig+0xb8>
 8000ec8:	2b00      	cmp	r3, #0
 8000eca:	d155      	bne.n	8000f78 <HAL_RCC_OscConfig+0xe4>
 8000ecc:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 8000ed0:	f503 3304 	add.w	r3, r3, #135168	@ 0x21000
 8000ed4:	681a      	ldr	r2, [r3, #0]
 8000ed6:	f422 3280 	bic.w	r2, r2, #65536	@ 0x10000
 8000eda:	601a      	str	r2, [r3, #0]
 8000edc:	681a      	ldr	r2, [r3, #0]
 8000ede:	f422 2280 	bic.w	r2, r2, #262144	@ 0x40000
 8000ee2:	601a      	str	r2, [r3, #0]
 8000ee4:	e037      	b.n	8000f56 <HAL_RCC_OscConfig+0xc2>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSE)))
 8000ee6:	4b8a      	ldr	r3, [pc, #552]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000ee8:	685b      	ldr	r3, [r3, #4]
 8000eea:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8000eee:	d0e7      	beq.n	8000ec0 <HAL_RCC_OscConfig+0x2c>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET) && (RCC_OscInitStruct->HSEState == RCC_HSE_OFF))
 8000ef0:	4b87      	ldr	r3, [pc, #540]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000ef2:	681b      	ldr	r3, [r3, #0]
 8000ef4:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8000ef8:	d003      	beq.n	8000f02 <HAL_RCC_OscConfig+0x6e>
 8000efa:	6863      	ldr	r3, [r4, #4]
 8000efc:	2b00      	cmp	r3, #0
 8000efe:	f000 81bf 	beq.w	8001280 <HAL_RCC_OscConfig+0x3ec>
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSI) == RCC_OSCILLATORTYPE_HSI)
 8000f02:	6823      	ldr	r3, [r4, #0]
 8000f04:	f013 0f02 	tst.w	r3, #2
 8000f08:	d075      	beq.n	8000ff6 <HAL_RCC_OscConfig+0x162>
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSI)
 8000f0a:	4b81      	ldr	r3, [pc, #516]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000f0c:	685b      	ldr	r3, [r3, #4]
 8000f0e:	f013 0f0c 	tst.w	r3, #12
 8000f12:	d05f      	beq.n	8000fd4 <HAL_RCC_OscConfig+0x140>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSI_DIV2)))
 8000f14:	4b7e      	ldr	r3, [pc, #504]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000f16:	685b      	ldr	r3, [r3, #4]
 8000f18:	f003 030c 	and.w	r3, r3, #12
 8000f1c:	2b08      	cmp	r3, #8
 8000f1e:	d054      	beq.n	8000fca <HAL_RCC_OscConfig+0x136>
      if (RCC_OscInitStruct->HSIState != RCC_HSI_OFF)
 8000f20:	6923      	ldr	r3, [r4, #16]
 8000f22:	2b00      	cmp	r3, #0
 8000f24:	f000 808a 	beq.w	800103c <HAL_RCC_OscConfig+0x1a8>
        __HAL_RCC_HSI_ENABLE();
 8000f28:	4b7a      	ldr	r3, [pc, #488]	@ (8001114 <HAL_RCC_OscConfig+0x280>)
 8000f2a:	2201      	movs	r2, #1
 8000f2c:	601a      	str	r2, [r3, #0]
        tickstart = HAL_GetTick();
 8000f2e:	f7ff fc95 	bl	800085c <HAL_GetTick>
 8000f32:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8000f34:	4b76      	ldr	r3, [pc, #472]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000f36:	681b      	ldr	r3, [r3, #0]
 8000f38:	f013 0f02 	tst.w	r3, #2
 8000f3c:	d175      	bne.n	800102a <HAL_RCC_OscConfig+0x196>
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 8000f3e:	f7ff fc8d 	bl	800085c <HAL_GetTick>
 8000f42:	1b40      	subs	r0, r0, r5
 8000f44:	2802      	cmp	r0, #2
 8000f46:	d9f5      	bls.n	8000f34 <HAL_RCC_OscConfig+0xa0>
            return HAL_TIMEOUT;
 8000f48:	2003      	movs	r0, #3
 8000f4a:	e19e      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8000f4c:	4a70      	ldr	r2, [pc, #448]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000f4e:	6813      	ldr	r3, [r2, #0]
 8000f50:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 8000f54:	6013      	str	r3, [r2, #0]
      if (RCC_OscInitStruct->HSEState != RCC_HSE_OFF)
 8000f56:	6863      	ldr	r3, [r4, #4]
 8000f58:	b343      	cbz	r3, 8000fac <HAL_RCC_OscConfig+0x118>
        tickstart = HAL_GetTick();
 8000f5a:	f7ff fc7f 	bl	800085c <HAL_GetTick>
 8000f5e:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8000f60:	4b6b      	ldr	r3, [pc, #428]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000f62:	681b      	ldr	r3, [r3, #0]
 8000f64:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8000f68:	d1cb      	bne.n	8000f02 <HAL_RCC_OscConfig+0x6e>
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 8000f6a:	f7ff fc77 	bl	800085c <HAL_GetTick>
 8000f6e:	1b40      	subs	r0, r0, r5
 8000f70:	2864      	cmp	r0, #100	@ 0x64
 8000f72:	d9f5      	bls.n	8000f60 <HAL_RCC_OscConfig+0xcc>
            return HAL_TIMEOUT;
 8000f74:	2003      	movs	r0, #3
 8000f76:	e188      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8000f78:	f5b3 2fa0 	cmp.w	r3, #327680	@ 0x50000
 8000f7c:	d009      	beq.n	8000f92 <HAL_RCC_OscConfig+0xfe>
 8000f7e:	4b64      	ldr	r3, [pc, #400]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000f80:	681a      	ldr	r2, [r3, #0]
 8000f82:	f422 3280 	bic.w	r2, r2, #65536	@ 0x10000
 8000f86:	601a      	str	r2, [r3, #0]
 8000f88:	681a      	ldr	r2, [r3, #0]
 8000f8a:	f422 2280 	bic.w	r2, r2, #262144	@ 0x40000
 8000f8e:	601a      	str	r2, [r3, #0]
 8000f90:	e7e1      	b.n	8000f56 <HAL_RCC_OscConfig+0xc2>
 8000f92:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 8000f96:	f5a3 333c 	sub.w	r3, r3, #192512	@ 0x2f000
 8000f9a:	681a      	ldr	r2, [r3, #0]
 8000f9c:	f442 2280 	orr.w	r2, r2, #262144	@ 0x40000
 8000fa0:	601a      	str	r2, [r3, #0]
 8000fa2:	681a      	ldr	r2, [r3, #0]
 8000fa4:	f442 3280 	orr.w	r2, r2, #65536	@ 0x10000
 8000fa8:	601a      	str	r2, [r3, #0]
 8000faa:	e7d4      	b.n	8000f56 <HAL_RCC_OscConfig+0xc2>
        tickstart = HAL_GetTick();
 8000fac:	f7ff fc56 	bl	800085c <HAL_GetTick>
 8000fb0:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 8000fb2:	4b57      	ldr	r3, [pc, #348]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000fb4:	681b      	ldr	r3, [r3, #0]
 8000fb6:	f413 3f00 	tst.w	r3, #131072	@ 0x20000
 8000fba:	d0a2      	beq.n	8000f02 <HAL_RCC_OscConfig+0x6e>
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 8000fbc:	f7ff fc4e 	bl	800085c <HAL_GetTick>
 8000fc0:	1b40      	subs	r0, r0, r5
 8000fc2:	2864      	cmp	r0, #100	@ 0x64
 8000fc4:	d9f5      	bls.n	8000fb2 <HAL_RCC_OscConfig+0x11e>
            return HAL_TIMEOUT;
 8000fc6:	2003      	movs	r0, #3
 8000fc8:	e15f      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSI_DIV2)))
 8000fca:	4b51      	ldr	r3, [pc, #324]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000fcc:	685b      	ldr	r3, [r3, #4]
 8000fce:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 8000fd2:	d1a5      	bne.n	8000f20 <HAL_RCC_OscConfig+0x8c>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET) && (RCC_OscInitStruct->HSIState != RCC_HSI_ON))
 8000fd4:	4b4e      	ldr	r3, [pc, #312]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000fd6:	681b      	ldr	r3, [r3, #0]
 8000fd8:	f013 0f02 	tst.w	r3, #2
 8000fdc:	d003      	beq.n	8000fe6 <HAL_RCC_OscConfig+0x152>
 8000fde:	6923      	ldr	r3, [r4, #16]
 8000fe0:	2b01      	cmp	r3, #1
 8000fe2:	f040 814f 	bne.w	8001284 <HAL_RCC_OscConfig+0x3f0>
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 8000fe6:	4a4a      	ldr	r2, [pc, #296]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8000fe8:	6813      	ldr	r3, [r2, #0]
 8000fea:	f023 03f8 	bic.w	r3, r3, #248	@ 0xf8
 8000fee:	6961      	ldr	r1, [r4, #20]
 8000ff0:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 8000ff4:	6013      	str	r3, [r2, #0]
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 8000ff6:	6823      	ldr	r3, [r4, #0]
 8000ff8:	f013 0f08 	tst.w	r3, #8
 8000ffc:	d033      	beq.n	8001066 <HAL_RCC_OscConfig+0x1d2>
    if (RCC_OscInitStruct->LSIState != RCC_LSI_OFF)
 8000ffe:	69a3      	ldr	r3, [r4, #24]
 8001000:	2b00      	cmp	r3, #0
 8001002:	d05c      	beq.n	80010be <HAL_RCC_OscConfig+0x22a>
      __HAL_RCC_LSI_ENABLE();
 8001004:	4b43      	ldr	r3, [pc, #268]	@ (8001114 <HAL_RCC_OscConfig+0x280>)
 8001006:	2201      	movs	r2, #1
 8001008:	f8c3 2480 	str.w	r2, [r3, #1152]	@ 0x480
      tickstart = HAL_GetTick();
 800100c:	f7ff fc26 	bl	800085c <HAL_GetTick>
 8001010:	4605      	mov	r5, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 8001012:	4b3f      	ldr	r3, [pc, #252]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8001014:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8001016:	f013 0f02 	tst.w	r3, #2
 800101a:	d121      	bne.n	8001060 <HAL_RCC_OscConfig+0x1cc>
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 800101c:	f7ff fc1e 	bl	800085c <HAL_GetTick>
 8001020:	1b40      	subs	r0, r0, r5
 8001022:	2802      	cmp	r0, #2
 8001024:	d9f5      	bls.n	8001012 <HAL_RCC_OscConfig+0x17e>
          return HAL_TIMEOUT;
 8001026:	2003      	movs	r0, #3
 8001028:	e12f      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 800102a:	4a39      	ldr	r2, [pc, #228]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 800102c:	6813      	ldr	r3, [r2, #0]
 800102e:	f023 03f8 	bic.w	r3, r3, #248	@ 0xf8
 8001032:	6961      	ldr	r1, [r4, #20]
 8001034:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 8001038:	6013      	str	r3, [r2, #0]
 800103a:	e7dc      	b.n	8000ff6 <HAL_RCC_OscConfig+0x162>
        __HAL_RCC_HSI_DISABLE();
 800103c:	4b35      	ldr	r3, [pc, #212]	@ (8001114 <HAL_RCC_OscConfig+0x280>)
 800103e:	2200      	movs	r2, #0
 8001040:	601a      	str	r2, [r3, #0]
        tickstart = HAL_GetTick();
 8001042:	f7ff fc0b 	bl	800085c <HAL_GetTick>
 8001046:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 8001048:	4b31      	ldr	r3, [pc, #196]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 800104a:	681b      	ldr	r3, [r3, #0]
 800104c:	f013 0f02 	tst.w	r3, #2
 8001050:	d0d1      	beq.n	8000ff6 <HAL_RCC_OscConfig+0x162>
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 8001052:	f7ff fc03 	bl	800085c <HAL_GetTick>
 8001056:	1b40      	subs	r0, r0, r5
 8001058:	2802      	cmp	r0, #2
 800105a:	d9f5      	bls.n	8001048 <HAL_RCC_OscConfig+0x1b4>
            return HAL_TIMEOUT;
 800105c:	2003      	movs	r0, #3
 800105e:	e114      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
      RCC_Delay(1);
 8001060:	2001      	movs	r0, #1
 8001062:	f7ff ff01 	bl	8000e68 <RCC_Delay>
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSE) == RCC_OSCILLATORTYPE_LSE)
 8001066:	6823      	ldr	r3, [r4, #0]
 8001068:	f013 0f04 	tst.w	r3, #4
 800106c:	f000 8096 	beq.w	800119c <HAL_RCC_OscConfig+0x308>
    if (__HAL_RCC_PWR_IS_CLK_DISABLED())
 8001070:	4b27      	ldr	r3, [pc, #156]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 8001072:	69db      	ldr	r3, [r3, #28]
 8001074:	f013 5f80 	tst.w	r3, #268435456	@ 0x10000000
 8001078:	d134      	bne.n	80010e4 <HAL_RCC_OscConfig+0x250>
      __HAL_RCC_PWR_CLK_ENABLE();
 800107a:	4b25      	ldr	r3, [pc, #148]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 800107c:	69da      	ldr	r2, [r3, #28]
 800107e:	f042 5280 	orr.w	r2, r2, #268435456	@ 0x10000000
 8001082:	61da      	str	r2, [r3, #28]
 8001084:	69db      	ldr	r3, [r3, #28]
 8001086:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 800108a:	9301      	str	r3, [sp, #4]
 800108c:	9b01      	ldr	r3, [sp, #4]
      pwrclkchanged = SET;
 800108e:	2501      	movs	r5, #1
    if (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8001090:	4b21      	ldr	r3, [pc, #132]	@ (8001118 <HAL_RCC_OscConfig+0x284>)
 8001092:	681b      	ldr	r3, [r3, #0]
 8001094:	f413 7f80 	tst.w	r3, #256	@ 0x100
 8001098:	d026      	beq.n	80010e8 <HAL_RCC_OscConfig+0x254>
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 800109a:	68e3      	ldr	r3, [r4, #12]
 800109c:	2b01      	cmp	r3, #1
 800109e:	d03d      	beq.n	800111c <HAL_RCC_OscConfig+0x288>
 80010a0:	2b00      	cmp	r3, #0
 80010a2:	d153      	bne.n	800114c <HAL_RCC_OscConfig+0x2b8>
 80010a4:	f103 4380 	add.w	r3, r3, #1073741824	@ 0x40000000
 80010a8:	f503 3304 	add.w	r3, r3, #135168	@ 0x21000
 80010ac:	6a1a      	ldr	r2, [r3, #32]
 80010ae:	f022 0201 	bic.w	r2, r2, #1
 80010b2:	621a      	str	r2, [r3, #32]
 80010b4:	6a1a      	ldr	r2, [r3, #32]
 80010b6:	f022 0204 	bic.w	r2, r2, #4
 80010ba:	621a      	str	r2, [r3, #32]
 80010bc:	e033      	b.n	8001126 <HAL_RCC_OscConfig+0x292>
      __HAL_RCC_LSI_DISABLE();
 80010be:	4b15      	ldr	r3, [pc, #84]	@ (8001114 <HAL_RCC_OscConfig+0x280>)
 80010c0:	2200      	movs	r2, #0
 80010c2:	f8c3 2480 	str.w	r2, [r3, #1152]	@ 0x480
      tickstart = HAL_GetTick();
 80010c6:	f7ff fbc9 	bl	800085c <HAL_GetTick>
 80010ca:	4605      	mov	r5, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 80010cc:	4b10      	ldr	r3, [pc, #64]	@ (8001110 <HAL_RCC_OscConfig+0x27c>)
 80010ce:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 80010d0:	f013 0f02 	tst.w	r3, #2
 80010d4:	d0c7      	beq.n	8001066 <HAL_RCC_OscConfig+0x1d2>
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 80010d6:	f7ff fbc1 	bl	800085c <HAL_GetTick>
 80010da:	1b40      	subs	r0, r0, r5
 80010dc:	2802      	cmp	r0, #2
 80010de:	d9f5      	bls.n	80010cc <HAL_RCC_OscConfig+0x238>
          return HAL_TIMEOUT;
 80010e0:	2003      	movs	r0, #3
 80010e2:	e0d2      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
    FlagStatus       pwrclkchanged = RESET;
 80010e4:	2500      	movs	r5, #0
 80010e6:	e7d3      	b.n	8001090 <HAL_RCC_OscConfig+0x1fc>
      SET_BIT(PWR->CR, PWR_CR_DBP);
 80010e8:	4a0b      	ldr	r2, [pc, #44]	@ (8001118 <HAL_RCC_OscConfig+0x284>)
 80010ea:	6813      	ldr	r3, [r2, #0]
 80010ec:	f443 7380 	orr.w	r3, r3, #256	@ 0x100
 80010f0:	6013      	str	r3, [r2, #0]
      tickstart = HAL_GetTick();
 80010f2:	f7ff fbb3 	bl	800085c <HAL_GetTick>
 80010f6:	4606      	mov	r6, r0
      while (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 80010f8:	4b07      	ldr	r3, [pc, #28]	@ (8001118 <HAL_RCC_OscConfig+0x284>)
 80010fa:	681b      	ldr	r3, [r3, #0]
 80010fc:	f413 7f80 	tst.w	r3, #256	@ 0x100
 8001100:	d1cb      	bne.n	800109a <HAL_RCC_OscConfig+0x206>
        if ((HAL_GetTick() - tickstart) > RCC_DBP_TIMEOUT_VALUE)
 8001102:	f7ff fbab 	bl	800085c <HAL_GetTick>
 8001106:	1b80      	subs	r0, r0, r6
 8001108:	2864      	cmp	r0, #100	@ 0x64
 800110a:	d9f5      	bls.n	80010f8 <HAL_RCC_OscConfig+0x264>
          return HAL_TIMEOUT;
 800110c:	2003      	movs	r0, #3
 800110e:	e0bc      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
 8001110:	40021000 	.word	0x40021000
 8001114:	42420000 	.word	0x42420000
 8001118:	40007000 	.word	0x40007000
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 800111c:	4a5f      	ldr	r2, [pc, #380]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 800111e:	6a13      	ldr	r3, [r2, #32]
 8001120:	f043 0301 	orr.w	r3, r3, #1
 8001124:	6213      	str	r3, [r2, #32]
    if (RCC_OscInitStruct->LSEState != RCC_LSE_OFF)
 8001126:	68e3      	ldr	r3, [r4, #12]
 8001128:	b333      	cbz	r3, 8001178 <HAL_RCC_OscConfig+0x2e4>
      tickstart = HAL_GetTick();
 800112a:	f7ff fb97 	bl	800085c <HAL_GetTick>
 800112e:	4606      	mov	r6, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 8001130:	4b5a      	ldr	r3, [pc, #360]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 8001132:	6a1b      	ldr	r3, [r3, #32]
 8001134:	f013 0f02 	tst.w	r3, #2
 8001138:	d12f      	bne.n	800119a <HAL_RCC_OscConfig+0x306>
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 800113a:	f7ff fb8f 	bl	800085c <HAL_GetTick>
 800113e:	1b80      	subs	r0, r0, r6
 8001140:	f241 3388 	movw	r3, #5000	@ 0x1388
 8001144:	4298      	cmp	r0, r3
 8001146:	d9f3      	bls.n	8001130 <HAL_RCC_OscConfig+0x29c>
          return HAL_TIMEOUT;
 8001148:	2003      	movs	r0, #3
 800114a:	e09e      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 800114c:	2b05      	cmp	r3, #5
 800114e:	d009      	beq.n	8001164 <HAL_RCC_OscConfig+0x2d0>
 8001150:	4b52      	ldr	r3, [pc, #328]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 8001152:	6a1a      	ldr	r2, [r3, #32]
 8001154:	f022 0201 	bic.w	r2, r2, #1
 8001158:	621a      	str	r2, [r3, #32]
 800115a:	6a1a      	ldr	r2, [r3, #32]
 800115c:	f022 0204 	bic.w	r2, r2, #4
 8001160:	621a      	str	r2, [r3, #32]
 8001162:	e7e0      	b.n	8001126 <HAL_RCC_OscConfig+0x292>
 8001164:	4b4d      	ldr	r3, [pc, #308]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 8001166:	6a1a      	ldr	r2, [r3, #32]
 8001168:	f042 0204 	orr.w	r2, r2, #4
 800116c:	621a      	str	r2, [r3, #32]
 800116e:	6a1a      	ldr	r2, [r3, #32]
 8001170:	f042 0201 	orr.w	r2, r2, #1
 8001174:	621a      	str	r2, [r3, #32]
 8001176:	e7d6      	b.n	8001126 <HAL_RCC_OscConfig+0x292>
      tickstart = HAL_GetTick();
 8001178:	f7ff fb70 	bl	800085c <HAL_GetTick>
 800117c:	4606      	mov	r6, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 800117e:	4b47      	ldr	r3, [pc, #284]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 8001180:	6a1b      	ldr	r3, [r3, #32]
 8001182:	f013 0f02 	tst.w	r3, #2
 8001186:	d008      	beq.n	800119a <HAL_RCC_OscConfig+0x306>
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 8001188:	f7ff fb68 	bl	800085c <HAL_GetTick>
 800118c:	1b80      	subs	r0, r0, r6
 800118e:	f241 3388 	movw	r3, #5000	@ 0x1388
 8001192:	4298      	cmp	r0, r3
 8001194:	d9f3      	bls.n	800117e <HAL_RCC_OscConfig+0x2ea>
          return HAL_TIMEOUT;
 8001196:	2003      	movs	r0, #3
 8001198:	e077      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
    if (pwrclkchanged == SET)
 800119a:	b9e5      	cbnz	r5, 80011d6 <HAL_RCC_OscConfig+0x342>
  if ((RCC_OscInitStruct->PLL.PLLState) != RCC_PLL_NONE)
 800119c:	69e3      	ldr	r3, [r4, #28]
 800119e:	2b00      	cmp	r3, #0
 80011a0:	d072      	beq.n	8001288 <HAL_RCC_OscConfig+0x3f4>
    if (__HAL_RCC_GET_SYSCLK_SOURCE() != RCC_SYSCLKSOURCE_STATUS_PLLCLK)
 80011a2:	4a3e      	ldr	r2, [pc, #248]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 80011a4:	6852      	ldr	r2, [r2, #4]
 80011a6:	f002 020c 	and.w	r2, r2, #12
 80011aa:	2a08      	cmp	r2, #8
 80011ac:	d056      	beq.n	800125c <HAL_RCC_OscConfig+0x3c8>
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_ON)
 80011ae:	2b02      	cmp	r3, #2
 80011b0:	d017      	beq.n	80011e2 <HAL_RCC_OscConfig+0x34e>
        __HAL_RCC_PLL_DISABLE();
 80011b2:	4b3b      	ldr	r3, [pc, #236]	@ (80012a0 <HAL_RCC_OscConfig+0x40c>)
 80011b4:	2200      	movs	r2, #0
 80011b6:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 80011b8:	f7ff fb50 	bl	800085c <HAL_GetTick>
 80011bc:	4604      	mov	r4, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 80011be:	4b37      	ldr	r3, [pc, #220]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 80011c0:	681b      	ldr	r3, [r3, #0]
 80011c2:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 80011c6:	d047      	beq.n	8001258 <HAL_RCC_OscConfig+0x3c4>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 80011c8:	f7ff fb48 	bl	800085c <HAL_GetTick>
 80011cc:	1b00      	subs	r0, r0, r4
 80011ce:	2802      	cmp	r0, #2
 80011d0:	d9f5      	bls.n	80011be <HAL_RCC_OscConfig+0x32a>
            return HAL_TIMEOUT;
 80011d2:	2003      	movs	r0, #3
 80011d4:	e059      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
      __HAL_RCC_PWR_CLK_DISABLE();
 80011d6:	4a31      	ldr	r2, [pc, #196]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 80011d8:	69d3      	ldr	r3, [r2, #28]
 80011da:	f023 5380 	bic.w	r3, r3, #268435456	@ 0x10000000
 80011de:	61d3      	str	r3, [r2, #28]
 80011e0:	e7dc      	b.n	800119c <HAL_RCC_OscConfig+0x308>
        __HAL_RCC_PLL_DISABLE();
 80011e2:	4b2f      	ldr	r3, [pc, #188]	@ (80012a0 <HAL_RCC_OscConfig+0x40c>)
 80011e4:	2200      	movs	r2, #0
 80011e6:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 80011e8:	f7ff fb38 	bl	800085c <HAL_GetTick>
 80011ec:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 80011ee:	4b2b      	ldr	r3, [pc, #172]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 80011f0:	681b      	ldr	r3, [r3, #0]
 80011f2:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 80011f6:	d006      	beq.n	8001206 <HAL_RCC_OscConfig+0x372>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 80011f8:	f7ff fb30 	bl	800085c <HAL_GetTick>
 80011fc:	1b40      	subs	r0, r0, r5
 80011fe:	2802      	cmp	r0, #2
 8001200:	d9f5      	bls.n	80011ee <HAL_RCC_OscConfig+0x35a>
            return HAL_TIMEOUT;
 8001202:	2003      	movs	r0, #3
 8001204:	e041      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
        if (RCC_OscInitStruct->PLL.PLLSource == RCC_PLLSOURCE_HSE)
 8001206:	6a23      	ldr	r3, [r4, #32]
 8001208:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 800120c:	d01a      	beq.n	8001244 <HAL_RCC_OscConfig+0x3b0>
        __HAL_RCC_PLL_CONFIG(RCC_OscInitStruct->PLL.PLLSource,
 800120e:	4923      	ldr	r1, [pc, #140]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 8001210:	684b      	ldr	r3, [r1, #4]
 8001212:	f423 1374 	bic.w	r3, r3, #3997696	@ 0x3d0000
 8001216:	6a22      	ldr	r2, [r4, #32]
 8001218:	6a60      	ldr	r0, [r4, #36]	@ 0x24
 800121a:	4302      	orrs	r2, r0
 800121c:	4313      	orrs	r3, r2
 800121e:	604b      	str	r3, [r1, #4]
        __HAL_RCC_PLL_ENABLE();
 8001220:	4b1f      	ldr	r3, [pc, #124]	@ (80012a0 <HAL_RCC_OscConfig+0x40c>)
 8001222:	2201      	movs	r2, #1
 8001224:	661a      	str	r2, [r3, #96]	@ 0x60
        tickstart = HAL_GetTick();
 8001226:	f7ff fb19 	bl	800085c <HAL_GetTick>
 800122a:	4604      	mov	r4, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  == RESET)
 800122c:	4b1b      	ldr	r3, [pc, #108]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 800122e:	681b      	ldr	r3, [r3, #0]
 8001230:	f013 7f00 	tst.w	r3, #33554432	@ 0x2000000
 8001234:	d10e      	bne.n	8001254 <HAL_RCC_OscConfig+0x3c0>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8001236:	f7ff fb11 	bl	800085c <HAL_GetTick>
 800123a:	1b00      	subs	r0, r0, r4
 800123c:	2802      	cmp	r0, #2
 800123e:	d9f5      	bls.n	800122c <HAL_RCC_OscConfig+0x398>
            return HAL_TIMEOUT;
 8001240:	2003      	movs	r0, #3
 8001242:	e022      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
          __HAL_RCC_HSE_PREDIV_CONFIG(RCC_OscInitStruct->HSEPredivValue);
 8001244:	4a15      	ldr	r2, [pc, #84]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 8001246:	6853      	ldr	r3, [r2, #4]
 8001248:	f423 3300 	bic.w	r3, r3, #131072	@ 0x20000
 800124c:	68a1      	ldr	r1, [r4, #8]
 800124e:	430b      	orrs	r3, r1
 8001250:	6053      	str	r3, [r2, #4]
 8001252:	e7dc      	b.n	800120e <HAL_RCC_OscConfig+0x37a>
  return HAL_OK;
 8001254:	2000      	movs	r0, #0
 8001256:	e018      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
 8001258:	2000      	movs	r0, #0
 800125a:	e016      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF)
 800125c:	2b01      	cmp	r3, #1
 800125e:	d016      	beq.n	800128e <HAL_RCC_OscConfig+0x3fa>
        pll_config = RCC->CFGR;
 8001260:	4b0e      	ldr	r3, [pc, #56]	@ (800129c <HAL_RCC_OscConfig+0x408>)
 8001262:	685b      	ldr	r3, [r3, #4]
        if ((READ_BIT(pll_config, RCC_CFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 8001264:	f403 3180 	and.w	r1, r3, #65536	@ 0x10000
 8001268:	6a22      	ldr	r2, [r4, #32]
 800126a:	4291      	cmp	r1, r2
 800126c:	d111      	bne.n	8001292 <HAL_RCC_OscConfig+0x3fe>
            (READ_BIT(pll_config, RCC_CFGR_PLLMULL) != RCC_OscInitStruct->PLL.PLLMUL))
 800126e:	f403 1370 	and.w	r3, r3, #3932160	@ 0x3c0000
 8001272:	6a62      	ldr	r2, [r4, #36]	@ 0x24
        if ((READ_BIT(pll_config, RCC_CFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 8001274:	4293      	cmp	r3, r2
 8001276:	d10e      	bne.n	8001296 <HAL_RCC_OscConfig+0x402>
  return HAL_OK;
 8001278:	2000      	movs	r0, #0
 800127a:	e006      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
    return HAL_ERROR;
 800127c:	2001      	movs	r0, #1
}
 800127e:	4770      	bx	lr
        return HAL_ERROR;
 8001280:	2001      	movs	r0, #1
 8001282:	e002      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
        return HAL_ERROR;
 8001284:	2001      	movs	r0, #1
 8001286:	e000      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
  return HAL_OK;
 8001288:	2000      	movs	r0, #0
}
 800128a:	b002      	add	sp, #8
 800128c:	bd70      	pop	{r4, r5, r6, pc}
        return HAL_ERROR;
 800128e:	2001      	movs	r0, #1
 8001290:	e7fb      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
          return HAL_ERROR;
 8001292:	2001      	movs	r0, #1
 8001294:	e7f9      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
 8001296:	2001      	movs	r0, #1
 8001298:	e7f7      	b.n	800128a <HAL_RCC_OscConfig+0x3f6>
 800129a:	bf00      	nop
 800129c:	40021000 	.word	0x40021000
 80012a0:	42420000 	.word	0x42420000

080012a4 <HAL_RCC_GetSysClockFreq>:
  tmpreg = RCC->CFGR;
 80012a4:	4b0f      	ldr	r3, [pc, #60]	@ (80012e4 <HAL_RCC_GetSysClockFreq+0x40>)
 80012a6:	685b      	ldr	r3, [r3, #4]
  switch (tmpreg & RCC_CFGR_SWS)
 80012a8:	f003 020c 	and.w	r2, r3, #12
 80012ac:	2a08      	cmp	r2, #8
 80012ae:	d001      	beq.n	80012b4 <HAL_RCC_GetSysClockFreq+0x10>
      sysclockfreq = HSE_VALUE;
 80012b0:	480d      	ldr	r0, [pc, #52]	@ (80012e8 <HAL_RCC_GetSysClockFreq+0x44>)
}
 80012b2:	4770      	bx	lr
      pllmul = aPLLMULFactorTable[(uint32_t)(tmpreg & RCC_CFGR_PLLMULL) >> RCC_CFGR_PLLMULL_Pos];
 80012b4:	f3c3 4283 	ubfx	r2, r3, #18, #4
 80012b8:	490c      	ldr	r1, [pc, #48]	@ (80012ec <HAL_RCC_GetSysClockFreq+0x48>)
 80012ba:	5c88      	ldrb	r0, [r1, r2]
      if ((tmpreg & RCC_CFGR_PLLSRC) != RCC_PLLSOURCE_HSI_DIV2)
 80012bc:	f413 3f80 	tst.w	r3, #65536	@ 0x10000
 80012c0:	d00b      	beq.n	80012da <HAL_RCC_GetSysClockFreq+0x36>
        prediv = aPredivFactorTable[(uint32_t)(RCC->CFGR & RCC_CFGR_PLLXTPRE) >> RCC_CFGR_PLLXTPRE_Pos];
 80012c2:	4b08      	ldr	r3, [pc, #32]	@ (80012e4 <HAL_RCC_GetSysClockFreq+0x40>)
 80012c4:	685b      	ldr	r3, [r3, #4]
 80012c6:	f3c3 4340 	ubfx	r3, r3, #17, #1
 80012ca:	4a09      	ldr	r2, [pc, #36]	@ (80012f0 <HAL_RCC_GetSysClockFreq+0x4c>)
 80012cc:	5cd3      	ldrb	r3, [r2, r3]
        pllclk = (uint32_t)((HSE_VALUE  * pllmul) / prediv);
 80012ce:	4a06      	ldr	r2, [pc, #24]	@ (80012e8 <HAL_RCC_GetSysClockFreq+0x44>)
 80012d0:	fb02 f000 	mul.w	r0, r2, r0
 80012d4:	fbb0 f0f3 	udiv	r0, r0, r3
 80012d8:	4770      	bx	lr
        pllclk = (uint32_t)((HSI_VALUE >> 1) * pllmul);
 80012da:	4b06      	ldr	r3, [pc, #24]	@ (80012f4 <HAL_RCC_GetSysClockFreq+0x50>)
 80012dc:	fb03 f000 	mul.w	r0, r3, r0
 80012e0:	4770      	bx	lr
 80012e2:	bf00      	nop
 80012e4:	40021000 	.word	0x40021000
 80012e8:	007a1200 	.word	0x007a1200
 80012ec:	08002998 	.word	0x08002998
 80012f0:	08002994 	.word	0x08002994
 80012f4:	003d0900 	.word	0x003d0900

080012f8 <HAL_RCC_ClockConfig>:
  if (RCC_ClkInitStruct == NULL)
 80012f8:	2800      	cmp	r0, #0
 80012fa:	f000 80a0 	beq.w	800143e <HAL_RCC_ClockConfig+0x146>
{
 80012fe:	b570      	push	{r4, r5, r6, lr}
 8001300:	460d      	mov	r5, r1
 8001302:	4604      	mov	r4, r0
  if (FLatency > __HAL_FLASH_GET_LATENCY())
 8001304:	4b52      	ldr	r3, [pc, #328]	@ (8001450 <HAL_RCC_ClockConfig+0x158>)
 8001306:	681b      	ldr	r3, [r3, #0]
 8001308:	f003 0307 	and.w	r3, r3, #7
 800130c:	428b      	cmp	r3, r1
 800130e:	d20b      	bcs.n	8001328 <HAL_RCC_ClockConfig+0x30>
    __HAL_FLASH_SET_LATENCY(FLatency);
 8001310:	4a4f      	ldr	r2, [pc, #316]	@ (8001450 <HAL_RCC_ClockConfig+0x158>)
 8001312:	6813      	ldr	r3, [r2, #0]
 8001314:	f023 0307 	bic.w	r3, r3, #7
 8001318:	430b      	orrs	r3, r1
 800131a:	6013      	str	r3, [r2, #0]
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 800131c:	6813      	ldr	r3, [r2, #0]
 800131e:	f003 0307 	and.w	r3, r3, #7
 8001322:	428b      	cmp	r3, r1
 8001324:	f040 808d 	bne.w	8001442 <HAL_RCC_ClockConfig+0x14a>
if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_HCLK) == RCC_CLOCKTYPE_HCLK)
 8001328:	6823      	ldr	r3, [r4, #0]
 800132a:	f013 0f02 	tst.w	r3, #2
 800132e:	d017      	beq.n	8001360 <HAL_RCC_ClockConfig+0x68>
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 8001330:	f013 0f04 	tst.w	r3, #4
 8001334:	d004      	beq.n	8001340 <HAL_RCC_ClockConfig+0x48>
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_HCLK_DIV16);
 8001336:	4a47      	ldr	r2, [pc, #284]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 8001338:	6853      	ldr	r3, [r2, #4]
 800133a:	f443 63e0 	orr.w	r3, r3, #1792	@ 0x700
 800133e:	6053      	str	r3, [r2, #4]
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 8001340:	6823      	ldr	r3, [r4, #0]
 8001342:	f013 0f08 	tst.w	r3, #8
 8001346:	d004      	beq.n	8001352 <HAL_RCC_ClockConfig+0x5a>
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, (RCC_HCLK_DIV16 << 3));
 8001348:	4a42      	ldr	r2, [pc, #264]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 800134a:	6853      	ldr	r3, [r2, #4]
 800134c:	f443 5360 	orr.w	r3, r3, #14336	@ 0x3800
 8001350:	6053      	str	r3, [r2, #4]
    MODIFY_REG(RCC->CFGR, RCC_CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
 8001352:	4a40      	ldr	r2, [pc, #256]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 8001354:	6853      	ldr	r3, [r2, #4]
 8001356:	f023 03f0 	bic.w	r3, r3, #240	@ 0xf0
 800135a:	68a1      	ldr	r1, [r4, #8]
 800135c:	430b      	orrs	r3, r1
 800135e:	6053      	str	r3, [r2, #4]
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_SYSCLK) == RCC_CLOCKTYPE_SYSCLK)
 8001360:	6823      	ldr	r3, [r4, #0]
 8001362:	f013 0f01 	tst.w	r3, #1
 8001366:	d031      	beq.n	80013cc <HAL_RCC_ClockConfig+0xd4>
    if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_HSE)
 8001368:	6863      	ldr	r3, [r4, #4]
 800136a:	2b01      	cmp	r3, #1
 800136c:	d020      	beq.n	80013b0 <HAL_RCC_ClockConfig+0xb8>
    else if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_PLLCLK)
 800136e:	2b02      	cmp	r3, #2
 8001370:	d025      	beq.n	80013be <HAL_RCC_ClockConfig+0xc6>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8001372:	4a38      	ldr	r2, [pc, #224]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 8001374:	6812      	ldr	r2, [r2, #0]
 8001376:	f012 0f02 	tst.w	r2, #2
 800137a:	d064      	beq.n	8001446 <HAL_RCC_ClockConfig+0x14e>
    __HAL_RCC_SYSCLK_CONFIG(RCC_ClkInitStruct->SYSCLKSource);
 800137c:	4935      	ldr	r1, [pc, #212]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 800137e:	684a      	ldr	r2, [r1, #4]
 8001380:	f022 0203 	bic.w	r2, r2, #3
 8001384:	4313      	orrs	r3, r2
 8001386:	604b      	str	r3, [r1, #4]
    tickstart = HAL_GetTick();
 8001388:	f7ff fa68 	bl	800085c <HAL_GetTick>
 800138c:	4606      	mov	r6, r0
    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 800138e:	4b31      	ldr	r3, [pc, #196]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 8001390:	685b      	ldr	r3, [r3, #4]
 8001392:	f003 030c 	and.w	r3, r3, #12
 8001396:	6862      	ldr	r2, [r4, #4]
 8001398:	ebb3 0f82 	cmp.w	r3, r2, lsl #2
 800139c:	d016      	beq.n	80013cc <HAL_RCC_ClockConfig+0xd4>
      if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
 800139e:	f7ff fa5d 	bl	800085c <HAL_GetTick>
 80013a2:	1b80      	subs	r0, r0, r6
 80013a4:	f241 3388 	movw	r3, #5000	@ 0x1388
 80013a8:	4298      	cmp	r0, r3
 80013aa:	d9f0      	bls.n	800138e <HAL_RCC_ClockConfig+0x96>
        return HAL_TIMEOUT;
 80013ac:	2003      	movs	r0, #3
 80013ae:	e045      	b.n	800143c <HAL_RCC_ClockConfig+0x144>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 80013b0:	4a28      	ldr	r2, [pc, #160]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 80013b2:	6812      	ldr	r2, [r2, #0]
 80013b4:	f412 3f00 	tst.w	r2, #131072	@ 0x20000
 80013b8:	d1e0      	bne.n	800137c <HAL_RCC_ClockConfig+0x84>
        return HAL_ERROR;
 80013ba:	2001      	movs	r0, #1
 80013bc:	e03e      	b.n	800143c <HAL_RCC_ClockConfig+0x144>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 80013be:	4a25      	ldr	r2, [pc, #148]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 80013c0:	6812      	ldr	r2, [r2, #0]
 80013c2:	f012 7f00 	tst.w	r2, #33554432	@ 0x2000000
 80013c6:	d1d9      	bne.n	800137c <HAL_RCC_ClockConfig+0x84>
        return HAL_ERROR;
 80013c8:	2001      	movs	r0, #1
 80013ca:	e037      	b.n	800143c <HAL_RCC_ClockConfig+0x144>
  if (FLatency < __HAL_FLASH_GET_LATENCY())
 80013cc:	4b20      	ldr	r3, [pc, #128]	@ (8001450 <HAL_RCC_ClockConfig+0x158>)
 80013ce:	681b      	ldr	r3, [r3, #0]
 80013d0:	f003 0307 	and.w	r3, r3, #7
 80013d4:	42ab      	cmp	r3, r5
 80013d6:	d90a      	bls.n	80013ee <HAL_RCC_ClockConfig+0xf6>
    __HAL_FLASH_SET_LATENCY(FLatency);
 80013d8:	4a1d      	ldr	r2, [pc, #116]	@ (8001450 <HAL_RCC_ClockConfig+0x158>)
 80013da:	6813      	ldr	r3, [r2, #0]
 80013dc:	f023 0307 	bic.w	r3, r3, #7
 80013e0:	432b      	orrs	r3, r5
 80013e2:	6013      	str	r3, [r2, #0]
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 80013e4:	6813      	ldr	r3, [r2, #0]
 80013e6:	f003 0307 	and.w	r3, r3, #7
 80013ea:	42ab      	cmp	r3, r5
 80013ec:	d12d      	bne.n	800144a <HAL_RCC_ClockConfig+0x152>
if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 80013ee:	6823      	ldr	r3, [r4, #0]
 80013f0:	f013 0f04 	tst.w	r3, #4
 80013f4:	d006      	beq.n	8001404 <HAL_RCC_ClockConfig+0x10c>
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_ClkInitStruct->APB1CLKDivider);
 80013f6:	4a17      	ldr	r2, [pc, #92]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 80013f8:	6853      	ldr	r3, [r2, #4]
 80013fa:	f423 63e0 	bic.w	r3, r3, #1792	@ 0x700
 80013fe:	68e1      	ldr	r1, [r4, #12]
 8001400:	430b      	orrs	r3, r1
 8001402:	6053      	str	r3, [r2, #4]
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 8001404:	6823      	ldr	r3, [r4, #0]
 8001406:	f013 0f08 	tst.w	r3, #8
 800140a:	d007      	beq.n	800141c <HAL_RCC_ClockConfig+0x124>
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, ((RCC_ClkInitStruct->APB2CLKDivider) << 3));
 800140c:	4a11      	ldr	r2, [pc, #68]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 800140e:	6853      	ldr	r3, [r2, #4]
 8001410:	f423 5360 	bic.w	r3, r3, #14336	@ 0x3800
 8001414:	6921      	ldr	r1, [r4, #16]
 8001416:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 800141a:	6053      	str	r3, [r2, #4]
  SystemCoreClock = HAL_RCC_GetSysClockFreq() >> AHBPrescTable[(RCC->CFGR & RCC_CFGR_HPRE) >> RCC_CFGR_HPRE_Pos];
 800141c:	f7ff ff42 	bl	80012a4 <HAL_RCC_GetSysClockFreq>
 8001420:	4b0c      	ldr	r3, [pc, #48]	@ (8001454 <HAL_RCC_ClockConfig+0x15c>)
 8001422:	685b      	ldr	r3, [r3, #4]
 8001424:	f3c3 1303 	ubfx	r3, r3, #4, #4
 8001428:	4a0b      	ldr	r2, [pc, #44]	@ (8001458 <HAL_RCC_ClockConfig+0x160>)
 800142a:	5cd3      	ldrb	r3, [r2, r3]
 800142c:	40d8      	lsrs	r0, r3
 800142e:	4b0b      	ldr	r3, [pc, #44]	@ (800145c <HAL_RCC_ClockConfig+0x164>)
 8001430:	6018      	str	r0, [r3, #0]
  HAL_InitTick(uwTickPrio);
 8001432:	4b0b      	ldr	r3, [pc, #44]	@ (8001460 <HAL_RCC_ClockConfig+0x168>)
 8001434:	6818      	ldr	r0, [r3, #0]
 8001436:	f7ff f9cd 	bl	80007d4 <HAL_InitTick>
  return HAL_OK;
 800143a:	2000      	movs	r0, #0
}
 800143c:	bd70      	pop	{r4, r5, r6, pc}
    return HAL_ERROR;
 800143e:	2001      	movs	r0, #1
}
 8001440:	4770      	bx	lr
    return HAL_ERROR;
 8001442:	2001      	movs	r0, #1
 8001444:	e7fa      	b.n	800143c <HAL_RCC_ClockConfig+0x144>
        return HAL_ERROR;
 8001446:	2001      	movs	r0, #1
 8001448:	e7f8      	b.n	800143c <HAL_RCC_ClockConfig+0x144>
    return HAL_ERROR;
 800144a:	2001      	movs	r0, #1
 800144c:	e7f6      	b.n	800143c <HAL_RCC_ClockConfig+0x144>
 800144e:	bf00      	nop
 8001450:	******** 	.word	0x********
 8001454:	40021000 	.word	0x40021000
 8001458:	08002984 	.word	0x08002984
 800145c:	20000004 	.word	0x20000004
 8001460:	2000000c 	.word	0x2000000c

08001464 <HAL_RCC_GetHCLKFreq>:
}
 8001464:	4b01      	ldr	r3, [pc, #4]	@ (800146c <HAL_RCC_GetHCLKFreq+0x8>)
 8001466:	6818      	ldr	r0, [r3, #0]
 8001468:	4770      	bx	lr
 800146a:	bf00      	nop
 800146c:	20000004 	.word	0x20000004

08001470 <HAL_RCC_GetPCLK1Freq>:
{
 8001470:	b508      	push	{r3, lr}
  return (HAL_RCC_GetHCLKFreq() >> APBPrescTable[(RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos]);
 8001472:	f7ff fff7 	bl	8001464 <HAL_RCC_GetHCLKFreq>
 8001476:	4b04      	ldr	r3, [pc, #16]	@ (8001488 <HAL_RCC_GetPCLK1Freq+0x18>)
 8001478:	685b      	ldr	r3, [r3, #4]
 800147a:	f3c3 2302 	ubfx	r3, r3, #8, #3
 800147e:	4a03      	ldr	r2, [pc, #12]	@ (800148c <HAL_RCC_GetPCLK1Freq+0x1c>)
 8001480:	5cd3      	ldrb	r3, [r2, r3]
}
 8001482:	40d8      	lsrs	r0, r3
 8001484:	bd08      	pop	{r3, pc}
 8001486:	bf00      	nop
 8001488:	40021000 	.word	0x40021000
 800148c:	0800297c 	.word	0x0800297c

08001490 <HAL_RCC_GetPCLK2Freq>:
{
 8001490:	b508      	push	{r3, lr}
  return (HAL_RCC_GetHCLKFreq() >> APBPrescTable[(RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos]);
 8001492:	f7ff ffe7 	bl	8001464 <HAL_RCC_GetHCLKFreq>
 8001496:	4b04      	ldr	r3, [pc, #16]	@ (80014a8 <HAL_RCC_GetPCLK2Freq+0x18>)
 8001498:	685b      	ldr	r3, [r3, #4]
 800149a:	f3c3 23c2 	ubfx	r3, r3, #11, #3
 800149e:	4a03      	ldr	r2, [pc, #12]	@ (80014ac <HAL_RCC_GetPCLK2Freq+0x1c>)
 80014a0:	5cd3      	ldrb	r3, [r2, r3]
}
 80014a2:	40d8      	lsrs	r0, r3
 80014a4:	bd08      	pop	{r3, pc}
 80014a6:	bf00      	nop
 80014a8:	40021000 	.word	0x40021000
 80014ac:	0800297c 	.word	0x0800297c

080014b0 <UART_EndRxTransfer>:
  * @retval None
  */
static void UART_EndRxTransfer(UART_HandleTypeDef *huart)
{
  /* Disable RXNE, PE and ERR (Frame error, noise error, overrun error) interrupts */
  ATOMIC_CLEAR_BIT(huart->Instance->CR1, (USART_CR1_RXNEIE | USART_CR1_PEIE));
 80014b0:	6802      	ldr	r2, [r0, #0]
 */
__STATIC_FORCEINLINE uint32_t __LDREXW(volatile uint32_t *addr)
{
    uint32_t result;

   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 80014b2:	f102 030c 	add.w	r3, r2, #12
 80014b6:	e853 3f00 	ldrex	r3, [r3]
 80014ba:	f423 7390 	bic.w	r3, r3, #288	@ 0x120
 */
__STATIC_FORCEINLINE uint32_t __STREXW(uint32_t value, volatile uint32_t *addr)
{
   uint32_t result;

   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 80014be:	320c      	adds	r2, #12
 80014c0:	e842 3100 	strex	r1, r3, [r2]
 80014c4:	2900      	cmp	r1, #0
 80014c6:	d1f3      	bne.n	80014b0 <UART_EndRxTransfer>
  ATOMIC_CLEAR_BIT(huart->Instance->CR3, USART_CR3_EIE);
 80014c8:	6802      	ldr	r2, [r0, #0]
   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 80014ca:	f102 0314 	add.w	r3, r2, #20
 80014ce:	e853 3f00 	ldrex	r3, [r3]
 80014d2:	f023 0301 	bic.w	r3, r3, #1
   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 80014d6:	3214      	adds	r2, #20
 80014d8:	e842 3100 	strex	r1, r3, [r2]
 80014dc:	2900      	cmp	r1, #0
 80014de:	d1f3      	bne.n	80014c8 <UART_EndRxTransfer+0x18>

  /* In case of reception waiting for IDLE event, disable also the IDLE IE interrupt source */
  if (huart->ReceptionType == HAL_UART_RECEPTION_TOIDLE)
 80014e0:	6b03      	ldr	r3, [r0, #48]	@ 0x30
 80014e2:	2b01      	cmp	r3, #1
 80014e4:	d005      	beq.n	80014f2 <UART_EndRxTransfer+0x42>
  {
    ATOMIC_CLEAR_BIT(huart->Instance->CR1, USART_CR1_IDLEIE);
  }

  /* At end of Rx process, restore huart->RxState to Ready */
  huart->RxState = HAL_UART_STATE_READY;
 80014e6:	2320      	movs	r3, #32
 80014e8:	f880 3042 	strb.w	r3, [r0, #66]	@ 0x42
  huart->ReceptionType = HAL_UART_RECEPTION_STANDARD;
 80014ec:	2300      	movs	r3, #0
 80014ee:	6303      	str	r3, [r0, #48]	@ 0x30
}
 80014f0:	4770      	bx	lr
    ATOMIC_CLEAR_BIT(huart->Instance->CR1, USART_CR1_IDLEIE);
 80014f2:	6802      	ldr	r2, [r0, #0]
   __ASM volatile ("ldrex %0, %1" : "=r" (result) : "Q" (*addr) );
 80014f4:	f102 030c 	add.w	r3, r2, #12
 80014f8:	e853 3f00 	ldrex	r3, [r3]
 80014fc:	f023 0310 	bic.w	r3, r3, #16
   __ASM volatile ("strex %0, %2, %1" : "=&r" (result), "=Q" (*addr) : "r" (value) );
 8001500:	320c      	adds	r2, #12
 8001502:	e842 3100 	strex	r1, r3, [r2]
 8001506:	2900      	cmp	r1, #0
 8001508:	d1f3      	bne.n	80014f2 <UART_EndRxTransfer+0x42>
 800150a:	e7ec      	b.n	80014e6 <UART_EndRxTransfer+0x36>

0800150c <UART_SetConfig>:
  * @param  huart  Pointer to a UART_HandleTypeDef structure that contains
  *                the configuration information for the specified UART module.
  * @retval None
  */
static void UART_SetConfig(UART_HandleTypeDef *huart)
{
 800150c:	b510      	push	{r4, lr}
 800150e:	4604      	mov	r4, r0
  assert_param(IS_UART_MODE(huart->Init.Mode));

  /*-------------------------- USART CR2 Configuration -----------------------*/
  /* Configure the UART Stop Bits: Set STOP[13:12] bits
     according to huart->Init.StopBits value */
  MODIFY_REG(huart->Instance->CR2, USART_CR2_STOP, huart->Init.StopBits);
 8001510:	6802      	ldr	r2, [r0, #0]
 8001512:	6913      	ldr	r3, [r2, #16]
 8001514:	f423 5340 	bic.w	r3, r3, #12288	@ 0x3000
 8001518:	68c1      	ldr	r1, [r0, #12]
 800151a:	430b      	orrs	r3, r1
 800151c:	6113      	str	r3, [r2, #16]
  tmpreg = (uint32_t)huart->Init.WordLength | huart->Init.Parity | huart->Init.Mode | huart->Init.OverSampling;
  MODIFY_REG(huart->Instance->CR1,
             (uint32_t)(USART_CR1_M | USART_CR1_PCE | USART_CR1_PS | USART_CR1_TE | USART_CR1_RE | USART_CR1_OVER8),
             tmpreg);
#else
  tmpreg = (uint32_t)huart->Init.WordLength | huart->Init.Parity | huart->Init.Mode;
 800151e:	6883      	ldr	r3, [r0, #8]
 8001520:	6902      	ldr	r2, [r0, #16]
 8001522:	4313      	orrs	r3, r2
 8001524:	6942      	ldr	r2, [r0, #20]
 8001526:	431a      	orrs	r2, r3
  MODIFY_REG(huart->Instance->CR1,
 8001528:	6801      	ldr	r1, [r0, #0]
 800152a:	68cb      	ldr	r3, [r1, #12]
 800152c:	f423 53b0 	bic.w	r3, r3, #5632	@ 0x1600
 8001530:	f023 030c 	bic.w	r3, r3, #12
 8001534:	4313      	orrs	r3, r2
 8001536:	60cb      	str	r3, [r1, #12]
             tmpreg);
#endif /* USART_CR1_OVER8 */

  /*-------------------------- USART CR3 Configuration -----------------------*/
  /* Configure the UART HFC: Set CTSE and RTSE bits according to huart->Init.HwFlowCtl value */
  MODIFY_REG(huart->Instance->CR3, (USART_CR3_RTSE | USART_CR3_CTSE), huart->Init.HwFlowCtl);
 8001538:	6802      	ldr	r2, [r0, #0]
 800153a:	6953      	ldr	r3, [r2, #20]
 800153c:	f423 7340 	bic.w	r3, r3, #768	@ 0x300
 8001540:	6981      	ldr	r1, [r0, #24]
 8001542:	430b      	orrs	r3, r1
 8001544:	6153      	str	r3, [r2, #20]


  if(huart->Instance == USART1)
 8001546:	6802      	ldr	r2, [r0, #0]
 8001548:	4b13      	ldr	r3, [pc, #76]	@ (8001598 <UART_SetConfig+0x8c>)
 800154a:	429a      	cmp	r2, r3
 800154c:	d020      	beq.n	8001590 <UART_SetConfig+0x84>
  {
    pclk = HAL_RCC_GetPCLK2Freq();
  }
  else
  {
    pclk = HAL_RCC_GetPCLK1Freq();
 800154e:	f7ff ff8f 	bl	8001470 <HAL_RCC_GetPCLK1Freq>
 8001552:	4602      	mov	r2, r0
  else
  {
    huart->Instance->BRR = UART_BRR_SAMPLING16(pclk, huart->Init.BaudRate);
  }
#else
  huart->Instance->BRR = UART_BRR_SAMPLING16(pclk, huart->Init.BaudRate);
 8001554:	eb02 0282 	add.w	r2, r2, r2, lsl #2
 8001558:	eb02 0282 	add.w	r2, r2, r2, lsl #2
 800155c:	6863      	ldr	r3, [r4, #4]
 800155e:	009b      	lsls	r3, r3, #2
 8001560:	fbb2 f2f3 	udiv	r2, r2, r3
 8001564:	480d      	ldr	r0, [pc, #52]	@ (800159c <UART_SetConfig+0x90>)
 8001566:	fba0 3102 	umull	r3, r1, r0, r2
 800156a:	0949      	lsrs	r1, r1, #5
 800156c:	2364      	movs	r3, #100	@ 0x64
 800156e:	fb03 2311 	mls	r3, r3, r1, r2
 8001572:	011b      	lsls	r3, r3, #4
 8001574:	3332      	adds	r3, #50	@ 0x32
 8001576:	fba0 0303 	umull	r0, r3, r0, r3
 800157a:	095b      	lsrs	r3, r3, #5
 800157c:	f003 02f0 	and.w	r2, r3, #240	@ 0xf0
 8001580:	eb02 1201 	add.w	r2, r2, r1, lsl #4
 8001584:	f003 030f 	and.w	r3, r3, #15
 8001588:	6821      	ldr	r1, [r4, #0]
 800158a:	4413      	add	r3, r2
 800158c:	608b      	str	r3, [r1, #8]
#endif /* USART_CR1_OVER8 */
}
 800158e:	bd10      	pop	{r4, pc}
    pclk = HAL_RCC_GetPCLK2Freq();
 8001590:	f7ff ff7e 	bl	8001490 <HAL_RCC_GetPCLK2Freq>
 8001594:	4602      	mov	r2, r0
 8001596:	e7dd      	b.n	8001554 <UART_SetConfig+0x48>
 8001598:	40013800 	.word	0x40013800
 800159c:	51eb851f 	.word	0x51eb851f

080015a0 <UART_WaitOnFlagUntilTimeout>:
{
 80015a0:	e92d 43f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, lr}
 80015a4:	b083      	sub	sp, #12
 80015a6:	4605      	mov	r5, r0
 80015a8:	460e      	mov	r6, r1
 80015aa:	4617      	mov	r7, r2
 80015ac:	4699      	mov	r9, r3
 80015ae:	f8dd 8028 	ldr.w	r8, [sp, #40]	@ 0x28
  while ((__HAL_UART_GET_FLAG(huart, Flag) ? SET : RESET) == Status)
 80015b2:	682b      	ldr	r3, [r5, #0]
 80015b4:	681c      	ldr	r4, [r3, #0]
 80015b6:	ea36 0404 	bics.w	r4, r6, r4
 80015ba:	bf0c      	ite	eq
 80015bc:	2401      	moveq	r4, #1
 80015be:	2400      	movne	r4, #0
 80015c0:	42bc      	cmp	r4, r7
 80015c2:	d128      	bne.n	8001616 <UART_WaitOnFlagUntilTimeout+0x76>
    if (Timeout != HAL_MAX_DELAY)
 80015c4:	f1b8 3fff 	cmp.w	r8, #4294967295
 80015c8:	d0f3      	beq.n	80015b2 <UART_WaitOnFlagUntilTimeout+0x12>
      if (((HAL_GetTick() - Tickstart) > Timeout) || (Timeout == 0U))
 80015ca:	f7ff f947 	bl	800085c <HAL_GetTick>
 80015ce:	eba0 0009 	sub.w	r0, r0, r9
 80015d2:	4540      	cmp	r0, r8
 80015d4:	d823      	bhi.n	800161e <UART_WaitOnFlagUntilTimeout+0x7e>
 80015d6:	f1b8 0f00 	cmp.w	r8, #0
 80015da:	d022      	beq.n	8001622 <UART_WaitOnFlagUntilTimeout+0x82>
      if ((READ_BIT(huart->Instance->CR1, USART_CR1_RE) != 0U) && (Flag != UART_FLAG_TXE) && (Flag != UART_FLAG_TC))
 80015dc:	682b      	ldr	r3, [r5, #0]
 80015de:	68da      	ldr	r2, [r3, #12]
 80015e0:	f012 0f04 	tst.w	r2, #4
 80015e4:	d0e5      	beq.n	80015b2 <UART_WaitOnFlagUntilTimeout+0x12>
 80015e6:	2e80      	cmp	r6, #128	@ 0x80
 80015e8:	d0e3      	beq.n	80015b2 <UART_WaitOnFlagUntilTimeout+0x12>
 80015ea:	2e40      	cmp	r6, #64	@ 0x40
 80015ec:	d0e1      	beq.n	80015b2 <UART_WaitOnFlagUntilTimeout+0x12>
        if (__HAL_UART_GET_FLAG(huart, UART_FLAG_ORE) == SET)
 80015ee:	681a      	ldr	r2, [r3, #0]
 80015f0:	f012 0f08 	tst.w	r2, #8
 80015f4:	d0dd      	beq.n	80015b2 <UART_WaitOnFlagUntilTimeout+0x12>
          __HAL_UART_CLEAR_OREFLAG(huart);
 80015f6:	2400      	movs	r4, #0
 80015f8:	9401      	str	r4, [sp, #4]
 80015fa:	681a      	ldr	r2, [r3, #0]
 80015fc:	9201      	str	r2, [sp, #4]
 80015fe:	685b      	ldr	r3, [r3, #4]
 8001600:	9301      	str	r3, [sp, #4]
 8001602:	9b01      	ldr	r3, [sp, #4]
          UART_EndRxTransfer(huart);
 8001604:	4628      	mov	r0, r5
 8001606:	f7ff ff53 	bl	80014b0 <UART_EndRxTransfer>
          huart->ErrorCode = HAL_UART_ERROR_ORE;
 800160a:	2308      	movs	r3, #8
 800160c:	646b      	str	r3, [r5, #68]	@ 0x44
          __HAL_UNLOCK(huart);
 800160e:	f885 4040 	strb.w	r4, [r5, #64]	@ 0x40
          return HAL_ERROR;
 8001612:	2001      	movs	r0, #1
 8001614:	e000      	b.n	8001618 <UART_WaitOnFlagUntilTimeout+0x78>
  return HAL_OK;
 8001616:	2000      	movs	r0, #0
}
 8001618:	b003      	add	sp, #12
 800161a:	e8bd 83f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, pc}
        return HAL_TIMEOUT;
 800161e:	2003      	movs	r0, #3
 8001620:	e7fa      	b.n	8001618 <UART_WaitOnFlagUntilTimeout+0x78>
 8001622:	2003      	movs	r0, #3
 8001624:	e7f8      	b.n	8001618 <UART_WaitOnFlagUntilTimeout+0x78>

08001626 <HAL_UART_Init>:
  if (huart == NULL)
 8001626:	b360      	cbz	r0, 8001682 <HAL_UART_Init+0x5c>
{
 8001628:	b510      	push	{r4, lr}
 800162a:	4604      	mov	r4, r0
  if (huart->gState == HAL_UART_STATE_RESET)
 800162c:	f890 3041 	ldrb.w	r3, [r0, #65]	@ 0x41
 8001630:	b313      	cbz	r3, 8001678 <HAL_UART_Init+0x52>
  huart->gState = HAL_UART_STATE_BUSY;
 8001632:	2324      	movs	r3, #36	@ 0x24
 8001634:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
  __HAL_UART_DISABLE(huart);
 8001638:	6822      	ldr	r2, [r4, #0]
 800163a:	68d3      	ldr	r3, [r2, #12]
 800163c:	f423 5300 	bic.w	r3, r3, #8192	@ 0x2000
 8001640:	60d3      	str	r3, [r2, #12]
  UART_SetConfig(huart);
 8001642:	4620      	mov	r0, r4
 8001644:	f7ff ff62 	bl	800150c <UART_SetConfig>
  CLEAR_BIT(huart->Instance->CR2, (USART_CR2_LINEN | USART_CR2_CLKEN));
 8001648:	6822      	ldr	r2, [r4, #0]
 800164a:	6913      	ldr	r3, [r2, #16]
 800164c:	f423 4390 	bic.w	r3, r3, #18432	@ 0x4800
 8001650:	6113      	str	r3, [r2, #16]
  CLEAR_BIT(huart->Instance->CR3, (USART_CR3_SCEN | USART_CR3_HDSEL | USART_CR3_IREN));
 8001652:	6822      	ldr	r2, [r4, #0]
 8001654:	6953      	ldr	r3, [r2, #20]
 8001656:	f023 032a 	bic.w	r3, r3, #42	@ 0x2a
 800165a:	6153      	str	r3, [r2, #20]
  __HAL_UART_ENABLE(huart);
 800165c:	6822      	ldr	r2, [r4, #0]
 800165e:	68d3      	ldr	r3, [r2, #12]
 8001660:	f443 5300 	orr.w	r3, r3, #8192	@ 0x2000
 8001664:	60d3      	str	r3, [r2, #12]
  huart->ErrorCode = HAL_UART_ERROR_NONE;
 8001666:	2000      	movs	r0, #0
 8001668:	6460      	str	r0, [r4, #68]	@ 0x44
  huart->gState = HAL_UART_STATE_READY;
 800166a:	2320      	movs	r3, #32
 800166c:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
  huart->RxState = HAL_UART_STATE_READY;
 8001670:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
  huart->RxEventType = HAL_UART_RXEVENT_TC;
 8001674:	6360      	str	r0, [r4, #52]	@ 0x34
}
 8001676:	bd10      	pop	{r4, pc}
    huart->Lock = HAL_UNLOCKED;
 8001678:	f880 3040 	strb.w	r3, [r0, #64]	@ 0x40
    HAL_UART_MspInit(huart);
 800167c:	f7fe ffba 	bl	80005f4 <HAL_UART_MspInit>
 8001680:	e7d7      	b.n	8001632 <HAL_UART_Init+0xc>
    return HAL_ERROR;
 8001682:	2001      	movs	r0, #1
}
 8001684:	4770      	bx	lr

08001686 <HAL_UART_Transmit>:
{
 8001686:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 800168a:	b082      	sub	sp, #8
 800168c:	461e      	mov	r6, r3
  if (huart->gState == HAL_UART_STATE_READY)
 800168e:	f890 3041 	ldrb.w	r3, [r0, #65]	@ 0x41
 8001692:	b2db      	uxtb	r3, r3
 8001694:	2b20      	cmp	r3, #32
 8001696:	d156      	bne.n	8001746 <HAL_UART_Transmit+0xc0>
 8001698:	4604      	mov	r4, r0
 800169a:	460d      	mov	r5, r1
 800169c:	4690      	mov	r8, r2
    if ((pData == NULL) || (Size == 0U))
 800169e:	2900      	cmp	r1, #0
 80016a0:	d055      	beq.n	800174e <HAL_UART_Transmit+0xc8>
 80016a2:	b90a      	cbnz	r2, 80016a8 <HAL_UART_Transmit+0x22>
      return  HAL_ERROR;
 80016a4:	2001      	movs	r0, #1
 80016a6:	e04f      	b.n	8001748 <HAL_UART_Transmit+0xc2>
    huart->ErrorCode = HAL_UART_ERROR_NONE;
 80016a8:	2300      	movs	r3, #0
 80016aa:	6443      	str	r3, [r0, #68]	@ 0x44
    huart->gState = HAL_UART_STATE_BUSY_TX;
 80016ac:	2321      	movs	r3, #33	@ 0x21
 80016ae:	f880 3041 	strb.w	r3, [r0, #65]	@ 0x41
    tickstart = HAL_GetTick();
 80016b2:	f7ff f8d3 	bl	800085c <HAL_GetTick>
 80016b6:	4607      	mov	r7, r0
    huart->TxXferSize = Size;
 80016b8:	f8a4 8024 	strh.w	r8, [r4, #36]	@ 0x24
    huart->TxXferCount = Size;
 80016bc:	f8a4 8026 	strh.w	r8, [r4, #38]	@ 0x26
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 80016c0:	68a3      	ldr	r3, [r4, #8]
 80016c2:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 80016c6:	d002      	beq.n	80016ce <HAL_UART_Transmit+0x48>
      pdata16bits = NULL;
 80016c8:	f04f 0800 	mov.w	r8, #0
 80016cc:	e014      	b.n	80016f8 <HAL_UART_Transmit+0x72>
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 80016ce:	6923      	ldr	r3, [r4, #16]
 80016d0:	b32b      	cbz	r3, 800171e <HAL_UART_Transmit+0x98>
      pdata16bits = NULL;
 80016d2:	f04f 0800 	mov.w	r8, #0
 80016d6:	e00f      	b.n	80016f8 <HAL_UART_Transmit+0x72>
        huart->gState = HAL_UART_STATE_READY;
 80016d8:	2320      	movs	r3, #32
 80016da:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
        return HAL_TIMEOUT;
 80016de:	2003      	movs	r0, #3
 80016e0:	e032      	b.n	8001748 <HAL_UART_Transmit+0xc2>
        huart->Instance->DR = (uint16_t)(*pdata16bits & 0x01FFU);
 80016e2:	f838 3b02 	ldrh.w	r3, [r8], #2
 80016e6:	6822      	ldr	r2, [r4, #0]
 80016e8:	f3c3 0308 	ubfx	r3, r3, #0, #9
 80016ec:	6053      	str	r3, [r2, #4]
      huart->TxXferCount--;
 80016ee:	8ce2      	ldrh	r2, [r4, #38]	@ 0x26
 80016f0:	b292      	uxth	r2, r2
 80016f2:	3a01      	subs	r2, #1
 80016f4:	b292      	uxth	r2, r2
 80016f6:	84e2      	strh	r2, [r4, #38]	@ 0x26
    while (huart->TxXferCount > 0U)
 80016f8:	8ce3      	ldrh	r3, [r4, #38]	@ 0x26
 80016fa:	b29b      	uxth	r3, r3
 80016fc:	b193      	cbz	r3, 8001724 <HAL_UART_Transmit+0x9e>
      if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_TXE, RESET, tickstart, Timeout) != HAL_OK)
 80016fe:	9600      	str	r6, [sp, #0]
 8001700:	463b      	mov	r3, r7
 8001702:	2200      	movs	r2, #0
 8001704:	2180      	movs	r1, #128	@ 0x80
 8001706:	4620      	mov	r0, r4
 8001708:	f7ff ff4a 	bl	80015a0 <UART_WaitOnFlagUntilTimeout>
 800170c:	2800      	cmp	r0, #0
 800170e:	d1e3      	bne.n	80016d8 <HAL_UART_Transmit+0x52>
      if (pdata8bits == NULL)
 8001710:	2d00      	cmp	r5, #0
 8001712:	d0e6      	beq.n	80016e2 <HAL_UART_Transmit+0x5c>
        huart->Instance->DR = (uint8_t)(*pdata8bits & 0xFFU);
 8001714:	f815 2b01 	ldrb.w	r2, [r5], #1
 8001718:	6823      	ldr	r3, [r4, #0]
 800171a:	605a      	str	r2, [r3, #4]
        pdata8bits++;
 800171c:	e7e7      	b.n	80016ee <HAL_UART_Transmit+0x68>
      pdata16bits = (const uint16_t *) pData;
 800171e:	46a8      	mov	r8, r5
      pdata8bits  = NULL;
 8001720:	2500      	movs	r5, #0
 8001722:	e7e9      	b.n	80016f8 <HAL_UART_Transmit+0x72>
    if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_TC, RESET, tickstart, Timeout) != HAL_OK)
 8001724:	9600      	str	r6, [sp, #0]
 8001726:	463b      	mov	r3, r7
 8001728:	2200      	movs	r2, #0
 800172a:	2140      	movs	r1, #64	@ 0x40
 800172c:	4620      	mov	r0, r4
 800172e:	f7ff ff37 	bl	80015a0 <UART_WaitOnFlagUntilTimeout>
 8001732:	b918      	cbnz	r0, 800173c <HAL_UART_Transmit+0xb6>
    huart->gState = HAL_UART_STATE_READY;
 8001734:	2320      	movs	r3, #32
 8001736:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
    return HAL_OK;
 800173a:	e005      	b.n	8001748 <HAL_UART_Transmit+0xc2>
      huart->gState = HAL_UART_STATE_READY;
 800173c:	2320      	movs	r3, #32
 800173e:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
      return HAL_TIMEOUT;
 8001742:	2003      	movs	r0, #3
 8001744:	e000      	b.n	8001748 <HAL_UART_Transmit+0xc2>
    return HAL_BUSY;
 8001746:	2002      	movs	r0, #2
}
 8001748:	b002      	add	sp, #8
 800174a:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
      return  HAL_ERROR;
 800174e:	2001      	movs	r0, #1
 8001750:	e7fa      	b.n	8001748 <HAL_UART_Transmit+0xc2>

08001752 <HAL_UART_Receive>:
{
 8001752:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 8001756:	b082      	sub	sp, #8
 8001758:	461e      	mov	r6, r3
  if (huart->RxState == HAL_UART_STATE_READY)
 800175a:	f890 3042 	ldrb.w	r3, [r0, #66]	@ 0x42
 800175e:	b2db      	uxtb	r3, r3
 8001760:	2b20      	cmp	r3, #32
 8001762:	d159      	bne.n	8001818 <HAL_UART_Receive+0xc6>
 8001764:	4604      	mov	r4, r0
 8001766:	460d      	mov	r5, r1
 8001768:	4690      	mov	r8, r2
    if ((pData == NULL) || (Size == 0U))
 800176a:	2900      	cmp	r1, #0
 800176c:	d058      	beq.n	8001820 <HAL_UART_Receive+0xce>
 800176e:	b90a      	cbnz	r2, 8001774 <HAL_UART_Receive+0x22>
      return  HAL_ERROR;
 8001770:	2001      	movs	r0, #1
 8001772:	e052      	b.n	800181a <HAL_UART_Receive+0xc8>
    huart->ErrorCode = HAL_UART_ERROR_NONE;
 8001774:	2300      	movs	r3, #0
 8001776:	6443      	str	r3, [r0, #68]	@ 0x44
    huart->RxState = HAL_UART_STATE_BUSY_RX;
 8001778:	2222      	movs	r2, #34	@ 0x22
 800177a:	f880 2042 	strb.w	r2, [r0, #66]	@ 0x42
    huart->ReceptionType = HAL_UART_RECEPTION_STANDARD;
 800177e:	6303      	str	r3, [r0, #48]	@ 0x30
    tickstart = HAL_GetTick();
 8001780:	f7ff f86c 	bl	800085c <HAL_GetTick>
 8001784:	4607      	mov	r7, r0
    huart->RxXferSize = Size;
 8001786:	f8a4 802c 	strh.w	r8, [r4, #44]	@ 0x2c
    huart->RxXferCount = Size;
 800178a:	f8a4 802e 	strh.w	r8, [r4, #46]	@ 0x2e
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 800178e:	68a3      	ldr	r3, [r4, #8]
 8001790:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 8001794:	d002      	beq.n	800179c <HAL_UART_Receive+0x4a>
      pdata16bits = NULL;
 8001796:	f04f 0800 	mov.w	r8, #0
 800179a:	e01c      	b.n	80017d6 <HAL_UART_Receive+0x84>
    if ((huart->Init.WordLength == UART_WORDLENGTH_9B) && (huart->Init.Parity == UART_PARITY_NONE))
 800179c:	6923      	ldr	r3, [r4, #16]
 800179e:	b113      	cbz	r3, 80017a6 <HAL_UART_Receive+0x54>
      pdata16bits = NULL;
 80017a0:	f04f 0800 	mov.w	r8, #0
 80017a4:	e017      	b.n	80017d6 <HAL_UART_Receive+0x84>
      pdata16bits = (uint16_t *) pData;
 80017a6:	46a8      	mov	r8, r5
      pdata8bits  = NULL;
 80017a8:	2500      	movs	r5, #0
 80017aa:	e014      	b.n	80017d6 <HAL_UART_Receive+0x84>
        huart->RxState = HAL_UART_STATE_READY;
 80017ac:	2320      	movs	r3, #32
 80017ae:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
        return HAL_TIMEOUT;
 80017b2:	2003      	movs	r0, #3
 80017b4:	e031      	b.n	800181a <HAL_UART_Receive+0xc8>
        *pdata16bits = (uint16_t)(huart->Instance->DR & 0x01FF);
 80017b6:	6823      	ldr	r3, [r4, #0]
 80017b8:	685b      	ldr	r3, [r3, #4]
 80017ba:	f3c3 0308 	ubfx	r3, r3, #0, #9
 80017be:	f828 3b02 	strh.w	r3, [r8], #2
        pdata16bits++;
 80017c2:	e003      	b.n	80017cc <HAL_UART_Receive+0x7a>
          *pdata8bits = (uint8_t)(huart->Instance->DR & (uint8_t)0x00FF);
 80017c4:	6823      	ldr	r3, [r4, #0]
 80017c6:	685b      	ldr	r3, [r3, #4]
 80017c8:	702b      	strb	r3, [r5, #0]
        pdata8bits++;
 80017ca:	3501      	adds	r5, #1
      huart->RxXferCount--;
 80017cc:	8de2      	ldrh	r2, [r4, #46]	@ 0x2e
 80017ce:	b292      	uxth	r2, r2
 80017d0:	3a01      	subs	r2, #1
 80017d2:	b292      	uxth	r2, r2
 80017d4:	85e2      	strh	r2, [r4, #46]	@ 0x2e
    while (huart->RxXferCount > 0U)
 80017d6:	8de3      	ldrh	r3, [r4, #46]	@ 0x2e
 80017d8:	b29b      	uxth	r3, r3
 80017da:	b1c3      	cbz	r3, 800180e <HAL_UART_Receive+0xbc>
      if (UART_WaitOnFlagUntilTimeout(huart, UART_FLAG_RXNE, RESET, tickstart, Timeout) != HAL_OK)
 80017dc:	9600      	str	r6, [sp, #0]
 80017de:	463b      	mov	r3, r7
 80017e0:	2200      	movs	r2, #0
 80017e2:	2120      	movs	r1, #32
 80017e4:	4620      	mov	r0, r4
 80017e6:	f7ff fedb 	bl	80015a0 <UART_WaitOnFlagUntilTimeout>
 80017ea:	2800      	cmp	r0, #0
 80017ec:	d1de      	bne.n	80017ac <HAL_UART_Receive+0x5a>
      if (pdata8bits == NULL)
 80017ee:	2d00      	cmp	r5, #0
 80017f0:	d0e1      	beq.n	80017b6 <HAL_UART_Receive+0x64>
        if ((huart->Init.WordLength == UART_WORDLENGTH_9B) || ((huart->Init.WordLength == UART_WORDLENGTH_8B) && (huart->Init.Parity == UART_PARITY_NONE)))
 80017f2:	68a3      	ldr	r3, [r4, #8]
 80017f4:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 80017f8:	d0e4      	beq.n	80017c4 <HAL_UART_Receive+0x72>
 80017fa:	b913      	cbnz	r3, 8001802 <HAL_UART_Receive+0xb0>
 80017fc:	6923      	ldr	r3, [r4, #16]
 80017fe:	2b00      	cmp	r3, #0
 8001800:	d0e0      	beq.n	80017c4 <HAL_UART_Receive+0x72>
          *pdata8bits = (uint8_t)(huart->Instance->DR & (uint8_t)0x007F);
 8001802:	6823      	ldr	r3, [r4, #0]
 8001804:	685b      	ldr	r3, [r3, #4]
 8001806:	f003 037f 	and.w	r3, r3, #127	@ 0x7f
 800180a:	702b      	strb	r3, [r5, #0]
 800180c:	e7dd      	b.n	80017ca <HAL_UART_Receive+0x78>
    huart->RxState = HAL_UART_STATE_READY;
 800180e:	2320      	movs	r3, #32
 8001810:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
    return HAL_OK;
 8001814:	2000      	movs	r0, #0
 8001816:	e000      	b.n	800181a <HAL_UART_Receive+0xc8>
    return HAL_BUSY;
 8001818:	2002      	movs	r0, #2
}
 800181a:	b002      	add	sp, #8
 800181c:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
      return  HAL_ERROR;
 8001820:	2001      	movs	r0, #1
 8001822:	e7fa      	b.n	800181a <HAL_UART_Receive+0xc8>

08001824 <std>:
 8001824:	2300      	movs	r3, #0
 8001826:	b510      	push	{r4, lr}
 8001828:	4604      	mov	r4, r0
 800182a:	e9c0 3300 	strd	r3, r3, [r0]
 800182e:	e9c0 3304 	strd	r3, r3, [r0, #16]
 8001832:	6083      	str	r3, [r0, #8]
 8001834:	8181      	strh	r1, [r0, #12]
 8001836:	6643      	str	r3, [r0, #100]	@ 0x64
 8001838:	81c2      	strh	r2, [r0, #14]
 800183a:	6183      	str	r3, [r0, #24]
 800183c:	4619      	mov	r1, r3
 800183e:	2208      	movs	r2, #8
 8001840:	305c      	adds	r0, #92	@ 0x5c
 8001842:	f000 f9f9 	bl	8001c38 <memset>
 8001846:	4b0d      	ldr	r3, [pc, #52]	@ (800187c <std+0x58>)
 8001848:	6224      	str	r4, [r4, #32]
 800184a:	6263      	str	r3, [r4, #36]	@ 0x24
 800184c:	4b0c      	ldr	r3, [pc, #48]	@ (8001880 <std+0x5c>)
 800184e:	62a3      	str	r3, [r4, #40]	@ 0x28
 8001850:	4b0c      	ldr	r3, [pc, #48]	@ (8001884 <std+0x60>)
 8001852:	62e3      	str	r3, [r4, #44]	@ 0x2c
 8001854:	4b0c      	ldr	r3, [pc, #48]	@ (8001888 <std+0x64>)
 8001856:	6323      	str	r3, [r4, #48]	@ 0x30
 8001858:	4b0c      	ldr	r3, [pc, #48]	@ (800188c <std+0x68>)
 800185a:	429c      	cmp	r4, r3
 800185c:	d006      	beq.n	800186c <std+0x48>
 800185e:	f103 0268 	add.w	r2, r3, #104	@ 0x68
 8001862:	4294      	cmp	r4, r2
 8001864:	d002      	beq.n	800186c <std+0x48>
 8001866:	33d0      	adds	r3, #208	@ 0xd0
 8001868:	429c      	cmp	r4, r3
 800186a:	d105      	bne.n	8001878 <std+0x54>
 800186c:	f104 0058 	add.w	r0, r4, #88	@ 0x58
 8001870:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 8001874:	f000 ba58 	b.w	8001d28 <__retarget_lock_init_recursive>
 8001878:	bd10      	pop	{r4, pc}
 800187a:	bf00      	nop
 800187c:	08001a89 	.word	0x08001a89
 8001880:	08001aab 	.word	0x08001aab
 8001884:	08001ae3 	.word	0x08001ae3
 8001888:	08001b07 	.word	0x08001b07
 800188c:	20000148 	.word	0x20000148

08001890 <stdio_exit_handler>:
 8001890:	4a02      	ldr	r2, [pc, #8]	@ (800189c <stdio_exit_handler+0xc>)
 8001892:	4903      	ldr	r1, [pc, #12]	@ (80018a0 <stdio_exit_handler+0x10>)
 8001894:	4803      	ldr	r0, [pc, #12]	@ (80018a4 <stdio_exit_handler+0x14>)
 8001896:	f000 b869 	b.w	800196c <_fwalk_sglue>
 800189a:	bf00      	nop
 800189c:	20000010 	.word	0x20000010
 80018a0:	080025c1 	.word	0x080025c1
 80018a4:	20000020 	.word	0x20000020

080018a8 <cleanup_stdio>:
 80018a8:	6841      	ldr	r1, [r0, #4]
 80018aa:	4b0c      	ldr	r3, [pc, #48]	@ (80018dc <cleanup_stdio+0x34>)
 80018ac:	b510      	push	{r4, lr}
 80018ae:	4299      	cmp	r1, r3
 80018b0:	4604      	mov	r4, r0
 80018b2:	d001      	beq.n	80018b8 <cleanup_stdio+0x10>
 80018b4:	f000 fe84 	bl	80025c0 <_fflush_r>
 80018b8:	68a1      	ldr	r1, [r4, #8]
 80018ba:	4b09      	ldr	r3, [pc, #36]	@ (80018e0 <cleanup_stdio+0x38>)
 80018bc:	4299      	cmp	r1, r3
 80018be:	d002      	beq.n	80018c6 <cleanup_stdio+0x1e>
 80018c0:	4620      	mov	r0, r4
 80018c2:	f000 fe7d 	bl	80025c0 <_fflush_r>
 80018c6:	68e1      	ldr	r1, [r4, #12]
 80018c8:	4b06      	ldr	r3, [pc, #24]	@ (80018e4 <cleanup_stdio+0x3c>)
 80018ca:	4299      	cmp	r1, r3
 80018cc:	d004      	beq.n	80018d8 <cleanup_stdio+0x30>
 80018ce:	4620      	mov	r0, r4
 80018d0:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 80018d4:	f000 be74 	b.w	80025c0 <_fflush_r>
 80018d8:	bd10      	pop	{r4, pc}
 80018da:	bf00      	nop
 80018dc:	20000148 	.word	0x20000148
 80018e0:	200001b0 	.word	0x200001b0
 80018e4:	20000218 	.word	0x20000218

080018e8 <global_stdio_init.part.0>:
 80018e8:	b510      	push	{r4, lr}
 80018ea:	4b0b      	ldr	r3, [pc, #44]	@ (8001918 <global_stdio_init.part.0+0x30>)
 80018ec:	4c0b      	ldr	r4, [pc, #44]	@ (800191c <global_stdio_init.part.0+0x34>)
 80018ee:	4a0c      	ldr	r2, [pc, #48]	@ (8001920 <global_stdio_init.part.0+0x38>)
 80018f0:	4620      	mov	r0, r4
 80018f2:	601a      	str	r2, [r3, #0]
 80018f4:	2104      	movs	r1, #4
 80018f6:	2200      	movs	r2, #0
 80018f8:	f7ff ff94 	bl	8001824 <std>
 80018fc:	f104 0068 	add.w	r0, r4, #104	@ 0x68
 8001900:	2201      	movs	r2, #1
 8001902:	2109      	movs	r1, #9
 8001904:	f7ff ff8e 	bl	8001824 <std>
 8001908:	f104 00d0 	add.w	r0, r4, #208	@ 0xd0
 800190c:	2202      	movs	r2, #2
 800190e:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 8001912:	2112      	movs	r1, #18
 8001914:	f7ff bf86 	b.w	8001824 <std>
 8001918:	20000280 	.word	0x20000280
 800191c:	20000148 	.word	0x20000148
 8001920:	08001891 	.word	0x08001891

08001924 <__sfp_lock_acquire>:
 8001924:	4801      	ldr	r0, [pc, #4]	@ (800192c <__sfp_lock_acquire+0x8>)
 8001926:	f000 ba00 	b.w	8001d2a <__retarget_lock_acquire_recursive>
 800192a:	bf00      	nop
 800192c:	20000289 	.word	0x20000289

08001930 <__sfp_lock_release>:
 8001930:	4801      	ldr	r0, [pc, #4]	@ (8001938 <__sfp_lock_release+0x8>)
 8001932:	f000 b9fb 	b.w	8001d2c <__retarget_lock_release_recursive>
 8001936:	bf00      	nop
 8001938:	20000289 	.word	0x20000289

0800193c <__sinit>:
 800193c:	b510      	push	{r4, lr}
 800193e:	4604      	mov	r4, r0
 8001940:	f7ff fff0 	bl	8001924 <__sfp_lock_acquire>
 8001944:	6a23      	ldr	r3, [r4, #32]
 8001946:	b11b      	cbz	r3, 8001950 <__sinit+0x14>
 8001948:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
 800194c:	f7ff bff0 	b.w	8001930 <__sfp_lock_release>
 8001950:	4b04      	ldr	r3, [pc, #16]	@ (8001964 <__sinit+0x28>)
 8001952:	6223      	str	r3, [r4, #32]
 8001954:	4b04      	ldr	r3, [pc, #16]	@ (8001968 <__sinit+0x2c>)
 8001956:	681b      	ldr	r3, [r3, #0]
 8001958:	2b00      	cmp	r3, #0
 800195a:	d1f5      	bne.n	8001948 <__sinit+0xc>
 800195c:	f7ff ffc4 	bl	80018e8 <global_stdio_init.part.0>
 8001960:	e7f2      	b.n	8001948 <__sinit+0xc>
 8001962:	bf00      	nop
 8001964:	080018a9 	.word	0x080018a9
 8001968:	20000280 	.word	0x20000280

0800196c <_fwalk_sglue>:
 800196c:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 8001970:	4607      	mov	r7, r0
 8001972:	4688      	mov	r8, r1
 8001974:	4614      	mov	r4, r2
 8001976:	2600      	movs	r6, #0
 8001978:	e9d4 9501 	ldrd	r9, r5, [r4, #4]
 800197c:	f1b9 0901 	subs.w	r9, r9, #1
 8001980:	d505      	bpl.n	800198e <_fwalk_sglue+0x22>
 8001982:	6824      	ldr	r4, [r4, #0]
 8001984:	2c00      	cmp	r4, #0
 8001986:	d1f7      	bne.n	8001978 <_fwalk_sglue+0xc>
 8001988:	4630      	mov	r0, r6
 800198a:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
 800198e:	89ab      	ldrh	r3, [r5, #12]
 8001990:	2b01      	cmp	r3, #1
 8001992:	d907      	bls.n	80019a4 <_fwalk_sglue+0x38>
 8001994:	f9b5 300e 	ldrsh.w	r3, [r5, #14]
 8001998:	3301      	adds	r3, #1
 800199a:	d003      	beq.n	80019a4 <_fwalk_sglue+0x38>
 800199c:	4629      	mov	r1, r5
 800199e:	4638      	mov	r0, r7
 80019a0:	47c0      	blx	r8
 80019a2:	4306      	orrs	r6, r0
 80019a4:	3568      	adds	r5, #104	@ 0x68
 80019a6:	e7e9      	b.n	800197c <_fwalk_sglue+0x10>

080019a8 <iprintf>:
 80019a8:	b40f      	push	{r0, r1, r2, r3}
 80019aa:	b507      	push	{r0, r1, r2, lr}
 80019ac:	4906      	ldr	r1, [pc, #24]	@ (80019c8 <iprintf+0x20>)
 80019ae:	ab04      	add	r3, sp, #16
 80019b0:	6808      	ldr	r0, [r1, #0]
 80019b2:	f853 2b04 	ldr.w	r2, [r3], #4
 80019b6:	6881      	ldr	r1, [r0, #8]
 80019b8:	9301      	str	r3, [sp, #4]
 80019ba:	f000 fad7 	bl	8001f6c <_vfiprintf_r>
 80019be:	b003      	add	sp, #12
 80019c0:	f85d eb04 	ldr.w	lr, [sp], #4
 80019c4:	b004      	add	sp, #16
 80019c6:	4770      	bx	lr
 80019c8:	2000001c 	.word	0x2000001c

080019cc <_puts_r>:
 80019cc:	6a03      	ldr	r3, [r0, #32]
 80019ce:	b570      	push	{r4, r5, r6, lr}
 80019d0:	4605      	mov	r5, r0
 80019d2:	460e      	mov	r6, r1
 80019d4:	6884      	ldr	r4, [r0, #8]
 80019d6:	b90b      	cbnz	r3, 80019dc <_puts_r+0x10>
 80019d8:	f7ff ffb0 	bl	800193c <__sinit>
 80019dc:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 80019de:	07db      	lsls	r3, r3, #31
 80019e0:	d405      	bmi.n	80019ee <_puts_r+0x22>
 80019e2:	89a3      	ldrh	r3, [r4, #12]
 80019e4:	0598      	lsls	r0, r3, #22
 80019e6:	d402      	bmi.n	80019ee <_puts_r+0x22>
 80019e8:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 80019ea:	f000 f99e 	bl	8001d2a <__retarget_lock_acquire_recursive>
 80019ee:	89a3      	ldrh	r3, [r4, #12]
 80019f0:	0719      	lsls	r1, r3, #28
 80019f2:	d502      	bpl.n	80019fa <_puts_r+0x2e>
 80019f4:	6923      	ldr	r3, [r4, #16]
 80019f6:	2b00      	cmp	r3, #0
 80019f8:	d135      	bne.n	8001a66 <_puts_r+0x9a>
 80019fa:	4621      	mov	r1, r4
 80019fc:	4628      	mov	r0, r5
 80019fe:	f000 f8c5 	bl	8001b8c <__swsetup_r>
 8001a02:	b380      	cbz	r0, 8001a66 <_puts_r+0x9a>
 8001a04:	f04f 35ff 	mov.w	r5, #4294967295
 8001a08:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 8001a0a:	07da      	lsls	r2, r3, #31
 8001a0c:	d405      	bmi.n	8001a1a <_puts_r+0x4e>
 8001a0e:	89a3      	ldrh	r3, [r4, #12]
 8001a10:	059b      	lsls	r3, r3, #22
 8001a12:	d402      	bmi.n	8001a1a <_puts_r+0x4e>
 8001a14:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 8001a16:	f000 f989 	bl	8001d2c <__retarget_lock_release_recursive>
 8001a1a:	4628      	mov	r0, r5
 8001a1c:	bd70      	pop	{r4, r5, r6, pc}
 8001a1e:	2b00      	cmp	r3, #0
 8001a20:	da04      	bge.n	8001a2c <_puts_r+0x60>
 8001a22:	69a2      	ldr	r2, [r4, #24]
 8001a24:	429a      	cmp	r2, r3
 8001a26:	dc17      	bgt.n	8001a58 <_puts_r+0x8c>
 8001a28:	290a      	cmp	r1, #10
 8001a2a:	d015      	beq.n	8001a58 <_puts_r+0x8c>
 8001a2c:	6823      	ldr	r3, [r4, #0]
 8001a2e:	1c5a      	adds	r2, r3, #1
 8001a30:	6022      	str	r2, [r4, #0]
 8001a32:	7019      	strb	r1, [r3, #0]
 8001a34:	68a3      	ldr	r3, [r4, #8]
 8001a36:	f816 1f01 	ldrb.w	r1, [r6, #1]!
 8001a3a:	3b01      	subs	r3, #1
 8001a3c:	60a3      	str	r3, [r4, #8]
 8001a3e:	2900      	cmp	r1, #0
 8001a40:	d1ed      	bne.n	8001a1e <_puts_r+0x52>
 8001a42:	2b00      	cmp	r3, #0
 8001a44:	da11      	bge.n	8001a6a <_puts_r+0x9e>
 8001a46:	4622      	mov	r2, r4
 8001a48:	210a      	movs	r1, #10
 8001a4a:	4628      	mov	r0, r5
 8001a4c:	f000 f85f 	bl	8001b0e <__swbuf_r>
 8001a50:	3001      	adds	r0, #1
 8001a52:	d0d7      	beq.n	8001a04 <_puts_r+0x38>
 8001a54:	250a      	movs	r5, #10
 8001a56:	e7d7      	b.n	8001a08 <_puts_r+0x3c>
 8001a58:	4622      	mov	r2, r4
 8001a5a:	4628      	mov	r0, r5
 8001a5c:	f000 f857 	bl	8001b0e <__swbuf_r>
 8001a60:	3001      	adds	r0, #1
 8001a62:	d1e7      	bne.n	8001a34 <_puts_r+0x68>
 8001a64:	e7ce      	b.n	8001a04 <_puts_r+0x38>
 8001a66:	3e01      	subs	r6, #1
 8001a68:	e7e4      	b.n	8001a34 <_puts_r+0x68>
 8001a6a:	6823      	ldr	r3, [r4, #0]
 8001a6c:	1c5a      	adds	r2, r3, #1
 8001a6e:	6022      	str	r2, [r4, #0]
 8001a70:	220a      	movs	r2, #10
 8001a72:	701a      	strb	r2, [r3, #0]
 8001a74:	e7ee      	b.n	8001a54 <_puts_r+0x88>
	...

08001a78 <puts>:
 8001a78:	4b02      	ldr	r3, [pc, #8]	@ (8001a84 <puts+0xc>)
 8001a7a:	4601      	mov	r1, r0
 8001a7c:	6818      	ldr	r0, [r3, #0]
 8001a7e:	f7ff bfa5 	b.w	80019cc <_puts_r>
 8001a82:	bf00      	nop
 8001a84:	2000001c 	.word	0x2000001c

08001a88 <__sread>:
 8001a88:	b510      	push	{r4, lr}
 8001a8a:	460c      	mov	r4, r1
 8001a8c:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8001a90:	f000 f8fc 	bl	8001c8c <_read_r>
 8001a94:	2800      	cmp	r0, #0
 8001a96:	bfab      	itete	ge
 8001a98:	6d63      	ldrge	r3, [r4, #84]	@ 0x54
 8001a9a:	89a3      	ldrhlt	r3, [r4, #12]
 8001a9c:	181b      	addge	r3, r3, r0
 8001a9e:	f423 5380 	biclt.w	r3, r3, #4096	@ 0x1000
 8001aa2:	bfac      	ite	ge
 8001aa4:	6563      	strge	r3, [r4, #84]	@ 0x54
 8001aa6:	81a3      	strhlt	r3, [r4, #12]
 8001aa8:	bd10      	pop	{r4, pc}

08001aaa <__swrite>:
 8001aaa:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 8001aae:	461f      	mov	r7, r3
 8001ab0:	898b      	ldrh	r3, [r1, #12]
 8001ab2:	4605      	mov	r5, r0
 8001ab4:	05db      	lsls	r3, r3, #23
 8001ab6:	460c      	mov	r4, r1
 8001ab8:	4616      	mov	r6, r2
 8001aba:	d505      	bpl.n	8001ac8 <__swrite+0x1e>
 8001abc:	2302      	movs	r3, #2
 8001abe:	2200      	movs	r2, #0
 8001ac0:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8001ac4:	f000 f8d0 	bl	8001c68 <_lseek_r>
 8001ac8:	89a3      	ldrh	r3, [r4, #12]
 8001aca:	4632      	mov	r2, r6
 8001acc:	f423 5380 	bic.w	r3, r3, #4096	@ 0x1000
 8001ad0:	81a3      	strh	r3, [r4, #12]
 8001ad2:	4628      	mov	r0, r5
 8001ad4:	463b      	mov	r3, r7
 8001ad6:	f9b4 100e 	ldrsh.w	r1, [r4, #14]
 8001ada:	e8bd 41f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, lr}
 8001ade:	f000 b8e7 	b.w	8001cb0 <_write_r>

08001ae2 <__sseek>:
 8001ae2:	b510      	push	{r4, lr}
 8001ae4:	460c      	mov	r4, r1
 8001ae6:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8001aea:	f000 f8bd 	bl	8001c68 <_lseek_r>
 8001aee:	1c43      	adds	r3, r0, #1
 8001af0:	89a3      	ldrh	r3, [r4, #12]
 8001af2:	bf15      	itete	ne
 8001af4:	6560      	strne	r0, [r4, #84]	@ 0x54
 8001af6:	f423 5380 	biceq.w	r3, r3, #4096	@ 0x1000
 8001afa:	f443 5380 	orrne.w	r3, r3, #4096	@ 0x1000
 8001afe:	81a3      	strheq	r3, [r4, #12]
 8001b00:	bf18      	it	ne
 8001b02:	81a3      	strhne	r3, [r4, #12]
 8001b04:	bd10      	pop	{r4, pc}

08001b06 <__sclose>:
 8001b06:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8001b0a:	f000 b89d 	b.w	8001c48 <_close_r>

08001b0e <__swbuf_r>:
 8001b0e:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8001b10:	460e      	mov	r6, r1
 8001b12:	4614      	mov	r4, r2
 8001b14:	4605      	mov	r5, r0
 8001b16:	b118      	cbz	r0, 8001b20 <__swbuf_r+0x12>
 8001b18:	6a03      	ldr	r3, [r0, #32]
 8001b1a:	b90b      	cbnz	r3, 8001b20 <__swbuf_r+0x12>
 8001b1c:	f7ff ff0e 	bl	800193c <__sinit>
 8001b20:	69a3      	ldr	r3, [r4, #24]
 8001b22:	60a3      	str	r3, [r4, #8]
 8001b24:	89a3      	ldrh	r3, [r4, #12]
 8001b26:	071a      	lsls	r2, r3, #28
 8001b28:	d501      	bpl.n	8001b2e <__swbuf_r+0x20>
 8001b2a:	6923      	ldr	r3, [r4, #16]
 8001b2c:	b943      	cbnz	r3, 8001b40 <__swbuf_r+0x32>
 8001b2e:	4621      	mov	r1, r4
 8001b30:	4628      	mov	r0, r5
 8001b32:	f000 f82b 	bl	8001b8c <__swsetup_r>
 8001b36:	b118      	cbz	r0, 8001b40 <__swbuf_r+0x32>
 8001b38:	f04f 37ff 	mov.w	r7, #4294967295
 8001b3c:	4638      	mov	r0, r7
 8001b3e:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
 8001b40:	6823      	ldr	r3, [r4, #0]
 8001b42:	6922      	ldr	r2, [r4, #16]
 8001b44:	b2f6      	uxtb	r6, r6
 8001b46:	1a98      	subs	r0, r3, r2
 8001b48:	6963      	ldr	r3, [r4, #20]
 8001b4a:	4637      	mov	r7, r6
 8001b4c:	4283      	cmp	r3, r0
 8001b4e:	dc05      	bgt.n	8001b5c <__swbuf_r+0x4e>
 8001b50:	4621      	mov	r1, r4
 8001b52:	4628      	mov	r0, r5
 8001b54:	f000 fd34 	bl	80025c0 <_fflush_r>
 8001b58:	2800      	cmp	r0, #0
 8001b5a:	d1ed      	bne.n	8001b38 <__swbuf_r+0x2a>
 8001b5c:	68a3      	ldr	r3, [r4, #8]
 8001b5e:	3b01      	subs	r3, #1
 8001b60:	60a3      	str	r3, [r4, #8]
 8001b62:	6823      	ldr	r3, [r4, #0]
 8001b64:	1c5a      	adds	r2, r3, #1
 8001b66:	6022      	str	r2, [r4, #0]
 8001b68:	701e      	strb	r6, [r3, #0]
 8001b6a:	6962      	ldr	r2, [r4, #20]
 8001b6c:	1c43      	adds	r3, r0, #1
 8001b6e:	429a      	cmp	r2, r3
 8001b70:	d004      	beq.n	8001b7c <__swbuf_r+0x6e>
 8001b72:	89a3      	ldrh	r3, [r4, #12]
 8001b74:	07db      	lsls	r3, r3, #31
 8001b76:	d5e1      	bpl.n	8001b3c <__swbuf_r+0x2e>
 8001b78:	2e0a      	cmp	r6, #10
 8001b7a:	d1df      	bne.n	8001b3c <__swbuf_r+0x2e>
 8001b7c:	4621      	mov	r1, r4
 8001b7e:	4628      	mov	r0, r5
 8001b80:	f000 fd1e 	bl	80025c0 <_fflush_r>
 8001b84:	2800      	cmp	r0, #0
 8001b86:	d0d9      	beq.n	8001b3c <__swbuf_r+0x2e>
 8001b88:	e7d6      	b.n	8001b38 <__swbuf_r+0x2a>
	...

08001b8c <__swsetup_r>:
 8001b8c:	b538      	push	{r3, r4, r5, lr}
 8001b8e:	4b29      	ldr	r3, [pc, #164]	@ (8001c34 <__swsetup_r+0xa8>)
 8001b90:	4605      	mov	r5, r0
 8001b92:	6818      	ldr	r0, [r3, #0]
 8001b94:	460c      	mov	r4, r1
 8001b96:	b118      	cbz	r0, 8001ba0 <__swsetup_r+0x14>
 8001b98:	6a03      	ldr	r3, [r0, #32]
 8001b9a:	b90b      	cbnz	r3, 8001ba0 <__swsetup_r+0x14>
 8001b9c:	f7ff fece 	bl	800193c <__sinit>
 8001ba0:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8001ba4:	0719      	lsls	r1, r3, #28
 8001ba6:	d422      	bmi.n	8001bee <__swsetup_r+0x62>
 8001ba8:	06da      	lsls	r2, r3, #27
 8001baa:	d407      	bmi.n	8001bbc <__swsetup_r+0x30>
 8001bac:	2209      	movs	r2, #9
 8001bae:	602a      	str	r2, [r5, #0]
 8001bb0:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 8001bb4:	f04f 30ff 	mov.w	r0, #4294967295
 8001bb8:	81a3      	strh	r3, [r4, #12]
 8001bba:	e033      	b.n	8001c24 <__swsetup_r+0x98>
 8001bbc:	0758      	lsls	r0, r3, #29
 8001bbe:	d512      	bpl.n	8001be6 <__swsetup_r+0x5a>
 8001bc0:	6b61      	ldr	r1, [r4, #52]	@ 0x34
 8001bc2:	b141      	cbz	r1, 8001bd6 <__swsetup_r+0x4a>
 8001bc4:	f104 0344 	add.w	r3, r4, #68	@ 0x44
 8001bc8:	4299      	cmp	r1, r3
 8001bca:	d002      	beq.n	8001bd2 <__swsetup_r+0x46>
 8001bcc:	4628      	mov	r0, r5
 8001bce:	f000 f8af 	bl	8001d30 <_free_r>
 8001bd2:	2300      	movs	r3, #0
 8001bd4:	6363      	str	r3, [r4, #52]	@ 0x34
 8001bd6:	89a3      	ldrh	r3, [r4, #12]
 8001bd8:	f023 0324 	bic.w	r3, r3, #36	@ 0x24
 8001bdc:	81a3      	strh	r3, [r4, #12]
 8001bde:	2300      	movs	r3, #0
 8001be0:	6063      	str	r3, [r4, #4]
 8001be2:	6923      	ldr	r3, [r4, #16]
 8001be4:	6023      	str	r3, [r4, #0]
 8001be6:	89a3      	ldrh	r3, [r4, #12]
 8001be8:	f043 0308 	orr.w	r3, r3, #8
 8001bec:	81a3      	strh	r3, [r4, #12]
 8001bee:	6923      	ldr	r3, [r4, #16]
 8001bf0:	b94b      	cbnz	r3, 8001c06 <__swsetup_r+0x7a>
 8001bf2:	89a3      	ldrh	r3, [r4, #12]
 8001bf4:	f403 7320 	and.w	r3, r3, #640	@ 0x280
 8001bf8:	f5b3 7f00 	cmp.w	r3, #512	@ 0x200
 8001bfc:	d003      	beq.n	8001c06 <__swsetup_r+0x7a>
 8001bfe:	4621      	mov	r1, r4
 8001c00:	4628      	mov	r0, r5
 8001c02:	f000 fd2a 	bl	800265a <__smakebuf_r>
 8001c06:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8001c0a:	f013 0201 	ands.w	r2, r3, #1
 8001c0e:	d00a      	beq.n	8001c26 <__swsetup_r+0x9a>
 8001c10:	2200      	movs	r2, #0
 8001c12:	60a2      	str	r2, [r4, #8]
 8001c14:	6962      	ldr	r2, [r4, #20]
 8001c16:	4252      	negs	r2, r2
 8001c18:	61a2      	str	r2, [r4, #24]
 8001c1a:	6922      	ldr	r2, [r4, #16]
 8001c1c:	b942      	cbnz	r2, 8001c30 <__swsetup_r+0xa4>
 8001c1e:	f013 0080 	ands.w	r0, r3, #128	@ 0x80
 8001c22:	d1c5      	bne.n	8001bb0 <__swsetup_r+0x24>
 8001c24:	bd38      	pop	{r3, r4, r5, pc}
 8001c26:	0799      	lsls	r1, r3, #30
 8001c28:	bf58      	it	pl
 8001c2a:	6962      	ldrpl	r2, [r4, #20]
 8001c2c:	60a2      	str	r2, [r4, #8]
 8001c2e:	e7f4      	b.n	8001c1a <__swsetup_r+0x8e>
 8001c30:	2000      	movs	r0, #0
 8001c32:	e7f7      	b.n	8001c24 <__swsetup_r+0x98>
 8001c34:	2000001c 	.word	0x2000001c

08001c38 <memset>:
 8001c38:	4603      	mov	r3, r0
 8001c3a:	4402      	add	r2, r0
 8001c3c:	4293      	cmp	r3, r2
 8001c3e:	d100      	bne.n	8001c42 <memset+0xa>
 8001c40:	4770      	bx	lr
 8001c42:	f803 1b01 	strb.w	r1, [r3], #1
 8001c46:	e7f9      	b.n	8001c3c <memset+0x4>

08001c48 <_close_r>:
 8001c48:	b538      	push	{r3, r4, r5, lr}
 8001c4a:	2300      	movs	r3, #0
 8001c4c:	4d05      	ldr	r5, [pc, #20]	@ (8001c64 <_close_r+0x1c>)
 8001c4e:	4604      	mov	r4, r0
 8001c50:	4608      	mov	r0, r1
 8001c52:	602b      	str	r3, [r5, #0]
 8001c54:	f7fe fd66 	bl	8000724 <_close>
 8001c58:	1c43      	adds	r3, r0, #1
 8001c5a:	d102      	bne.n	8001c62 <_close_r+0x1a>
 8001c5c:	682b      	ldr	r3, [r5, #0]
 8001c5e:	b103      	cbz	r3, 8001c62 <_close_r+0x1a>
 8001c60:	6023      	str	r3, [r4, #0]
 8001c62:	bd38      	pop	{r3, r4, r5, pc}
 8001c64:	20000284 	.word	0x20000284

08001c68 <_lseek_r>:
 8001c68:	b538      	push	{r3, r4, r5, lr}
 8001c6a:	4604      	mov	r4, r0
 8001c6c:	4608      	mov	r0, r1
 8001c6e:	4611      	mov	r1, r2
 8001c70:	2200      	movs	r2, #0
 8001c72:	4d05      	ldr	r5, [pc, #20]	@ (8001c88 <_lseek_r+0x20>)
 8001c74:	602a      	str	r2, [r5, #0]
 8001c76:	461a      	mov	r2, r3
 8001c78:	f7fe fd5e 	bl	8000738 <_lseek>
 8001c7c:	1c43      	adds	r3, r0, #1
 8001c7e:	d102      	bne.n	8001c86 <_lseek_r+0x1e>
 8001c80:	682b      	ldr	r3, [r5, #0]
 8001c82:	b103      	cbz	r3, 8001c86 <_lseek_r+0x1e>
 8001c84:	6023      	str	r3, [r4, #0]
 8001c86:	bd38      	pop	{r3, r4, r5, pc}
 8001c88:	20000284 	.word	0x20000284

08001c8c <_read_r>:
 8001c8c:	b538      	push	{r3, r4, r5, lr}
 8001c8e:	4604      	mov	r4, r0
 8001c90:	4608      	mov	r0, r1
 8001c92:	4611      	mov	r1, r2
 8001c94:	2200      	movs	r2, #0
 8001c96:	4d05      	ldr	r5, [pc, #20]	@ (8001cac <_read_r+0x20>)
 8001c98:	602a      	str	r2, [r5, #0]
 8001c9a:	461a      	mov	r2, r3
 8001c9c:	f7fe fd24 	bl	80006e8 <_read>
 8001ca0:	1c43      	adds	r3, r0, #1
 8001ca2:	d102      	bne.n	8001caa <_read_r+0x1e>
 8001ca4:	682b      	ldr	r3, [r5, #0]
 8001ca6:	b103      	cbz	r3, 8001caa <_read_r+0x1e>
 8001ca8:	6023      	str	r3, [r4, #0]
 8001caa:	bd38      	pop	{r3, r4, r5, pc}
 8001cac:	20000284 	.word	0x20000284

08001cb0 <_write_r>:
 8001cb0:	b538      	push	{r3, r4, r5, lr}
 8001cb2:	4604      	mov	r4, r0
 8001cb4:	4608      	mov	r0, r1
 8001cb6:	4611      	mov	r1, r2
 8001cb8:	2200      	movs	r2, #0
 8001cba:	4d05      	ldr	r5, [pc, #20]	@ (8001cd0 <_write_r+0x20>)
 8001cbc:	602a      	str	r2, [r5, #0]
 8001cbe:	461a      	mov	r2, r3
 8001cc0:	f7fe fd22 	bl	8000708 <_write>
 8001cc4:	1c43      	adds	r3, r0, #1
 8001cc6:	d102      	bne.n	8001cce <_write_r+0x1e>
 8001cc8:	682b      	ldr	r3, [r5, #0]
 8001cca:	b103      	cbz	r3, 8001cce <_write_r+0x1e>
 8001ccc:	6023      	str	r3, [r4, #0]
 8001cce:	bd38      	pop	{r3, r4, r5, pc}
 8001cd0:	20000284 	.word	0x20000284

08001cd4 <__errno>:
 8001cd4:	4b01      	ldr	r3, [pc, #4]	@ (8001cdc <__errno+0x8>)
 8001cd6:	6818      	ldr	r0, [r3, #0]
 8001cd8:	4770      	bx	lr
 8001cda:	bf00      	nop
 8001cdc:	2000001c 	.word	0x2000001c

08001ce0 <__libc_init_array>:
 8001ce0:	b570      	push	{r4, r5, r6, lr}
 8001ce2:	2600      	movs	r6, #0
 8001ce4:	4d0c      	ldr	r5, [pc, #48]	@ (8001d18 <__libc_init_array+0x38>)
 8001ce6:	4c0d      	ldr	r4, [pc, #52]	@ (8001d1c <__libc_init_array+0x3c>)
 8001ce8:	1b64      	subs	r4, r4, r5
 8001cea:	10a4      	asrs	r4, r4, #2
 8001cec:	42a6      	cmp	r6, r4
 8001cee:	d109      	bne.n	8001d04 <__libc_init_array+0x24>
 8001cf0:	f000 fd30 	bl	8002754 <_init>
 8001cf4:	2600      	movs	r6, #0
 8001cf6:	4d0a      	ldr	r5, [pc, #40]	@ (8001d20 <__libc_init_array+0x40>)
 8001cf8:	4c0a      	ldr	r4, [pc, #40]	@ (8001d24 <__libc_init_array+0x44>)
 8001cfa:	1b64      	subs	r4, r4, r5
 8001cfc:	10a4      	asrs	r4, r4, #2
 8001cfe:	42a6      	cmp	r6, r4
 8001d00:	d105      	bne.n	8001d0e <__libc_init_array+0x2e>
 8001d02:	bd70      	pop	{r4, r5, r6, pc}
 8001d04:	f855 3b04 	ldr.w	r3, [r5], #4
 8001d08:	4798      	blx	r3
 8001d0a:	3601      	adds	r6, #1
 8001d0c:	e7ee      	b.n	8001cec <__libc_init_array+0xc>
 8001d0e:	f855 3b04 	ldr.w	r3, [r5], #4
 8001d12:	4798      	blx	r3
 8001d14:	3601      	adds	r6, #1
 8001d16:	e7f2      	b.n	8001cfe <__libc_init_array+0x1e>
 8001d18:	080029dc 	.word	0x080029dc
 8001d1c:	080029dc 	.word	0x080029dc
 8001d20:	080029dc 	.word	0x080029dc
 8001d24:	080029e0 	.word	0x080029e0

08001d28 <__retarget_lock_init_recursive>:
 8001d28:	4770      	bx	lr

08001d2a <__retarget_lock_acquire_recursive>:
 8001d2a:	4770      	bx	lr

08001d2c <__retarget_lock_release_recursive>:
 8001d2c:	4770      	bx	lr
	...

08001d30 <_free_r>:
 8001d30:	b538      	push	{r3, r4, r5, lr}
 8001d32:	4605      	mov	r5, r0
 8001d34:	2900      	cmp	r1, #0
 8001d36:	d040      	beq.n	8001dba <_free_r+0x8a>
 8001d38:	f851 3c04 	ldr.w	r3, [r1, #-4]
 8001d3c:	1f0c      	subs	r4, r1, #4
 8001d3e:	2b00      	cmp	r3, #0
 8001d40:	bfb8      	it	lt
 8001d42:	18e4      	addlt	r4, r4, r3
 8001d44:	f000 f8de 	bl	8001f04 <__malloc_lock>
 8001d48:	4a1c      	ldr	r2, [pc, #112]	@ (8001dbc <_free_r+0x8c>)
 8001d4a:	6813      	ldr	r3, [r2, #0]
 8001d4c:	b933      	cbnz	r3, 8001d5c <_free_r+0x2c>
 8001d4e:	6063      	str	r3, [r4, #4]
 8001d50:	6014      	str	r4, [r2, #0]
 8001d52:	4628      	mov	r0, r5
 8001d54:	e8bd 4038 	ldmia.w	sp!, {r3, r4, r5, lr}
 8001d58:	f000 b8da 	b.w	8001f10 <__malloc_unlock>
 8001d5c:	42a3      	cmp	r3, r4
 8001d5e:	d908      	bls.n	8001d72 <_free_r+0x42>
 8001d60:	6820      	ldr	r0, [r4, #0]
 8001d62:	1821      	adds	r1, r4, r0
 8001d64:	428b      	cmp	r3, r1
 8001d66:	bf01      	itttt	eq
 8001d68:	6819      	ldreq	r1, [r3, #0]
 8001d6a:	685b      	ldreq	r3, [r3, #4]
 8001d6c:	1809      	addeq	r1, r1, r0
 8001d6e:	6021      	streq	r1, [r4, #0]
 8001d70:	e7ed      	b.n	8001d4e <_free_r+0x1e>
 8001d72:	461a      	mov	r2, r3
 8001d74:	685b      	ldr	r3, [r3, #4]
 8001d76:	b10b      	cbz	r3, 8001d7c <_free_r+0x4c>
 8001d78:	42a3      	cmp	r3, r4
 8001d7a:	d9fa      	bls.n	8001d72 <_free_r+0x42>
 8001d7c:	6811      	ldr	r1, [r2, #0]
 8001d7e:	1850      	adds	r0, r2, r1
 8001d80:	42a0      	cmp	r0, r4
 8001d82:	d10b      	bne.n	8001d9c <_free_r+0x6c>
 8001d84:	6820      	ldr	r0, [r4, #0]
 8001d86:	4401      	add	r1, r0
 8001d88:	1850      	adds	r0, r2, r1
 8001d8a:	4283      	cmp	r3, r0
 8001d8c:	6011      	str	r1, [r2, #0]
 8001d8e:	d1e0      	bne.n	8001d52 <_free_r+0x22>
 8001d90:	6818      	ldr	r0, [r3, #0]
 8001d92:	685b      	ldr	r3, [r3, #4]
 8001d94:	4408      	add	r0, r1
 8001d96:	6010      	str	r0, [r2, #0]
 8001d98:	6053      	str	r3, [r2, #4]
 8001d9a:	e7da      	b.n	8001d52 <_free_r+0x22>
 8001d9c:	d902      	bls.n	8001da4 <_free_r+0x74>
 8001d9e:	230c      	movs	r3, #12
 8001da0:	602b      	str	r3, [r5, #0]
 8001da2:	e7d6      	b.n	8001d52 <_free_r+0x22>
 8001da4:	6820      	ldr	r0, [r4, #0]
 8001da6:	1821      	adds	r1, r4, r0
 8001da8:	428b      	cmp	r3, r1
 8001daa:	bf01      	itttt	eq
 8001dac:	6819      	ldreq	r1, [r3, #0]
 8001dae:	685b      	ldreq	r3, [r3, #4]
 8001db0:	1809      	addeq	r1, r1, r0
 8001db2:	6021      	streq	r1, [r4, #0]
 8001db4:	6063      	str	r3, [r4, #4]
 8001db6:	6054      	str	r4, [r2, #4]
 8001db8:	e7cb      	b.n	8001d52 <_free_r+0x22>
 8001dba:	bd38      	pop	{r3, r4, r5, pc}
 8001dbc:	20000290 	.word	0x20000290

08001dc0 <sbrk_aligned>:
 8001dc0:	b570      	push	{r4, r5, r6, lr}
 8001dc2:	4e0f      	ldr	r6, [pc, #60]	@ (8001e00 <sbrk_aligned+0x40>)
 8001dc4:	460c      	mov	r4, r1
 8001dc6:	6831      	ldr	r1, [r6, #0]
 8001dc8:	4605      	mov	r5, r0
 8001dca:	b911      	cbnz	r1, 8001dd2 <sbrk_aligned+0x12>
 8001dcc:	f000 fca4 	bl	8002718 <_sbrk_r>
 8001dd0:	6030      	str	r0, [r6, #0]
 8001dd2:	4621      	mov	r1, r4
 8001dd4:	4628      	mov	r0, r5
 8001dd6:	f000 fc9f 	bl	8002718 <_sbrk_r>
 8001dda:	1c43      	adds	r3, r0, #1
 8001ddc:	d103      	bne.n	8001de6 <sbrk_aligned+0x26>
 8001dde:	f04f 34ff 	mov.w	r4, #4294967295
 8001de2:	4620      	mov	r0, r4
 8001de4:	bd70      	pop	{r4, r5, r6, pc}
 8001de6:	1cc4      	adds	r4, r0, #3
 8001de8:	f024 0403 	bic.w	r4, r4, #3
 8001dec:	42a0      	cmp	r0, r4
 8001dee:	d0f8      	beq.n	8001de2 <sbrk_aligned+0x22>
 8001df0:	1a21      	subs	r1, r4, r0
 8001df2:	4628      	mov	r0, r5
 8001df4:	f000 fc90 	bl	8002718 <_sbrk_r>
 8001df8:	3001      	adds	r0, #1
 8001dfa:	d1f2      	bne.n	8001de2 <sbrk_aligned+0x22>
 8001dfc:	e7ef      	b.n	8001dde <sbrk_aligned+0x1e>
 8001dfe:	bf00      	nop
 8001e00:	2000028c 	.word	0x2000028c

08001e04 <_malloc_r>:
 8001e04:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
 8001e08:	1ccd      	adds	r5, r1, #3
 8001e0a:	f025 0503 	bic.w	r5, r5, #3
 8001e0e:	3508      	adds	r5, #8
 8001e10:	2d0c      	cmp	r5, #12
 8001e12:	bf38      	it	cc
 8001e14:	250c      	movcc	r5, #12
 8001e16:	2d00      	cmp	r5, #0
 8001e18:	4606      	mov	r6, r0
 8001e1a:	db01      	blt.n	8001e20 <_malloc_r+0x1c>
 8001e1c:	42a9      	cmp	r1, r5
 8001e1e:	d904      	bls.n	8001e2a <_malloc_r+0x26>
 8001e20:	230c      	movs	r3, #12
 8001e22:	6033      	str	r3, [r6, #0]
 8001e24:	2000      	movs	r0, #0
 8001e26:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
 8001e2a:	f8df 80d4 	ldr.w	r8, [pc, #212]	@ 8001f00 <_malloc_r+0xfc>
 8001e2e:	f000 f869 	bl	8001f04 <__malloc_lock>
 8001e32:	f8d8 3000 	ldr.w	r3, [r8]
 8001e36:	461c      	mov	r4, r3
 8001e38:	bb44      	cbnz	r4, 8001e8c <_malloc_r+0x88>
 8001e3a:	4629      	mov	r1, r5
 8001e3c:	4630      	mov	r0, r6
 8001e3e:	f7ff ffbf 	bl	8001dc0 <sbrk_aligned>
 8001e42:	1c43      	adds	r3, r0, #1
 8001e44:	4604      	mov	r4, r0
 8001e46:	d158      	bne.n	8001efa <_malloc_r+0xf6>
 8001e48:	f8d8 4000 	ldr.w	r4, [r8]
 8001e4c:	4627      	mov	r7, r4
 8001e4e:	2f00      	cmp	r7, #0
 8001e50:	d143      	bne.n	8001eda <_malloc_r+0xd6>
 8001e52:	2c00      	cmp	r4, #0
 8001e54:	d04b      	beq.n	8001eee <_malloc_r+0xea>
 8001e56:	6823      	ldr	r3, [r4, #0]
 8001e58:	4639      	mov	r1, r7
 8001e5a:	4630      	mov	r0, r6
 8001e5c:	eb04 0903 	add.w	r9, r4, r3
 8001e60:	f000 fc5a 	bl	8002718 <_sbrk_r>
 8001e64:	4581      	cmp	r9, r0
 8001e66:	d142      	bne.n	8001eee <_malloc_r+0xea>
 8001e68:	6821      	ldr	r1, [r4, #0]
 8001e6a:	4630      	mov	r0, r6
 8001e6c:	1a6d      	subs	r5, r5, r1
 8001e6e:	4629      	mov	r1, r5
 8001e70:	f7ff ffa6 	bl	8001dc0 <sbrk_aligned>
 8001e74:	3001      	adds	r0, #1
 8001e76:	d03a      	beq.n	8001eee <_malloc_r+0xea>
 8001e78:	6823      	ldr	r3, [r4, #0]
 8001e7a:	442b      	add	r3, r5
 8001e7c:	6023      	str	r3, [r4, #0]
 8001e7e:	f8d8 3000 	ldr.w	r3, [r8]
 8001e82:	685a      	ldr	r2, [r3, #4]
 8001e84:	bb62      	cbnz	r2, 8001ee0 <_malloc_r+0xdc>
 8001e86:	f8c8 7000 	str.w	r7, [r8]
 8001e8a:	e00f      	b.n	8001eac <_malloc_r+0xa8>
 8001e8c:	6822      	ldr	r2, [r4, #0]
 8001e8e:	1b52      	subs	r2, r2, r5
 8001e90:	d420      	bmi.n	8001ed4 <_malloc_r+0xd0>
 8001e92:	2a0b      	cmp	r2, #11
 8001e94:	d917      	bls.n	8001ec6 <_malloc_r+0xc2>
 8001e96:	1961      	adds	r1, r4, r5
 8001e98:	42a3      	cmp	r3, r4
 8001e9a:	6025      	str	r5, [r4, #0]
 8001e9c:	bf18      	it	ne
 8001e9e:	6059      	strne	r1, [r3, #4]
 8001ea0:	6863      	ldr	r3, [r4, #4]
 8001ea2:	bf08      	it	eq
 8001ea4:	f8c8 1000 	streq.w	r1, [r8]
 8001ea8:	5162      	str	r2, [r4, r5]
 8001eaa:	604b      	str	r3, [r1, #4]
 8001eac:	4630      	mov	r0, r6
 8001eae:	f000 f82f 	bl	8001f10 <__malloc_unlock>
 8001eb2:	f104 000b 	add.w	r0, r4, #11
 8001eb6:	1d23      	adds	r3, r4, #4
 8001eb8:	f020 0007 	bic.w	r0, r0, #7
 8001ebc:	1ac2      	subs	r2, r0, r3
 8001ebe:	bf1c      	itt	ne
 8001ec0:	1a1b      	subne	r3, r3, r0
 8001ec2:	50a3      	strne	r3, [r4, r2]
 8001ec4:	e7af      	b.n	8001e26 <_malloc_r+0x22>
 8001ec6:	6862      	ldr	r2, [r4, #4]
 8001ec8:	42a3      	cmp	r3, r4
 8001eca:	bf0c      	ite	eq
 8001ecc:	f8c8 2000 	streq.w	r2, [r8]
 8001ed0:	605a      	strne	r2, [r3, #4]
 8001ed2:	e7eb      	b.n	8001eac <_malloc_r+0xa8>
 8001ed4:	4623      	mov	r3, r4
 8001ed6:	6864      	ldr	r4, [r4, #4]
 8001ed8:	e7ae      	b.n	8001e38 <_malloc_r+0x34>
 8001eda:	463c      	mov	r4, r7
 8001edc:	687f      	ldr	r7, [r7, #4]
 8001ede:	e7b6      	b.n	8001e4e <_malloc_r+0x4a>
 8001ee0:	461a      	mov	r2, r3
 8001ee2:	685b      	ldr	r3, [r3, #4]
 8001ee4:	42a3      	cmp	r3, r4
 8001ee6:	d1fb      	bne.n	8001ee0 <_malloc_r+0xdc>
 8001ee8:	2300      	movs	r3, #0
 8001eea:	6053      	str	r3, [r2, #4]
 8001eec:	e7de      	b.n	8001eac <_malloc_r+0xa8>
 8001eee:	230c      	movs	r3, #12
 8001ef0:	4630      	mov	r0, r6
 8001ef2:	6033      	str	r3, [r6, #0]
 8001ef4:	f000 f80c 	bl	8001f10 <__malloc_unlock>
 8001ef8:	e794      	b.n	8001e24 <_malloc_r+0x20>
 8001efa:	6005      	str	r5, [r0, #0]
 8001efc:	e7d6      	b.n	8001eac <_malloc_r+0xa8>
 8001efe:	bf00      	nop
 8001f00:	20000290 	.word	0x20000290

08001f04 <__malloc_lock>:
 8001f04:	4801      	ldr	r0, [pc, #4]	@ (8001f0c <__malloc_lock+0x8>)
 8001f06:	f7ff bf10 	b.w	8001d2a <__retarget_lock_acquire_recursive>
 8001f0a:	bf00      	nop
 8001f0c:	20000288 	.word	0x20000288

08001f10 <__malloc_unlock>:
 8001f10:	4801      	ldr	r0, [pc, #4]	@ (8001f18 <__malloc_unlock+0x8>)
 8001f12:	f7ff bf0b 	b.w	8001d2c <__retarget_lock_release_recursive>
 8001f16:	bf00      	nop
 8001f18:	20000288 	.word	0x20000288

08001f1c <__sfputc_r>:
 8001f1c:	6893      	ldr	r3, [r2, #8]
 8001f1e:	b410      	push	{r4}
 8001f20:	3b01      	subs	r3, #1
 8001f22:	2b00      	cmp	r3, #0
 8001f24:	6093      	str	r3, [r2, #8]
 8001f26:	da07      	bge.n	8001f38 <__sfputc_r+0x1c>
 8001f28:	6994      	ldr	r4, [r2, #24]
 8001f2a:	42a3      	cmp	r3, r4
 8001f2c:	db01      	blt.n	8001f32 <__sfputc_r+0x16>
 8001f2e:	290a      	cmp	r1, #10
 8001f30:	d102      	bne.n	8001f38 <__sfputc_r+0x1c>
 8001f32:	bc10      	pop	{r4}
 8001f34:	f7ff bdeb 	b.w	8001b0e <__swbuf_r>
 8001f38:	6813      	ldr	r3, [r2, #0]
 8001f3a:	1c58      	adds	r0, r3, #1
 8001f3c:	6010      	str	r0, [r2, #0]
 8001f3e:	7019      	strb	r1, [r3, #0]
 8001f40:	4608      	mov	r0, r1
 8001f42:	bc10      	pop	{r4}
 8001f44:	4770      	bx	lr

08001f46 <__sfputs_r>:
 8001f46:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8001f48:	4606      	mov	r6, r0
 8001f4a:	460f      	mov	r7, r1
 8001f4c:	4614      	mov	r4, r2
 8001f4e:	18d5      	adds	r5, r2, r3
 8001f50:	42ac      	cmp	r4, r5
 8001f52:	d101      	bne.n	8001f58 <__sfputs_r+0x12>
 8001f54:	2000      	movs	r0, #0
 8001f56:	e007      	b.n	8001f68 <__sfputs_r+0x22>
 8001f58:	463a      	mov	r2, r7
 8001f5a:	4630      	mov	r0, r6
 8001f5c:	f814 1b01 	ldrb.w	r1, [r4], #1
 8001f60:	f7ff ffdc 	bl	8001f1c <__sfputc_r>
 8001f64:	1c43      	adds	r3, r0, #1
 8001f66:	d1f3      	bne.n	8001f50 <__sfputs_r+0xa>
 8001f68:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
	...

08001f6c <_vfiprintf_r>:
 8001f6c:	e92d 4ff0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
 8001f70:	460d      	mov	r5, r1
 8001f72:	4614      	mov	r4, r2
 8001f74:	4698      	mov	r8, r3
 8001f76:	4606      	mov	r6, r0
 8001f78:	b09d      	sub	sp, #116	@ 0x74
 8001f7a:	b118      	cbz	r0, 8001f84 <_vfiprintf_r+0x18>
 8001f7c:	6a03      	ldr	r3, [r0, #32]
 8001f7e:	b90b      	cbnz	r3, 8001f84 <_vfiprintf_r+0x18>
 8001f80:	f7ff fcdc 	bl	800193c <__sinit>
 8001f84:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 8001f86:	07d9      	lsls	r1, r3, #31
 8001f88:	d405      	bmi.n	8001f96 <_vfiprintf_r+0x2a>
 8001f8a:	89ab      	ldrh	r3, [r5, #12]
 8001f8c:	059a      	lsls	r2, r3, #22
 8001f8e:	d402      	bmi.n	8001f96 <_vfiprintf_r+0x2a>
 8001f90:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 8001f92:	f7ff feca 	bl	8001d2a <__retarget_lock_acquire_recursive>
 8001f96:	89ab      	ldrh	r3, [r5, #12]
 8001f98:	071b      	lsls	r3, r3, #28
 8001f9a:	d501      	bpl.n	8001fa0 <_vfiprintf_r+0x34>
 8001f9c:	692b      	ldr	r3, [r5, #16]
 8001f9e:	b99b      	cbnz	r3, 8001fc8 <_vfiprintf_r+0x5c>
 8001fa0:	4629      	mov	r1, r5
 8001fa2:	4630      	mov	r0, r6
 8001fa4:	f7ff fdf2 	bl	8001b8c <__swsetup_r>
 8001fa8:	b170      	cbz	r0, 8001fc8 <_vfiprintf_r+0x5c>
 8001faa:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 8001fac:	07dc      	lsls	r4, r3, #31
 8001fae:	d504      	bpl.n	8001fba <_vfiprintf_r+0x4e>
 8001fb0:	f04f 30ff 	mov.w	r0, #4294967295
 8001fb4:	b01d      	add	sp, #116	@ 0x74
 8001fb6:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
 8001fba:	89ab      	ldrh	r3, [r5, #12]
 8001fbc:	0598      	lsls	r0, r3, #22
 8001fbe:	d4f7      	bmi.n	8001fb0 <_vfiprintf_r+0x44>
 8001fc0:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 8001fc2:	f7ff feb3 	bl	8001d2c <__retarget_lock_release_recursive>
 8001fc6:	e7f3      	b.n	8001fb0 <_vfiprintf_r+0x44>
 8001fc8:	2300      	movs	r3, #0
 8001fca:	9309      	str	r3, [sp, #36]	@ 0x24
 8001fcc:	2320      	movs	r3, #32
 8001fce:	f88d 3029 	strb.w	r3, [sp, #41]	@ 0x29
 8001fd2:	2330      	movs	r3, #48	@ 0x30
 8001fd4:	f04f 0901 	mov.w	r9, #1
 8001fd8:	f8cd 800c 	str.w	r8, [sp, #12]
 8001fdc:	f8df 81a8 	ldr.w	r8, [pc, #424]	@ 8002188 <_vfiprintf_r+0x21c>
 8001fe0:	f88d 302a 	strb.w	r3, [sp, #42]	@ 0x2a
 8001fe4:	4623      	mov	r3, r4
 8001fe6:	469a      	mov	sl, r3
 8001fe8:	f813 2b01 	ldrb.w	r2, [r3], #1
 8001fec:	b10a      	cbz	r2, 8001ff2 <_vfiprintf_r+0x86>
 8001fee:	2a25      	cmp	r2, #37	@ 0x25
 8001ff0:	d1f9      	bne.n	8001fe6 <_vfiprintf_r+0x7a>
 8001ff2:	ebba 0b04 	subs.w	fp, sl, r4
 8001ff6:	d00b      	beq.n	8002010 <_vfiprintf_r+0xa4>
 8001ff8:	465b      	mov	r3, fp
 8001ffa:	4622      	mov	r2, r4
 8001ffc:	4629      	mov	r1, r5
 8001ffe:	4630      	mov	r0, r6
 8002000:	f7ff ffa1 	bl	8001f46 <__sfputs_r>
 8002004:	3001      	adds	r0, #1
 8002006:	f000 80a7 	beq.w	8002158 <_vfiprintf_r+0x1ec>
 800200a:	9a09      	ldr	r2, [sp, #36]	@ 0x24
 800200c:	445a      	add	r2, fp
 800200e:	9209      	str	r2, [sp, #36]	@ 0x24
 8002010:	f89a 3000 	ldrb.w	r3, [sl]
 8002014:	2b00      	cmp	r3, #0
 8002016:	f000 809f 	beq.w	8002158 <_vfiprintf_r+0x1ec>
 800201a:	2300      	movs	r3, #0
 800201c:	f04f 32ff 	mov.w	r2, #4294967295
 8002020:	e9cd 2305 	strd	r2, r3, [sp, #20]
 8002024:	f10a 0a01 	add.w	sl, sl, #1
 8002028:	9304      	str	r3, [sp, #16]
 800202a:	9307      	str	r3, [sp, #28]
 800202c:	f88d 3053 	strb.w	r3, [sp, #83]	@ 0x53
 8002030:	931a      	str	r3, [sp, #104]	@ 0x68
 8002032:	4654      	mov	r4, sl
 8002034:	2205      	movs	r2, #5
 8002036:	f814 1b01 	ldrb.w	r1, [r4], #1
 800203a:	4853      	ldr	r0, [pc, #332]	@ (8002188 <_vfiprintf_r+0x21c>)
 800203c:	f000 fb7c 	bl	8002738 <memchr>
 8002040:	9a04      	ldr	r2, [sp, #16]
 8002042:	b9d8      	cbnz	r0, 800207c <_vfiprintf_r+0x110>
 8002044:	06d1      	lsls	r1, r2, #27
 8002046:	bf44      	itt	mi
 8002048:	2320      	movmi	r3, #32
 800204a:	f88d 3053 	strbmi.w	r3, [sp, #83]	@ 0x53
 800204e:	0713      	lsls	r3, r2, #28
 8002050:	bf44      	itt	mi
 8002052:	232b      	movmi	r3, #43	@ 0x2b
 8002054:	f88d 3053 	strbmi.w	r3, [sp, #83]	@ 0x53
 8002058:	f89a 3000 	ldrb.w	r3, [sl]
 800205c:	2b2a      	cmp	r3, #42	@ 0x2a
 800205e:	d015      	beq.n	800208c <_vfiprintf_r+0x120>
 8002060:	4654      	mov	r4, sl
 8002062:	2000      	movs	r0, #0
 8002064:	f04f 0c0a 	mov.w	ip, #10
 8002068:	9a07      	ldr	r2, [sp, #28]
 800206a:	4621      	mov	r1, r4
 800206c:	f811 3b01 	ldrb.w	r3, [r1], #1
 8002070:	3b30      	subs	r3, #48	@ 0x30
 8002072:	2b09      	cmp	r3, #9
 8002074:	d94b      	bls.n	800210e <_vfiprintf_r+0x1a2>
 8002076:	b1b0      	cbz	r0, 80020a6 <_vfiprintf_r+0x13a>
 8002078:	9207      	str	r2, [sp, #28]
 800207a:	e014      	b.n	80020a6 <_vfiprintf_r+0x13a>
 800207c:	eba0 0308 	sub.w	r3, r0, r8
 8002080:	fa09 f303 	lsl.w	r3, r9, r3
 8002084:	4313      	orrs	r3, r2
 8002086:	46a2      	mov	sl, r4
 8002088:	9304      	str	r3, [sp, #16]
 800208a:	e7d2      	b.n	8002032 <_vfiprintf_r+0xc6>
 800208c:	9b03      	ldr	r3, [sp, #12]
 800208e:	1d19      	adds	r1, r3, #4
 8002090:	681b      	ldr	r3, [r3, #0]
 8002092:	9103      	str	r1, [sp, #12]
 8002094:	2b00      	cmp	r3, #0
 8002096:	bfbb      	ittet	lt
 8002098:	425b      	neglt	r3, r3
 800209a:	f042 0202 	orrlt.w	r2, r2, #2
 800209e:	9307      	strge	r3, [sp, #28]
 80020a0:	9307      	strlt	r3, [sp, #28]
 80020a2:	bfb8      	it	lt
 80020a4:	9204      	strlt	r2, [sp, #16]
 80020a6:	7823      	ldrb	r3, [r4, #0]
 80020a8:	2b2e      	cmp	r3, #46	@ 0x2e
 80020aa:	d10a      	bne.n	80020c2 <_vfiprintf_r+0x156>
 80020ac:	7863      	ldrb	r3, [r4, #1]
 80020ae:	2b2a      	cmp	r3, #42	@ 0x2a
 80020b0:	d132      	bne.n	8002118 <_vfiprintf_r+0x1ac>
 80020b2:	9b03      	ldr	r3, [sp, #12]
 80020b4:	3402      	adds	r4, #2
 80020b6:	1d1a      	adds	r2, r3, #4
 80020b8:	681b      	ldr	r3, [r3, #0]
 80020ba:	9203      	str	r2, [sp, #12]
 80020bc:	ea43 73e3 	orr.w	r3, r3, r3, asr #31
 80020c0:	9305      	str	r3, [sp, #20]
 80020c2:	f8df a0c8 	ldr.w	sl, [pc, #200]	@ 800218c <_vfiprintf_r+0x220>
 80020c6:	2203      	movs	r2, #3
 80020c8:	4650      	mov	r0, sl
 80020ca:	7821      	ldrb	r1, [r4, #0]
 80020cc:	f000 fb34 	bl	8002738 <memchr>
 80020d0:	b138      	cbz	r0, 80020e2 <_vfiprintf_r+0x176>
 80020d2:	2240      	movs	r2, #64	@ 0x40
 80020d4:	9b04      	ldr	r3, [sp, #16]
 80020d6:	eba0 000a 	sub.w	r0, r0, sl
 80020da:	4082      	lsls	r2, r0
 80020dc:	4313      	orrs	r3, r2
 80020de:	3401      	adds	r4, #1
 80020e0:	9304      	str	r3, [sp, #16]
 80020e2:	f814 1b01 	ldrb.w	r1, [r4], #1
 80020e6:	2206      	movs	r2, #6
 80020e8:	4829      	ldr	r0, [pc, #164]	@ (8002190 <_vfiprintf_r+0x224>)
 80020ea:	f88d 1028 	strb.w	r1, [sp, #40]	@ 0x28
 80020ee:	f000 fb23 	bl	8002738 <memchr>
 80020f2:	2800      	cmp	r0, #0
 80020f4:	d03f      	beq.n	8002176 <_vfiprintf_r+0x20a>
 80020f6:	4b27      	ldr	r3, [pc, #156]	@ (8002194 <_vfiprintf_r+0x228>)
 80020f8:	bb1b      	cbnz	r3, 8002142 <_vfiprintf_r+0x1d6>
 80020fa:	9b03      	ldr	r3, [sp, #12]
 80020fc:	3307      	adds	r3, #7
 80020fe:	f023 0307 	bic.w	r3, r3, #7
 8002102:	3308      	adds	r3, #8
 8002104:	9303      	str	r3, [sp, #12]
 8002106:	9b09      	ldr	r3, [sp, #36]	@ 0x24
 8002108:	443b      	add	r3, r7
 800210a:	9309      	str	r3, [sp, #36]	@ 0x24
 800210c:	e76a      	b.n	8001fe4 <_vfiprintf_r+0x78>
 800210e:	460c      	mov	r4, r1
 8002110:	2001      	movs	r0, #1
 8002112:	fb0c 3202 	mla	r2, ip, r2, r3
 8002116:	e7a8      	b.n	800206a <_vfiprintf_r+0xfe>
 8002118:	2300      	movs	r3, #0
 800211a:	f04f 0c0a 	mov.w	ip, #10
 800211e:	4619      	mov	r1, r3
 8002120:	3401      	adds	r4, #1
 8002122:	9305      	str	r3, [sp, #20]
 8002124:	4620      	mov	r0, r4
 8002126:	f810 2b01 	ldrb.w	r2, [r0], #1
 800212a:	3a30      	subs	r2, #48	@ 0x30
 800212c:	2a09      	cmp	r2, #9
 800212e:	d903      	bls.n	8002138 <_vfiprintf_r+0x1cc>
 8002130:	2b00      	cmp	r3, #0
 8002132:	d0c6      	beq.n	80020c2 <_vfiprintf_r+0x156>
 8002134:	9105      	str	r1, [sp, #20]
 8002136:	e7c4      	b.n	80020c2 <_vfiprintf_r+0x156>
 8002138:	4604      	mov	r4, r0
 800213a:	2301      	movs	r3, #1
 800213c:	fb0c 2101 	mla	r1, ip, r1, r2
 8002140:	e7f0      	b.n	8002124 <_vfiprintf_r+0x1b8>
 8002142:	ab03      	add	r3, sp, #12
 8002144:	9300      	str	r3, [sp, #0]
 8002146:	462a      	mov	r2, r5
 8002148:	4630      	mov	r0, r6
 800214a:	4b13      	ldr	r3, [pc, #76]	@ (8002198 <_vfiprintf_r+0x22c>)
 800214c:	a904      	add	r1, sp, #16
 800214e:	f3af 8000 	nop.w
 8002152:	4607      	mov	r7, r0
 8002154:	1c78      	adds	r0, r7, #1
 8002156:	d1d6      	bne.n	8002106 <_vfiprintf_r+0x19a>
 8002158:	6e6b      	ldr	r3, [r5, #100]	@ 0x64
 800215a:	07d9      	lsls	r1, r3, #31
 800215c:	d405      	bmi.n	800216a <_vfiprintf_r+0x1fe>
 800215e:	89ab      	ldrh	r3, [r5, #12]
 8002160:	059a      	lsls	r2, r3, #22
 8002162:	d402      	bmi.n	800216a <_vfiprintf_r+0x1fe>
 8002164:	6da8      	ldr	r0, [r5, #88]	@ 0x58
 8002166:	f7ff fde1 	bl	8001d2c <__retarget_lock_release_recursive>
 800216a:	89ab      	ldrh	r3, [r5, #12]
 800216c:	065b      	lsls	r3, r3, #25
 800216e:	f53f af1f 	bmi.w	8001fb0 <_vfiprintf_r+0x44>
 8002172:	9809      	ldr	r0, [sp, #36]	@ 0x24
 8002174:	e71e      	b.n	8001fb4 <_vfiprintf_r+0x48>
 8002176:	ab03      	add	r3, sp, #12
 8002178:	9300      	str	r3, [sp, #0]
 800217a:	462a      	mov	r2, r5
 800217c:	4630      	mov	r0, r6
 800217e:	4b06      	ldr	r3, [pc, #24]	@ (8002198 <_vfiprintf_r+0x22c>)
 8002180:	a904      	add	r1, sp, #16
 8002182:	f000 f87d 	bl	8002280 <_printf_i>
 8002186:	e7e4      	b.n	8002152 <_vfiprintf_r+0x1e6>
 8002188:	080029a8 	.word	0x080029a8
 800218c:	080029ae 	.word	0x080029ae
 8002190:	080029b2 	.word	0x080029b2
 8002194:	00000000 	.word	0x00000000
 8002198:	08001f47 	.word	0x08001f47

0800219c <_printf_common>:
 800219c:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
 80021a0:	4616      	mov	r6, r2
 80021a2:	4698      	mov	r8, r3
 80021a4:	688a      	ldr	r2, [r1, #8]
 80021a6:	690b      	ldr	r3, [r1, #16]
 80021a8:	4607      	mov	r7, r0
 80021aa:	4293      	cmp	r3, r2
 80021ac:	bfb8      	it	lt
 80021ae:	4613      	movlt	r3, r2
 80021b0:	6033      	str	r3, [r6, #0]
 80021b2:	f891 2043 	ldrb.w	r2, [r1, #67]	@ 0x43
 80021b6:	460c      	mov	r4, r1
 80021b8:	f8dd 9020 	ldr.w	r9, [sp, #32]
 80021bc:	b10a      	cbz	r2, 80021c2 <_printf_common+0x26>
 80021be:	3301      	adds	r3, #1
 80021c0:	6033      	str	r3, [r6, #0]
 80021c2:	6823      	ldr	r3, [r4, #0]
 80021c4:	0699      	lsls	r1, r3, #26
 80021c6:	bf42      	ittt	mi
 80021c8:	6833      	ldrmi	r3, [r6, #0]
 80021ca:	3302      	addmi	r3, #2
 80021cc:	6033      	strmi	r3, [r6, #0]
 80021ce:	6825      	ldr	r5, [r4, #0]
 80021d0:	f015 0506 	ands.w	r5, r5, #6
 80021d4:	d106      	bne.n	80021e4 <_printf_common+0x48>
 80021d6:	f104 0a19 	add.w	sl, r4, #25
 80021da:	68e3      	ldr	r3, [r4, #12]
 80021dc:	6832      	ldr	r2, [r6, #0]
 80021de:	1a9b      	subs	r3, r3, r2
 80021e0:	42ab      	cmp	r3, r5
 80021e2:	dc2b      	bgt.n	800223c <_printf_common+0xa0>
 80021e4:	f894 3043 	ldrb.w	r3, [r4, #67]	@ 0x43
 80021e8:	6822      	ldr	r2, [r4, #0]
 80021ea:	3b00      	subs	r3, #0
 80021ec:	bf18      	it	ne
 80021ee:	2301      	movne	r3, #1
 80021f0:	0692      	lsls	r2, r2, #26
 80021f2:	d430      	bmi.n	8002256 <_printf_common+0xba>
 80021f4:	4641      	mov	r1, r8
 80021f6:	4638      	mov	r0, r7
 80021f8:	f104 0243 	add.w	r2, r4, #67	@ 0x43
 80021fc:	47c8      	blx	r9
 80021fe:	3001      	adds	r0, #1
 8002200:	d023      	beq.n	800224a <_printf_common+0xae>
 8002202:	6823      	ldr	r3, [r4, #0]
 8002204:	6922      	ldr	r2, [r4, #16]
 8002206:	f003 0306 	and.w	r3, r3, #6
 800220a:	2b04      	cmp	r3, #4
 800220c:	bf14      	ite	ne
 800220e:	2500      	movne	r5, #0
 8002210:	6833      	ldreq	r3, [r6, #0]
 8002212:	f04f 0600 	mov.w	r6, #0
 8002216:	bf08      	it	eq
 8002218:	68e5      	ldreq	r5, [r4, #12]
 800221a:	f104 041a 	add.w	r4, r4, #26
 800221e:	bf08      	it	eq
 8002220:	1aed      	subeq	r5, r5, r3
 8002222:	f854 3c12 	ldr.w	r3, [r4, #-18]
 8002226:	bf08      	it	eq
 8002228:	ea25 75e5 	biceq.w	r5, r5, r5, asr #31
 800222c:	4293      	cmp	r3, r2
 800222e:	bfc4      	itt	gt
 8002230:	1a9b      	subgt	r3, r3, r2
 8002232:	18ed      	addgt	r5, r5, r3
 8002234:	42b5      	cmp	r5, r6
 8002236:	d11a      	bne.n	800226e <_printf_common+0xd2>
 8002238:	2000      	movs	r0, #0
 800223a:	e008      	b.n	800224e <_printf_common+0xb2>
 800223c:	2301      	movs	r3, #1
 800223e:	4652      	mov	r2, sl
 8002240:	4641      	mov	r1, r8
 8002242:	4638      	mov	r0, r7
 8002244:	47c8      	blx	r9
 8002246:	3001      	adds	r0, #1
 8002248:	d103      	bne.n	8002252 <_printf_common+0xb6>
 800224a:	f04f 30ff 	mov.w	r0, #4294967295
 800224e:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 8002252:	3501      	adds	r5, #1
 8002254:	e7c1      	b.n	80021da <_printf_common+0x3e>
 8002256:	2030      	movs	r0, #48	@ 0x30
 8002258:	18e1      	adds	r1, r4, r3
 800225a:	f881 0043 	strb.w	r0, [r1, #67]	@ 0x43
 800225e:	1c5a      	adds	r2, r3, #1
 8002260:	f894 1045 	ldrb.w	r1, [r4, #69]	@ 0x45
 8002264:	4422      	add	r2, r4
 8002266:	3302      	adds	r3, #2
 8002268:	f882 1043 	strb.w	r1, [r2, #67]	@ 0x43
 800226c:	e7c2      	b.n	80021f4 <_printf_common+0x58>
 800226e:	2301      	movs	r3, #1
 8002270:	4622      	mov	r2, r4
 8002272:	4641      	mov	r1, r8
 8002274:	4638      	mov	r0, r7
 8002276:	47c8      	blx	r9
 8002278:	3001      	adds	r0, #1
 800227a:	d0e6      	beq.n	800224a <_printf_common+0xae>
 800227c:	3601      	adds	r6, #1
 800227e:	e7d9      	b.n	8002234 <_printf_common+0x98>

08002280 <_printf_i>:
 8002280:	e92d 47ff 	stmdb	sp!, {r0, r1, r2, r3, r4, r5, r6, r7, r8, r9, sl, lr}
 8002284:	7e0f      	ldrb	r7, [r1, #24]
 8002286:	4691      	mov	r9, r2
 8002288:	2f78      	cmp	r7, #120	@ 0x78
 800228a:	4680      	mov	r8, r0
 800228c:	460c      	mov	r4, r1
 800228e:	469a      	mov	sl, r3
 8002290:	9e0c      	ldr	r6, [sp, #48]	@ 0x30
 8002292:	f101 0243 	add.w	r2, r1, #67	@ 0x43
 8002296:	d807      	bhi.n	80022a8 <_printf_i+0x28>
 8002298:	2f62      	cmp	r7, #98	@ 0x62
 800229a:	d80a      	bhi.n	80022b2 <_printf_i+0x32>
 800229c:	2f00      	cmp	r7, #0
 800229e:	f000 80d3 	beq.w	8002448 <_printf_i+0x1c8>
 80022a2:	2f58      	cmp	r7, #88	@ 0x58
 80022a4:	f000 80ba 	beq.w	800241c <_printf_i+0x19c>
 80022a8:	f104 0642 	add.w	r6, r4, #66	@ 0x42
 80022ac:	f884 7042 	strb.w	r7, [r4, #66]	@ 0x42
 80022b0:	e03a      	b.n	8002328 <_printf_i+0xa8>
 80022b2:	f1a7 0363 	sub.w	r3, r7, #99	@ 0x63
 80022b6:	2b15      	cmp	r3, #21
 80022b8:	d8f6      	bhi.n	80022a8 <_printf_i+0x28>
 80022ba:	a101      	add	r1, pc, #4	@ (adr r1, 80022c0 <_printf_i+0x40>)
 80022bc:	f851 f023 	ldr.w	pc, [r1, r3, lsl #2]
 80022c0:	08002319 	.word	0x08002319
 80022c4:	0800232d 	.word	0x0800232d
 80022c8:	080022a9 	.word	0x080022a9
 80022cc:	080022a9 	.word	0x080022a9
 80022d0:	080022a9 	.word	0x080022a9
 80022d4:	080022a9 	.word	0x080022a9
 80022d8:	0800232d 	.word	0x0800232d
 80022dc:	080022a9 	.word	0x080022a9
 80022e0:	080022a9 	.word	0x080022a9
 80022e4:	080022a9 	.word	0x080022a9
 80022e8:	080022a9 	.word	0x080022a9
 80022ec:	0800242f 	.word	0x0800242f
 80022f0:	08002357 	.word	0x08002357
 80022f4:	080023e9 	.word	0x080023e9
 80022f8:	080022a9 	.word	0x080022a9
 80022fc:	080022a9 	.word	0x080022a9
 8002300:	08002451 	.word	0x08002451
 8002304:	080022a9 	.word	0x080022a9
 8002308:	08002357 	.word	0x08002357
 800230c:	080022a9 	.word	0x080022a9
 8002310:	080022a9 	.word	0x080022a9
 8002314:	080023f1 	.word	0x080023f1
 8002318:	6833      	ldr	r3, [r6, #0]
 800231a:	1d1a      	adds	r2, r3, #4
 800231c:	681b      	ldr	r3, [r3, #0]
 800231e:	6032      	str	r2, [r6, #0]
 8002320:	f104 0642 	add.w	r6, r4, #66	@ 0x42
 8002324:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
 8002328:	2301      	movs	r3, #1
 800232a:	e09e      	b.n	800246a <_printf_i+0x1ea>
 800232c:	6833      	ldr	r3, [r6, #0]
 800232e:	6820      	ldr	r0, [r4, #0]
 8002330:	1d19      	adds	r1, r3, #4
 8002332:	6031      	str	r1, [r6, #0]
 8002334:	0606      	lsls	r6, r0, #24
 8002336:	d501      	bpl.n	800233c <_printf_i+0xbc>
 8002338:	681d      	ldr	r5, [r3, #0]
 800233a:	e003      	b.n	8002344 <_printf_i+0xc4>
 800233c:	0645      	lsls	r5, r0, #25
 800233e:	d5fb      	bpl.n	8002338 <_printf_i+0xb8>
 8002340:	f9b3 5000 	ldrsh.w	r5, [r3]
 8002344:	2d00      	cmp	r5, #0
 8002346:	da03      	bge.n	8002350 <_printf_i+0xd0>
 8002348:	232d      	movs	r3, #45	@ 0x2d
 800234a:	426d      	negs	r5, r5
 800234c:	f884 3043 	strb.w	r3, [r4, #67]	@ 0x43
 8002350:	230a      	movs	r3, #10
 8002352:	4859      	ldr	r0, [pc, #356]	@ (80024b8 <_printf_i+0x238>)
 8002354:	e011      	b.n	800237a <_printf_i+0xfa>
 8002356:	6821      	ldr	r1, [r4, #0]
 8002358:	6833      	ldr	r3, [r6, #0]
 800235a:	0608      	lsls	r0, r1, #24
 800235c:	f853 5b04 	ldr.w	r5, [r3], #4
 8002360:	d402      	bmi.n	8002368 <_printf_i+0xe8>
 8002362:	0649      	lsls	r1, r1, #25
 8002364:	bf48      	it	mi
 8002366:	b2ad      	uxthmi	r5, r5
 8002368:	2f6f      	cmp	r7, #111	@ 0x6f
 800236a:	6033      	str	r3, [r6, #0]
 800236c:	bf14      	ite	ne
 800236e:	230a      	movne	r3, #10
 8002370:	2308      	moveq	r3, #8
 8002372:	4851      	ldr	r0, [pc, #324]	@ (80024b8 <_printf_i+0x238>)
 8002374:	2100      	movs	r1, #0
 8002376:	f884 1043 	strb.w	r1, [r4, #67]	@ 0x43
 800237a:	6866      	ldr	r6, [r4, #4]
 800237c:	2e00      	cmp	r6, #0
 800237e:	bfa8      	it	ge
 8002380:	6821      	ldrge	r1, [r4, #0]
 8002382:	60a6      	str	r6, [r4, #8]
 8002384:	bfa4      	itt	ge
 8002386:	f021 0104 	bicge.w	r1, r1, #4
 800238a:	6021      	strge	r1, [r4, #0]
 800238c:	b90d      	cbnz	r5, 8002392 <_printf_i+0x112>
 800238e:	2e00      	cmp	r6, #0
 8002390:	d04b      	beq.n	800242a <_printf_i+0x1aa>
 8002392:	4616      	mov	r6, r2
 8002394:	fbb5 f1f3 	udiv	r1, r5, r3
 8002398:	fb03 5711 	mls	r7, r3, r1, r5
 800239c:	5dc7      	ldrb	r7, [r0, r7]
 800239e:	f806 7d01 	strb.w	r7, [r6, #-1]!
 80023a2:	462f      	mov	r7, r5
 80023a4:	42bb      	cmp	r3, r7
 80023a6:	460d      	mov	r5, r1
 80023a8:	d9f4      	bls.n	8002394 <_printf_i+0x114>
 80023aa:	2b08      	cmp	r3, #8
 80023ac:	d10b      	bne.n	80023c6 <_printf_i+0x146>
 80023ae:	6823      	ldr	r3, [r4, #0]
 80023b0:	07df      	lsls	r7, r3, #31
 80023b2:	d508      	bpl.n	80023c6 <_printf_i+0x146>
 80023b4:	6923      	ldr	r3, [r4, #16]
 80023b6:	6861      	ldr	r1, [r4, #4]
 80023b8:	4299      	cmp	r1, r3
 80023ba:	bfde      	ittt	le
 80023bc:	2330      	movle	r3, #48	@ 0x30
 80023be:	f806 3c01 	strble.w	r3, [r6, #-1]
 80023c2:	f106 36ff 	addle.w	r6, r6, #4294967295
 80023c6:	1b92      	subs	r2, r2, r6
 80023c8:	6122      	str	r2, [r4, #16]
 80023ca:	464b      	mov	r3, r9
 80023cc:	4621      	mov	r1, r4
 80023ce:	4640      	mov	r0, r8
 80023d0:	f8cd a000 	str.w	sl, [sp]
 80023d4:	aa03      	add	r2, sp, #12
 80023d6:	f7ff fee1 	bl	800219c <_printf_common>
 80023da:	3001      	adds	r0, #1
 80023dc:	d14a      	bne.n	8002474 <_printf_i+0x1f4>
 80023de:	f04f 30ff 	mov.w	r0, #4294967295
 80023e2:	b004      	add	sp, #16
 80023e4:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 80023e8:	6823      	ldr	r3, [r4, #0]
 80023ea:	f043 0320 	orr.w	r3, r3, #32
 80023ee:	6023      	str	r3, [r4, #0]
 80023f0:	2778      	movs	r7, #120	@ 0x78
 80023f2:	4832      	ldr	r0, [pc, #200]	@ (80024bc <_printf_i+0x23c>)
 80023f4:	f884 7045 	strb.w	r7, [r4, #69]	@ 0x45
 80023f8:	6823      	ldr	r3, [r4, #0]
 80023fa:	6831      	ldr	r1, [r6, #0]
 80023fc:	061f      	lsls	r7, r3, #24
 80023fe:	f851 5b04 	ldr.w	r5, [r1], #4
 8002402:	d402      	bmi.n	800240a <_printf_i+0x18a>
 8002404:	065f      	lsls	r7, r3, #25
 8002406:	bf48      	it	mi
 8002408:	b2ad      	uxthmi	r5, r5
 800240a:	6031      	str	r1, [r6, #0]
 800240c:	07d9      	lsls	r1, r3, #31
 800240e:	bf44      	itt	mi
 8002410:	f043 0320 	orrmi.w	r3, r3, #32
 8002414:	6023      	strmi	r3, [r4, #0]
 8002416:	b11d      	cbz	r5, 8002420 <_printf_i+0x1a0>
 8002418:	2310      	movs	r3, #16
 800241a:	e7ab      	b.n	8002374 <_printf_i+0xf4>
 800241c:	4826      	ldr	r0, [pc, #152]	@ (80024b8 <_printf_i+0x238>)
 800241e:	e7e9      	b.n	80023f4 <_printf_i+0x174>
 8002420:	6823      	ldr	r3, [r4, #0]
 8002422:	f023 0320 	bic.w	r3, r3, #32
 8002426:	6023      	str	r3, [r4, #0]
 8002428:	e7f6      	b.n	8002418 <_printf_i+0x198>
 800242a:	4616      	mov	r6, r2
 800242c:	e7bd      	b.n	80023aa <_printf_i+0x12a>
 800242e:	6833      	ldr	r3, [r6, #0]
 8002430:	6825      	ldr	r5, [r4, #0]
 8002432:	1d18      	adds	r0, r3, #4
 8002434:	6961      	ldr	r1, [r4, #20]
 8002436:	6030      	str	r0, [r6, #0]
 8002438:	062e      	lsls	r6, r5, #24
 800243a:	681b      	ldr	r3, [r3, #0]
 800243c:	d501      	bpl.n	8002442 <_printf_i+0x1c2>
 800243e:	6019      	str	r1, [r3, #0]
 8002440:	e002      	b.n	8002448 <_printf_i+0x1c8>
 8002442:	0668      	lsls	r0, r5, #25
 8002444:	d5fb      	bpl.n	800243e <_printf_i+0x1be>
 8002446:	8019      	strh	r1, [r3, #0]
 8002448:	2300      	movs	r3, #0
 800244a:	4616      	mov	r6, r2
 800244c:	6123      	str	r3, [r4, #16]
 800244e:	e7bc      	b.n	80023ca <_printf_i+0x14a>
 8002450:	6833      	ldr	r3, [r6, #0]
 8002452:	2100      	movs	r1, #0
 8002454:	1d1a      	adds	r2, r3, #4
 8002456:	6032      	str	r2, [r6, #0]
 8002458:	681e      	ldr	r6, [r3, #0]
 800245a:	6862      	ldr	r2, [r4, #4]
 800245c:	4630      	mov	r0, r6
 800245e:	f000 f96b 	bl	8002738 <memchr>
 8002462:	b108      	cbz	r0, 8002468 <_printf_i+0x1e8>
 8002464:	1b80      	subs	r0, r0, r6
 8002466:	6060      	str	r0, [r4, #4]
 8002468:	6863      	ldr	r3, [r4, #4]
 800246a:	6123      	str	r3, [r4, #16]
 800246c:	2300      	movs	r3, #0
 800246e:	f884 3043 	strb.w	r3, [r4, #67]	@ 0x43
 8002472:	e7aa      	b.n	80023ca <_printf_i+0x14a>
 8002474:	4632      	mov	r2, r6
 8002476:	4649      	mov	r1, r9
 8002478:	4640      	mov	r0, r8
 800247a:	6923      	ldr	r3, [r4, #16]
 800247c:	47d0      	blx	sl
 800247e:	3001      	adds	r0, #1
 8002480:	d0ad      	beq.n	80023de <_printf_i+0x15e>
 8002482:	6823      	ldr	r3, [r4, #0]
 8002484:	079b      	lsls	r3, r3, #30
 8002486:	d413      	bmi.n	80024b0 <_printf_i+0x230>
 8002488:	68e0      	ldr	r0, [r4, #12]
 800248a:	9b03      	ldr	r3, [sp, #12]
 800248c:	4298      	cmp	r0, r3
 800248e:	bfb8      	it	lt
 8002490:	4618      	movlt	r0, r3
 8002492:	e7a6      	b.n	80023e2 <_printf_i+0x162>
 8002494:	2301      	movs	r3, #1
 8002496:	4632      	mov	r2, r6
 8002498:	4649      	mov	r1, r9
 800249a:	4640      	mov	r0, r8
 800249c:	47d0      	blx	sl
 800249e:	3001      	adds	r0, #1
 80024a0:	d09d      	beq.n	80023de <_printf_i+0x15e>
 80024a2:	3501      	adds	r5, #1
 80024a4:	68e3      	ldr	r3, [r4, #12]
 80024a6:	9903      	ldr	r1, [sp, #12]
 80024a8:	1a5b      	subs	r3, r3, r1
 80024aa:	42ab      	cmp	r3, r5
 80024ac:	dcf2      	bgt.n	8002494 <_printf_i+0x214>
 80024ae:	e7eb      	b.n	8002488 <_printf_i+0x208>
 80024b0:	2500      	movs	r5, #0
 80024b2:	f104 0619 	add.w	r6, r4, #25
 80024b6:	e7f5      	b.n	80024a4 <_printf_i+0x224>
 80024b8:	080029b9 	.word	0x080029b9
 80024bc:	080029ca 	.word	0x080029ca

080024c0 <__sflush_r>:
 80024c0:	f9b1 200c 	ldrsh.w	r2, [r1, #12]
 80024c4:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 80024c6:	0716      	lsls	r6, r2, #28
 80024c8:	4605      	mov	r5, r0
 80024ca:	460c      	mov	r4, r1
 80024cc:	d454      	bmi.n	8002578 <__sflush_r+0xb8>
 80024ce:	684b      	ldr	r3, [r1, #4]
 80024d0:	2b00      	cmp	r3, #0
 80024d2:	dc02      	bgt.n	80024da <__sflush_r+0x1a>
 80024d4:	6c0b      	ldr	r3, [r1, #64]	@ 0x40
 80024d6:	2b00      	cmp	r3, #0
 80024d8:	dd48      	ble.n	800256c <__sflush_r+0xac>
 80024da:	6ae6      	ldr	r6, [r4, #44]	@ 0x2c
 80024dc:	2e00      	cmp	r6, #0
 80024de:	d045      	beq.n	800256c <__sflush_r+0xac>
 80024e0:	2300      	movs	r3, #0
 80024e2:	f412 5280 	ands.w	r2, r2, #4096	@ 0x1000
 80024e6:	682f      	ldr	r7, [r5, #0]
 80024e8:	6a21      	ldr	r1, [r4, #32]
 80024ea:	602b      	str	r3, [r5, #0]
 80024ec:	d030      	beq.n	8002550 <__sflush_r+0x90>
 80024ee:	6d62      	ldr	r2, [r4, #84]	@ 0x54
 80024f0:	89a3      	ldrh	r3, [r4, #12]
 80024f2:	0759      	lsls	r1, r3, #29
 80024f4:	d505      	bpl.n	8002502 <__sflush_r+0x42>
 80024f6:	6863      	ldr	r3, [r4, #4]
 80024f8:	1ad2      	subs	r2, r2, r3
 80024fa:	6b63      	ldr	r3, [r4, #52]	@ 0x34
 80024fc:	b10b      	cbz	r3, 8002502 <__sflush_r+0x42>
 80024fe:	6c23      	ldr	r3, [r4, #64]	@ 0x40
 8002500:	1ad2      	subs	r2, r2, r3
 8002502:	2300      	movs	r3, #0
 8002504:	4628      	mov	r0, r5
 8002506:	6ae6      	ldr	r6, [r4, #44]	@ 0x2c
 8002508:	6a21      	ldr	r1, [r4, #32]
 800250a:	47b0      	blx	r6
 800250c:	1c43      	adds	r3, r0, #1
 800250e:	89a3      	ldrh	r3, [r4, #12]
 8002510:	d106      	bne.n	8002520 <__sflush_r+0x60>
 8002512:	6829      	ldr	r1, [r5, #0]
 8002514:	291d      	cmp	r1, #29
 8002516:	d82b      	bhi.n	8002570 <__sflush_r+0xb0>
 8002518:	4a28      	ldr	r2, [pc, #160]	@ (80025bc <__sflush_r+0xfc>)
 800251a:	410a      	asrs	r2, r1
 800251c:	07d6      	lsls	r6, r2, #31
 800251e:	d427      	bmi.n	8002570 <__sflush_r+0xb0>
 8002520:	2200      	movs	r2, #0
 8002522:	6062      	str	r2, [r4, #4]
 8002524:	6922      	ldr	r2, [r4, #16]
 8002526:	04d9      	lsls	r1, r3, #19
 8002528:	6022      	str	r2, [r4, #0]
 800252a:	d504      	bpl.n	8002536 <__sflush_r+0x76>
 800252c:	1c42      	adds	r2, r0, #1
 800252e:	d101      	bne.n	8002534 <__sflush_r+0x74>
 8002530:	682b      	ldr	r3, [r5, #0]
 8002532:	b903      	cbnz	r3, 8002536 <__sflush_r+0x76>
 8002534:	6560      	str	r0, [r4, #84]	@ 0x54
 8002536:	6b61      	ldr	r1, [r4, #52]	@ 0x34
 8002538:	602f      	str	r7, [r5, #0]
 800253a:	b1b9      	cbz	r1, 800256c <__sflush_r+0xac>
 800253c:	f104 0344 	add.w	r3, r4, #68	@ 0x44
 8002540:	4299      	cmp	r1, r3
 8002542:	d002      	beq.n	800254a <__sflush_r+0x8a>
 8002544:	4628      	mov	r0, r5
 8002546:	f7ff fbf3 	bl	8001d30 <_free_r>
 800254a:	2300      	movs	r3, #0
 800254c:	6363      	str	r3, [r4, #52]	@ 0x34
 800254e:	e00d      	b.n	800256c <__sflush_r+0xac>
 8002550:	2301      	movs	r3, #1
 8002552:	4628      	mov	r0, r5
 8002554:	47b0      	blx	r6
 8002556:	4602      	mov	r2, r0
 8002558:	1c50      	adds	r0, r2, #1
 800255a:	d1c9      	bne.n	80024f0 <__sflush_r+0x30>
 800255c:	682b      	ldr	r3, [r5, #0]
 800255e:	2b00      	cmp	r3, #0
 8002560:	d0c6      	beq.n	80024f0 <__sflush_r+0x30>
 8002562:	2b1d      	cmp	r3, #29
 8002564:	d001      	beq.n	800256a <__sflush_r+0xaa>
 8002566:	2b16      	cmp	r3, #22
 8002568:	d11d      	bne.n	80025a6 <__sflush_r+0xe6>
 800256a:	602f      	str	r7, [r5, #0]
 800256c:	2000      	movs	r0, #0
 800256e:	e021      	b.n	80025b4 <__sflush_r+0xf4>
 8002570:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 8002574:	b21b      	sxth	r3, r3
 8002576:	e01a      	b.n	80025ae <__sflush_r+0xee>
 8002578:	690f      	ldr	r7, [r1, #16]
 800257a:	2f00      	cmp	r7, #0
 800257c:	d0f6      	beq.n	800256c <__sflush_r+0xac>
 800257e:	0793      	lsls	r3, r2, #30
 8002580:	bf18      	it	ne
 8002582:	2300      	movne	r3, #0
 8002584:	680e      	ldr	r6, [r1, #0]
 8002586:	bf08      	it	eq
 8002588:	694b      	ldreq	r3, [r1, #20]
 800258a:	1bf6      	subs	r6, r6, r7
 800258c:	600f      	str	r7, [r1, #0]
 800258e:	608b      	str	r3, [r1, #8]
 8002590:	2e00      	cmp	r6, #0
 8002592:	ddeb      	ble.n	800256c <__sflush_r+0xac>
 8002594:	4633      	mov	r3, r6
 8002596:	463a      	mov	r2, r7
 8002598:	4628      	mov	r0, r5
 800259a:	6a21      	ldr	r1, [r4, #32]
 800259c:	f8d4 c028 	ldr.w	ip, [r4, #40]	@ 0x28
 80025a0:	47e0      	blx	ip
 80025a2:	2800      	cmp	r0, #0
 80025a4:	dc07      	bgt.n	80025b6 <__sflush_r+0xf6>
 80025a6:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 80025aa:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 80025ae:	f04f 30ff 	mov.w	r0, #4294967295
 80025b2:	81a3      	strh	r3, [r4, #12]
 80025b4:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
 80025b6:	4407      	add	r7, r0
 80025b8:	1a36      	subs	r6, r6, r0
 80025ba:	e7e9      	b.n	8002590 <__sflush_r+0xd0>
 80025bc:	dfbffffe 	.word	0xdfbffffe

080025c0 <_fflush_r>:
 80025c0:	b538      	push	{r3, r4, r5, lr}
 80025c2:	690b      	ldr	r3, [r1, #16]
 80025c4:	4605      	mov	r5, r0
 80025c6:	460c      	mov	r4, r1
 80025c8:	b913      	cbnz	r3, 80025d0 <_fflush_r+0x10>
 80025ca:	2500      	movs	r5, #0
 80025cc:	4628      	mov	r0, r5
 80025ce:	bd38      	pop	{r3, r4, r5, pc}
 80025d0:	b118      	cbz	r0, 80025da <_fflush_r+0x1a>
 80025d2:	6a03      	ldr	r3, [r0, #32]
 80025d4:	b90b      	cbnz	r3, 80025da <_fflush_r+0x1a>
 80025d6:	f7ff f9b1 	bl	800193c <__sinit>
 80025da:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 80025de:	2b00      	cmp	r3, #0
 80025e0:	d0f3      	beq.n	80025ca <_fflush_r+0xa>
 80025e2:	6e62      	ldr	r2, [r4, #100]	@ 0x64
 80025e4:	07d0      	lsls	r0, r2, #31
 80025e6:	d404      	bmi.n	80025f2 <_fflush_r+0x32>
 80025e8:	0599      	lsls	r1, r3, #22
 80025ea:	d402      	bmi.n	80025f2 <_fflush_r+0x32>
 80025ec:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 80025ee:	f7ff fb9c 	bl	8001d2a <__retarget_lock_acquire_recursive>
 80025f2:	4628      	mov	r0, r5
 80025f4:	4621      	mov	r1, r4
 80025f6:	f7ff ff63 	bl	80024c0 <__sflush_r>
 80025fa:	6e63      	ldr	r3, [r4, #100]	@ 0x64
 80025fc:	4605      	mov	r5, r0
 80025fe:	07da      	lsls	r2, r3, #31
 8002600:	d4e4      	bmi.n	80025cc <_fflush_r+0xc>
 8002602:	89a3      	ldrh	r3, [r4, #12]
 8002604:	059b      	lsls	r3, r3, #22
 8002606:	d4e1      	bmi.n	80025cc <_fflush_r+0xc>
 8002608:	6da0      	ldr	r0, [r4, #88]	@ 0x58
 800260a:	f7ff fb8f 	bl	8001d2c <__retarget_lock_release_recursive>
 800260e:	e7dd      	b.n	80025cc <_fflush_r+0xc>

08002610 <__swhatbuf_r>:
 8002610:	b570      	push	{r4, r5, r6, lr}
 8002612:	460c      	mov	r4, r1
 8002614:	f9b1 100e 	ldrsh.w	r1, [r1, #14]
 8002618:	4615      	mov	r5, r2
 800261a:	2900      	cmp	r1, #0
 800261c:	461e      	mov	r6, r3
 800261e:	b096      	sub	sp, #88	@ 0x58
 8002620:	da0c      	bge.n	800263c <__swhatbuf_r+0x2c>
 8002622:	89a3      	ldrh	r3, [r4, #12]
 8002624:	2100      	movs	r1, #0
 8002626:	f013 0f80 	tst.w	r3, #128	@ 0x80
 800262a:	bf14      	ite	ne
 800262c:	2340      	movne	r3, #64	@ 0x40
 800262e:	f44f 6380 	moveq.w	r3, #1024	@ 0x400
 8002632:	2000      	movs	r0, #0
 8002634:	6031      	str	r1, [r6, #0]
 8002636:	602b      	str	r3, [r5, #0]
 8002638:	b016      	add	sp, #88	@ 0x58
 800263a:	bd70      	pop	{r4, r5, r6, pc}
 800263c:	466a      	mov	r2, sp
 800263e:	f000 f849 	bl	80026d4 <_fstat_r>
 8002642:	2800      	cmp	r0, #0
 8002644:	dbed      	blt.n	8002622 <__swhatbuf_r+0x12>
 8002646:	9901      	ldr	r1, [sp, #4]
 8002648:	f401 4170 	and.w	r1, r1, #61440	@ 0xf000
 800264c:	f5a1 5300 	sub.w	r3, r1, #8192	@ 0x2000
 8002650:	4259      	negs	r1, r3
 8002652:	4159      	adcs	r1, r3
 8002654:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 8002658:	e7eb      	b.n	8002632 <__swhatbuf_r+0x22>

0800265a <__smakebuf_r>:
 800265a:	898b      	ldrh	r3, [r1, #12]
 800265c:	b5f7      	push	{r0, r1, r2, r4, r5, r6, r7, lr}
 800265e:	079d      	lsls	r5, r3, #30
 8002660:	4606      	mov	r6, r0
 8002662:	460c      	mov	r4, r1
 8002664:	d507      	bpl.n	8002676 <__smakebuf_r+0x1c>
 8002666:	f104 0347 	add.w	r3, r4, #71	@ 0x47
 800266a:	6023      	str	r3, [r4, #0]
 800266c:	6123      	str	r3, [r4, #16]
 800266e:	2301      	movs	r3, #1
 8002670:	6163      	str	r3, [r4, #20]
 8002672:	b003      	add	sp, #12
 8002674:	bdf0      	pop	{r4, r5, r6, r7, pc}
 8002676:	466a      	mov	r2, sp
 8002678:	ab01      	add	r3, sp, #4
 800267a:	f7ff ffc9 	bl	8002610 <__swhatbuf_r>
 800267e:	9f00      	ldr	r7, [sp, #0]
 8002680:	4605      	mov	r5, r0
 8002682:	4639      	mov	r1, r7
 8002684:	4630      	mov	r0, r6
 8002686:	f7ff fbbd 	bl	8001e04 <_malloc_r>
 800268a:	b948      	cbnz	r0, 80026a0 <__smakebuf_r+0x46>
 800268c:	f9b4 300c 	ldrsh.w	r3, [r4, #12]
 8002690:	059a      	lsls	r2, r3, #22
 8002692:	d4ee      	bmi.n	8002672 <__smakebuf_r+0x18>
 8002694:	f023 0303 	bic.w	r3, r3, #3
 8002698:	f043 0302 	orr.w	r3, r3, #2
 800269c:	81a3      	strh	r3, [r4, #12]
 800269e:	e7e2      	b.n	8002666 <__smakebuf_r+0xc>
 80026a0:	89a3      	ldrh	r3, [r4, #12]
 80026a2:	e9c4 0704 	strd	r0, r7, [r4, #16]
 80026a6:	f043 0380 	orr.w	r3, r3, #128	@ 0x80
 80026aa:	81a3      	strh	r3, [r4, #12]
 80026ac:	9b01      	ldr	r3, [sp, #4]
 80026ae:	6020      	str	r0, [r4, #0]
 80026b0:	b15b      	cbz	r3, 80026ca <__smakebuf_r+0x70>
 80026b2:	4630      	mov	r0, r6
 80026b4:	f9b4 100e 	ldrsh.w	r1, [r4, #14]
 80026b8:	f000 f81e 	bl	80026f8 <_isatty_r>
 80026bc:	b128      	cbz	r0, 80026ca <__smakebuf_r+0x70>
 80026be:	89a3      	ldrh	r3, [r4, #12]
 80026c0:	f023 0303 	bic.w	r3, r3, #3
 80026c4:	f043 0301 	orr.w	r3, r3, #1
 80026c8:	81a3      	strh	r3, [r4, #12]
 80026ca:	89a3      	ldrh	r3, [r4, #12]
 80026cc:	431d      	orrs	r5, r3
 80026ce:	81a5      	strh	r5, [r4, #12]
 80026d0:	e7cf      	b.n	8002672 <__smakebuf_r+0x18>
	...

080026d4 <_fstat_r>:
 80026d4:	b538      	push	{r3, r4, r5, lr}
 80026d6:	2300      	movs	r3, #0
 80026d8:	4d06      	ldr	r5, [pc, #24]	@ (80026f4 <_fstat_r+0x20>)
 80026da:	4604      	mov	r4, r0
 80026dc:	4608      	mov	r0, r1
 80026de:	4611      	mov	r1, r2
 80026e0:	602b      	str	r3, [r5, #0]
 80026e2:	f7fe f822 	bl	800072a <_fstat>
 80026e6:	1c43      	adds	r3, r0, #1
 80026e8:	d102      	bne.n	80026f0 <_fstat_r+0x1c>
 80026ea:	682b      	ldr	r3, [r5, #0]
 80026ec:	b103      	cbz	r3, 80026f0 <_fstat_r+0x1c>
 80026ee:	6023      	str	r3, [r4, #0]
 80026f0:	bd38      	pop	{r3, r4, r5, pc}
 80026f2:	bf00      	nop
 80026f4:	20000284 	.word	0x20000284

080026f8 <_isatty_r>:
 80026f8:	b538      	push	{r3, r4, r5, lr}
 80026fa:	2300      	movs	r3, #0
 80026fc:	4d05      	ldr	r5, [pc, #20]	@ (8002714 <_isatty_r+0x1c>)
 80026fe:	4604      	mov	r4, r0
 8002700:	4608      	mov	r0, r1
 8002702:	602b      	str	r3, [r5, #0]
 8002704:	f7fe f816 	bl	8000734 <_isatty>
 8002708:	1c43      	adds	r3, r0, #1
 800270a:	d102      	bne.n	8002712 <_isatty_r+0x1a>
 800270c:	682b      	ldr	r3, [r5, #0]
 800270e:	b103      	cbz	r3, 8002712 <_isatty_r+0x1a>
 8002710:	6023      	str	r3, [r4, #0]
 8002712:	bd38      	pop	{r3, r4, r5, pc}
 8002714:	20000284 	.word	0x20000284

08002718 <_sbrk_r>:
 8002718:	b538      	push	{r3, r4, r5, lr}
 800271a:	2300      	movs	r3, #0
 800271c:	4d05      	ldr	r5, [pc, #20]	@ (8002734 <_sbrk_r+0x1c>)
 800271e:	4604      	mov	r4, r0
 8002720:	4608      	mov	r0, r1
 8002722:	602b      	str	r3, [r5, #0]
 8002724:	f7fe f80a 	bl	800073c <_sbrk>
 8002728:	1c43      	adds	r3, r0, #1
 800272a:	d102      	bne.n	8002732 <_sbrk_r+0x1a>
 800272c:	682b      	ldr	r3, [r5, #0]
 800272e:	b103      	cbz	r3, 8002732 <_sbrk_r+0x1a>
 8002730:	6023      	str	r3, [r4, #0]
 8002732:	bd38      	pop	{r3, r4, r5, pc}
 8002734:	20000284 	.word	0x20000284

08002738 <memchr>:
 8002738:	4603      	mov	r3, r0
 800273a:	b510      	push	{r4, lr}
 800273c:	b2c9      	uxtb	r1, r1
 800273e:	4402      	add	r2, r0
 8002740:	4293      	cmp	r3, r2
 8002742:	4618      	mov	r0, r3
 8002744:	d101      	bne.n	800274a <memchr+0x12>
 8002746:	2000      	movs	r0, #0
 8002748:	e003      	b.n	8002752 <memchr+0x1a>
 800274a:	7804      	ldrb	r4, [r0, #0]
 800274c:	3301      	adds	r3, #1
 800274e:	428c      	cmp	r4, r1
 8002750:	d1f6      	bne.n	8002740 <memchr+0x8>
 8002752:	bd10      	pop	{r4, pc}

08002754 <_init>:
 8002754:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8002756:	bf00      	nop
 8002758:	bcf8      	pop	{r3, r4, r5, r6, r7}
 800275a:	bc08      	pop	{r3}
 800275c:	469e      	mov	lr, r3
 800275e:	4770      	bx	lr

08002760 <_fini>:
 8002760:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8002762:	bf00      	nop
 8002764:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8002766:	bc08      	pop	{r3}
 8002768:	469e      	mov	lr, r3
 800276a:	4770      	bx	lr
