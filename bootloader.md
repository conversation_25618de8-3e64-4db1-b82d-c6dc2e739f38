# STM32F103C8T6 Custom Bootloader Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Memory Layout](#memory-layout)
4. [Current Implementation](#current-implementation)
5. [Communication Protocols](#communication-protocols)
6. [FUOTA Research & Implementation Plan](#fuota-research--implementation-plan)
7. [Hardware Requirements](#hardware-requirements)
8. [Development Status](#development-status)
9. [Future Enhancements](#future-enhancements)

## Overview

This project implements a custom bootloader for the STM32F103C8T6 microcontroller (Blue Pill board) that enables **Firmware Update Over The Air (FUOTA)** capabilities. The bootloader supports multiple communication protocols including UART, LoRa, and 4G/HTTP for robust firmware update mechanisms in IoT deployments.

### Key Features
- **Dual-stage bootloader architecture** with application separation
- **Multiple communication interfaces** (UART, LoRa, 4G/HTTP)
- **Robust flash memory management** with error handling
- **Visual feedback** via LED indicators
- **Timeout mechanisms** for automatic application launch
- **Extensible design** for future protocol additions

## System Architecture

### Project Structure
```
STM32F103C8T6-Bootloader-2/
├── Bootloader/           # Primary bootloader firmware
│   ├── Core/
│   │   ├── Inc/         # Header files
│   │   │   ├── main.h
│   │   │   ├── lora_handler.h
│   │   │   └── gsm_handler.h
│   │   └── Src/         # Source files
│   │       ├── main.c
│   │       └── lora_handler.c
│   └── STM32F103C8TX_FLASH.ld  # Bootloader linker script
├── Application/          # User application firmware
│   └── STM32F103C8TX_FLASH.ld  # Application linker script
├── PcTool/              # PC-based firmware update utility
│   ├── RS232/           # Serial communication library
│   └── etx_ota_update_main.c
└── gps_eth_clock3/      # Example application project
```

### Hardware Platform
- **Microcontroller**: STM32F103C8T6 (ARM Cortex-M3)
- **Flash Memory**: 64KB total
- **RAM**: 20KB
- **Clock**: 8MHz HSI (Internal RC Oscillator)
- **Communication**: Dual UART interfaces

## Memory Layout

### Flash Memory Partitioning
The 64KB flash memory is strategically partitioned to support the dual-stage bootloader architecture:

```
Flash Memory Map (64KB Total):
┌─────────────────────────────────────┐ 0x08000000
│           Bootloader                │
│            (16KB)                   │ 0x8000000 - 0x8003FFF
├─────────────────────────────────────┤ 0x08004000
│          Reserved                   │
│           (1KB)                     │ 0x8004000 - 0x80043FF
├─────────────────────────────────────┤ 0x08004400
│         Application                 │
│           (47KB)                    │ 0x8004400 - 0x800FFFF
└─────────────────────────────────────┘ 0x08010000
```

### Memory Configuration Details
- **Bootloader Region**: `0x8000000 - 0x8003FFF` (16KB)
  - Contains bootloader code, communication handlers, and flash management
  - Protected from application overwrites
  
- **Reserved Region**: `0x8004000 - 0x80043FF` (1KB)
  - Buffer zone for safety and future metadata storage
  - Can store firmware version info, update flags, etc.
  
- **Application Region**: `0x8004400 - 0x800FFFF` (47KB)
  - User application code space
  - Dynamically updated via bootloader
  - Erased in 47 pages during firmware updates

### RAM Allocation
- **Total RAM**: 20KB (`0x20000000 - 0x20004FFF`)
- **Heap Size**: 512 bytes minimum
- **Stack Size**: 1024 bytes minimum
- **Shared between bootloader and application** (context switching)

## Current Implementation

### Bootloader Core (`main.c`)
The bootloader implements a robust firmware update mechanism with the following key components:

#### 1. Initialization Sequence
```c
// System initialization
HAL_Init();
SystemClock_Config();
MX_GPIO_Init();
MX_USART1_UART_Init();  // Debug interface (115200 baud)
MX_USART3_UART_Init();  // Update interface (115200 baud)
```

#### 2. Update Detection Protocol
- **Timeout**: 5 seconds waiting for update signal
- **Handshake**: Expects 'o' character to initiate update
- **Visual Feedback**: LED on PB13 toggles during waiting period
- **Automatic Fallback**: Jumps to application if no update requested

#### 3. Firmware Reception Protocol
The bootloader implements a custom binary protocol for reliable data transfer:

```
Protocol Flow:
1. Handshake: Wait for 'o' → Send confirmation
2. Size Exchange: Receive firmware size (16-bit, little-endian)
3. Data Transfer: Byte-by-byte with acknowledgment
   - Send 'y' → Receive low byte
   - Send 'x' → Receive high byte
   - Repeat for entire firmware
4. Flash Programming: Write in 1KB blocks
5. Verification: Jump to application
```

#### 4. Flash Management
- **Erase Strategy**: Complete application region erase on first block
- **Write Method**: 16-bit halfword programming for STM32F1 compatibility
- **Error Handling**: Comprehensive status checking and recovery
- **Block Size**: 1KB chunks for efficient memory usage


### Communication Interfaces

#### UART Configuration
- **USART1** (PA9/PA10): Debug and status output (115200 baud)
- **USART3** (PB10/PB11): Firmware update interface (115200 baud)
- **Flow Control**: None (software-based protocol)
- **Data Format**: 8N1 (8 data bits, no parity, 1 stop bit)

#### GPIO Configuration
- **PB13**: Status LED (active high)
  - Toggles during update waiting period
  - Off during application execution
  - Indicates bootloader activity

### PC Tool Integration
The project includes a Windows-compatible PC tool (`etx_ota_update_main.c`) that:
- Reads binary firmware files
- Implements the bootloader communication protocol
- Provides progress feedback during updates
- Handles error conditions and retries

## Communication Protocols

### Current: UART-Based Updates
- **Reliability**: High (wired connection)
- **Speed**: 115200 baud (~11.5KB/s theoretical)
- **Range**: Limited to physical connection
- **Use Case**: Development and local updates

### Planned: LoRa FUOTA
- **Frequency**: 868MHz (EU) / 915MHz (US)
- **Range**: 2-15km (depending on environment)
- **Data Rate**: 0.3-50 kbps (adaptive)
- **Block Size**: 256 bytes (optimized for LoRa packet size)
- **Use Case**: Remote IoT device updates

### Planned: 4G/HTTP Updates
- **Connectivity**: Cellular data networks
- **Speed**: Up to several Mbps
- **Block Size**: 1024 bytes (larger blocks for faster transfer)
- **Use Case**: High-bandwidth remote updates

## FUOTA Research & Implementation Plan

### FUOTA (Firmware Update Over The Air) Background
FUOTA is a critical capability for IoT devices deployed in remote or inaccessible locations. It enables:
- **Remote maintenance** without physical access
- **Security patches** and bug fixes
- **Feature updates** and functionality enhancements
- **Cost reduction** in device lifecycle management

### Research Findings

#### LoRaWAN FUOTA Standards
- **LoRaWAN 1.0.4**: Introduces native FUOTA support
- **Fragmentation**: Large firmware split into small fragments
- **Redundancy**: Forward Error Correction (FEC) for reliability
- **Multicast**: Efficient updates for multiple devices

#### Security Considerations
- **Digital Signatures**: Firmware authenticity verification
- **Encryption**: AES-128/256 for data protection
- **Rollback Protection**: Version monotonicity enforcement
- **Secure Boot**: Chain of trust from bootloader to application

#### Cloud Integration Patterns
- **Device Management Platforms**: AWS IoT, Azure IoT, Google Cloud IoT
- **Update Orchestration**: Staged rollouts and A/B testing
- **Monitoring**: Update success/failure tracking
- **Rollback Mechanisms**: Automatic recovery from failed updates

### Implementation Architecture

#### Phase 1: Core FUOTA Manager
```c
// Proposed structure
typedef struct {
    uint32_t version;
    uint32_t size;
    uint32_t crc32;
    uint8_t signature[64];  // Digital signature
    uint8_t metadata[128];  // Additional info
} firmware_header_t;

typedef enum {
    FUOTA_IDLE,
    FUOTA_CHECKING,
    FUOTA_DOWNLOADING,
    FUOTA_VERIFYING,
    FUOTA_INSTALLING,
    FUOTA_COMPLETE,
    FUOTA_ERROR
} fuota_state_t;
```

#### Phase 2: Multi-Protocol Support
- **Protocol Abstraction**: Common interface for LoRa/4G/UART
- **Automatic Fallback**: Try LoRa first, fallback to 4G
- **Adaptive Parameters**: Adjust block sizes and timeouts per protocol
- **Quality of Service**: Prioritize critical updates

#### Phase 3: Advanced Features
- **Delta Updates**: Only transfer changed portions
- **Compression**: Reduce transfer size and time
- **Resumable Downloads**: Continue interrupted transfers
- **Batch Updates**: Update multiple devices simultaneously

## Hardware Requirements

### Current Setup
- **STM32F103C8T6** development board (Blue Pill)
- **USB-to-Serial** adapter for PC communication
- **Power Supply**: 3.3V via USB or external
- **Debug Interface**: ST-Link V2 for programming

### LoRa Extension Requirements
- **LoRa Module**: SX1276/SX1278 based (e.g., RFM95W)
- **Antenna**: 868MHz/915MHz quarter-wave or PCB antenna
- **SPI Interface**: Connect to STM32 SPI1 or SPI2
- **Additional GPIOs**: DIO0, DIO1 for interrupt handling

### 4G Extension Requirements
- **GSM/4G Module**: SIM800L, SIM7600, or Quectel EC25
- **SIM Card**: Data-enabled cellular subscription
- **UART Interface**: Connect to available USART
- **Power Management**: Higher current requirements (up to 2A peaks)

## Development Status

### ✅ Completed Features
- [x] Basic bootloader architecture
- [x] UART-based firmware updates
- [x] Flash memory management
- [x] Application jumping mechanism
- [x] PC tool for firmware upload
- [x] LED status indication
- [x] Error handling and recovery

### 🚧 In Progress
- [ ] LoRa handler implementation (skeleton exists)
- [ ] GSM handler implementation (header defined)
- [ ] FUOTA manager core
- [ ] Security framework

### 📋 Planned Features
- [ ] LoRaWAN FUOTA protocol implementation
- [ ] HTTP/HTTPS client for 4G updates
- [ ] Cloud service integration
- [ ] Firmware signing and verification
- [ ] Delta update support
- [ ] Web-based device management interface
- [ ] Over-the-air configuration updates

## Future Enhancements

### Short Term (1-3 months)
1. **Complete LoRa Integration**
   - Implement SX1276 driver
   - Add LoRaWAN stack
   - Test point-to-point updates

2. **Basic Security**
   - Add CRC32 verification
   - Implement simple authentication
   - Version checking

### Medium Term (3-6 months)
1. **4G/HTTP Implementation**
   - GSM module integration
   - HTTP client development
   - Cloud service backend

2. **Advanced Security**
   - Digital signature verification
   - AES encryption support
   - Secure key storage

### Long Term (6+ months)
1. **Production Features**
   - Delta update algorithms
   - Resumable downloads
   - A/B testing framework
   - Device fleet management

2. **Optimization**
   - Power management improvements
   - Memory usage optimization
   - Update speed enhancements

## Technical Deep Dive

### Bootloader Startup Sequence
```c
1. Reset_Handler (startup_stm32f103c8tx.s)
   ├── Copy .data section from Flash to RAM
   ├── Zero-initialize .bss section
   ├── Call SystemInit()
   └── Jump to main()

2. main() Function Flow
   ├── HAL_Init() - Initialize HAL library
   ├── SystemClock_Config() - Configure 8MHz HSI
   ├── Peripheral Initialization (GPIO, UART1, UART3)
   ├── Print bootloader version
   ├── Firmware_Update() - Check for updates
   └── goto_application() - Jump to user app
```

### Flash Programming Details
The STM32F103 requires specific programming procedures:

```c
// Flash programming sequence
1. HAL_FLASH_Unlock() - Unlock flash for writing
2. HAL_FLASHEx_Erase() - Erase pages (first block only)
3. HAL_FLASH_Program() - Write 16-bit halfwords
4. HAL_FLASH_Lock() - Lock flash after writing

// Memory alignment requirements
- Flash writes must be 16-bit aligned
- Erase operations work on 1KB pages
- Application starts at page boundary (0x8004400)
```

### Communication Protocol Analysis

#### Current UART Protocol
```
Handshake Phase:
Bootloader → PC: '.' (every 20ms, 250 iterations = 5s timeout)
PC → Bootloader: 'o' (start update)

Size Exchange:
Bootloader → PC: 'y'
PC → Bootloader: size_low_byte
Bootloader → PC: 'x'
PC → Bootloader: size_high_byte

Data Transfer (repeated for each byte):
Bootloader → PC: 'y'
PC → Bootloader: data_low_byte
Bootloader → PC: 'x'
PC → Bootloader: data_high_byte
```

#### Proposed LoRa Protocol
```c
typedef struct {
    uint8_t msg_type;        // 0x01=DATA, 0x02=ACK, 0x03=NACK
    uint16_t sequence_num;   // Packet sequence number
    uint16_t total_packets;  // Total packets in transfer
    uint8_t data_len;        // Payload length (max 250)
    uint8_t data[250];       // Actual firmware data
    uint16_t crc16;          // Packet integrity check
} lora_packet_t;
```

### Error Handling Mechanisms

#### Flash Operation Errors
- **Write Failures**: Retry mechanism with exponential backoff
- **Erase Failures**: Alternative page selection or full chip erase
- **Verification Errors**: Re-download corrupted blocks

#### Communication Errors
- **Timeout Handling**: Configurable timeouts per protocol
- **Checksum Failures**: Automatic retransmission requests
- **Protocol Violations**: State machine reset and recovery

### Performance Characteristics

#### Update Speed Comparison
| Protocol | Theoretical Speed | Practical Speed | Range |
|----------|------------------|-----------------|-------|
| UART     | 11.5 KB/s       | ~8 KB/s        | 3m    |
| LoRa     | 0.3-50 kbps     | ~2 KB/s        | 15km  |
| 4G HTTP  | 1-100 Mbps      | ~50 KB/s       | Global|

#### Memory Usage
- **Bootloader Size**: ~12KB (4KB available for expansion)
- **RAM Usage**: ~2KB during operation
- **Flash Buffer**: 1KB for block processing
- **Stack Usage**: ~512 bytes maximum

## Troubleshooting Guide

### Common Issues

#### 1. Bootloader Not Starting
**Symptoms**: No LED activity, no UART output
**Causes**:
- Incorrect flash programming
- Clock configuration issues
- Power supply problems
**Solutions**:
- Verify bootloader binary integrity
- Check HSI oscillator configuration
- Measure 3.3V supply voltage

#### 2. Update Timeout
**Symptoms**: "No Data Received for Firmware Update"
**Causes**:
- PC tool not running
- Wrong COM port selection
- UART wiring issues
python stm32_updater_python.py
**Solutions**:
- Verify COM port in Device Manager
- Check TX/RX connections (crossed)
- Test with loopback

#### 3. Flash Write Errors
**Symptoms**: "Flash Write Error...HALT!!!"
**Causes**:
- Flash memory corruption
- Voltage instability during write
- Hardware failure
**Solutions**:
- Mass erase and reprogram
- Check power supply stability
- Verify flash memory integrity

#### 4. Application Won't Start
**Symptoms**: "Invalid Application... HALT!!!"
**Causes**:
- Corrupted application binary
- Wrong application start address
- Incomplete firmware transfer
**Solutions**:
- Re-upload application firmware
- Verify linker script addresses
- Check transfer completion

### Debug Techniques

#### UART Debug Output
```c
// Enable debug output in bootloader
printf("Bootloader v%d:%d Started!!!\n", BL_Version[0], BL_Version[1]);
printf("Application Size = %d bytes\r\n", application_size);
printf("Received Block[%d]\r\n", current_app_size/MAX_BLOCK_SIZE);
```

#### LED Status Codes
- **Fast Blink**: Waiting for update
- **Solid On**: Update in progress
- **Off**: Application running
- **Slow Blink**: Error condition

#### Memory Inspection
```c
// Check application validity
uint32_t app_stack = *(volatile uint32_t*)ETX_APP_START_ADDRESS;
uint32_t app_reset = *(volatile uint32_t*)(ETX_APP_START_ADDRESS + 4);
printf("App Stack: 0x%08X, Reset: 0x%08X\r\n", app_stack, app_reset);
```

## Security Considerations

### Current Security Level: Basic
- **No encryption**: Firmware transferred in plaintext
- **No authentication**: No verification of firmware source
- **No integrity checking**: Basic protocol-level error detection only
- **No rollback protection**: Can downgrade to older versions

### Recommended Security Enhancements

#### 1. Firmware Signing (High Priority)
```c
typedef struct {
    uint32_t magic;          // 0xDEADBEEF
    uint32_t version;        // Semantic version
    uint32_t size;           // Firmware size
    uint32_t crc32;          // Integrity check
    uint8_t signature[256];  // RSA-2048 signature
    uint8_t reserved[252];   // Future use
} secure_header_t;
```

#### 2. Encrypted Transfer (Medium Priority)
- **AES-128 CBC**: Encrypt firmware during transfer
- **Key Management**: Secure key storage in flash
- **IV Generation**: Random initialization vectors

#### 3. Secure Boot Chain (Low Priority)
- **Hardware Security Module**: STM32 crypto peripherals
- **Certificate Chain**: Root CA → Intermediate → Device certificates
- **Measured Boot**: Hash verification at each stage

### Threat Model Analysis

#### Potential Attacks
1. **Man-in-the-Middle**: Intercept and modify firmware
2. **Replay Attacks**: Reuse old firmware versions
3. **Denial of Service**: Corrupt bootloader or application
4. **Privilege Escalation**: Exploit bootloader vulnerabilities

#### Mitigation Strategies
1. **Cryptographic Signatures**: Prevent firmware tampering
2. **Version Monotonicity**: Prevent downgrade attacks
3. **Watchdog Timers**: Recover from DoS conditions
4. **Code Review**: Minimize vulnerability surface

## Compliance and Standards

### Relevant Standards
- **ISO 26262**: Automotive functional safety (if applicable)
- **IEC 62443**: Industrial cybersecurity
- **NIST Cybersecurity Framework**: General security guidelines
- **LoRaWAN 1.0.4**: FUOTA specification compliance

### Regulatory Considerations
- **FCC Part 15**: Radio frequency emissions (LoRa)
- **CE Marking**: European conformity (if commercial)
- **GDPR**: Data protection (if collecting telemetry)

---

**Document Version**: 1.1
**Last Updated**: December 2024
**Author**: STM32 Bootloader Development Team
**Review Status**: Technical Review Complete
