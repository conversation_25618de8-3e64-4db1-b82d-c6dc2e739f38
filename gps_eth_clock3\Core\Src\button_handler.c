#include "button_handler.h"
#include "time_manager.h"
#include "7seg_display.h"
#include "main.h"
#include "stdint.h"
#include "disp_i2c.h"

// Button state variables
static uint32_t button1_press_time = 0;
static uint32_t button2_press_time = 0;
static uint32_t button3_press_time = 0;
static uint8_t button1_pressed = 0;
static uint8_t button2_pressed = 0;
static uint8_t button3_pressed = 0;
static uint32_t last_blink = 0;
static uint8_t blink_state = 0;
static uint32_t last_debounce1 = 0;
static uint32_t last_debounce2 = 0;
static uint32_t last_debounce3 = 0;
static uint8_t button2_long_press_active = 0;
static uint32_t last_button2_increment = 0;
static uint8_t pattern_state = 0;     // Track C pattern state

// External variables
extern volatile uint8_t test_mode;
extern volatile uint8_t time_setting_mode;
extern volatile uint8_t selected_field;
extern volatile uint8_t manual_seconds;
extern volatile uint8_t manual_minutes;
extern volatile uint8_t manual_hours;
extern volatile uint8_t is_pm;
extern volatile uint8_t rtc_read_needed;
extern volatile uint8_t rtc_write_needed;
extern volatile uint8_t display_update_needed;
extern volatile uint8_t button_operation_in_progress;
extern uint8_t displayBuffer[6]; // Changed from 8 to 6
// Use the same volatile qualifier as in the header
extern volatile uint8_t day;
extern volatile uint8_t date;
extern volatile uint8_t month;
extern volatile uint8_t year;
extern volatile uint8_t show_date_mode;

/**
 * @brief Initialize button handler
 */
void init_button_handler(void)
{
    // Initialize button state variables
    button1_press_time = 0;
    button2_press_time = 0;
    button3_press_time = 0;
    button1_pressed = 0;
    button2_pressed = 0;
    button3_pressed = 0;
    last_blink = 0;
    blink_state = 0;
    last_debounce1 = 0;
    last_debounce2 = 0;
    last_debounce3 = 0;
    button2_long_press_active = 0;
    last_button2_increment = 0;
    pattern_state = 0;
    
    // Initialize global variables
    show_date_mode = 0;
}

/**
 * @brief Handle button presses and implement button logic
 * @param current_time Current system time in milliseconds
 */
void handle_buttons(uint32_t current_time)
{
    // Set flag to prevent I2C operations during button handling
    button_operation_in_progress = 1;
    
    //-----------------------------------------------------------------------------------
    // Button 1 (PB10) - Cycle through fields, save settings, show date
    //-----------------------------------------------------------------------------------
    if(HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_10) == GPIO_PIN_RESET) // Button pressed
    {
        if(!button1_pressed && (current_time - last_debounce1 > DEBOUNCE_TIME)) {
            button1_pressed = 1;
            button1_press_time = current_time;
        }
    }
    else if(button1_pressed) // Button released
    {
        uint32_t press_duration = current_time - button1_press_time;
        
        if(press_duration < LONG_PRESS_TIME) {
            // Short press detected
            if(time_setting_mode) {
                // In setting mode, cycle through fields
                selected_field = (selected_field + 1) % 3;  // 0=seconds, 1=minutes, 2=hours
            } else {
                // In normal mode, enter setting mode
                time_setting_mode = 1;
                selected_field = 0;  // Start with seconds
            }
        } else {
            // Long press detected
            if(time_setting_mode) {
                // In setting mode, save settings and exit
                time_setting_mode = 0;
                selected_field = 0;
                
                // Request RTC write
                rtc_write_needed = 1;
            } else {
                // In normal mode, show date
                showDate();
            }
        }
        
        button1_pressed = 0;
        last_debounce1 = current_time;
        display_update_needed = 1;
    }
    
    //-----------------------------------------------------------------------------------
    // Button 2 (PB11) - Increment field or toggle AM/PM
    //-----------------------------------------------------------------------------------
    if(HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_11) == GPIO_PIN_RESET) // Button pressed
    {
        // Handle initial button press
        if(!button2_pressed && (current_time - last_debounce2 > DEBOUNCE_TIME)) {
            button2_pressed = 1;
            button2_press_time = current_time;
            button2_long_press_active = 0;
            
            // Initial increment on button press
            if(time_setting_mode) {
                incrementSelectedField();
                display_update_needed = 1;
                last_button2_increment = current_time;
            }
        }
        
        // Handle long press detection
        if(button2_pressed && !button2_long_press_active && 
           (current_time - button2_press_time >= LONG_PRESS_TIME)) {
            
            button2_long_press_active = 1;
            
            if(time_setting_mode) {
                // Reset the selected field to zero
                resetSelectedField();
            } else {
                // Toggle AM/PM in normal mode
                toggleAmPm();
            }
            
            display_update_needed = 1;
        }
        
        // Handle auto-repeat for incrementing
        // Only perform auto-repeat if not in long press mode
        if(time_setting_mode && button2_pressed && !button2_long_press_active && 
           (current_time - button2_press_time > 500) && 
           (current_time - last_button2_increment > 80)) {
            
            // Increment the selected field safely
            incrementSelectedField();
            
            // Force display update
            display_update_needed = 1;
            
            // Update last increment time
            last_button2_increment = current_time;
        }
    }
    else if(button2_pressed) // Button released
    {
        button2_pressed = 0;
        button2_long_press_active = 0;
        last_debounce2 = current_time;
    }
    
    //-----------------------------------------------------------------------------------
    // Button 3 (PB5) - Original functionality
    //-----------------------------------------------------------------------------------
    if(HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_5) == GPIO_PIN_RESET) // Button pressed
    {
        if(!button3_pressed && (current_time - last_debounce3 > DEBOUNCE_TIME)) {
            button3_pressed = 1;
            button3_press_time = current_time;
            button_operation_in_progress = 1;  // Set flag to prevent I2C operations
        }
        
        uint32_t hold_duration = current_time - button3_press_time;
        if(hold_duration >= 5000) {
            for(int i = 0; i < 6; i++) {
                displayBuffer[i] = 5;
            }
        } else {
            for(int i = 0; i < 6; i++) {
                displayBuffer[i] = 4;
            }
        }
    }
    else if(button3_pressed) // Button released
    {
        // Show "4" briefly on release
        for(int i = 0; i < 6; i++) {
            displayBuffer[i] = 4;
        }
        
        // Schedule display update after 500ms
        HAL_Delay(500);
        
        if(test_mode) {
//            test_c_digits(pattern_state);
            rtc_read_needed = 1;

        } else {
            // Request RTC read
            rtc_read_needed = 1;
        }
        
        button3_pressed = 0;
        last_debounce3 = current_time;
        button_operation_in_progress = 0;  // Clear flag to allow I2C operations
    }
    
    //-----------------------------------------------------------------------------------
    // Update display for time setting mode
    //-----------------------------------------------------------------------------------
    if(time_setting_mode) {
        // Blink selected field at interval
        if(current_time - last_blink > BLINK_INTERVAL) {
            blink_state = !blink_state;
            last_blink = current_time;
            
            // Create display buffer with current time values
            uint8_t disp[6];
            
            // Ensure values are within valid range
            uint8_t h = (manual_hours < 24) ? manual_hours : 0;
            uint8_t m = (manual_minutes < 60) ? manual_minutes : 0;
            uint8_t s = (manual_seconds < 60) ? manual_seconds : 0;
            
            disp[0] = h / 10;
            disp[1] = h % 10;
            disp[2] = m / 10;
            disp[3] = m % 10;
            disp[4] = s / 10;
            disp[5] = s % 10;
            
            // Apply blinking to selected field
            if(blink_state) {
                switch(selected_field) {
                    case 0: // seconds (SS)
                        disp[4] = 0xFF; 
                        disp[5] = 0xFF; 
                        break;
                    case 1: // minutes (MM)
                        disp[2] = 0xFF; 
                        disp[3] = 0xFF; 
                        break;
                    case 2: // hours (HH)
                        disp[0] = 0xFF; 
                        disp[1] = 0xFF; 
                        break;
                }
            }
            
            // Update display buffer
            for(int i = 0; i < 6; i++) {
                displayBuffer[i] = disp[i];
            }
        }
    }
    
    // Clear flag after all button handling is complete
    button_operation_in_progress = 0;
}

/**
 * @brief Increment the currently selected field (seconds, minutes, or hours)
 */
void incrementSelectedField(void)
{
    // Disable interrupts during critical section
    __disable_irq();
    
    switch(selected_field) {
        case 0: // seconds
            // Safely increment seconds with bounds checking
            if(manual_seconds >= 59) {
                manual_seconds = 0;
            } else {
                manual_seconds++;
            }
            break;
            
        case 1: // minutes
            // Safely increment minutes with bounds checking
            if(manual_minutes >= 59) {
                manual_minutes = 0;
            } else {
                manual_minutes++;
            }
            break;
            
        case 2: // hours
            // Safely increment hours with bounds checking
            if(manual_hours >= 23) {
                manual_hours = 0;
            } else {
                manual_hours++;
            }
            // Update AM/PM flag
            is_pm = (manual_hours >= 12) ? 1 : 0;
            break;
            // This line updates the AM/PM flag based on the current hour.
            // If the hour is 12 or greater, it sets is_pm to 1 (true), indicating PM.
            // Otherwise, it sets is_pm to 0 (false), indicating AM.
            // This ensures that the AM/PM status is always correct after changing the hour.
    }
    
    // Re-enable interrupts
    __enable_irq();
}

/**
 * @brief Reset the currently selected field to zero
 */
void resetSelectedField(void)
{
    switch(selected_field) {
        case 0: manual_seconds = 0; break;
        case 1: manual_minutes = 0; break;
        case 2: 
            manual_hours = 0; 
            is_pm = 0;
            break;
    }
}

/**
 * @brief Toggle between AM and PM
 */
void toggleAmPm(void)
{
    if(is_pm) {
        // Currently PM, switch to AM
        if(manual_hours >= 12) {
            manual_hours -= 12;
        }
        is_pm = 0;
    } else {
        // Currently AM, switch to PM
        if(manual_hours < 12) {
            manual_hours += 12;
        }
        is_pm = 1;
    }
    
    // Save the change to RTC
    rtc_write_needed = 1;
}

/**
 * @brief Show the current date on the display
 */
void showDate(void)
{
    // Read date from RTC
    if(ds1307_read_date_safe(&day, &date, &month, &year)) {
        // Set flag to show date mode
        show_date_mode = 1;
        
        // Update display buffer with date
        displayBuffer[0] = date / 10;    // Tens digit of date
        displayBuffer[1] = date % 10;    // Units digit of date
        displayBuffer[2] = month / 10;   // Tens digit of month
        displayBuffer[3] = month % 10;   // Units digit of month
        displayBuffer[4] = year / 10;    // Tens digit of year
        displayBuffer[5] = year % 10;    // Units digit of year
        
        // Force display update
        display_update_needed = 1;
        
        // Wait for 3 seconds to show date
        HAL_Delay(3000);
        
        // Clear date mode flag
        show_date_mode = 0;
        
        // Request display update to show time again
        display_update_needed = 1;
    }
}
